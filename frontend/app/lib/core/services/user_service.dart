import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:fixnum/fixnum.dart';
import 'package:path/path.dart' as path;
import '../models/api_response.dart';
import '../models/base_model.dart';
import '../models/friend_models.dart';
import '../constants/app_constants.dart';
import '../utils/logger.dart';
import '../services/http/http_service.dart';
import '../services/secure_storage_service.dart';
import '../../generated/protobuf/protobuf_exports.dart';

/// 用户服务 - 管理用户信息和状态
class UserService extends ChangeNotifier {
  static const String _tag = 'UserService';

  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  // HTTP服务实例
  final HttpService _httpService = HttpService.instance;

  // 安全存储服务实例
  final SecureStorageService _secureStorage = SecureStorageService.instance;

  // 当前用户
  UserModel? _currentUser;

  // 联系人列表
  final List<UserModel> _contacts = [];

  // 用户缓存
  final Map<String, UserModel> _userCache = {};

  // 在线状态
  final Map<String, String> _userStatus = {};

  // 收藏联系人
  final Set<String> _favoriteContacts = {};

  // 是否已初始化
  bool _isInitialized = false;

  /// 获取当前用户
  UserModel? get currentUser => _currentUser;

  /// 获取所有联系人
  List<UserModel> get contacts => List.unmodifiable(_contacts);

  /// 获取收藏联系人
  List<UserModel> get favoriteContacts {
    return _contacts
        .where((user) => _favoriteContacts.contains(user.id))
        .toList();
  }

  /// 获取在线联系人
  List<UserModel> get onlineContacts {
    return _contacts.where((user) => isUserOnline(user.id)).toList();
  }

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化用户服务
  Future<void> initialize() async {
    if (_isInitialized) {
      Logger.debug(_tag, 'UserService already initialized');
      return;
    }

    try {
      Logger.info(_tag, 'Initializing UserService');

      // 尝试从本地存储加载用户信息
      await _loadUserFromStorage();

      // 如果有用户信息，尝试刷新用户数据
      if (_currentUser != null) {
        await _refreshCurrentUserProfile();
      }

      _isInitialized = true;
      Logger.info(_tag, 'UserService initialized successfully');
    } catch (e) {
      Logger.error(_tag, 'Failed to initialize UserService', e);
      // 即使初始化失败，也标记为已初始化，避免重复尝试
      _isInitialized = true;
    }
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    try {
      final userData = await _secureStorage.getJson('current_user');
      if (userData != null) {
        _currentUser = UserModel.fromMap(userData);
        Logger.debug(
          _tag,
          'Loaded user from storage: ${_currentUser?.username}',
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Failed to load user from storage', e);
    }
  }

  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage() async {
    try {
      if (_currentUser != null) {
        await _secureStorage.setJson('current_user', _currentUser!.toMap());
        Logger.debug(_tag, 'Saved user to storage: ${_currentUser?.username}');
      }
    } catch (e) {
      Logger.error(_tag, 'Failed to save user to storage', e);
    }
  }

  /// 刷新当前用户资料
  Future<void> _refreshCurrentUserProfile() async {
    try {
      final response = await getCurrentUserProfile();
      if (response.isSuccess && response.data != null) {
        _updateCurrentUserFromProtobuf(response.data!);
        await _saveUserToStorage();
      }
    } catch (e) {
      Logger.error(_tag, 'Failed to refresh user profile', e);
    }
  }

  /// 从protobuf User对象更新当前用户
  void _updateCurrentUserFromProtobuf(User protoUser) {
    _currentUser = UserModel(
      id: protoUser.id.toString(),
      username: protoUser.username,
      displayName: protoUser.nickname.isNotEmpty ? protoUser.nickname : null,
      bio: protoUser.bio.isNotEmpty ? protoUser.bio : null,
      phone: protoUser.phone.isNotEmpty ? protoUser.phone : null,
      email: protoUser.email.isNotEmpty ? protoUser.email : null,
      avatar: protoUser.avatarUrl.isNotEmpty ? protoUser.avatarUrl : null,
      isOnline:
          protoUser.presenceStatus == PresenceStatus.PRESENCE_STATUS_ONLINE,
      lastSeen:
          protoUser.hasLastActiveAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoUser.lastActiveAt.seconds.toInt() * 1000,
              )
              : null,
      createdAt:
          protoUser.hasRegisteredAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoUser.registeredAt.seconds.toInt() * 1000,
              )
              : null,
    );
    notifyListeners();
  }

  /// 设置当前用户 - 用于登录后设置用户信息
  Future<void> setCurrentUser(UserModel user) async {
    try {
      _currentUser = user;
      await _saveUserToStorage();
      notifyListeners();
      Logger.info(_tag, 'Current user set: ${user.username}');
    } catch (e) {
      Logger.error(_tag, 'Failed to set current user', e);
    }
  }

  /// 设置当前用户从protobuf对象
  Future<void> setCurrentUserFromProtobuf(User protoUser) async {
    try {
      _updateCurrentUserFromProtobuf(protoUser);
      await _saveUserToStorage();
      Logger.info(
        _tag,
        'Current user set from protobuf: ${protoUser.username}',
      );
    } catch (e) {
      Logger.error(_tag, 'Failed to set current user from protobuf', e);
    }
  }

  /// 加载联系人列表 - 真实API调用
  Future<ApiResponse<List<UserModel>>> loadContacts({
    int page = 1,
    int pageSize = 50,
  }) async {
    try {
      Logger.info(_tag, 'Loading contacts from API');

      // 创建protobuf请求
      final request =
          UserSearchRequest()
            ..pagination =
                (PaginationRequest()
                  ..mode = PaginationMode.PAGINATION_MODE_OFFSET
                  ..offsetPagination =
                      (OffsetPagination()
                        ..page = page
                        ..size = pageSize));

      // 发送API请求
      final response = await _httpService.post(
        '/api/v1/users/contacts',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析protobuf响应
        final searchResponse = UserSearchResponse.fromBuffer(response.data!);
        final contacts = <UserModel>[];

        for (final userSummary in searchResponse.users) {
          final userModel = _convertUserSummaryToUserModel(userSummary);
          contacts.add(userModel);
          _userCache[userModel.id] = userModel;
        }

        // 更新本地联系人列表
        if (page == 1) {
          _contacts.clear();
        }
        _contacts.addAll(contacts);
        _sortContacts();
        notifyListeners();

        Logger.info(_tag, 'Loaded ${contacts.length} contacts from API');
        return ApiResponse.success(contacts);
      } else {
        Logger.error(_tag, 'Failed to load contacts: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to load contacts',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error loading contacts', e);
      return ApiResponse.error('Failed to load contacts: ${e.toString()}');
    }
  }

  /// 将protobuf UserSummary对象转换为UserModel
  UserModel _convertUserSummaryToUserModel(UserSummary userSummary) {
    return UserModel(
      id: userSummary.id.toString(),
      username: userSummary.username,
      displayName:
          userSummary.nickname.isNotEmpty ? userSummary.nickname : null,
      avatar: userSummary.avatarUrl.isNotEmpty ? userSummary.avatarUrl : null,
      isOnline:
          userSummary.presenceStatus == PresenceStatus.PRESENCE_STATUS_ONLINE,
      lastSeen:
          userSummary.hasLastActiveAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                userSummary.lastActiveAt.seconds.toInt() * 1000,
              )
              : null,
    );
  }

  /// 将protobuf User对象转换为UserModel
  UserModel _convertProtobufToUserModel(User protoUser) {
    return UserModel(
      id: protoUser.id.toString(),
      username: protoUser.username,
      displayName: protoUser.nickname.isNotEmpty ? protoUser.nickname : null,
      bio: protoUser.bio.isNotEmpty ? protoUser.bio : null,
      phone: protoUser.phone.isNotEmpty ? protoUser.phone : null,
      email: protoUser.email.isNotEmpty ? protoUser.email : null,
      avatar: protoUser.avatarUrl.isNotEmpty ? protoUser.avatarUrl : null,
      isOnline:
          protoUser.presenceStatus == PresenceStatus.PRESENCE_STATUS_ONLINE,
      lastSeen:
          protoUser.hasLastActiveAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoUser.lastActiveAt.seconds.toInt() * 1000,
              )
              : null,
      createdAt:
          protoUser.hasRegisteredAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoUser.registeredAt.seconds.toInt() * 1000,
              )
              : null,
    );
  }

  /// 更新当前用户信息 - 真实API调用
  Future<ApiResponse<UserModel>> updateCurrentUser({
    String? username,
    String? displayName,
    String? bio,
    String? phone,
    String? email,
    String? avatar,
  }) async {
    try {
      Logger.info(_tag, 'Updating current user profile');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      // 创建更新请求数据
      final updateData = <String, dynamic>{};
      if (username != null) updateData['username'] = username;
      if (displayName != null) updateData['nickname'] = displayName;
      if (bio != null) updateData['bio'] = bio;
      if (phone != null) updateData['phone'] = phone;
      if (email != null) updateData['email'] = email;
      if (avatar != null) updateData['avatarUrl'] = avatar;

      // 发送API请求 - 使用JSON格式
      final response = await _httpService.put(
        '/api/v1/users/profile',
        Uint8List.fromList(utf8.encode(jsonEncode(updateData))),
        headers: {'Content-Type': 'application/json'},
        requireAuth: true,
      );

      if (response.isSuccess) {
        // 更新本地用户信息
        _currentUser = _currentUser!.copyWith(
          username: username,
          displayName: displayName,
          bio: bio,
          phone: phone,
          email: email,
          avatar: avatar,
        );

        await _saveUserToStorage();
        notifyListeners();

        Logger.info(_tag, 'User profile updated successfully');
        return ApiResponse.success(_currentUser!);
      } else {
        Logger.error(
          _tag,
          'Failed to update profile: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to update profile',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error updating user profile', e);
      return ApiResponse.error('Failed to update profile: ${e.toString()}');
    }
  }

  /// 添加联系人 - 真实API调用
  Future<ApiResponse<bool>> addContact(UserModel user) async {
    try {
      Logger.info(_tag, 'Adding contact: ${user.username}');

      // 创建简单的请求体
      final requestData = {
        'targetUserId': int.parse(user.id),
        'message': '', // 可选的添加好友消息
      };

      // 发送API请求 - 使用JSON格式暂时替代protobuf
      final response = await _httpService.post(
        '/api/v1/users/friend-requests',
        Uint8List.fromList(utf8.encode(jsonEncode(requestData))),
        headers: {'Content-Type': 'application/json'},
        requireAuth: true,
      );

      if (response.isSuccess) {
        // 添加到本地联系人列表
        if (!_contacts.any((contact) => contact.id == user.id)) {
          _contacts.add(user);
          _userCache[user.id] = user;
          _sortContacts();
          notifyListeners();
        }

        Logger.info(_tag, 'Contact added successfully: ${user.username}');
        return ApiResponse.success(true);
      } else {
        Logger.error(_tag, 'Failed to add contact: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to add contact',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error adding contact', e);
      return ApiResponse.error('Failed to add contact: ${e.toString()}');
    }
  }

  /// 删除联系人 - 真实API调用
  Future<ApiResponse<bool>> removeContact(String userId) async {
    try {
      Logger.info(_tag, 'Removing contact: $userId');

      // 发送API请求
      final response = await _httpService.delete(
        '/api/v1/users/contacts/$userId',
        requireAuth: true,
      );

      if (response.isSuccess) {
        // 从本地联系人列表中移除
        final initialLength = _contacts.length;
        _contacts.removeWhere((contact) => contact.id == userId);
        final removed = _contacts.length < initialLength;

        if (removed) {
          _userCache.remove(userId);
          _favoriteContacts.remove(userId);
          notifyListeners();
        }

        Logger.info(_tag, 'Contact removed successfully: $userId');
        return ApiResponse.success(removed);
      } else {
        Logger.error(
          _tag,
          'Failed to remove contact: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to remove contact',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error removing contact', e);
      return ApiResponse.error('Failed to remove contact: ${e.toString()}');
    }
  }

  /// 更新联系人信息
  void updateContact(
    String userId, {
    String? username,
    String? displayName,
    String? bio,
    String? phone,
    String? email,
    String? avatar,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    final contactIndex = _contacts.indexWhere(
      (contact) => contact.id == userId,
    );
    if (contactIndex != -1) {
      _contacts[contactIndex] = _contacts[contactIndex].copyWith(
        username: username,
        displayName: displayName,
        bio: bio,
        phone: phone,
        email: email,
        avatar: avatar,
        isOnline: isOnline,
        lastSeen: lastSeen,
      );

      // 更新缓存
      _userCache[userId] = _contacts[contactIndex];
      notifyListeners();
    }
  }

  /// 切换收藏状态
  void toggleFavorite(String userId) {
    if (_favoriteContacts.contains(userId)) {
      _favoriteContacts.remove(userId);
    } else {
      _favoriteContacts.add(userId);
    }
    notifyListeners();
  }

  /// 检查是否为收藏联系人
  bool isFavoriteContact(String userId) {
    return _favoriteContacts.contains(userId);
  }

  /// 搜索联系人
  List<UserModel> searchContacts(String query) {
    if (query.isEmpty) return contacts;

    return _contacts.where((user) {
      return user.username.toLowerCase().contains(query.toLowerCase()) ||
          user.displayName?.toLowerCase().contains(query.toLowerCase()) ==
              true ||
          user.phone?.contains(query) == true ||
          user.email?.toLowerCase().contains(query.toLowerCase()) == true;
    }).toList();
  }

  /// 搜索用户 - 真实API调用
  Future<ApiResponse<List<UserModel>>> searchUsers(
    String query, {
    UserSearchType searchType = UserSearchType.USER_SEARCH_TYPE_FUZZY,
    List<UserSearchField> searchFields = const [
      UserSearchField.USER_SEARCH_FIELD_USERNAME,
      UserSearchField.USER_SEARCH_FIELD_NICKNAME,
    ],
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      Logger.info(_tag, 'Searching users with query: $query');

      if (query.trim().isEmpty) {
        return ApiResponse.success(<UserModel>[]);
      }

      // 创建protobuf搜索请求
      final request =
          UserSearchRequest()
            ..query = query.trim()
            ..searchType = searchType
            ..searchFields.addAll(searchFields)
            ..pagination =
                (PaginationRequest()
                  ..mode = PaginationMode.PAGINATION_MODE_OFFSET
                  ..offsetPagination =
                      (OffsetPagination()
                        ..page = page
                        ..size = pageSize));

      // 发送API请求
      final response = await _httpService.post(
        '/api/v1/users/search',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析protobuf响应
        final searchResponse = UserSearchResponse.fromBuffer(response.data!);
        final searchResults = <UserModel>[];

        for (final userSummary in searchResponse.users) {
          final userModel = _convertUserSummaryToUserModel(userSummary);
          searchResults.add(userModel);
          // 缓存搜索结果
          _userCache[userModel.id] = userModel;
        }

        Logger.info(
          _tag,
          'Found ${searchResults.length} users for query: $query',
        );
        return ApiResponse.success(searchResults);
      } else {
        Logger.error(_tag, 'Failed to search users: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to search users',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error searching users', e);
      return ApiResponse.error('Failed to search users: ${e.toString()}');
    }
  }

  /// 根据ID获取用户信息
  UserModel? getUserById(String userId) {
    return _userCache[userId];
  }

  /// 获取用户详情 - 真实API调用
  Future<ApiResponse<UserModel>> getUserDetails(String userId) async {
    try {
      Logger.info(_tag, 'Getting user details for: $userId');

      // 先检查缓存
      final cachedUser = _userCache[userId];
      if (cachedUser != null) {
        Logger.debug(_tag, 'Found user in cache: $userId');
        return ApiResponse.success(cachedUser);
      }

      // 发送API请求
      final response = await _httpService.get(
        '/api/v1/users/$userId',
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析protobuf响应
        final user = User.fromBuffer(response.data!);
        final userModel = _convertProtobufToUserModel(user);

        // 缓存用户信息
        _userCache[userModel.id] = userModel;

        Logger.info(_tag, 'User details retrieved successfully: $userId');
        return ApiResponse.success(userModel);
      } else {
        Logger.error(
          _tag,
          'Failed to get user details: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get user details',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting user details', e);
      return ApiResponse.error('Failed to get user details: ${e.toString()}');
    }
  }

  /// 批量获取用户信息 - 真实API调用
  Future<ApiResponse<List<UserModel>>> getUsersByIds(
    List<String> userIds,
  ) async {
    try {
      Logger.info(
        _tag,
        'Getting batch user details for ${userIds.length} users',
      );

      if (userIds.isEmpty) {
        return ApiResponse.success(<UserModel>[]);
      }

      // 检查缓存中已有的用户
      final cachedUsers = <UserModel>[];
      final missingUserIds = <String>[];

      for (final userId in userIds) {
        final cachedUser = _userCache[userId];
        if (cachedUser != null) {
          cachedUsers.add(cachedUser);
        } else {
          missingUserIds.add(userId);
        }
      }

      // 如果所有用户都在缓存中，直接返回
      if (missingUserIds.isEmpty) {
        Logger.debug(_tag, 'All users found in cache');
        return ApiResponse.success(cachedUsers);
      }

      // 创建批量查询请求
      final request =
          BatchUserRequest()
            ..userIds.addAll(missingUserIds.map((id) => Int64.parseInt(id)))
            ..includePresence = true
            ..includeSettings = false;

      // 发送API请求
      final response = await _httpService.post(
        '/api/v1/users/batch',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析protobuf响应
        final batchResponse = BatchUserResponse.fromBuffer(response.data!);
        final fetchedUsers = <UserModel>[];

        for (final user in batchResponse.users) {
          final userModel = _convertProtobufToUserModel(user);
          fetchedUsers.add(userModel);
          // 缓存用户信息
          _userCache[userModel.id] = userModel;
        }

        // 合并缓存的用户和新获取的用户
        final allUsers = [...cachedUsers, ...fetchedUsers];

        Logger.info(
          _tag,
          'Batch user details retrieved successfully: ${allUsers.length} users',
        );
        return ApiResponse.success(allUsers);
      } else {
        Logger.error(
          _tag,
          'Failed to get batch user details: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get batch user details',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting batch user details', e);
      return ApiResponse.error(
        'Failed to get batch user details: ${e.toString()}',
      );
    }
  }

  /// 设置用户在线状态
  void setUserStatus(String userId, String status) {
    _userStatus[userId] = status;

    // 更新联系人的在线状态
    final isOnline = status == AppConstants.userStatusOnline;
    updateContact(userId, isOnline: isOnline, lastSeen: DateTime.now());
  }

  /// 更新当前用户状态 - 真实API调用
  Future<ApiResponse<bool>> updateUserStatus(
    PresenceStatus presenceStatus, {
    String? statusMessage,
  }) async {
    try {
      Logger.info(_tag, 'Updating user status to: $presenceStatus');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      // 创建状态更新请求
      final request =
          UserStatusUpdateRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..presenceStatus = presenceStatus
            ..statusMessage = statusMessage ?? ''
            ..timestamp =
                (Timestamp()
                  ..seconds = Int64(
                    DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  ));

      // 发送API请求
      final response = await _httpService.post(
        '/api/v1/users/status',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析响应
        final updateResponse = UserStatusUpdateResponse.fromBuffer(
          response.data!,
        );

        if (updateResponse.success) {
          // 更新本地状态
          final isOnline =
              presenceStatus == PresenceStatus.PRESENCE_STATUS_ONLINE;
          _currentUser = _currentUser!.copyWith(isOnline: isOnline);
          setUserStatus(
            _currentUser!.id,
            isOnline
                ? AppConstants.userStatusOnline
                : AppConstants.userStatusOffline,
          );

          await _saveUserToStorage();
          notifyListeners();

          Logger.info(_tag, 'User status updated successfully');
          return ApiResponse.success(true);
        } else {
          Logger.error(_tag, 'Failed to update user status');
          return ApiResponse.error('Failed to update user status');
        }
      } else {
        Logger.error(
          _tag,
          'Failed to update user status: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to update user status',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error updating user status', e);
      return ApiResponse.error('Failed to update user status: ${e.toString()}');
    }
  }

  /// 获取用户状态
  String getUserStatus(String userId) {
    return _userStatus[userId] ?? AppConstants.userStatusOffline;
  }

  /// 检查用户是否在线
  bool isUserOnline(String userId) {
    return getUserStatus(userId) == AppConstants.userStatusOnline;
  }

  /// 获取共同联系人
  List<UserModel> getMutualContacts(String userId) {
    // 模拟获取共同联系人逻辑
    return _contacts.take(3).toList();
  }

  /// 获取联系人统计信息
  ContactStats getContactStats() {
    final totalContacts = _contacts.length;
    final onlineContacts =
        _contacts.where((user) => isUserOnline(user.id)).length;
    final favoriteContacts = _favoriteContacts.length;

    return ContactStats(
      totalContacts: totalContacts,
      onlineContacts: onlineContacts,
      favoriteContacts: favoriteContacts,
      offlineContacts: totalContacts - onlineContacts,
    );
  }

  /// 导出用户数据
  Map<String, dynamic> exportUserData() {
    return {
      'currentUser': _currentUser?.toMap(),
      'contacts': _contacts.map((user) => user.toMap()).toList(),
      'favoriteContacts': _favoriteContacts.toList(),
      'userStatus': _userStatus,
      'exportTime': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 导入用户数据
  void importUserData(Map<String, dynamic> data) {
    try {
      // 导入当前用户
      final currentUserData = data['currentUser'] as Map<String, dynamic>?;
      if (currentUserData != null) {
        _currentUser = UserModel.fromMap(currentUserData);
      }

      // 导入联系人
      _contacts.clear();
      final contactsData = data['contacts'] as List<dynamic>?;
      if (contactsData != null) {
        for (final contactData in contactsData) {
          final user = UserModel.fromMap(contactData as Map<String, dynamic>);
          _contacts.add(user);
          _userCache[user.id] = user;
        }
      }

      // 导入收藏联系人
      _favoriteContacts.clear();
      final favoriteData = data['favoriteContacts'] as List<dynamic>?;
      if (favoriteData != null) {
        _favoriteContacts.addAll(favoriteData.cast<String>());
      }

      // 导入用户状态
      _userStatus.clear();
      final statusData = data['userStatus'] as Map<String, dynamic>?;
      if (statusData != null) {
        _userStatus.addAll(statusData.cast<String, String>());
      }

      _sortContacts();
      notifyListeners();
    } catch (e) {
      debugPrint('Error importing user data: $e');
    }
  }

  /// 获取当前用户资料 - 真实API调用
  Future<ApiResponse<User>> getCurrentUserProfile() async {
    try {
      Logger.info(_tag, 'Getting current user profile');

      // 发送API请求
      final response = await _httpService.get(
        '/api/v1/users/profile',
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        // 解析protobuf响应
        final user = User.fromBuffer(response.data!);

        // 更新本地用户信息
        _updateCurrentUserFromProtobuf(user);
        await _saveUserToStorage();

        Logger.info(_tag, 'User profile retrieved successfully');
        return ApiResponse.success(user);
      } else {
        Logger.error(
          _tag,
          'Failed to get user profile: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get user profile',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting user profile', e);
      return ApiResponse.error(
        'Failed to get user profile: ${e.toString()}',
        statusCode: 500,
        errorCode: 'INTERNAL_ERROR',
      );
    }
  }

  /// 退出登录 - 真实API调用
  Future<ApiResponse<bool>> logout() async {
    try {
      Logger.info(_tag, 'Performing logout');

      // 发送API请求
      final response = await _httpService.post(
        '/api/v1/auth/logout',
        Uint8List(0), // 空请求体
        requireAuth: true,
      );

      // 无论API调用是否成功，都清除本地数据
      await _clearUserData();

      if (response.isSuccess) {
        Logger.info(_tag, 'Logout completed successfully');
        return ApiResponse.success(true);
      } else {
        Logger.warning(
          _tag,
          'Logout API failed but local data cleared: ${response.errorMessage}',
        );
        // 即使API失败，本地数据已清除，仍然返回成功
        return ApiResponse.success(true);
      }
    } catch (e) {
      Logger.error(_tag, 'Error during logout', e);
      // 即使出现异常，也尝试清除本地数据
      await _clearUserData();
      return ApiResponse.success(true); // 本地清除成功就算成功
    }
  }

  /// 清除用户数据
  Future<void> _clearUserData() async {
    try {
      // 清除当前用户信息
      _currentUser = null;

      // 清除缓存数据
      _contacts.clear();
      _userCache.clear();
      _userStatus.clear();
      _favoriteContacts.clear();

      // 清除本地存储
      await _secureStorage.remove('current_user');

      // 重置初始化状态
      _isInitialized = false;

      notifyListeners();
      Logger.info(_tag, 'User data cleared successfully');
    } catch (e) {
      Logger.error(_tag, 'Error clearing user data', e);
    }
  }

  /// 私有方法：排序联系人
  void _sortContacts() {
    _contacts.sort((a, b) {
      // 收藏联系人排在前面
      final aIsFavorite = _favoriteContacts.contains(a.id);
      final bIsFavorite = _favoriteContacts.contains(b.id);

      if (aIsFavorite && !bIsFavorite) return -1;
      if (!aIsFavorite && bIsFavorite) return 1;

      // 在线用户排在前面
      final aIsOnline = isUserOnline(a.id);
      final bIsOnline = isUserOnline(b.id);

      if (aIsOnline && !bIsOnline) return -1;
      if (!aIsOnline && bIsOnline) return 1;

      // 按显示名称排序
      final aName = a.displayName ?? a.username;
      final bName = b.displayName ?? b.username;
      return aName.compareTo(bName);
    });
  }

  // ==================== 好友管理API ====================

  /// 发送好友请求
  Future<ApiResponse<bool>> sendFriendRequest(
    String targetUserId, {
    String? message,
    FriendSourceType sourceType = FriendSourceType.FRIEND_SOURCE_TYPE_SEARCH,
  }) async {
    try {
      Logger.info(_tag, 'Sending friend request to: $targetUserId');

      final request =
          SendUserFriendRequestRequest()
            ..targetUserId = Int64.parseInt(targetUserId)
            ..message = message ?? ''
            ..sourceType = sourceType
            ..deviceInfo =
                (DeviceInfo()
                  ..deviceId = 'flutter_device'
                  ..os = 'flutter'
                  ..deviceType = DeviceType.DEVICE_TYPE_IOS);

      final response = await _httpService.post(
        '/api/v1/users/friend-requests',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final friendResponse = SendUserFriendRequestResponse.fromBuffer(
          response.data!,
        );

        if (friendResponse.success) {
          Logger.info(_tag, 'Friend request sent successfully');
          return ApiResponse.success(true);
        } else {
          Logger.error(
            _tag,
            'Failed to send friend request: ${friendResponse.message}',
          );
          return ApiResponse.error(friendResponse.message);
        }
      } else {
        Logger.error(
          _tag,
          'Failed to send friend request: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to send friend request',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error sending friend request', e);
      return ApiResponse.error(
        'Failed to send friend request: ${e.toString()}',
      );
    }
  }

  /// 获取好友请求列表
  Future<ApiResponse<List<FriendRequestModel>>> getFriendRequests({
    FriendRequestType requestType =
        FriendRequestType.FRIEND_REQUEST_TYPE_RECEIVED,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      Logger.info(_tag, 'Getting friend requests: $requestType');

      final request =
          GetUserFriendRequestsRequest()
            ..requestType = requestType
            ..pagination =
                (PaginationRequest()
                  ..mode = PaginationMode.PAGINATION_MODE_OFFSET
                  ..offsetPagination =
                      (OffsetPagination()
                        ..page = page
                        ..size = pageSize));

      final response = await _httpService.post(
        '/api/v1/users/friend-requests/list',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final friendRequestsResponse = GetUserFriendRequestsResponse.fromBuffer(
          response.data!,
        );
        final friendRequests = <FriendRequestModel>[];

        for (final protoRequest in friendRequestsResponse.requests) {
          final requestModel = _convertProtobufToFriendRequestModel(
            protoRequest,
          );
          friendRequests.add(requestModel);
        }

        Logger.info(_tag, 'Retrieved ${friendRequests.length} friend requests');
        return ApiResponse.success(friendRequests);
      } else {
        Logger.error(
          _tag,
          'Failed to get friend requests: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get friend requests',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting friend requests', e);
      return ApiResponse.error(
        'Failed to get friend requests: ${e.toString()}',
      );
    }
  }

  /// 处理好友请求（接受/拒绝）
  Future<ApiResponse<bool>> processFriendRequest(
    String requestId,
    FriendRequestAction action, {
    String? note,
    String? remarkName,
  }) async {
    try {
      Logger.info(
        _tag,
        'Processing friend request: $requestId, action: $action',
      );

      final request =
          ProcessFriendRequestRequest()
            ..requestId = Int64.parseInt(requestId)
            ..action = action
            ..note = note ?? ''
            ..remarkName = remarkName ?? ''
            ..deviceInfo =
                (DeviceInfo()
                  ..deviceId = 'flutter_device'
                  ..os = 'flutter'
                  ..deviceType = DeviceType.DEVICE_TYPE_IOS);

      final response = await _httpService.post(
        '/api/v1/users/friend-requests/process',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final processResponse = ProcessFriendRequestResponse.fromBuffer(
          response.data!,
        );

        if (processResponse.success) {
          // 如果接受了好友请求，更新本地好友列表
          if (action == FriendRequestAction.FRIEND_REQUEST_ACTION_ACCEPT &&
              processResponse.hasFriendInfo()) {
            final friendModel = _convertProtobufToFriendModel(
              processResponse.friendInfo,
            );
            _addFriendToLocalList(friendModel);
          }

          Logger.info(_tag, 'Friend request processed successfully');
          return ApiResponse.success(true);
        } else {
          Logger.error(
            _tag,
            'Failed to process friend request: ${processResponse.message}',
          );
          return ApiResponse.error(processResponse.message);
        }
      } else {
        Logger.error(
          _tag,
          'Failed to process friend request: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to process friend request',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error processing friend request', e);
      return ApiResponse.error(
        'Failed to process friend request: ${e.toString()}',
      );
    }
  }

  /// 获取好友列表
  Future<ApiResponse<List<FriendModel>>> getFriends({
    List<FriendshipStatus>? statusFilter,
    bool onlineOnly = false,
    bool pinnedOnly = false,
    String? searchQuery,
    FriendSortType sortType = FriendSortType.FRIEND_SORT_TYPE_REMARK_NAME,
    int page = 1,
    int pageSize = 50,
  }) async {
    try {
      Logger.info(_tag, 'Getting friends list');

      final request =
          GetUserFriendsRequest()
            ..userId = Int64.parseInt(_currentUser?.id ?? '0')
            ..onlineOnly = onlineOnly
            ..pagination =
                (PaginationRequest()
                  ..mode = PaginationMode.PAGINATION_MODE_OFFSET
                  ..offsetPagination =
                      (OffsetPagination()
                        ..page = page
                        ..size = pageSize))
            ..includeUserDetails = true;

      final response = await _httpService.post(
        '/api/v1/users/friends',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final friendsResponse = GetUserFriendsResponse.fromBuffer(
          response.data!,
        );
        final friends = <FriendModel>[];

        for (final protoFriend in friendsResponse.friends) {
          final friendModel = _convertProtobufToFriendModel(protoFriend);
          friends.add(friendModel);
        }

        // 更新本地联系人列表
        if (page == 1) {
          _contacts.clear();
          for (final friend in friends) {
            _contacts.add(friend.userInfo);
            _userCache[friend.userInfo.id] = friend.userInfo;
          }
          _sortContacts();
          notifyListeners();
        }

        Logger.info(_tag, 'Retrieved ${friends.length} friends');
        return ApiResponse.success(friends);
      } else {
        Logger.error(_tag, 'Failed to get friends: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get friends',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting friends', e);
      return ApiResponse.error('Failed to get friends: ${e.toString()}');
    }
  }

  // ==================== 辅助方法 ====================

  /// 将protobuf FriendRequest转换为FriendRequestModel
  FriendRequestModel _convertProtobufToFriendRequestModel(
    FriendRequest protoRequest,
  ) {
    return FriendRequestModel(
      id: protoRequest.id.toString(),
      senderId: protoRequest.senderId.toString(),
      receiverId: protoRequest.receiverId.toString(),
      status: protoRequest.status,
      message: protoRequest.message,
      sourceType: protoRequest.sourceType,
      sentAt: DateTime.fromMillisecondsSinceEpoch(
        protoRequest.sentAt.seconds.toInt() * 1000,
      ),
      processedAt:
          protoRequest.hasProcessedAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoRequest.processedAt.seconds.toInt() * 1000,
              )
              : null,
      senderInfo:
          protoRequest.hasSenderInfo()
              ? _convertUserSummaryToUserModel(protoRequest.senderInfo)
              : null,
      receiverInfo:
          protoRequest.hasReceiverInfo()
              ? _convertUserSummaryToUserModel(protoRequest.receiverInfo)
              : null,
    );
  }

  /// 将protobuf FriendInfo转换为FriendModel
  FriendModel _convertProtobufToFriendModel(FriendInfo protoFriend) {
    return FriendModel(
      friendshipId: protoFriend.friendship.id.toString(),
      userId: protoFriend.friendship.userId.toString(),
      friendId: protoFriend.friendship.friendId.toString(),
      status: protoFriend.friendship.status,
      remarkName:
          protoFriend.friendship.remarkName.isNotEmpty
              ? protoFriend.friendship.remarkName
              : null,
      tags: protoFriend.friendship.tags,
      isPinned: protoFriend.friendship.isPinned,
      isMuted: protoFriend.friendship.isMuted,
      sourceType: protoFriend.friendship.sourceType,
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        protoFriend.friendship.createdAt.seconds.toInt() * 1000,
      ),
      userInfo: _convertUserSummaryToUserModel(protoFriend.friendUser),
      mutualFriendsCount: protoFriend.mutualFriendsCount,
      mutualGroupsCount: protoFriend.mutualGroupsCount,
      lastInteractionAt:
          protoFriend.hasLastInteractionAt()
              ? DateTime.fromMillisecondsSinceEpoch(
                protoFriend.lastInteractionAt.seconds.toInt() * 1000,
              )
              : null,
    );
  }

  /// 添加好友到本地列表
  void _addFriendToLocalList(FriendModel friend) {
    // 检查是否已存在
    final existingIndex = _contacts.indexWhere(
      (contact) => contact.id == friend.userInfo.id,
    );

    if (existingIndex == -1) {
      _contacts.add(friend.userInfo);
      _userCache[friend.userInfo.id] = friend.userInfo;
      _sortContacts();
      notifyListeners();
    }
  }

  // ==================== 头像管理API ====================

  /// 上传用户头像
  Future<ApiResponse<AvatarInfo>> uploadUserAvatar(
    String filePath, {
    bool setAsCurrent = true,
    CropParameters? cropParams,
  }) async {
    try {
      Logger.info(_tag, 'Uploading user avatar');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      // 读取文件数据
      final file = File(filePath);
      if (!await file.exists()) {
        return ApiResponse.error('File not found: $filePath');
      }

      final fileBytes = await file.readAsBytes();
      final fileName = path.basename(filePath);
      final fileExtension = path.extension(filePath).toLowerCase();

      String format;
      switch (fileExtension) {
        case '.jpg':
        case '.jpeg':
          format = 'jpeg';
          break;
        case '.png':
          format = 'png';
          break;
        case '.gif':
          format = 'gif';
          break;
        case '.webp':
          format = 'webp';
          break;
        default:
          format = 'jpeg';
      }

      final request =
          UploadUserAvatarRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..fileData = fileBytes
            ..fileName = fileName
            ..format = format
            ..setAsCurrent = setAsCurrent
            ..deviceInfo =
                (DeviceInfo()
                  ..deviceId = 'flutter_device'
                  ..os = 'flutter'
                  ..deviceType = DeviceType.DEVICE_TYPE_IOS);

      if (cropParams != null) {
        request.cropParams = cropParams;
      }

      final response = await _httpService.post(
        '/api/v1/users/avatar/upload',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final uploadResponse = UploadUserAvatarResponse.fromBuffer(
          response.data!,
        );

        if (uploadResponse.success) {
          // 如果设置为当前头像，更新本地用户信息
          if (setAsCurrent && uploadResponse.hasAvatarInfo()) {
            _currentUser = _currentUser!.copyWith(
              avatar: uploadResponse.avatarInfo.originalUrl,
            );
            await _saveUserToStorage();
            notifyListeners();
          }

          Logger.info(_tag, 'Avatar uploaded successfully');
          return ApiResponse.success(uploadResponse.avatarInfo);
        } else {
          Logger.error(
            _tag,
            'Failed to upload avatar: ${uploadResponse.message}',
          );
          return ApiResponse.error(uploadResponse.message);
        }
      } else {
        Logger.error(_tag, 'Failed to upload avatar: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to upload avatar',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error uploading avatar', e);
      return ApiResponse.error('Failed to upload avatar: ${e.toString()}');
    }
  }

  /// 获取用户当前头像
  Future<ApiResponse<AvatarInfo?>> getCurrentUserAvatar({
    bool includeMetadata = false,
  }) async {
    try {
      Logger.info(_tag, 'Getting current user avatar');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      final request =
          GetUserCurrentAvatarRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..includeMetadata = includeMetadata;

      final response = await _httpService.post(
        '/api/v1/users/avatar/current',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final avatarResponse = GetUserCurrentAvatarResponse.fromBuffer(
          response.data!,
        );

        if (avatarResponse.hasAvatar) {
          Logger.info(_tag, 'Current avatar retrieved successfully');
          return ApiResponse.success(avatarResponse.currentAvatar);
        } else {
          Logger.info(_tag, 'User has no current avatar');
          return ApiResponse.success(null);
        }
      } else {
        Logger.error(
          _tag,
          'Failed to get current avatar: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get current avatar',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting current avatar', e);
      return ApiResponse.error('Failed to get current avatar: ${e.toString()}');
    }
  }

  /// 获取用户头像历史
  Future<ApiResponse<List<AvatarInfo>>> getUserAvatarHistory({
    int page = 1,
    int pageSize = 20,
    bool includeMetadata = false,
  }) async {
    try {
      Logger.info(_tag, 'Getting user avatar history');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      final request =
          GetUserAvatarHistoryRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..pagination =
                (PaginationRequest()
                  ..mode = PaginationMode.PAGINATION_MODE_OFFSET
                  ..offsetPagination =
                      (OffsetPagination()
                        ..page = page
                        ..size = pageSize))
            ..includeMetadata = includeMetadata;

      final response = await _httpService.post(
        '/api/v1/users/avatar/history',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final historyResponse = GetUserAvatarHistoryResponse.fromBuffer(
          response.data!,
        );

        Logger.info(
          _tag,
          'Avatar history retrieved: ${historyResponse.avatarHistory.length} items',
        );
        return ApiResponse.success(historyResponse.avatarHistory);
      } else {
        Logger.error(
          _tag,
          'Failed to get avatar history: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to get avatar history',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error getting avatar history', e);
      return ApiResponse.error('Failed to get avatar history: ${e.toString()}');
    }
  }

  /// 设置当前头像
  Future<ApiResponse<bool>> setCurrentUserAvatar(String avatarId) async {
    try {
      Logger.info(_tag, 'Setting current user avatar: $avatarId');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      final request =
          SetUserCurrentAvatarRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..avatarId = Int64.parseInt(avatarId)
            ..deviceInfo =
                (DeviceInfo()
                  ..deviceId = 'flutter_device'
                  ..os = 'flutter'
                  ..deviceType = DeviceType.DEVICE_TYPE_IOS);

      final response = await _httpService.post(
        '/api/v1/users/avatar/set-current',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final setResponse = SetUserCurrentAvatarResponse.fromBuffer(
          response.data!,
        );

        if (setResponse.success) {
          // 更新本地用户头像信息
          if (setResponse.hasCurrentAvatar()) {
            _currentUser = _currentUser!.copyWith(
              avatar: setResponse.currentAvatar.originalUrl,
            );
            await _saveUserToStorage();
            notifyListeners();
          }

          Logger.info(_tag, 'Current avatar set successfully');
          return ApiResponse.success(true);
        } else {
          Logger.error(
            _tag,
            'Failed to set current avatar: ${setResponse.message}',
          );
          return ApiResponse.error(setResponse.message);
        }
      } else {
        Logger.error(
          _tag,
          'Failed to set current avatar: ${response.errorMessage}',
        );
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to set current avatar',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error setting current avatar', e);
      return ApiResponse.error('Failed to set current avatar: ${e.toString()}');
    }
  }

  /// 删除用户头像
  Future<ApiResponse<bool>> deleteUserAvatar(
    String avatarId, {
    String? reason,
  }) async {
    try {
      Logger.info(_tag, 'Deleting user avatar: $avatarId');

      if (_currentUser == null) {
        return ApiResponse.error('No current user found');
      }

      final request =
          DeleteUserAvatarRequest()
            ..userId = Int64.parseInt(_currentUser!.id)
            ..avatarId = Int64.parseInt(avatarId)
            ..reason = reason ?? ''
            ..deviceInfo =
                (DeviceInfo()
                  ..deviceId = 'flutter_device'
                  ..os = 'flutter'
                  ..deviceType = DeviceType.DEVICE_TYPE_IOS);

      final response = await _httpService.post(
        '/api/v1/users/avatar/delete',
        request.writeToBuffer(),
        requireAuth: true,
      );

      if (response.isSuccess && response.data != null) {
        final deleteResponse = DeleteUserAvatarResponse.fromBuffer(
          response.data!,
        );

        if (deleteResponse.success) {
          Logger.info(_tag, 'Avatar deleted successfully');
          return ApiResponse.success(true);
        } else {
          Logger.error(
            _tag,
            'Failed to delete avatar: ${deleteResponse.message}',
          );
          return ApiResponse.error(deleteResponse.message);
        }
      } else {
        Logger.error(_tag, 'Failed to delete avatar: ${response.errorMessage}');
        return ApiResponse.error(
          response.errorMessage ?? 'Failed to delete avatar',
          statusCode: response.statusCode,
          errorCode: response.errorCode,
        );
      }
    } catch (e) {
      Logger.error(_tag, 'Error deleting avatar', e);
      return ApiResponse.error('Failed to delete avatar: ${e.toString()}');
    }
  }
}

/// 联系人统计信息
class ContactStats {
  final int totalContacts;
  final int onlineContacts;
  final int offlineContacts;
  final int favoriteContacts;

  ContactStats({
    required this.totalContacts,
    required this.onlineContacts,
    required this.offlineContacts,
    required this.favoriteContacts,
  });
}

/// 用户事件类型
enum UserEventType {
  login,
  logout,
  statusChanged,
  profileUpdated,
  contactAdded,
  contactRemoved,
}

/// 用户事件
class UserEvent {
  final UserEventType type;
  final UserModel? user;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  UserEvent({required this.type, this.user, this.data, DateTime? timestamp})
    : timestamp = timestamp ?? DateTime.now();
}
