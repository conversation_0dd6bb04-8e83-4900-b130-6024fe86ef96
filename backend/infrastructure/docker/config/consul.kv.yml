
# =============================================
# IM平台公共配置
# 所有微服务共享的配置
# =============================================

# 数据库配置
spring:
  datasource:
    username: postgres
    password: postgres123
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false

  # Redis基础配置
  data:
    redis:
      host: localhost
      port: 6379
      password: redis123
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms

  # 服务发现配置
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        prefer-ip-address: true
        health-check-interval: 10s
      config:
        enabled: true
        format: yaml
        prefixes: config
        default-context: application
        data-key: data

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,refresh,configprops,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      probes:
        enabled: true
    loggers:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
  # 分布式追踪配置
  tracing:
    sampling:
      probability: ${TRACING_SAMPLING_PROBABILITY:0.1}

# 基础日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    "[com.implatform]": ${LOG_LEVEL_APP:INFO}
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.springframework.web: WARN
    org.springframework.boot: WARN
    org.apache.http: WARN
    com.zaxxer.hikari: WARN
    io.lettuce.core: WARN
    org.springframework.data.redis: WARN
  file:
    path: ${LOG_PATH:./logs}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  async:
    enabled: ${LOGGING_ASYNC_ENABLED:true}
    queue-size: ${LOGGING_ASYNC_QUEUE_SIZE:1024}
    discarding-threshold: ${LOGGING_ASYNC_DISCARDING_THRESHOLD:0}
    include-caller-data: ${LOGGING_ASYNC_INCLUDE_CALLER_DATA:true}
  events:
    enabled: ${LOGGING_EVENTS_ENABLED:true}
  performance:
    enabled: ${LOGGING_PERFORMANCE_ENABLED:true}
    slow-threshold: ${LOGGING_SLOW_THRESHOLD:5000}
  audit:
    enabled: ${LOGGING_AUDIT_ENABLED:true}

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn

app:
  jwt:
    # 普通用户JWT配置
    user:
      secret: ${USER_JWT_SECRET:user-secret-key-for-im-platform-2024}
      expiration: 86400  # 24 hours in seconds
      refresh-expiration: 604800  # 7 days in seconds
      issuer: https://mmkj.info/user
      audience: im-platform-users
      algorithm: HS256

    # 管理员JWT配置
    admin:
      secret: ${ADMIN_JWT_SECRET:admin-secret-key-for-im-platform-2024}
      expiration: 3600   # 1 hour in seconds
      refresh-expiration: 7200  # 2 hours in seconds
      issuer: https://mmkj.info/admin
      audience: im-platform-admins
      algorithm: HS256
