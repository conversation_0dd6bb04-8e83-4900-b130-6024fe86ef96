dependencies {
    // 聚合所有子模块，保持向后兼容
    api project(':common:common-core')
    api project(':common:common-data')
    api project(':common:common-security')
    api project(':common:common-messaging')
    api project(':common:common-api')
    api project(':common:common-protobuf')
}

// 不生成可执行jar
jar {
    enabled = true
    archiveClassifier = ''
}

// 禁用bootJar任务，因为这是一个库模块
bootJar {
    enabled = false
} 