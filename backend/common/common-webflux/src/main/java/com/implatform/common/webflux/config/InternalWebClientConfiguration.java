package com.implatform.common.webflux.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 通用 WebClient 配置。
 * 为内部微服务调用提供统一命名的 WebClient Bean —— "internalWebClient"。
 */
@Configuration
public class InternalWebClientConfiguration {

    /**
     * 提供一个默认构建的 WebClient，命名为 internalWebClient。
     * 其他模块可以通过 @Qualifier("internalWebClient") 注入。
     */
    @Bean
    @Qualifier("internalWebClient")
    public WebClient internalWebClient(WebClient.Builder builder) {
        return builder.build();
    }
} 