package com.implatform.common.webflux.web.interceptor;

import com.implatform.common.core.logging.CorrelationIdUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志拦截器
 * 负责设置请求追踪上下文和记录请求日志
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
public class LoggingInterceptor implements HandlerInterceptor {
    
    private static final String REQUEST_START_TIME = "requestStartTime";
    private static final String REQUEST_URI = "requestUri";
    private static final String REQUEST_METHOD = "requestMethod";
    
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, 
                           @NonNull HttpServletResponse response, 
                           @NonNull Object handler) throws Exception {
        
        long startTime = System.currentTimeMillis();
        request.setAttribute(REQUEST_START_TIME, startTime);
        request.setAttribute(REQUEST_URI, request.getRequestURI());
        request.setAttribute(REQUEST_METHOD, request.getMethod());
        
        // 设置追踪上下文
        setupTraceContext(request);
        
        // 设置请求信息到MDC
        setupRequestContext(request);
        
        // 记录请求开始日志
        logRequestStart(request);
        
        // 设置响应头
        setResponseHeaders(response);
        
        return true;
    }
    
    @Override
    public void postHandle(@NonNull HttpServletRequest request, 
                          @NonNull HttpServletResponse response,
                          @NonNull Object handler, 
                          ModelAndView modelAndView) throws Exception {
        // 可以在这里添加额外的处理逻辑
    }
    
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, 
                               @NonNull HttpServletResponse response,
                               @NonNull Object handler, 
                               Exception ex) throws Exception {
        try {
            // 记录请求完成日志
            logRequestCompletion(request, response, ex);
        } finally {
            // 清理MDC上下文
            CorrelationIdUtils.clear();
        }
    }
    
    /**
     * 设置追踪上下文
     */
    private void setupTraceContext(HttpServletRequest request) {
        // 从请求头获取追踪ID，如果没有则生成新的
        String traceId = getHeaderValue(request, CorrelationIdUtils.HEADER_TRACE_ID);
        String spanId = getHeaderValue(request, CorrelationIdUtils.HEADER_SPAN_ID);
        String requestId = getHeaderValue(request, CorrelationIdUtils.HEADER_REQUEST_ID);
        String correlationId = getHeaderValue(request, CorrelationIdUtils.HEADER_CORRELATION_ID);
        String userId = getHeaderValue(request, CorrelationIdUtils.HEADER_USER_ID);
        
        // 设置或生成追踪ID
        if (StringUtils.hasText(traceId)) {
            CorrelationIdUtils.setTraceId(traceId);
        } else {
            CorrelationIdUtils.setTraceId(CorrelationIdUtils.generateTraceId());
        }
        
        // 设置或生成跨度ID
        if (StringUtils.hasText(spanId)) {
            CorrelationIdUtils.setSpanId(spanId);
        } else {
            CorrelationIdUtils.setSpanId(CorrelationIdUtils.generateSpanId());
        }
        
        // 设置或生成请求ID
        if (StringUtils.hasText(requestId)) {
            CorrelationIdUtils.setRequestId(requestId);
        } else {
            CorrelationIdUtils.setRequestId(CorrelationIdUtils.generateRequestId());
        }
        
        // 设置或生成关联ID
        if (StringUtils.hasText(correlationId)) {
            CorrelationIdUtils.setCorrelationId(correlationId);
        } else {
            CorrelationIdUtils.setCorrelationId(CorrelationIdUtils.generateCorrelationId());
        }
        
        // 设置用户ID（如果存在）
        if (StringUtils.hasText(userId)) {
            CorrelationIdUtils.setUserId(userId);
        }
    }
    
    /**
     * 设置请求上下文信息
     */
    private void setupRequestContext(HttpServletRequest request) {
        // 设置客户端IP
        String clientIp = getClientIpAddress(request);
        CorrelationIdUtils.setClientIp(clientIp);
        
        // 设置用户代理
        String userAgent = request.getHeader("User-Agent");
        CorrelationIdUtils.setUserAgent(userAgent);
        
        // 设置会话ID
        if (request.getSession(false) != null) {
            CorrelationIdUtils.setSessionId(request.getSession().getId());
        }
        
        // 设置操作名称
        String operation = request.getMethod() + " " + request.getRequestURI();
        CorrelationIdUtils.setOperation(operation);
    }
    
    /**
     * 设置响应头
     */
    private void setResponseHeaders(HttpServletResponse response) {
        response.setHeader(CorrelationIdUtils.HEADER_TRACE_ID, CorrelationIdUtils.getTraceId());
        response.setHeader(CorrelationIdUtils.HEADER_SPAN_ID, CorrelationIdUtils.getSpanId());
        response.setHeader(CorrelationIdUtils.HEADER_REQUEST_ID, CorrelationIdUtils.getRequestId());
        response.setHeader(CorrelationIdUtils.HEADER_CORRELATION_ID, CorrelationIdUtils.getCorrelationId());
    }
    
    /**
     * 记录请求开始日志
     */
    private void logRequestStart(HttpServletRequest request) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "request_start");
        logData.put("method", request.getMethod());
        logData.put("uri", request.getRequestURI());
        logData.put("queryString", request.getQueryString());
        logData.put("clientIp", CorrelationIdUtils.getClientIp());
        logData.put("userAgent", CorrelationIdUtils.getUserAgent());
        logData.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        // 记录重要的请求头
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (isImportantHeader(headerName)) {
                headers.put(headerName, request.getHeader(headerName));
            }
        }
        logData.put("headers", headers);
        
        log.info("HTTP Request Started: {}", logData);
    }
    
    /**
     * 记录请求完成日志
     */
    private void logRequestCompletion(HttpServletRequest request, HttpServletResponse response, Exception ex) {
        Long startTime = (Long) request.getAttribute(REQUEST_START_TIME);
        long duration = startTime != null ? System.currentTimeMillis() - startTime : 0;
        
        Map<String, Object> logData = new HashMap<>();
        logData.put("event", "request_completed");
        logData.put("method", request.getAttribute(REQUEST_METHOD));
        logData.put("uri", request.getAttribute(REQUEST_URI));
        logData.put("status", response.getStatus());
        logData.put("duration", duration);
        logData.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        if (ex != null) {
            logData.put("exception", ex.getClass().getSimpleName());
            logData.put("errorMessage", ex.getMessage());
            log.error("HTTP Request Failed: {}", logData, ex);
        } else {
            log.info("HTTP Request Completed: {}", logData);
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR"
        };
        
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // 处理多个IP的情况，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 获取请求头值
     */
    private String getHeaderValue(HttpServletRequest request, String headerName) {
        return request.getHeader(headerName);
    }
    
    /**
     * 判断是否为重要的请求头
     */
    private boolean isImportantHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.equals("content-type") ||
               lowerCaseName.equals("accept") ||
               lowerCaseName.equals("authorization") ||
               lowerCaseName.startsWith("x-") ||
               lowerCaseName.equals("user-agent") ||
               lowerCaseName.equals("referer");
    }
}
