package com.implatform.common.webflux;

import com.implatform.common.config.RequestHeaderContext;

public class BaseServiceImpl implements BaseService{


    protected String getCurrentUserId() {
        return RequestHeaderContext.getHeader("X-User-Id");
    }

    protected String getToken() {
        return RequestHeaderContext.getHeader("X-Token");
    }
    protected String getClientIp() {
        return RequestHeaderContext.getHeader("X-Client-Ip");
    }
    protected String getUserAgent() {
        return RequestHeaderContext.getHeader("X-User-Agent");
    }
}
