package com.implatform.common.webflux.config;

import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RequestHeaderContext {
    private static final ThreadLocal<Map<String, String>> HEADERS = new ThreadLocal<>();

    public static void setHeaders(Map<String, String> headers) {
        HEADERS.set(headers);
    }

    public static String getHeader(String name) {
        Map<String, String> headers = HEADERS.get();
        return headers != null ? headers.get(name) : null;
    }

    public static void clear() {
        HEADERS.remove();
    }
}
