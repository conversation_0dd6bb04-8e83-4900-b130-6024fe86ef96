package com.implatform.common.webflux.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页结果响应类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总元素数量
     */
    private Long totalElements;
    
    /**
     * 总页数.
     */
    private Integer totalPages;
    
    /**
     * 当前页码（从0开始）
     */
    private Integer currentPage;
    
    /**
     * 页面大小
     */
    private Integer pageSize;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否最后一页
     */
    private Boolean isLast;
    
    /**
     * 是否为空
     */
    private Boolean isEmpty;
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> content, Long totalElements, Integer currentPage, Integer pageSize) {
        Integer totalPages = (int) Math.ceil((double) totalElements / pageSize);
        
        return PageResult.<T>builder()
                .records(content)
                .totalElements(totalElements)
                .totalPages(totalPages)
                .currentPage(currentPage)
                .pageSize(pageSize)
                .hasNext(currentPage < totalPages - 1)
                .hasPrevious(currentPage > 0)
                .isFirst(currentPage == 0)
                .isLast(currentPage == totalPages - 1)
                .isEmpty(content == null || content.isEmpty())
                .build();
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty(Integer currentPage, Integer pageSize) {
        return PageResult.<T>builder()
                .records(List.of())
                .totalElements(0L)
                .totalPages(0)
                .currentPage(currentPage)
                .pageSize(pageSize)
                .hasNext(false)
                .hasPrevious(false)
                .isFirst(true)
                .isLast(true)
                .isEmpty(true)
                .build();
    }
}
