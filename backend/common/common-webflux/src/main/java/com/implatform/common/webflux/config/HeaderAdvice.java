package com.implatform.common.webflux.config;

import com.implatform.common.webflux.RequestHeaderInfo;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;

@ControllerAdvice
public class HeaderAdvice {

    @ModelAttribute("headerInfo")
    public RequestHeaderInfo headerInfo(
            @RequestHeader(value = "X-User-Id",required = false) Long userId,
            @RequestHeader(value = "X-Admin-User-Id",required = false) String adminUserId,
            @RequestHeader(value = "Accept-Language",required = false) String locale,
            @RequestHeader(value = "Accept", required = false, defaultValue = "application/json") String acceptHeader,
            @RequestHeader(value = "X-Device-Id", required = false, defaultValue = "") String deviceId,
            @RequestHeader(value = "X-Token",required = false, defaultValue = "") String accessToken,
            @RequestHeader(value = "X-User-Type",required = false, defaultValue = "") String userType,
            @RequestHeader(value = "X-User-Role",required = false, defaultValue = "") String userRole,
            @RequestHeader(value = "X-Username",required = false, defaultValue = "") String username,
            @RequestHeader(value = "X-Client-Ip",required = false, defaultValue = "") String clientIp,
            @RequestHeader(value = "X-User-Agent",required = false, defaultValue = "") String userAgent
            ) {
        RequestHeaderInfo info = new RequestHeaderInfo();
        info.setUserId(userId);
        info.setAdminUserId(adminUserId);
        info.setLocale(locale);
        info.setAcceptHeader(acceptHeader);
        info.setDeviceId(deviceId);
        info.setAccessToken(accessToken);
        info.setUserType(userType);
        info.setUserRole(userRole);
        info.setUsername(username);
        info.setClientIp(clientIp);
        info.setUserAgent(userAgent);
        return info;
    }
}
