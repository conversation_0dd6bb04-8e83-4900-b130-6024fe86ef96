dependencies {
    // 依赖核心模块
    api project(':common:common-core')
    
    // WebFlux 支持
    api 'org.springframework.boot:spring-boot-starter-webflux'

    // 监控和健康检查
    api 'org.springframework.boot:spring-boot-starter-actuator'
    api 'io.micrometer:micrometer-registry-prometheus'
    api 'io.micrometer:micrometer-tracing-bridge-brave'
    
    // HTTP客户端 (响应式)
    api 'org.springframework:spring-webflux'

    api "com.github.xiaoymin:knife4j-openapi3-jakarta-spring-boot-starter:4.4.0"

    // R2DBC for reactive database access
    api 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    api 'org.postgresql:r2dbc-postgresql'

    // 服务发现
    api 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    api 'org.springframework.cloud:spring-cloud-starter-consul-config'
    api 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
}

jar {
    enabled = true
    archiveClassifier = ''
}

bootJar {
    enabled = false
}
