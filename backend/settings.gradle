rootProject.name = 'im-platform'

// 公共模块
include 'common'
include 'common:common-core'
include 'common:common-webflux'
include 'common:common-data'
include 'common:common-security'
include 'common:common-messaging'
include 'common:common-api'
include 'common:common-protobuf'

include 'services:gateway-service'
include 'services:media-service'
include 'services:notification-service'
include 'services:realtime-service'
include 'services:user-service'

// 项目目录映射
project(':common').projectDir = file('common')
project(':common:common-core').projectDir = file('common/common-core')
project(':common:common-webflux').projectDir = file('common/common-webflux')
project(':common:common-data').projectDir = file('common/common-data')
project(':common:common-security').projectDir = file('common/common-security')
project(':common:common-messaging').projectDir = file('common/common-messaging')
project(':common:common-api').projectDir = file('common/common-api')
project(':common:common-protobuf').projectDir = file('common/common-protobuf')

project(':services:gateway-service').projectDir = file('services/gateway-service')
project(':services:media-service').projectDir = file('services/media-service')
project(':services:notification-service').projectDir = file('services/notification-service')
project(':services:realtime-service').projectDir = file('services/realtime-service')
project(':services:user-service').projectDir = file('services/user-service')

