plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.3' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

allprojects {
    group = 'com.implatform'
    version = '1.0.0'
    
    repositories {
        mavenCentral()
        gradlePluginPortal()
    }
}

subprojects {
    // 排除 services 目录本身，它只是一个容器目录
    if (project.name == 'services') {
        return
    }
    
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    
    // 对于 common 相关模块应用 java-library 插件
    if (project.name == 'common' || project.name.startsWith('common-')) {
        apply plugin: 'java-library'
    }
    
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }
    
    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }
    
    dependencies {
        implementation 'org.springframework.boot:spring-boot-starter'
        implementation 'org.springframework.boot:spring-boot-starter-actuator'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.boot:spring-boot-starter-logging'

        // ELK Stack 集成 - 为所有服务添加结构化日志支持
        if (project.name.endsWith('-service') && !project.name.startsWith('common-')) {
            implementation 'net.logstash.logback:logstash-logback-encoder:8.0'
            implementation 'ch.qos.logback:logback-classic'
            implementation 'ch.qos.logback:logback-core'
        }

        // Micrometer 监控
        implementation 'io.micrometer:micrometer-registry-prometheus'
        implementation 'io.micrometer:micrometer-tracing-bridge-brave'

        // Zipkin Reporter for Jaeger
        implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
        
        // Spring Cloud
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
        implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
        
        // Lombok
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.projectlombok:lombok'
        
        // 测试依赖
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.boot:spring-boot-testcontainers'
        testImplementation 'org.testcontainers:junit-jupiter'
        testImplementation 'org.testcontainers:postgresql'
        testImplementation 'org.testcontainers:consul'
    }
    
    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:2024.0.0"
        }
    }
    
    tasks.named('test') {
        useJUnitPlatform()
    }
    
    // Gradle 构建优化
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
        options.compilerArgs += ['-parameters']
    }
    
    // 开发时热重载
    bootRun {
        jvmArgs = [
            '-Dspring.profiles.active=dev',
            '-Dspring.devtools.restart.enabled=true'
        ]
    }
} 