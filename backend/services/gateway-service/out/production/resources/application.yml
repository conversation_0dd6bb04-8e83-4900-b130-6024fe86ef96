# =============================================
# Gateway Service Application Configuration
# 网关服务应用配置 - 生产就绪的完整配置
# =============================================

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}


  # =============================================
  # Spring Cloud Gateway 核心配置
  # =============================================
  cloud:
    gateway:
      # 全局过滤器配置
      filter:
        # 禁用gRPC过滤器避免依赖冲突
        json-to-grpc:
          enabled: false

      # 全局CORS配置 - 生产级安全策略
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns:
              - "https://*.implatform.com"
              - "http://localhost:3000"
              - "http://localhost:3001"
              - "http://127.0.0.1:3000"
              - "http://127.0.0.1:3001"
            allowed-methods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - PATCH
            allowed-headers:
              - Authorization
              - Content-Type
              - X-Requested-With
              - Accept
              - Origin
              - Access-Control-Request-Method
              - Access-Control-Request-Headers
              - X-User-Id
              - X-User-Type
              - X-Device-Id
              - application/x-protobuf
            allow-credentials: true
            max-age: 3600

      # 限流配置将在路由级别配置

      # =============================================
      # 服务路由配置 - 8个微服务的统一路由规则
      # =============================================
      routes:
        # 管理服务 - 后台管理和分析
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/v1/admin/**
            - Path=/api/v1/auth/admin/**
            - Path=/api/v1/analytics/**
          filters:
            - name: CircuitBreaker
              args:
                name: admin-service
                fallbackUri: forward:/fallback/admin-service
          metadata:
            description: "后台管理服务"
            admin-only: true
        # 媒体服务 - 文件上传、图片、视频处理
        - id: media-service
          uri: lb://media-service
          predicates:
            - Path=/api/v1/media/**
          filters:
            - name: CircuitBreaker
              args:
                name: media-service
                fallbackUri: forward:/fallback/media-service
          metadata:
            description: "媒体文件服务"
            large-payload: true
        # 消息服务 - 即时消息和聊天
        - id: message-service
          uri: lb://message-service
          predicates:
            - Path=/api/v1/messages/**
            - Path=/api/v1/search/**
          filters:
            - name: CircuitBreaker
              args:
                name: message-service
                fallbackUri: forward:/fallback/message-service
            # 条件性启用限流 (当Redis可用时)
            # - name: RequestRateLimiter
            #   args:
            #     redis-rate-limiter.replenish-rate: 200
            #     redis-rate-limiter.burst-capacity: 400
          metadata:
            description: "即时消息服务"
            high-traffic: true
        # 通知服务 - 推送通知
        - id: notification-service
          uri: lb://notification-service
          predicates:
            - Path=/api/v1/notifications/**
            - Path=/api/v1/push/**
          filters:
            - name: CircuitBreaker
              args:
                name: notification-service
                fallbackUri: forward:/fallback/notification-service
          metadata:
            description: "消息通知服务"
        # 支付服务 - 支付处理和账单
        - id: payment-service
          uri: lb://payment-service
          predicates:
            - Path=/api/v1/payments/**
            - Path=/api/v1/red-packets/**
            - Path=/api/v1/wallet/**
          filters:
            - name: CircuitBreaker
              args:
                name: payment-service
                fallbackUri: forward:/fallback/payment-service
          metadata:
            description: "支付处理服务"
            security-critical: true
        # 实时服务 - WebSocket和实时通信
        - id: realtime-service-websocket
          uri: ${REALTIME_WEBSOCKET_URI:http://localhost:8111}
          predicates:
            - Path=/ws/**
          filters:
            - AddRequestHeader=X-From-Gateway,true
            - name: CircuitBreaker
              args:
                name: realtime-service-websocket
                fallbackUri: forward:/fallback/realtime-service
          metadata:
            description: "实时通信WebSocket服务"
            websocket: true
        # 实时服务 - HTTP API
        - id: realtime-service-http
          uri: lb://realtime-service
          predicates:
            - Path=/api/v1/realtime/**
            - Path=/api/v1/sessions/**
            - Path=/api/v1/presence/**
            - Path=/api/v1/webrtc/**
          filters:
            - name: CircuitBreaker
              args:
                name: realtime-service-http
                fallbackUri: forward:/fallback/realtime-service
          metadata:
            description: "实时通信HTTP API服务"
        # 社交服务 - 群聊,频道,对话等等
        - id: social-service
          uri: lb://social-service
          predicates:
            - Path=/api/v1/social/**
            - Path=/api/v1/bots/**
            - Path=/api/v1/bot-store/**
            - Path=/api/v1/channels/**
            - Path=/api/v1/cloud-notes/**
            - Path=/api/v1/cloud-note-folders/**
            - Path=/api/v1/favorite-folders/**
            - Path=/api/v1/checkin/**
            - Path=/api/v1/groups/**
            - Path=/api/v1/conversations/**
            - Path=/api/v1/official-accounts/**
            - Path=/api/v1/polls/**
            - Path=/api/v1/ai-conversations/**
          filters:
            - name: CircuitBreaker
              args:
                name: social-service
                fallbackUri: forward:/fallback/social-service
          metadata:
            description: "社交管理服务"
        # 用户认证服务 - 登录、注册、密码重置等认证功能
        - id: user-auth-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/auth/user/**
          filters:
            - name: CircuitBreaker
              args:
                name: user-auth-service
                fallbackUri: forward:/fallback/user-service
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
          metadata:
            description: "用户认证服务"
            version: "1.0.0"
            auth-required: false

        # 用户服务 - 用户管理和个人资料
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/users/**
            - Path=/api/v1/contact-tags/**
            - Path=/api/v1/device-management/**
            - Path=/api/v1/devices/**
            - Path=/api/v1/friends/**
            - Path=/api/v1/secret-chats/**
            - Path=/api/v1/usernames/**
          filters:
            - name: CircuitBreaker
              args:
                name: user-service
                fallbackUri: forward:/fallback/user-service
          metadata:
            description: "用户管理服务"
            version: "1.0.0"

        # 用户认证服务 - 认证和授权（通用认证路径）
        - id: user-auth-general-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - name: CircuitBreaker
              args:
                name: user-service
                fallbackUri: forward:/fallback/user-service
          metadata:
            description: "用户认证服务（通用）"
            version: "1.0.0"

        # 国际化服务 - 多语言支持（公共接口，无需认证）
        - id: i18n-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/public/i18n/**
          filters:
            - name: CircuitBreaker
              args:
                name: i18n-service
                fallbackUri: forward:/fallback/user-service
          metadata:
            description: "国际化多语言服务"
            version: "1.0.0"
            auth-required: false
      # =============================================
      # 全局默认过滤器 - 应用于所有路由
      # =============================================
      default-filters:
        # 重试机制
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
            backoff:
              firstBackoff: 50ms
              maxBackoff: 500ms
              factor: 2
              basedOnPreviousValue: false

        # 请求追踪ID
        - name: AddRequestHeader
          args:
            name: X-Gateway-Request-Id
            value: "gateway-request-${random.uuid}"

        # 网关标识
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version
            value: "1.0.0"

        # 请求时间戳（使用静态值，避免SpEL安全限制）
        - name: AddRequestHeader
          args:
            name: X-Request-Time
            value: "gateway-request-time"

        # 响应头优化（使用静态值，避免SpEL安全限制）
        - name: AddResponseHeader
          args:
            name: X-Response-Time
            value: "gateway-response-time"


---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    "[com.implatform.gateway]": ${LOG_LEVEL_GATEWAY_SERVICE:INFO}
    "[org.springframework.cloud.gateway]": INFO
    "[reactor.netty.http.server.AccessLog]": OFF
    "[reactor.netty.http.server]": WARN
    "[reactor.netty]": WARN
    "[io.netty]": WARN

---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    consul:
      host: ${CONSUL_HOST:consul}
      discovery:
        ip-address: ${HOSTNAME:gateway-service}

logging:
  level:
    "[reactor.netty.http.server.AccessLog]": OFF
    "[reactor.netty.http.server]": WARN
    "[reactor.netty]": WARN
    "[io.netty]": WARN
  file:
    name: /app/logs/gateway-service.log

# =============================================
# Application Configuration
# =============================================
app:
  version: ${APP_VERSION:1.0.0}
  name: ${spring.application.name:gateway-service}
  description: "IM平台网关服务 - 统一入口和路由管理"

# =============================================
# SpringDoc OpenAPI配置
# =============================================
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLED:true}
    path: /v3/api-docs
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:true}
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha
    display-request-duration: true
    try-it-out-enabled: true
    filter: true
    # 配置各微服务的API文档URL
    urls:
      - name: gateway-service
        url: /v3/api-docs
        display-name: "网关服务API"
      - name: user-service
        url: /api/v1/users/v3/api-docs
        display-name: "用户服务API"
      - name: media-service
        url: /api/v1/media/v3/api-docs
        display-name: "媒体服务API"
      - name: notification-service
        url: /api/v1/notifications/v3/api-docs
        display-name: "通知服务API"
      - name: realtime-service
        url: /api/v1/realtime/v3/api-docs
        display-name: "实时服务API"
