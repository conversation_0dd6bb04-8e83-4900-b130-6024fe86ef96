dependencies {
    // =============================================
    // Spring Cloud Gateway - 响应式架构核心依赖
    // =============================================

    // Spring Cloud Gateway (包含WebFlux + Netty)
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'

    // 响应式Redis支持
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'

    // 验证框架
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // =============================================
    // Common Modules - 精确排除servlet依赖
    // =============================================

    // 核心模块 - 排除servlet相关依赖
    implementation(project(':common:common-core')) {
        exclude group: 'org.springframework', module: 'spring-webmvc'
        exclude group: 'jakarta.servlet', module: 'jakarta.servlet-api'
    }

    // WebFlux模块 - 网关的响应式Web支持
    implementation project(':common:common-webflux')

    // API模块 - 排除servlet依赖
    implementation(project(':common:common-api')) {
        exclude group: 'org.springframework', module: 'spring-webmvc'
        exclude group: 'jakarta.servlet', module: 'jakarta.servlet-api'
    }

    // 安全模块 - 排除servlet依赖
    implementation(project(':common:common-security')) {
        exclude group: 'org.springframework', module: 'spring-webmvc'
        exclude group: 'jakarta.servlet', module: 'jakarta.servlet-api'
    }

    // =============================================
    // 全局排除配置 - 仅排除Servlet容器
    // =============================================
    configurations.all {
        exclude group: 'org.apache.tomcat.embed'
        exclude group: 'org.springframework', module: 'spring-webmvc'
        exclude group: 'jakarta.servlet', module: 'jakarta.servlet-api'

        // 排除protobuf - 避免gRPC冲突
        exclude group: 'com.google.protobuf'
        exclude group: 'io.grpc'
        exclude module: 'common-protobuf'
    }

    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

    implementation 'io.netty:netty-resolver-dns-native-macos'


    // Security and Service Discovery are included via common modules

    // Observability
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'

    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.projectreactor:reactor-test'
}