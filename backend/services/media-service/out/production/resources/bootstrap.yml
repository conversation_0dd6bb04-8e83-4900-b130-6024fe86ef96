# =============================================
# Media Service Unified Configuration
# Combines bootstrap and application configuration
# =============================================

server:
  port: ${SERVER_PORT:8083}

spring:
  application:
    name: media-service

  main:
    web-application-type: servlet

  cloud:
    consul:
      enabled: true
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}

      # Service Discovery Configuration
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}:${server.port}:${random.value}
        health-check-path: /actuator/health
        health-check-interval: 10s
        health-check-timeout: 5s
        # 关键：使用hostname进行注册，支持内网IP变化时自动恢复
        prefer-ip-address: false
        hostname: ${spring.application.name}
        ip-address: ${spring.cloud.client.ip-address:localhost}
        health-check-critical-timeout: 60s
        # 自动重新注册配置
        register: true
        deregister: true
        # 查询配置
        query-passing: true
        fail-fast: false
      config:
        enabled: true
        format: yaml
        prefixes: config
        default-context: application
        data-key: data
        watch:
          enabled: true
          delay: 1000
        fail-fast: false
  # R2DBC Configuration for reactive database access
  r2dbc:
    url: ${MEDIA_DB_R2DBC_URL:r2dbc:postgresql://localhost:5432/media_db}
    username: ${MEDIA_DB_USERNAME:postgres}
    password: ${MEDIA_DB_PASSWORD:postgres123}
    pool:
      enabled: true
      initial-size: 5
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1