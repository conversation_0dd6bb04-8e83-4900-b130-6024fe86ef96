dependencies {
    // Common Modules (排除JPA依赖，使用R2DBC)
    implementation project(':common:common-core')
    implementation project(':common:common-webflux')
    implementation project(':common:common-messaging')
    implementation project(':common:common-api')
    implementation project(':common:common-security')
    implementation project(':common:common-protobuf')
    implementation(project(':common:common-data')) {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-jpa'
        exclude group: 'com.zaxxer', module: 'HikariCP'
    }

    // R2DBC for reactive database access
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'org.postgresql:r2dbc-postgresql'

    // 文件处理
    implementation 'io.minio:minio:8.5.15'
    implementation 'net.coobird:thumbnailator:0.4.20'
    implementation 'org.apache.tika:tika-core:2.9.1'

    // 分布式存储依赖 (集成storage功能)
    implementation 'io.minio:minio:8.5.7'
    implementation 'software.amazon.awssdk:s3:2.21.29'
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.17.4'
    implementation 'com.qcloud:cos_api:5.6.155'

    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
}