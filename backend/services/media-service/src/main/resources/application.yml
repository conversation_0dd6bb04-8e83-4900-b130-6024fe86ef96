# =============================================
# media-service Bootstrap Configuration
# Minimal local config - Business logic in Consul
# =============================================

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: media-service

# Protocol Buffers Configuration
im:
  protobuf:
    enabled: true
    pool:
      enabled: true
      max-total: 200
      max-idle: 100
      min-idle: 20
    compression:
      enabled: true
      threshold: 1024  # 1KB
    metrics:
      enabled: true

app:
  # File Upload Configuration
  file:
    upload:
      base-path: ${FILE_UPLOAD_PATH:/app/uploads}
      max-size: 104857600 # 100MB
      allowed-types: jpg,jpeg,png,gif,bmp,webp,mp4,avi,mov,wmv,mp3,wav,aac,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar
  
  # Media Processing Configuration
  media:
    image:
      max-width: 4096
      max-height: 4096
      quality: 0.8
      thumbnail-size: 200
    
    video:
      max-duration: 600 # 10 minutes
      max-size: 500MB
      supported-formats:
        - mp4
        - avi
        - mov
        - wmv
    
    audio:
      max-duration: 1800 # 30 minutes
      max-size: 50MB
      supported-formats:
        - mp3
        - wav
        - aac
  
# Cache Configuration
cache:
  file-metadata:
    ttl: 3600s # 1 hour
    max-size: 10000
  
  sticker-packs:
    ttl: 7200s # 2 hours
    max-size: 1000

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      user-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10

---
# Development Environment Overrides
spring:
  config:
    activate:
      on-profile: dev

# =============================================
# SpringDoc OpenAPI配置 - 仅提供API文档，不启用UI
# =============================================
springdoc:
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.implatform.media.controller

app:
  file:
    upload:
      base-path: ./uploads # Local development path

logging:
  level:
    com.implatform.media: ${LOG_LEVEL_MEDIA_SERVICE:INFO}
  performance:
    slow-threshold: 10000
  file-operations:
    enabled: true
    log-upload-progress: true
    log-processing-steps: true

---
# Production Environment Overrides
spring:
  config:
    activate:
      on-profile: prod

  cloud:
    consul:
      host: ${CONSUL_HOST:consul}
      discovery:
        ip-address: ${HOSTNAME:media-service}

logging:
  file:
    name: /app/logs/media-service.log

app:
  file:
    upload:
      base-path: /app/uploads # Production path