package com.implatform.media.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.webflux.response.PageResult;
import com.implatform.common.core.enums.StickerErrorCode;
import com.implatform.media.dto.StickerDTO;
import com.implatform.media.dto.StickerPackDTO;
import com.implatform.media.entity.Sticker;
import com.implatform.media.entity.StickerPack;
import com.implatform.media.repository.StickerPackRepository;
import com.implatform.media.repository.StickerRepository;
import com.implatform.media.service.StickerPackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表情包服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StickerPackServiceImpl implements StickerPackService {
    
    private final StickerPackRepository stickerPackRepository;
    private final StickerRepository stickerRepository;
    private final ObjectMapper objectMapper;
    
    @Override
    @Transactional
    public StickerPackDTO createStickerPack(StickerPackDTO stickerPackDTO, Long creatorId) {
        log.info("创建表情包: name={}, creatorId={}", stickerPackDTO.getName(), creatorId);
        
        StickerPack stickerPack = new StickerPack();
        BeanUtils.copyProperties(stickerPackDTO, stickerPack);
        stickerPack.setCreatorId(creatorId);
        stickerPack.setCreatedAt(Instant.now());
        stickerPack.setUpdatedAt(Instant.now());
        
        // 设置标签
        if (stickerPackDTO.getTags() != null && !stickerPackDTO.getTags().isEmpty()) {
            stickerPack.setTags(stickerPackDTO.getTags().toArray(new String[0]));
        }
        
        StickerPack saved = stickerPackRepository.save(stickerPack);
        return convertToDTO(saved);
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "stickerPacks", key = "#packId")
    public StickerPackDTO updateStickerPack(Long packId, StickerPackDTO stickerPackDTO, Long userId) {
        log.info("更新表情包: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));

        if (!stickerPack.getCreatorId().equals(userId)) {
            throw new BusinessException(StickerErrorCode.STICKER_PACK_MODIFY_DENIED);
        }
        
        BeanUtils.copyProperties(stickerPackDTO, stickerPack, "id", "creatorId", "createdAt", 
                "downloadCount", "usageCount", "favoriteCount", "rating", "ratingCount");
        stickerPack.setUpdatedAt(Instant.now());
        
        // 更新标签
        if (stickerPackDTO.getTags() != null) {
            stickerPack.setTags(stickerPackDTO.getTags().toArray(new String[0]));
        }
        
        StickerPack updated = stickerPackRepository.save(stickerPack);
        return convertToDTO(updated);
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "stickerPacks", key = "#packId")
    public void deleteStickerPack(Long packId, Long userId) {
        log.info("删除表情包: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));

        if (!stickerPack.getCreatorId().equals(userId)) {
            throw new BusinessException(StickerErrorCode.STICKER_PACK_DELETE_DENIED);
        }
        
        stickerPack.setStatus(StickerPack.PackStatus.DELETED);
        stickerPack.setUpdatedAt(Instant.now());
        stickerPackRepository.save(stickerPack);
    }
    
    @Override
    @Cacheable(value = "stickerPacks", key = "#packId")
    public StickerPackDTO getStickerPackById(Long packId, Long userId) {
        log.info("获取表情包详情: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));

        if (stickerPack.getStatus() == StickerPack.PackStatus.DELETED) {
            throw new BusinessException(StickerErrorCode.STICKER_PACK_DELETED);
        }
        
        StickerPackDTO dto = convertToDTO(stickerPack);
        
        // 获取表情列表
        List<Sticker> stickers = stickerRepository.findByPackIdOrderBySortOrderAsc(packId);
        dto.setStickers(stickers.stream().map(this::convertStickerToDTO).collect(Collectors.toList()));
        
        return dto;
    }
    
    @Override
    public PageResult<StickerPackDTO> getPublicStickerPacks(Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findByIsPublicAndStatus(
                true, StickerPack.PackStatus.ACTIVE, pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public PageResult<StickerPackDTO> getStickerPacksByCategory(String category, Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findByCategoryAndIsPublicAndStatus(
                category, true, StickerPack.PackStatus.ACTIVE, pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public PageResult<StickerPackDTO> getStickerPacksByType(StickerPack.PackType packType, Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findByPackTypeAndIsPublicAndStatus(
                packType, true, StickerPack.PackStatus.ACTIVE, pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public PageResult<StickerPackDTO> searchStickerPacks(String keyword, Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.searchStickerPacks(keyword, pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public PageResult<StickerPackDTO> getPopularStickerPacks(Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findPopularStickerPacks(pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public PageResult<StickerPackDTO> getLatestStickerPacks(Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findLatestStickerPacks(pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public List<StickerPackDTO> getSystemStickerPacks() {
        List<StickerPack> systemPacks = stickerPackRepository.findByIsSystemAndStatusOrderByCreatedAtAsc(
                true, StickerPack.PackStatus.ACTIVE);
        
        return systemPacks.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    @Override
    public PageResult<StickerPackDTO> getStickerPacksByTag(String tag, Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<StickerPack> packPage = stickerPackRepository.findByTag(tag, pageable);
        
        return convertToPageResult(packPage, page, size);
    }
    
    @Override
    public List<StickerPackDTO> getUserStickerPacks(Long userId) {
        List<StickerPack> userPacks = stickerPackRepository.findByCreatorIdAndStatus(
                userId, StickerPack.PackStatus.ACTIVE);
        
        return userPacks.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void downloadStickerPack(Long packId, Long userId) {
        log.info("下载表情包: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));
        
        stickerPack.setDownloadCount(stickerPack.getDownloadCount() + 1);
        stickerPackRepository.save(stickerPack);
    }
    
    @Override
    @Transactional
    public void favoriteStickerPack(Long packId, Long userId) {
        log.info("收藏表情包: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));
        
        stickerPack.setFavoriteCount(stickerPack.getFavoriteCount() + 1);
        stickerPackRepository.save(stickerPack);
    }
    
    @Override
    @Transactional
    public void unfavoriteStickerPack(Long packId, Long userId) {
        log.info("取消收藏表情包: packId={}, userId={}", packId, userId);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));
        
        stickerPack.setFavoriteCount(Math.max(0, stickerPack.getFavoriteCount() - 1));
        stickerPackRepository.save(stickerPack);
    }
    
    @Override
    @Transactional
    public void rateStickerPack(Long packId, Long userId, Double rating) {
        log.info("评分表情包: packId={}, userId={}, rating={}", packId, userId, rating);
        
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElseThrow(() -> new BusinessException(StickerErrorCode.STICKER_PACK_NOT_FOUND));
        
        // 简化的评分计算，实际应该存储用户评分记录
        double currentRating = stickerPack.getRating();
        long ratingCount = stickerPack.getRatingCount();
        
        double newRating = (currentRating * ratingCount + rating) / (ratingCount + 1);
        
        stickerPack.setRating(newRating);
        stickerPack.setRatingCount(ratingCount + 1);
        stickerPackRepository.save(stickerPack);
    }
    
    @Override
    @Transactional
    public void incrementUsageCount(Long packId) {
        StickerPack stickerPack = stickerPackRepository.findById(packId)
                .orElse(null);
        
        if (stickerPack != null) {
            stickerPack.setUsageCount(stickerPack.getUsageCount() + 1);
            stickerPackRepository.save(stickerPack);
        }
    }
    
    private StickerPackDTO convertToDTO(StickerPack stickerPack) {
        StickerPackDTO dto = new StickerPackDTO();
        BeanUtils.copyProperties(stickerPack, dto);
        
        // 解析标签
        if (stickerPack.getTags() != null) {
            dto.setTags(List.of(stickerPack.getTags()));
        } else {
            dto.setTags(Collections.emptyList());
        }
        
        return dto;
    }
    
    private StickerDTO convertStickerToDTO(Sticker sticker) {
        StickerDTO dto = new StickerDTO();
        BeanUtils.copyProperties(sticker, dto);
        
        // 解析标签
        if (sticker.getTags() != null) {
            try {
                List<String> tags = objectMapper.readValue(sticker.getTags(), new TypeReference<List<String>>() {});
                dto.setTags(tags);
            } catch (JsonProcessingException e) {
                dto.setTags(Collections.emptyList());
            }
        }
        
        return dto;
    }
    
    private PageResult<StickerPackDTO> convertToPageResult(Page<StickerPack> packPage, Integer page, Integer size) {
        List<StickerPackDTO> dtos = packPage.getContent()
                .stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return PageResult.of(
                dtos,
                packPage.getTotalElements(),
                page,
                size
        );
    }
} 