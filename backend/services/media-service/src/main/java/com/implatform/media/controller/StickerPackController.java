package com.implatform.media.controller;

import java.util.List;

import com.implatform.common.webflux.response.PageResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.implatform.common.core.domain.Result;
import com.implatform.common.core.enums.StickerErrorCode;
import com.implatform.media.dto.StickerPackDTO;
import com.implatform.media.dto.AnimatedStickerDTO;
import com.implatform.media.entity.StickerPack;
import com.implatform.media.service.StickerPackService;
import com.implatform.media.service.StickerPackImportExportService;
import com.implatform.media.service.AnimatedStickerService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.CompletableFuture;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 表情包控制器
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/media/sticker-packs")
@RequiredArgsConstructor
@Tag(name = "表情包管理", description = "表情包创建、查询、下载、收藏等功能")
public class StickerPackController {
    
    private final StickerPackService stickerPackService;
    private final StickerPackImportExportService importExportService;
    private final AnimatedStickerService animatedStickerService;
    
    /**
     * 创建表情包
     */
    @PostMapping
    @Operation(summary = "创建表情包", description = "用户创建自己的表情包")
    public Result<StickerPackDTO> createStickerPack(
            @Valid @RequestBody StickerPackDTO stickerPackDTO,
            @Parameter(description = "创建者用户ID") @RequestHeader("X-User-Id") Long creatorId) {
        log.info("创建表情包: name={}, creatorId={}", stickerPackDTO.getName(), creatorId);
        
        StickerPackDTO result = stickerPackService.createStickerPack(stickerPackDTO, creatorId);
        return Result.success(result);
    }
    
    /**
     * 更新表情包
     */
    @PutMapping("/{packId}")
    @Operation(summary = "更新表情包", description = "更新表情包信息")
    public Result<StickerPackDTO> updateStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Valid @RequestBody StickerPackDTO stickerPackDTO,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("更新表情包: packId={}, userId={}", packId, userId);
        
        StickerPackDTO result = stickerPackService.updateStickerPack(packId, stickerPackDTO, userId);
        return Result.success(result);
    }
    
    /**
     * 删除表情包
     */
    @DeleteMapping("/{packId}")
    @Operation(summary = "删除表情包", description = "删除指定的表情包")
    public Result<Void> deleteStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("删除表情包: packId={}, userId={}", packId, userId);
        
        stickerPackService.deleteStickerPack(packId, userId);
        return Result.success();
    }
    
    /**
     * 获取表情包详情
     */
    @GetMapping("/{packId}")
    @Operation(summary = "表情包详情", description = "获取指定表情包的详细信息")
    public Result<StickerPackDTO> getStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("获取表情包详情: packId={}, userId={}", packId, userId);
        
        StickerPackDTO result = stickerPackService.getStickerPackById(packId, userId);
        return Result.success(result);
    }
    
    /**
     * 获取公开表情包列表
     */
    @GetMapping("/public")
    @Operation(summary = "公开表情包", description = "获取所有公开的表情包列表")
    public Result<PageResult<StickerPackDTO>> getPublicStickerPacks(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取公开表情包列表: page={}, size={}", page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getPublicStickerPacks(page, size);
        return Result.success(result);
    }
    
    /**
     * 根据分类获取表情包
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "分类表情包", description = "根据分类获取表情包列表")
    public Result<PageResult<StickerPackDTO>> getStickerPacksByCategory(
            @Parameter(description = "分类名称") @PathVariable String category,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("根据分类获取表情包: category={}, page={}, size={}", category, page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getStickerPacksByCategory(category, page, size);
        return Result.success(result);
    }
    
    /**
     * 根据类型获取表情包
     */
    @GetMapping("/type/{packType}")
    @Operation(summary = "类型表情包", description = "根据表情包类型获取表情包列表")
    public Result<PageResult<StickerPackDTO>> getStickerPacksByType(
            @Parameter(description = "表情包类型") @PathVariable StickerPack.PackType packType,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("根据类型获取表情包: packType={}, page={}, size={}", packType, page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getStickerPacksByType(packType, page, size);
        return Result.success(result);
    }
    
    /**
     * 搜索表情包
     */
    @GetMapping("/search")
    @Operation(summary = "搜索表情包", description = "根据关键词搜索表情包")
    public Result<PageResult<StickerPackDTO>> searchStickerPacks(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("搜索表情包: keyword={}, page={}, size={}", keyword, page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.searchStickerPacks(keyword, page, size);
        return Result.success(result);
    }
    
    /**
     * 获取热门表情包
     */
    @GetMapping("/popular")
    @Operation(summary = "热门表情包", description = "获取热门表情包列表")
    public Result<PageResult<StickerPackDTO>> getPopularStickerPacks(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取热门表情包: page={}, size={}", page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getPopularStickerPacks(page, size);
        return Result.success(result);
    }
    
    /**
     * 获取最新表情包
     */
    @GetMapping("/latest")
    @Operation(summary = "最新表情包", description = "获取最新发布的表情包列表")
    public Result<PageResult<StickerPackDTO>> getLatestStickerPacks(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取最新表情包: page={}, size={}", page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getLatestStickerPacks(page, size);
        return Result.success(result);
    }
    
    /**
     * 获取系统表情包
     */
    @GetMapping("/system")
    @Operation(summary = "系统表情包", description = "获取系统内置表情包")
    public Result<List<StickerPackDTO>> getSystemStickerPacks() {
        log.info("获取系统表情包");
        
        List<StickerPackDTO> result = stickerPackService.getSystemStickerPacks();
        return Result.success(result);
    }
    
    /**
     * 根据标签获取表情包
     */
    @GetMapping("/tag/{tag}")
    @Operation(summary = "标签表情包", description = "根据标签获取表情包列表")
    public Result<PageResult<StickerPackDTO>> getStickerPacksByTag(
            @Parameter(description = "标签名称") @PathVariable String tag,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size) {
        log.info("根据标签获取表情包: tag={}, page={}, size={}", tag, page, size);
        
        PageResult<StickerPackDTO> result = stickerPackService.getStickerPacksByTag(tag, page, size);
        return Result.success(result);
    }
    
    /**
     * 获取用户创建的表情包
     */
    @GetMapping("/my")
    @Operation(summary = "我的表情包", description = "获取用户创建的表情包列表")
    public Result<List<StickerPackDTO>> getUserStickerPacks(
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("获取用户表情包: userId={}", userId);
        
        List<StickerPackDTO> result = stickerPackService.getUserStickerPacks(userId);
        return Result.success(result);
    }
    
    /**
     * 下载表情包
     */
    @PostMapping("/{packId}/download")
    @Operation(summary = "下载表情包", description = "用户下载表情包")
    public Result<Void> downloadStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("下载表情包: packId={}, userId={}", packId, userId);
        
        stickerPackService.downloadStickerPack(packId, userId);
        return Result.success();
    }
    
    /**
     * 收藏表情包
     */
    @PostMapping("/{packId}/favorite")
    @Operation(summary = "收藏表情包", description = "用户收藏表情包")
    public Result<Void> favoriteStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("收藏表情包: packId={}, userId={}", packId, userId);
        
        stickerPackService.favoriteStickerPack(packId, userId);
        return Result.success();
    }
    
    /**
     * 取消收藏表情包
     */
    @DeleteMapping("/{packId}/favorite")
    @Operation(summary = "取消收藏", description = "用户取消收藏表情包")
    public Result<Void> unfavoriteStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("取消收藏表情包: packId={}, userId={}", packId, userId);
        
        stickerPackService.unfavoriteStickerPack(packId, userId);
        return Result.success();
    }
    
    /**
     * 评分表情包
     */
    @PostMapping("/{packId}/rate")
    @Operation(summary = "评分表情包", description = "用户为表情包评分")
    public Result<Void> rateStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "评分", example = "4.5") @RequestParam Double rating,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("评分表情包: packId={}, userId={}, rating={}", packId, userId, rating);
        
        stickerPackService.rateStickerPack(packId, userId, rating);
        return Result.success();
    }

    /**
     * 导出表情包
     */
    @GetMapping("/{packId}/export")
    @Operation(summary = "导出表情包", description = "将表情包导出为ZIP文件")
    public ResponseEntity<byte[]> exportStickerPack(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("导出表情包: packId={}, userId={}", packId, userId);

        try {
            byte[] zipData = importExportService.exportStickerPack(packId);

            return ResponseEntity.ok()
                    .header("Content-Type", "application/zip")
                    .header("Content-Disposition", "attachment; filename=sticker_pack_" + packId + ".zip")
                    .body(zipData);

        } catch (Exception e) {
            log.error("导出表情包失败: packId={}", packId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 导入表情包
     */
    @PostMapping("/import")
    @Operation(summary = "导入表情包", description = "从ZIP文件导入表情包")
    public Result<StickerPackDTO> importStickerPack(
            @Parameter(description = "ZIP文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("导入表情包: filename={}, size={}, userId={}", file.getOriginalFilename(), file.getSize(), userId);

        try {
            StickerPackDTO result = importExportService.importStickerPack(file, userId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("导入表情包失败", e);
            return Result.error(StickerErrorCode.STICKER_UPLOAD_FAILED, e.getMessage());
        }
    }

    /**
     * 上传动画表情包
     */
    @PostMapping("/{packId}/animated-stickers")
    @Operation(summary = "上传动画表情", description = "上传动画表情到指定表情包")
    public Result<AnimatedStickerDTO> uploadAnimatedSticker(
            @Parameter(description = "表情包ID") @PathVariable Long packId,
            @Parameter(description = "动画文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("上传动画表情: packId={}, filename={}, userId={}", packId, file.getOriginalFilename(), userId);

        try {
            CompletableFuture<AnimatedStickerDTO> future = animatedStickerService.processAnimatedSticker(file, packId);
            AnimatedStickerDTO result = future.get(); // 同步等待处理完成

            return Result.success(result);
        } catch (Exception e) {
            log.error("上传动画表情失败: packId={}", packId, e);
            return Result.error(StickerErrorCode.STICKER_UPLOAD_FAILED, e.getMessage());
        }
    }

    /**
     * 获取动画预览
     */
    @GetMapping("/stickers/{stickerId}/animation-preview")
    @Operation(summary = "动画预览", description = "获取动画表情的预览信息")
    public Result<AnimatedStickerService.AnimationPreview> getAnimationPreview(
            @Parameter(description = "表情ID") @PathVariable Long stickerId,
            @Parameter(description = "用户ID") @RequestHeader("X-User-Id") Long userId) {
        log.info("获取动画预览: stickerId={}, userId={}", stickerId, userId);

        try {
            AnimatedStickerService.AnimationPreview preview = animatedStickerService.getAnimationPreview(stickerId);
            return Result.success(preview);
        } catch (Exception e) {
            log.error("获取动画预览失败: stickerId={}", stickerId, e);
            return Result.error(StickerErrorCode.STICKER_NOT_FOUND, e.getMessage());
        }
    }
}