package com.implatform.media.service;

import java.util.List;

import com.implatform.common.webflux.response.PageResult;
import com.implatform.media.dto.StickerPackDTO;
import com.implatform.media.entity.StickerPack;

/**
 * 表情包服务接口
 */
public interface StickerPackService {
    
    /**
     * 创建表情包
     */
    StickerPackDTO createStickerPack(StickerPackDTO stickerPackDTO, Long creatorId);
    
    /**
     * 更新表情包
     */
    StickerPackDTO updateStickerPack(Long packId, StickerPackDTO stickerPackDTO, Long userId);
    
    /**
     * 删除表情包
     */
    void deleteStickerPack(Long packId, Long userId);
    
    /**
     * 根据ID获取表情包
     */
    StickerPackDTO getStickerPackById(Long packId, Long userId);
    
    /**
     * 获取公开表情包列表
     */
    PageResult<StickerPackDTO> getPublicStickerPacks(Integer page, Integer size);
    
    /**
     * 根据分类获取表情包
     */
    PageResult<StickerPackDTO> getStickerPacksByCategory(String category, Integer page, Integer size);
    
    /**
     * 根据类型获取表情包
     */
    PageResult<StickerPackDTO> getStickerPacksByType(StickerPack.PackType packType, Integer page, Integer size);
    
    /**
     * 搜索表情包
     */
    PageResult<StickerPackDTO> searchStickerPacks(String keyword, Integer page, Integer size);
    
    /**
     * 获取热门表情包
     */
    PageResult<StickerPackDTO> getPopularStickerPacks(Integer page, Integer size);
    
    /**
     * 获取最新表情包
     */
    PageResult<StickerPackDTO> getLatestStickerPacks(Integer page, Integer size);
    
    /**
     * 获取系统表情包
     */
    List<StickerPackDTO> getSystemStickerPacks();
    
    /**
     * 根据标签获取表情包
     */
    PageResult<StickerPackDTO> getStickerPacksByTag(String tag, Integer page, Integer size);
    
    /**
     * 获取用户创建的表情包
     */
    List<StickerPackDTO> getUserStickerPacks(Long userId);
    
    /**
     * 下载表情包
     */
    void downloadStickerPack(Long packId, Long userId);
    
    /**
     * 收藏表情包
     */
    void favoriteStickerPack(Long packId, Long userId);
    
    /**
     * 取消收藏表情包
     */
    void unfavoriteStickerPack(Long packId, Long userId);
    
    /**
     * 评分表情包
     */
    void rateStickerPack(Long packId, Long userId, Double rating);
    
    /**
     * 增加使用次数
     */
    void incrementUsageCount(Long packId);
} 