package com.implatform;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * IM平台用户、管理服务启动类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
        "com.implatform.user",
        "com.implatform.admin",
        "com.implatform.common"
})
@EnableR2dbcAuditing
@EnableCaching
@EnableAsync
@EnableScheduling
public class UserServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
