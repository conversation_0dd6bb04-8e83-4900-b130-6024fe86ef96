package com.implatform.admin.config;

import com.implatform.admin.security.AdminJwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.List;
import java.util.Properties;

/**
 * 管理后台安全配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final AdminJwtAuthenticationFilter adminJwtAuthenticationFilter;

    // PasswordEncoder bean is provided by common-security module

    /**
     * 邮件发送器配置
     * 提供基本的邮件发送功能，具体配置从Consul获取
     */
    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        // 默认配置，实际配置应该从Consul获取
        mailSender.setHost("smtp.gmail.com");
        mailSender.setPort(587);
        mailSender.setUsername("<EMAIL>");
        mailSender.setPassword("password");

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.debug", "false");

        return mailSender;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF保护
                .csrf(AbstractHttpConfigurer::disable)

                // 会话管理 - 无状态
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )

                // 请求授权配置
                .authorizeHttpRequests(auth -> auth
                        // 公共端点
                        .requestMatchers(
                                "/actuator/**",                 // 健康检查
                                "/api-docs/**",                 // OpenAPI 文档
                                "/swagger-ui/**",
                                "/swagger-ui.html",
                                "/v3/api-docs/**",              // 添加 v3 api-docs
                                "/swagger-resources/**"         // Swagger 资源
                        ).permitAll()

                        // 登录和公共认证端点
                        .requestMatchers(
                                "/api/v1/auth/admin/login",
                                "/api/v1/auth/login",
                                "/api/v1/public/**"             // 可能的公共API
                        ).permitAll()

                        // 其他所有请求需要认证
                        .anyRequest().authenticated()
                )

                // 添加JWT认证过滤器
                .addFilterBefore(adminJwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)

                // CORS配置
                .cors(Customizer.withDefaults());


        return http.build();
    }
}
