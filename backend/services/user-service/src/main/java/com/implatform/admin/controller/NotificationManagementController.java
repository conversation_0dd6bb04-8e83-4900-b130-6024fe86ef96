package com.implatform.admin.controller;

import com.implatform.admin.clients.NotificationServiceWebClient;
import com.implatform.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通知管理Controller
 * 代理通知服务的所有管理接口
 */
@Tag(name = "通知管理", description = "通知服务管理接口")
@RestController
@RequestMapping("/api/v1/admin/notifications")
@RequiredArgsConstructor
public class NotificationManagementController {

    private final NotificationServiceWebClient notificationServiceClient;

    // ==================== 通知管理 ====================
    
    @Operation(summary = "获取通知列表")
    @GetMapping
    public Result<Page<Object>> getNotifications(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getNotifications(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知")
    @GetMapping("/{id}")
    public Result<Object> getNotificationById(@PathVariable Long id) {
        return notificationServiceClient.getNotificationById(id);
    }
    
    @Operation(summary = "创建通知")
    @PostMapping
    public Result<Object> createNotification(@RequestBody Object request) {
        return notificationServiceClient.createNotification(request);
    }
    
    @Operation(summary = "更新通知")
    @PutMapping("/{id}")
    public Result<Object> updateNotification(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateNotification(id, request);
    }
    
    @Operation(summary = "删除通知")
    @DeleteMapping("/{id}")
    public Result<Void> deleteNotification(@PathVariable Long id) {
        return notificationServiceClient.deleteNotification(id);
    }
    
    @Operation(summary = "批量删除通知")
    @DeleteMapping("/batch")
    public Result<Void> deleteNotifications(@RequestBody List<Long> ids) {
        return notificationServiceClient.deleteNotifications(ids);
    }
    
    @Operation(summary = "重试发送通知")
    @PostMapping("/{id}/retry")
    public Result<Void> retryNotification(@PathVariable Long id) {
        return notificationServiceClient.retryNotification(id);
    }

    // ==================== 推送通知管理 ====================
    
    @Operation(summary = "获取推送通知列表")
    @GetMapping("/push")
    public Result<Page<Object>> getPushNotifications(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getPushNotifications(params, pageable);
    }
    
    @Operation(summary = "根据ID获取推送通知")
    @GetMapping("/push/{id}")
    public Result<Object> getPushNotificationById(@PathVariable Long id) {
        return notificationServiceClient.getPushNotificationById(id);
    }
    
    @Operation(summary = "创建推送通知")
    @PostMapping("/push")
    public Result<Object> createPushNotification(@RequestBody Object request) {
        return notificationServiceClient.createPushNotification(request);
    }
    
    @Operation(summary = "删除推送通知")
    @DeleteMapping("/push/{id}")
    public Result<Void> deletePushNotification(@PathVariable Long id) {
        return notificationServiceClient.deletePushNotification(id);
    }

    // ==================== 通知模板管理 ====================
    
    @Operation(summary = "获取通知模板列表")
    @GetMapping("/templates")
    public Result<Page<Object>> getTemplates(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getTemplates(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知模板")
    @GetMapping("/templates/{id}")
    public Result<Object> getTemplateById(@PathVariable Long id) {
        return notificationServiceClient.getTemplateById(id);
    }
    
    @Operation(summary = "创建通知模板")
    @PostMapping("/templates")
    public Result<Object> createTemplate(@RequestBody Object request) {
        return notificationServiceClient.createTemplate(request);
    }
    
    @Operation(summary = "更新通知模板")
    @PutMapping("/templates/{id}")
    public Result<Object> updateTemplate(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateTemplate(id, request);
    }
    
    @Operation(summary = "删除通知模板")
    @DeleteMapping("/templates/{id}")
    public Result<Void> deleteTemplate(@PathVariable Long id) {
        return notificationServiceClient.deleteTemplate(id);
    }
    
    @Operation(summary = "启用/禁用通知模板")
    @PostMapping("/templates/{id}/toggle")
    public Result<Void> toggleTemplate(@PathVariable Long id) {
        return notificationServiceClient.toggleTemplate(id);
    }

    // ==================== 订阅管理 ====================
    
    @Operation(summary = "获取订阅列表")
    @GetMapping("/subscriptions")
    public Result<Page<Object>> getSubscriptions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getSubscriptions(params, pageable);
    }
    
    @Operation(summary = "根据ID获取订阅")
    @GetMapping("/subscriptions/{id}")
    public Result<Object> getSubscriptionById(@PathVariable Long id) {
        return notificationServiceClient.getSubscriptionById(id);
    }
    
    @Operation(summary = "创建订阅")
    @PostMapping("/subscriptions")
    public Result<Object> createSubscription(@RequestBody Object request) {
        return notificationServiceClient.createSubscription(request);
    }
    
    @Operation(summary = "更新订阅")
    @PutMapping("/subscriptions/{id}")
    public Result<Object> updateSubscription(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateSubscription(id, request);
    }
    
    @Operation(summary = "删除订阅")
    @DeleteMapping("/subscriptions/{id}")
    public Result<Void> deleteSubscription(@PathVariable Long id) {
        return notificationServiceClient.deleteSubscription(id);
    }

    // ==================== 设备管理 ====================
    
    @Operation(summary = "获取设备列表")
    @GetMapping("/devices")
    public Result<Page<Object>> getDevices(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getDevices(params, pageable);
    }
    
    @Operation(summary = "根据ID获取设备")
    @GetMapping("/devices/{id}")
    public Result<Object> getDeviceById(@PathVariable Long id) {
        return notificationServiceClient.getDeviceById(id);
    }
    
    @Operation(summary = "删除设备")
    @DeleteMapping("/devices/{id}")
    public Result<Void> deleteDevice(@PathVariable Long id) {
        return notificationServiceClient.deleteDevice(id);
    }
    
    @Operation(summary = "更新设备令牌")
    @PutMapping("/devices/{id}/token")
    public Result<Object> updateDeviceToken(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateDeviceToken(id, request);
    }

    // ==================== 通知统计管理 ====================
    
    @Operation(summary = "获取通知统计列表")
    @GetMapping("/analytics")
    public Result<Page<Object>> getAnalytics(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getAnalytics(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知统计")
    @GetMapping("/analytics/{id}")
    public Result<Object> getAnalyticsById(@PathVariable Long id) {
        return notificationServiceClient.getAnalyticsById(id);
    }

    // ==================== 通知日志管理 ====================
    
    @Operation(summary = "获取通知日志列表")
    @GetMapping("/logs")
    public Result<Page<Object>> getLogs(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getLogs(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知日志")
    @GetMapping("/logs/{id}")
    public Result<Object> getLogById(@PathVariable Long id) {
        return notificationServiceClient.getLogById(id);
    }

    // ==================== 通知规则管理 ====================
    
    @Operation(summary = "获取通知规则列表")
    @GetMapping("/rules")
    public Result<Page<Object>> getRules(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getRules(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知规则")
    @GetMapping("/rules/{id}")
    public Result<Object> getRuleById(@PathVariable Long id) {
        return notificationServiceClient.getRuleById(id);
    }
    
    @Operation(summary = "创建通知规则")
    @PostMapping("/rules")
    public Result<Object> createRule(@RequestBody Object request) {
        return notificationServiceClient.createRule(request);
    }
    
    @Operation(summary = "更新通知规则")
    @PutMapping("/rules/{id}")
    public Result<Object> updateRule(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateRule(id, request);
    }
    
    @Operation(summary = "删除通知规则")
    @DeleteMapping("/rules/{id}")
    public Result<Void> deleteRule(@PathVariable Long id) {
        return notificationServiceClient.deleteRule(id);
    }
    
    @Operation(summary = "启用/禁用通知规则")
    @PostMapping("/rules/{id}/toggle")
    public Result<Void> toggleRule(@PathVariable Long id) {
        return notificationServiceClient.toggleRule(id);
    }

    // ==================== 通知渠道管理 ====================
    
    @Operation(summary = "获取通知渠道列表")
    @GetMapping("/channels")
    public Result<Page<Object>> getChannels(@RequestParam Map<String, Object> params, Pageable pageable) {
        return notificationServiceClient.getChannels(params, pageable);
    }
    
    @Operation(summary = "根据ID获取通知渠道")
    @GetMapping("/channels/{id}")
    public Result<Object> getChannelById(@PathVariable Long id) {
        return notificationServiceClient.getChannelById(id);
    }
    
    @Operation(summary = "创建通知渠道")
    @PostMapping("/channels")
    public Result<Object> createChannel(@RequestBody Object request) {
        return notificationServiceClient.createChannel(request);
    }
    
    @Operation(summary = "更新通知渠道")
    @PutMapping("/channels/{id}")
    public Result<Object> updateChannel(@PathVariable Long id, @RequestBody Object request) {
        return notificationServiceClient.updateChannel(id, request);
    }
    
    @Operation(summary = "删除通知渠道")
    @DeleteMapping("/channels/{id}")
    public Result<Void> deleteChannel(@PathVariable Long id) {
        return notificationServiceClient.deleteChannel(id);
    }
    
    @Operation(summary = "测试通知渠道")
    @PostMapping("/channels/{id}/test")
    public Result<Void> testChannel(@PathVariable Long id) {
        return notificationServiceClient.testChannel(id);
    }
}
