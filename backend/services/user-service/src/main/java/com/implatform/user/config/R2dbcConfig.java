package com.implatform.user.config;

import com.implatform.user.entity.User;
import com.implatform.user.entity.UserDevice;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing;
import org.springframework.data.r2dbc.convert.R2dbcCustomConversions;

import java.util.Arrays;
import java.util.List;

/**
 * R2DBC 配置类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Configuration
@EnableR2dbcAuditing
public class R2dbcConfig {

    @Bean
    public R2dbcCustomConversions r2dbcCustomConversions() {
        List<Converter<?, ?>> converters = Arrays.asList(
            // User 枚举转换器
            new UserStatusWritingConverter(),
            new UserStatusReadingConverter(),
            new OnlineStatusWritingConverter(),
            new OnlineStatusReadingConverter(),
            new UserTypeWritingConverter(),
            new UserTypeReadingConverter(),
            
            // UserDevice 枚举转换器
            new DeviceTypeWritingConverter(),
            new DeviceTypeReadingConverter(),
            new DeviceStatusWritingConverter(),
            new DeviceStatusReadingConverter(),
            new SecurityLevelWritingConverter(),
            new SecurityLevelReadingConverter()
        );
        return new R2dbcCustomConversions(converters);
    }

    // User 枚举转换器
    @WritingConverter
    static class UserStatusWritingConverter implements Converter<User.UserStatus, String> {
        @Override
        public String convert(User.UserStatus source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class UserStatusReadingConverter implements Converter<String, User.UserStatus> {
        @Override
        public User.UserStatus convert(String source) {
            return User.UserStatus.valueOf(source);
        }
    }

    @WritingConverter
    static class OnlineStatusWritingConverter implements Converter<User.OnlineStatus, String> {
        @Override
        public String convert(User.OnlineStatus source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class OnlineStatusReadingConverter implements Converter<String, User.OnlineStatus> {
        @Override
        public User.OnlineStatus convert(String source) {
            return User.OnlineStatus.valueOf(source);
        }
    }

    @WritingConverter
    static class UserTypeWritingConverter implements Converter<User.UserType, String> {
        @Override
        public String convert(User.UserType source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class UserTypeReadingConverter implements Converter<String, User.UserType> {
        @Override
        public User.UserType convert(String source) {
            return User.UserType.valueOf(source);
        }
    }

    // UserDevice 枚举转换器
    @WritingConverter
    static class DeviceTypeWritingConverter implements Converter<UserDevice.DeviceType, String> {
        @Override
        public String convert(UserDevice.DeviceType source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class DeviceTypeReadingConverter implements Converter<String, UserDevice.DeviceType> {
        @Override
        public UserDevice.DeviceType convert(String source) {
            return UserDevice.DeviceType.valueOf(source);
        }
    }

    @WritingConverter
    static class DeviceStatusWritingConverter implements Converter<UserDevice.DeviceStatus, String> {
        @Override
        public String convert(UserDevice.DeviceStatus source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class DeviceStatusReadingConverter implements Converter<String, UserDevice.DeviceStatus> {
        @Override
        public UserDevice.DeviceStatus convert(String source) {
            return UserDevice.DeviceStatus.valueOf(source);
        }
    }

    @WritingConverter
    static class SecurityLevelWritingConverter implements Converter<UserDevice.SecurityLevel, String> {
        @Override
        public String convert(UserDevice.SecurityLevel source) {
            return source.name();
        }
    }

    @ReadingConverter
    static class SecurityLevelReadingConverter implements Converter<String, UserDevice.SecurityLevel> {
        @Override
        public UserDevice.SecurityLevel convert(String source) {
            return UserDevice.SecurityLevel.valueOf(source);
        }
    }
}
