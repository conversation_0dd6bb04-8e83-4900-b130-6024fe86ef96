package com.implatform.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.Instant;
import java.util.regex.Pattern;

/**
 * 用户实体类 - IM平台核心用户管理实体
 *
 * <p><strong>业务用途</strong>：
 * 管理IM平台中所有用户的基本信息、认证信息、个人资料和状态数据。作为整个平台的用户身份载体，
 * 支持用户注册、登录、资料管理、状态跟踪、等级系统等完整的用户生命周期管理。
 *
 * <p><strong>使用场景</strong>：
 * <ul>
 *   <li>用户注册和身份验证（用户名、邮箱、手机号）</li>
 *   <li>用户登录和会话管理</li>
 *   <li>个人资料展示和编辑（昵称、头像、个性签名等）</li>
 *   <li>用户在线状态管理和最后活跃时间跟踪</li>
 *   <li>用户等级系统和经验值管理</li>
 *   <li>隐私设置和权限控制</li>
 *   <li>用户搜索和好友推荐</li>
 * </ul>
 *
 * <p><strong>业务上下文</strong>：
 * 作为IM平台的核心用户实体，User与消息、会话、群组、好友关系等所有业务模块都有关联。
 * 用户的状态和权限直接影响其在平台上的行为和可见性。支持多种认证方式和隐私保护机制。
 *
 * <p><strong>关键关系</strong>：
 * <ul>
 *   <li>一对多关系：User → Message（用户发送的消息）</li>
 *   <li>多对多关系：User ↔ Conversation（用户参与的会话）</li>
 *   <li>多对多关系：User ↔ Group（用户加入的群组）</li>
 *   <li>多对多关系：User ↔ User（好友关系）</li>
 *   <li>一对多关系：User → UserDevice（用户的设备）</li>
 *   <li>一对一关系：User → UserProfile（扩展资料）</li>
 * </ul>
 *
 * <p><strong>实际应用示例</strong>：
 * <pre>
 * // 创建新用户
 * User newUser = new User();
 * newUser.setUsername("john_doe");
 * newUser.setEmail("<EMAIL>");
 * newUser.setNickname("John");
 * newUser.setPassword(encodedPassword);
 *
 * // 更新用户状态
 * user.setOnline();  // 设置在线状态
 * user.addExperience(100);  // 增加经验值
 * user.updateLastLogin("192.168.1.1");  // 更新登录信息
 *
 * // 验证用户权限
 * if (user.canChangeUsername()) {
 *     user.setUsername("new_username");
 * }
 * </pre>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 users 表，包含用户名、邮箱、手机号、状态等关键字段的唯一索引和查询索引，
 * 支持高效的用户查找、认证和状态查询操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Data
@Table("users")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class User {
    
    @Id
    private Long id;

    /**
     * 用户名（唯一）
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Column("username")
    private String username;

    /**
     * 系统账号
     */
    @Column("system_account")
    private String systemAccount;

    /**
     * 密码哈希值
     */
    @JsonIgnore
    @Column("password_hash")
    private String password;

    /**
     * 密码盐值
     */
    @JsonIgnore
    @Column("password_salt")
    private String passwordSalt;

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Column("nickname")
    private String nickname;

    /**
     * 邮箱（唯一）
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @Column("email")
    private String email;

    /**
     * 手机号（不含国家代码）
     */
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    @Column("phone")
    private String phone;

    /**
     * 国家代码（如：+86, +1, +44等）
     */
    @Builder.Default
    @Size(max = 10, message = "国家代码长度不能超过10个字符")
    @Column("country_code")
    private String countryCode = "+86";

    /**
     * 手机号是否已验证
     */
    @Builder.Default
    @Column("is_phone_verified")
    private Boolean isPhoneVerified = false;

    /**
     * 头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    @Column("avatar")
    private String avatar;


    /**
     * 性别：MALE-男，FEMALE-女，OTHER-其他
     */
    @Builder.Default
    @Column("gender")
    private Gender gender = Gender.UNKNOWN;

    /**
     * 生日
     */
    @Column("birth_day")
    private Instant birthday;

    /**
     * 用户简介/Bio
     */
    @Size(max = 500, message = "用户简介长度不能超过500个字符")
    @Column("bio")
    private String bio;

    /**
     * 个性签名
     */
    @Size(max = 200, message = "个性签名长度不能超过200个字符")
    @Column("signature")
    private String signature;

    /**
     * 自定义状态消息
     */
    @Size(max = 200, message = "自定义状态长度不能超过200个字符")
    @Column("custom_status")
    private String customStatus;

    /**
     * 验证徽章
     */
    @Builder.Default
    @Column("verification_badge")
    private Boolean verificationBadge = false;

    /**
     * 用户等级
     */
    @Builder.Default
    @Column("user_level")
    private Integer userLevel = 1;

    /**
     * 经验值
     */
    @Builder.Default
    @Column("experience_points")
    private Long experiencePoints = 0L;

    /**
     * 用户名链接（类似t.me/username）
     */
    @Column("username_link")
    private String usernameLink;

    /**
     * 地区
     */
    @Column("region")
    private String region;

    /**
     * 语言设置
     */
    @Builder.Default
    @Column("language")
    private String language = "zh-CN";

    /**
     * 时区设置
     */
    @Builder.Default
    @Column("timezone")
    private String timezone = "Asia/Shanghai";

    /**
     * 最后在线时间
     */
    @Column("last_seen_at")
    private Instant lastSeenAt;
    
    /**
     * 用户状态
     */
    @Builder.Default
    @Column("status")
    private UserStatus status = UserStatus.ACTIVE;

    /**
     * 在线状态
     */
    @Builder.Default
    @Column("online_status")
    private OnlineStatus onlineStatus = OnlineStatus.OFFLINE;

    /**
     * 用户类型
     */
    @Builder.Default
    @Column("user_type")
    private UserType userType = UserType.NORMAL;

    /**
     * 是否已验证邮箱
     */
    @Builder.Default
    @Column("is_email_verified")
    private Boolean isEmailVerified = false;

    /**
     * 登录尝试次数
     */
    @Builder.Default
    @Column("login_attempts")
    private Integer loginAttempts = 0;

    /**
     * 账号锁定到期时间
     */
    @Column("locked_until")
    private Instant lockedUntil;

    /**
     * 最后修改密码时间
     */
    @Column("last_password_change")
    private Instant lastPasswordChange;

    /**
     * 安全等级：1-低，2-中，3-高
     */
    @Builder.Default
    @Column("security_level")
    private Integer securityLevel = 1;

    /**
     * 是否启用双因子认证
     */
    @Builder.Default
    @Column("two_factor_enabled")
    private Boolean twoFactorEnabled = false;

    /**
     * 双因子认证密钥
     */
    @Column("two_factor_secret")
    private String twoFactorSecret;

    /**
     * 备用邮箱
     */
    @Size(max = 100, message = "备用邮箱长度不能超过100个字符")
    @Column("backup_email")
    private String backupEmail;

    /**
     * 最后登录时间
     */
    @Column("last_login_at")
    private Instant lastLoginAt;

    /**
     * 最后登录IP
     */
    @Column("last_login_ip")
    private String lastLoginIp;

    /**
     * 最后活跃时间
     */
    @Column("last_active_at")
    private Instant lastActiveAt;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        /**
         * 正常
         */
        ACTIVE,
        /**
         * 禁用
         */
        INACTIVE,
        /**
         * 锁定
         */
        LOCKED,
        /**
         * 封禁
         */
        BANNED,
        /**
         * 已删除
         */
        DELETED
    }
    
    /**
     * 在线状态枚举
     */
    public enum OnlineStatus {
        /**
         * 在线
         */
        ONLINE,
        /**
         * 离线
         */
        OFFLINE,
        /**
         * 离开
         */
        AWAY,
        /**
         * 忙碌
         */
        BUSY
    }

    /**
     * 性别枚举
     */
    public enum Gender {
        /**
         * 男性
         */
        MALE,
        /**
         * 女性
         */
        FEMALE,
        /**
         * 未知/其他
         */
        UNKNOWN
    }

    /**
     * 用户类型枚举
     */
    public enum UserType {
        /**
         * 普通用户
         */
        NORMAL,
        /**
         * 系统用户
         */
        SYSTEM,
        /**
         * 管理员用户
         */
        ADMIN
    }

    // =============================================
    // 业务方法
    // =============================================

    /**
     * 用户名格式验证正则表达式
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,50}$");

    /**
     * 邮箱格式验证正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");

    /**
     * 手机号格式验证正则表达式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 检查用户是否激活
     */
    public boolean isActive() {
        return status == UserStatus.ACTIVE;
    }

    /**
     * 检查用户是否被封禁
     */
    public boolean isBanned() {
        return status == UserStatus.BANNED;
    }

    /**
     * 检查用户是否已删除
     */
    public boolean isDeleted() {
        return status == UserStatus.DELETED;
    }

    /**
     * 检查用户是否被锁定
     */
    public boolean isLocked() {
        return status == UserStatus.LOCKED;
    }

    /**
     * 检查用户是否在线
     */
    public boolean isOnline() {
        return onlineStatus == OnlineStatus.ONLINE;
    }

    /**
     * 检查邮箱是否已验证
     */
    public boolean isEmailVerified() {
        return Boolean.TRUE.equals(isEmailVerified);
    }

    /**
     * 检查手机是否已验证
     */
    public boolean isPhoneVerified() {
        return Boolean.TRUE.equals(isPhoneVerified);
    }

    /**
     * 获取邮箱验证状态
     */
    public Boolean getEmailVerified() {
        return isEmailVerified;
    }

    /**
     * 设置邮箱验证状态
     */
    public void setEmailVerified(Boolean emailVerified) {
        this.isEmailVerified = emailVerified;
        updateSecurityLevel();
    }

    /**
     * 获取手机验证状态
     */
    public Boolean getPhoneVerified() {
        return isPhoneVerified;
    }

    /**
     * 设置手机验证状态
     */
    public void setPhoneVerified(Boolean phoneVerified) {
        this.isPhoneVerified = phoneVerified;
        updateSecurityLevel();
    }

    /**
     * 检查是否为验证用户
     */
    public boolean isVerified() {
        return Boolean.TRUE.equals(verificationBadge);
    }

    /**
     * 获取显示名称（优先昵称，其次用户名）
     */
    public String getDisplayName() {
        return nickname != null && !nickname.trim().isEmpty() ? nickname : username;
    }

    /**
     * 验证用户名格式
     */
    public static boolean isValidUsername(String username) {
        return username != null && USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 激活用户
     */
    public void activate() {
        this.status = UserStatus.ACTIVE;
    }

    /**
     * 禁用用户
     */
    public void deactivate() {
        this.status = UserStatus.INACTIVE;
    }

    /**
     * 锁定用户
     */
    public void lock() {
        this.status = UserStatus.LOCKED;
    }

    /**
     * 封禁用户
     */
    public void ban() {
        this.status = UserStatus.BANNED;
    }

    /**
     * 删除用户
     */
    public void delete() {
        this.status = UserStatus.DELETED;
    }

    /**
     * 设置在线状态
     */
    public void setOnline() {
        this.onlineStatus = OnlineStatus.ONLINE;
        this.lastSeenAt = Instant.now();
    }

    /**
     * 设置离线状态
     */
    public void setOffline() {
        this.onlineStatus = OnlineStatus.OFFLINE;
        this.lastSeenAt = Instant.now();
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail() {
        this.isEmailVerified = true;
        updateSecurityLevel();
    }

    /**
     * 验证手机
     */
    public void verifyPhone() {
        this.isPhoneVerified = true;
        updateSecurityLevel();
    }

    /**
     * 添加经验值
     */
    public void addExperience(long points) {
        this.experiencePoints += points;
        // 触发器会自动更新用户等级
    }

    /**
     * 检查是否为系统用户
     */
    public boolean isSystemUser() {
        return userType == UserType.SYSTEM;
    }

    /**
     * 检查是否为管理员用户
     */
    public boolean isAdminUser() {
        return userType == UserType.ADMIN;
    }



    /**
     * 检查是否为普通用户
     */
    public boolean isNormalUser() {
        return userType == UserType.NORMAL;
    }

    /**
     * 更新最后登录信息
     */
    public void updateLastLogin(String ip) {
        this.lastLoginAt = Instant.now();
        this.lastLoginIp = ip;
        this.lastSeenAt = Instant.now();
        this.lastActiveAt = Instant.now();
        this.loginAttempts = 0; // 重置登录尝试次数
    }

    /**
     * 获取最后活跃时间
     */
    public Instant getLastActiveAt() {
        return lastActiveAt;
    }

    /**
     * 设置最后活跃时间
     */
    public void setLastActiveAt(Instant lastActiveAt) {
        this.lastActiveAt = lastActiveAt;
    }

    /**
     * 检查是否可以修改用户名
     */
    public boolean canChangeUsername() {
        // 可以根据业务规则定制，比如VIP用户可以修改
        return isVerified() || userLevel >= 5;
    }

    /**
     * 获取用户等级描述
     */
    public String getLevelDescription() {
        if (userLevel <= 1) return "新手";
        if (userLevel <= 5) return "初级";
        if (userLevel <= 10) return "中级";
        if (userLevel <= 20) return "高级";
        if (userLevel <= 50) return "专家";
        return "大师";
    }

    /**
     * 检查账号是否被锁定
     */
    public boolean isAccountLocked() {
        return lockedUntil != null && Instant.now().isBefore(lockedUntil);
    }

    /**
     * 锁定账号指定时间（分钟）
     */
    public void lockAccount(int minutes) {
        this.lockedUntil = Instant.now().plusSeconds(minutes * 60L);
        this.status = UserStatus.LOCKED;
    }

    /**
     * 解锁账号
     */
    public void unlockAccount() {
        this.lockedUntil = null;
        this.loginAttempts = 0;
        if (this.status == UserStatus.LOCKED) {
            this.status = UserStatus.ACTIVE;
        }
    }

    /**
     * 增加登录尝试次数
     * 注意：这里只增加失败次数，不直接锁定账号
     * 登录限制由SecurityService通过Redis管理，不影响已登录设备
     */
    public void incrementLoginAttempts() {
        this.loginAttempts++;
        // 注释掉自动锁定逻辑，改为由SecurityService管理登录限制
        // if (this.loginAttempts >= 5) {
        //     lockAccount(30);
        // }
    }

    /**
     * 重置登录尝试次数
     */
    public void resetLoginAttempts() {
        this.loginAttempts = 0;
    }

    /**
     * 锁定账号（用于严重安全事件）
     * 这会影响所有设备，包括已登录的设备
     *
     * @param reason 锁定原因
     * @param minutes 锁定时长（分钟）
     */
    public void lockAccountForSecurity(String reason, int minutes) {
        this.lockedUntil = Instant.now().plusSeconds(minutes * 60L);
        this.status = UserStatus.LOCKED;
        this.loginAttempts = 0; // 重置登录尝试次数
        log.warn("账号因安全原因被锁定: userId={}, reason={}, lockedUntil={}",
            this.id, reason, this.lockedUntil);
    }

    /**
     * 更新安全等级
     */
    private void updateSecurityLevel() {
        int level = 1;
        if (isEmailVerified()) level++;
        if (isPhoneVerified()) level++;
        if (Boolean.TRUE.equals(twoFactorEnabled)) level++;
        this.securityLevel = Math.min(level, 3);
    }

    /**
     * 启用双因子认证
     */
    public void enableTwoFactor() {
        this.twoFactorEnabled = true;
        updateSecurityLevel();
    }

    /**
     * 禁用双因子认证
     */
    public void disableTwoFactor() {
        this.twoFactorEnabled = false;
        updateSecurityLevel();
    }

    /**
     * 更新密码
     */
    public void updatePassword(String passwordHash, String salt) {
        this.password = passwordHash;
        this.passwordSalt = salt;
        this.lastPasswordChange = Instant.now();
    }

    /**
     * 检查是否需要更新密码（超过90天）
     */
    public boolean needsPasswordUpdate() {
        return lastPasswordChange == null ||
               lastPasswordChange.isBefore(Instant.now().minusSeconds(90 * 24 * 3600));
    }

    /**
     * 验证密码强度
     */
    public static boolean isStrongPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;:,.<>?".indexOf(ch) >= 0);

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
}