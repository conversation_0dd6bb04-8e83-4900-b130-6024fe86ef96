package com.implatform.admin.controller;


import com.implatform.admin.clients.RealtimeServiceWebClient;
import com.implatform.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 实时服务管理Controller
 * 代理实时服务的所有管理接口
 */
@Tag(name = "实时服务管理", description = "实时服务管理接口")
@RestController
@RequestMapping("/api/v1/admin/realtime")
@RequiredArgsConstructor
public class RealtimeManagementController {

    private final RealtimeServiceWebClient realtimeServiceClient;

    // ==================== 实时连接管理 ====================
    
    @Operation(summary = "获取实时连接列表")
    @GetMapping("/connections")
    public Result<Page<Object>> getConnections(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getConnections(params, pageable);
    }
    
    @Operation(summary = "根据ID获取实时连接")
    @GetMapping("/connections/{id}")
    public Result<Object> getConnectionById(@PathVariable Long id) {
        return realtimeServiceClient.getConnectionById(id);
    }
    
    @Operation(summary = "断开连接")
    @PostMapping("/connections/{id}/disconnect")
    public Result<Void> disconnectConnection(@PathVariable Long id) {
        return realtimeServiceClient.disconnectConnection(id);
    }
    
    @Operation(summary = "批量断开连接")
    @PostMapping("/connections/batch-disconnect")
    public Result<Void> batchDisconnectConnections(@RequestBody List<Long> ids) {
        return realtimeServiceClient.batchDisconnectConnections(ids);
    }

    // ==================== WebRTC通话管理 ====================
    
    @Operation(summary = "获取WebRTC通话列表")
    @GetMapping("/calls")
    public Result<Page<Object>> getCalls(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getCalls(params, pageable);
    }
    
    @Operation(summary = "根据ID获取WebRTC通话")
    @GetMapping("/calls/{id}")
    public Result<Object> getCallById(@PathVariable Long id) {
        return realtimeServiceClient.getCallById(id);
    }
    
    @Operation(summary = "强制结束通话")
    @PostMapping("/calls/{id}/end")
    public Result<Void> endCall(@PathVariable Long id) {
        return realtimeServiceClient.endCall(id);
    }

    // ==================== 设备管理 ====================
    
    @Operation(summary = "获取设备列表")
    @GetMapping("/devices")
    public Result<Page<Object>> getDevices(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getDevices(params, pageable);
    }
    
    @Operation(summary = "根据ID获取设备")
    @GetMapping("/devices/{id}")
    public Result<Object> getDeviceById(@PathVariable Long id) {
        return realtimeServiceClient.getDeviceById(id);
    }
    
    @Operation(summary = "删除设备")
    @DeleteMapping("/devices/{id}")
    public Result<Void> deleteDevice(@PathVariable Long id) {
        return realtimeServiceClient.deleteDevice(id);
    }
    
    @Operation(summary = "强制下线设备")
    @PostMapping("/devices/{id}/offline")
    public Result<Void> offlineDevice(@PathVariable Long id) {
        return realtimeServiceClient.offlineDevice(id);
    }

    // ==================== 黑名单管理 ====================
    
    @Operation(summary = "获取黑名单列表")
    @GetMapping("/blacklist")
    public Result<Page<Object>> getBlacklist(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getBlacklist(params, pageable);
    }
    
    @Operation(summary = "根据ID获取黑名单")
    @GetMapping("/blacklist/{id}")
    public Result<Object> getBlacklistById(@PathVariable Long id) {
        return realtimeServiceClient.getBlacklistById(id);
    }
    
    @Operation(summary = "添加到黑名单")
    @PostMapping("/blacklist")
    public Result<Object> addToBlacklist(@RequestBody Object request) {
        return realtimeServiceClient.addToBlacklist(request);
    }
    
    @Operation(summary = "从黑名单移除")
    @DeleteMapping("/blacklist/{id}")
    public Result<Void> removeFromBlacklist(@PathVariable Long id) {
        return realtimeServiceClient.removeFromBlacklist(id);
    }

    // ==================== 会话管理 ====================
    
    @Operation(summary = "获取会话列表")
    @GetMapping("/sessions")
    public Result<Page<Object>> getSessions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getSessions(params, pageable);
    }
    
    @Operation(summary = "根据ID获取会话")
    @GetMapping("/sessions/{id}")
    public Result<Object> getSessionById(@PathVariable Long id) {
        return realtimeServiceClient.getSessionById(id);
    }
    
    @Operation(summary = "删除会话")
    @DeleteMapping("/sessions/{id}")
    public Result<Void> deleteSession(@PathVariable Long id) {
        return realtimeServiceClient.deleteSession(id);
    }
    
    @Operation(summary = "强制结束会话")
    @PostMapping("/sessions/{id}/end")
    public Result<Void> endSession(@PathVariable Long id) {
        return realtimeServiceClient.endSession(id);
    }

    // ==================== 性能监控管理 ====================
    
    @Operation(summary = "获取性能监控列表")
    @GetMapping("/monitoring")
    public Result<Page<Object>> getMonitoring(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getMonitoring(params, pageable);
    }
    
    @Operation(summary = "根据ID获取性能监控")
    @GetMapping("/monitoring/{id}")
    public Result<Object> getMonitoringById(@PathVariable Long id) {
        return realtimeServiceClient.getMonitoringById(id);
    }

    // ==================== 服务器节点管理 ====================
    
    @Operation(summary = "获取服务器节点列表")
    @GetMapping("/nodes")
    public Result<Page<Object>> getNodes(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getNodes(params, pageable);
    }
    
    @Operation(summary = "根据ID获取服务器节点")
    @GetMapping("/nodes/{id}")
    public Result<Object> getNodeById(@PathVariable Long id) {
        return realtimeServiceClient.getNodeById(id);
    }
    
    @Operation(summary = "创建服务器节点")
    @PostMapping("/nodes")
    public Result<Object> createNode(@RequestBody Object request) {
        return realtimeServiceClient.createNode(request);
    }
    
    @Operation(summary = "更新服务器节点")
    @PutMapping("/nodes/{id}")
    public Result<Object> updateNode(@PathVariable Long id, @RequestBody Object request) {
        return realtimeServiceClient.updateNode(id, request);
    }
    
    @Operation(summary = "删除服务器节点")
    @DeleteMapping("/nodes/{id}")
    public Result<Void> deleteNode(@PathVariable Long id) {
        return realtimeServiceClient.deleteNode(id);
    }
    
    @Operation(summary = "启用/禁用服务器节点")
    @PostMapping("/nodes/{id}/toggle")
    public Result<Void> toggleNode(@PathVariable Long id) {
        return realtimeServiceClient.toggleNode(id);
    }

    // ==================== 负载均衡管理 ====================
    
    @Operation(summary = "获取负载均衡列表")
    @GetMapping("/load-balancer")
    public Result<Page<Object>> getLoadBalancer(@RequestParam Map<String, Object> params, Pageable pageable) {
        return realtimeServiceClient.getLoadBalancer(params, pageable);
    }
    
    @Operation(summary = "根据ID获取负载均衡")
    @GetMapping("/load-balancer/{id}")
    public Result<Object> getLoadBalancerById(@PathVariable Long id) {
        return realtimeServiceClient.getLoadBalancerById(id);
    }
    
    @Operation(summary = "更新负载均衡配置")
    @PutMapping("/load-balancer/{id}")
    public Result<Object> updateLoadBalancer(@PathVariable Long id, @RequestBody Object request) {
        return realtimeServiceClient.updateLoadBalancer(id, request);
    }
}
