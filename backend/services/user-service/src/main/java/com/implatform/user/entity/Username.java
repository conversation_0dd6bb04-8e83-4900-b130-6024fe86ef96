package com.implatform.user.entity;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 用户名实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("usernames")
@EqualsAndHashCode(callSuper = false)
public class Username {

    /**
     * 用户名ID
     */
    @Id
    private Long id;

    /**
     * 用户名（全局唯一）
     */
    @Column("username")
    private String username;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 是否已验证
     */
    @Column("is_verified")
    private Boolean isVerified = false;

    /**
     * 是否为高级用户
     */
    @Column("is_premium")
    private Boolean isPremium = false;

    /**
     * 保留到期时间
     */
    @Column("reserved_until")
    private LocalDateTime reservedUntil;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 用户名格式正则表达式
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]{4,31}$");

    /**
     * 验证用户名格式
     */
    public static boolean isValidFormat(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        // 基本格式检查
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            return false;
        }
        
        // 不能包含连续下划线
        if (username.contains("__")) {
            return false;
        }
        
        // 不能以下划线开头或结尾
        if (username.startsWith("_") || username.endsWith("_")) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查用户名是否激活
     */
    public boolean isUsernameActive() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查用户名是否已验证
     */
    public boolean isUsernameVerified() {
        return Boolean.TRUE.equals(isVerified);
    }

    /**
     * 检查是否为高级用户名
     */
    public boolean isPremiumUsername() {
        return Boolean.TRUE.equals(isPremium);
    }

    /**
     * 检查用户名是否被保留
     */
    public boolean isReserved() {
        return reservedUntil != null && LocalDateTime.now().isBefore(reservedUntil);
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isAvailable() {
        return isUsernameActive() && !isReserved();
    }

    /**
     * 激活用户名
     */
    public void activate() {
        this.isActive = true;
    }

    /**
     * 停用用户名
     */
    public void deactivate() {
        this.isActive = false;
    }

    /**
     * 验证用户名
     */
    public void verify() {
        this.isVerified = true;
    }

    /**
     * 取消验证
     */
    public void unverify() {
        this.isVerified = false;
    }

    /**
     * 设置为高级用户名
     */
    public void setPremium() {
        this.isPremium = true;
    }

    /**
     * 取消高级用户名
     */
    public void removePremium() {
        this.isPremium = false;
    }

    /**
     * 设置保留期
     */
    public void setReservation(LocalDateTime until) {
        this.reservedUntil = until;
    }

    /**
     * 清除保留
     */
    public void clearReservation() {
        this.reservedUntil = null;
    }

    /**
     * 获取用户名状态描述
     */
    public String getStatusDescription() {
        if (!isUsernameActive()) {
            return "已停用";
        } else if (isReserved()) {
            return "已保留";
        } else if (isUsernameVerified()) {
            return "已验证";
        } else if (isPremiumUsername()) {
            return "高级用户";
        } else {
            return "正常";
        }
    }

    /**
     * 获取用户名类型描述
     */
    public String getTypeDescription() {
        StringBuilder type = new StringBuilder();
        
        if (isUsernameVerified()) {
            type.append("已验证");
        }
        
        if (isPremiumUsername()) {
            if (type.length() > 0) {
                type.append(" + ");
            }
            type.append("高级");
        }
        
        if (type.length() == 0) {
            type.append("普通");
        }
        
        return type.toString();
    }

    /**
     * 获取用户名长度
     */
    public int getUsernameLength() {
        return username != null ? username.length() : 0;
    }

    /**
     * 检查用户名是否为短用户名（5个字符以下）
     */
    public boolean isShortUsername() {
        return getUsernameLength() <= 5;
    }

    /**
     * 获取用户名的显示格式
     */
    public String getDisplayUsername() {
        if (username == null) {
            return null;
        }
        
        StringBuilder display = new StringBuilder("@");
        display.append(username);
        
        if (isUsernameVerified()) {
            display.append(" ✓");
        }
        
        return display.toString();
    }

    /**
     * 获取用户名详细信息
     */
    public String getDetailedInfo() {
        StringBuilder info = new StringBuilder();
        info.append("用户名: @").append(username);
        info.append("\n状态: ").append(getStatusDescription());
        info.append("\n类型: ").append(getTypeDescription());
        info.append("\n长度: ").append(getUsernameLength()).append(" 字符");
        info.append("\n创建时间: ").append(createdAt);
        
        if (reservedUntil != null) {
            info.append("\n保留到期: ").append(reservedUntil);
        }
        
        return info.toString();
    }

    /**
     * 获取用户名摘要
     */
    public String getUsernameSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("@").append(username);
        summary.append(" (").append(getStatusDescription()).append(")");
        
        if (isShortUsername()) {
            summary.append(" [短用户名]");
        }
        
        return summary.toString();
    }

    /**
     * 检查用户名是否匹配搜索关键词
     */
    public boolean matchesSearch(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return true;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        String lowerUsername = username.toLowerCase();
        
        // 精确匹配
        if (lowerUsername.equals(lowerKeyword)) {
            return true;
        }
        
        // 前缀匹配
        if (lowerUsername.startsWith(lowerKeyword)) {
            return true;
        }
        
        // 包含匹配
        return lowerUsername.contains(lowerKeyword);
    }

    /**
     * 获取搜索匹配度
     */
    public int getSearchMatchScore(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return 0;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        String lowerUsername = username.toLowerCase();
        
        // 精确匹配得分最高
        if (lowerUsername.equals(lowerKeyword)) {
            return 100;
        }
        
        // 前缀匹配得分较高
        if (lowerUsername.startsWith(lowerKeyword)) {
            return 80;
        }
        
        // 包含匹配得分中等
        if (lowerUsername.contains(lowerKeyword)) {
            return 60;
        }
        
        return 0;
    }
}
