package com.implatform.admin.clients;

import com.implatform.common.core.domain.Result;
import com.implatform.common.webflux.BaseWebClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * user-service服务WebClient客户端
 * 用于admin-service调用user-service的内部管理接口
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class UserServiceClient extends BaseWebClientService {

    private static final String BASE_PATH = "/api/v1/admin/users";

    public UserServiceClient(@Qualifier("internalWebClient") WebClient webClient, DiscoveryClient discoveryClient) {
        super(webClient, discoveryClient, "user-service");
    }

    /**
     * 获取用户列表
     */
    public Mono<Result<Page<Object>>> getUsers(Pageable pageable, Map<String, Object> filters) {
        return post(BASE_PATH + "/list", Map.of("pageable", pageable, "filters", filters), 
                   new ParameterizedTypeReference<Result<Page<Object>>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 获取用户详情
     */
    public Mono<Result<Object>> getUserById(Long userId) {
        return get(BASE_PATH + "/" + userId, new ParameterizedTypeReference<Result<Object>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 创建用户
     */
    public Mono<Result<Object>> createUser(Map<String, Object> userData) {
        return post(BASE_PATH, userData, new ParameterizedTypeReference<Result<Object>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 更新用户
     */
    public Mono<Result<Object>> updateUser(Long userId, Map<String, Object> userData) {
        return put(BASE_PATH + "/" + userId, userData, new ParameterizedTypeReference<Result<Object>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 删除用户
     */
    public Mono<Result<Void>> deleteUser(Long userId) {
        return delete(BASE_PATH + "/" + userId, new ParameterizedTypeReference<Result<Void>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 批量删除用户
     */
    public Mono<Result<Void>> batchDeleteUsers(List<Long> userIds) {
        return post(BASE_PATH + "/batch-delete", userIds, new ParameterizedTypeReference<Result<Void>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 锁定用户
     */
    public Mono<Result<Void>> lockUser(Long userId, String reason) {
        return post(BASE_PATH + "/" + userId + "/lock", Map.of("reason", reason), 
                   new ParameterizedTypeReference<Result<Void>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 解锁用户
     */
    public Mono<Result<Void>> unlockUser(Long userId) {
        return post(BASE_PATH + "/" + userId + "/unlock", null, 
                   new ParameterizedTypeReference<Result<Void>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 重置用户密码
     */
    public Mono<Result<Void>> resetPassword(Long userId, String newPassword) {
        return post(BASE_PATH + "/" + userId + "/reset-password", Map.of("newPassword", newPassword), 
                   new ParameterizedTypeReference<Result<Void>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 获取用户统计信息
     */
    public Mono<Result<Map<String, Object>>> getUserStatistics() {
        return get(BASE_PATH + "/statistics", new ParameterizedTypeReference<Result<Map<String, Object>>>() {}.getType())
                .cast(Result.class);
    }

    /**
     * 搜索用户
     */
    public Mono<Result<List<Object>>> searchUsers(String keyword, int limit) {
        return get(BASE_PATH + "/search?keyword=" + keyword + "&limit=" + limit, 
                  new ParameterizedTypeReference<Result<List<Object>>>() {}.getType())
                .cast(Result.class);
    }
}
