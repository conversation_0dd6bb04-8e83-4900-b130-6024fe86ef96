package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 用户设备实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("user_devices")
@EqualsAndHashCode(callSuper = false)
public class UserDevice {

    /**
     * 设备ID
     */
    @Id
    private Long id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 设备令牌
     */
    @Column("device_token")
    private String deviceToken;

    /**
     * 设备名称
     */
    @Column("device_name")
    private String deviceName;

    /**
     * 设备类型
     */
    @Column("device_type")
    private DeviceType deviceType;

    /**
     * 操作系统
     */
    @Column("operating_system")
    private String operatingSystem;

    /**
     * 操作系统版本
     */
    @Column("os_version")
    private String osVersion;

    /**
     * 应用版本
     */
    @Column("app_version")
    private String appVersion;

    /**
     * 浏览器信息
     */
    @Column("browser_info")
    private String browserInfo;

    /**
     * 设备型号
     */
    @Column("device_model")
    private String deviceModel;

    /**
     * 设备厂商
     */
    @Column("device_manufacturer")
    private String deviceManufacturer;

    /**
     * 屏幕分辨率
     */
    @Column("screen_resolution")
    private String screenResolution;

    /**
     * IP地址
     */
    @Column("ip_address")
    private String ipAddress;

    /**
     * 地理位置
     */
    @Column("location")
    private String location;

    /**
     * 国家
     */
    @Column("country")
    private String country;

    /**
     * 城市
     */
    @Column("city")
    private String city;

    /**
     * 设备状态
     */
    @Column("device_status")
    private DeviceStatus deviceStatus = DeviceStatus.ACTIVE;

    /**
     * 是否为当前设备
     */
    @Column("is_current")
    private Boolean isCurrent = false;

    /**
     * 是否信任设备
     */
    @Column("is_trusted")
    private Boolean isTrusted = false;

    /**
     * 推送令牌
     */
    @Column("push_token")
    private String pushToken;

    /**
     * 是否启用推送
     */
    @Column("push_enabled")
    private Boolean pushEnabled = true;

    /**
     * 登录时间
     */
    @Column("login_time")
    private LocalDateTime loginTime;

    /**
     * 最后活跃时间
     */
    @Column("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 最后IP地址
     */
    @Column("last_ip_address")
    private String lastIpAddress;

    /**
     * 会话过期时间
     */
    @Column("session_expires_at")
    private LocalDateTime sessionExpiresAt;

    /**
     * 安全等级
     */
    @Column("security_level")
    private SecurityLevel securityLevel = SecurityLevel.NORMAL;

    /**
     * 登录次数
     */
    @Column("login_count")
    private Integer loginCount = 0;

    /**
     * 最后登录时间
     */
    @Column("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 注销时间
     */
    @Column("logout_time")
    private LocalDateTime logoutTime;

    /**
     * 注销原因
     */
    @Column("logout_reason")
    private String logoutReason;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;

    // ==================== 枚举定义 ====================

    /**
     * 设备类型枚举
     */
    public enum DeviceType {
        IOS("iOS"), ANDROID("Android"), WEB("Web"),
        WINDOWS("Windows"), MACOS("macOS"), LINUX("Linux"),
        MINI_PROGRAM("小程序"), OTHER("其他");

        private final String description;
        DeviceType(String description) { this.description = description; }
        public String getDescription() { return description; }
    }

    /**
     * 设备状态枚举
     */
    public enum DeviceStatus {
        ACTIVE("活跃"), INACTIVE("非活跃"), BLOCKED("已封禁"), EXPIRED("已过期");

        private final String description;
        DeviceStatus(String description) { this.description = description; }
        public String getDescription() { return description; }
    }

    /**
     * 安全等级枚举
     */
    public enum SecurityLevel {
        LOW("低"), NORMAL("普通"), HIGH("高"), CRITICAL("关键");

        private final String description;
        SecurityLevel(String description) { this.description = description; }
        public String getDescription() { return description; }
    }
}
