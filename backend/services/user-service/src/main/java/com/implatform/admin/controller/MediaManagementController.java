package com.implatform.admin.controller;

import com.implatform.admin.clients.MediaServiceWebClient;
import com.implatform.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 媒体管理Controller
 * 代理媒体服务的所有管理接口
 */
@Tag(name = "媒体管理", description = "媒体服务管理接口")
@RestController
@RequestMapping("/api/v1/admin/media")
@RequiredArgsConstructor
public class MediaManagementController {

    private final MediaServiceWebClient mediaServiceClient;

    // ==================== 文件管理 ====================
    
    @Operation(summary = "获取文件列表")
    @GetMapping("/files")
    public Result<Page<Object>> getFiles(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getFiles(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件")
    @GetMapping("/files/{id}")
    public Result<Object> getFileById(@PathVariable Long id) {
        return mediaServiceClient.getFileById(id);
    }
    
    @Operation(summary = "删除文件")
    @DeleteMapping("/files/{id}")
    public Result<Void> deleteFile(@PathVariable Long id) {
        return mediaServiceClient.deleteFile(id);
    }
    
    @Operation(summary = "批量删除文件")
    @DeleteMapping("/files/batch")
    public Result<Void> deleteFiles(@RequestBody List<Long> ids) {
        return mediaServiceClient.deleteFiles(ids);
    }

    // ==================== 媒体文件管理 ====================
    
    @Operation(summary = "获取媒体文件列表")
    @GetMapping("/media-files")
    public Result<Page<Object>> getMediaFiles(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getMediaFiles(params, pageable);
    }
    
    @Operation(summary = "根据ID获取媒体文件")
    @GetMapping("/media-files/{id}")
    public Result<Object> getMediaFileById(@PathVariable Long id) {
        return mediaServiceClient.getMediaFileById(id);
    }
    
    @Operation(summary = "删除媒体文件")
    @DeleteMapping("/media-files/{id}")
    public Result<Void> deleteMediaFile(@PathVariable Long id) {
        return mediaServiceClient.deleteMediaFile(id);
    }

    // ==================== 文件夹管理 ====================
    
    @Operation(summary = "获取文件夹列表")
    @GetMapping("/folders")
    public Result<Page<Object>> getFolders(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getFolders(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件夹")
    @GetMapping("/folders/{id}")
    public Result<Object> getFolderById(@PathVariable Long id) {
        return mediaServiceClient.getFolderById(id);
    }
    
    @Operation(summary = "创建文件夹")
    @PostMapping("/folders")
    public Result<Object> createFolder(@RequestBody Object request) {
        return mediaServiceClient.createFolder(request);
    }
    
    @Operation(summary = "更新文件夹")
    @PutMapping("/folders/{id}")
    public Result<Object> updateFolder(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateFolder(id, request);
    }
    
    @Operation(summary = "删除文件夹")
    @DeleteMapping("/folders/{id}")
    public Result<Void> deleteFolder(@PathVariable Long id) {
        return mediaServiceClient.deleteFolder(id);
    }

    // ==================== 缩略图管理 ====================
    
    @Operation(summary = "获取缩略图列表")
    @GetMapping("/thumbnails")
    public Result<Page<Object>> getThumbnails(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getThumbnails(params, pageable);
    }
    
    @Operation(summary = "根据ID获取缩略图")
    @GetMapping("/thumbnails/{id}")
    public Result<Object> getThumbnailById(@PathVariable Long id) {
        return mediaServiceClient.getThumbnailById(id);
    }
    
    @Operation(summary = "重新生成缩略图")
    @PostMapping("/thumbnails/{id}/regenerate")
    public Result<Void> regenerateThumbnail(@PathVariable Long id) {
        return mediaServiceClient.regenerateThumbnail(id);
    }
    
    @Operation(summary = "删除缩略图")
    @DeleteMapping("/thumbnails/{id}")
    public Result<Void> deleteThumbnail(@PathVariable Long id) {
        return mediaServiceClient.deleteThumbnail(id);
    }

    // ==================== 元数据管理 ====================
    
    @Operation(summary = "获取元数据列表")
    @GetMapping("/metadata")
    public Result<Page<Object>> getMetadata(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getMetadata(params, pageable);
    }
    
    @Operation(summary = "根据ID获取元数据")
    @GetMapping("/metadata/{id}")
    public Result<Object> getMetadataById(@PathVariable Long id) {
        return mediaServiceClient.getMetadataById(id);
    }
    
    @Operation(summary = "更新元数据")
    @PutMapping("/metadata/{id}")
    public Result<Object> updateMetadata(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateMetadata(id, request);
    }

    // ==================== 文件版本管理 ====================
    
    @Operation(summary = "获取文件版本列表")
    @GetMapping("/versions")
    public Result<Page<Object>> getVersions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getVersions(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件版本")
    @GetMapping("/versions/{id}")
    public Result<Object> getVersionById(@PathVariable Long id) {
        return mediaServiceClient.getVersionById(id);
    }
    
    @Operation(summary = "删除文件版本")
    @DeleteMapping("/versions/{id}")
    public Result<Void> deleteVersion(@PathVariable Long id) {
        return mediaServiceClient.deleteVersion(id);
    }

    // ==================== 文件权限管理 ====================
    
    @Operation(summary = "获取文件权限列表")
    @GetMapping("/permissions")
    public Result<Page<Object>> getPermissions(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getPermissions(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件权限")
    @GetMapping("/permissions/{id}")
    public Result<Object> getPermissionById(@PathVariable Long id) {
        return mediaServiceClient.getPermissionById(id);
    }
    
    @Operation(summary = "创建文件权限")
    @PostMapping("/permissions")
    public Result<Object> createPermission(@RequestBody Object request) {
        return mediaServiceClient.createPermission(request);
    }
    
    @Operation(summary = "更新文件权限")
    @PutMapping("/permissions/{id}")
    public Result<Object> updatePermission(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updatePermission(id, request);
    }
    
    @Operation(summary = "删除文件权限")
    @DeleteMapping("/permissions/{id}")
    public Result<Void> deletePermission(@PathVariable Long id) {
        return mediaServiceClient.deletePermission(id);
    }

    // ==================== 文件分享管理 ====================
    
    @Operation(summary = "获取文件分享列表")
    @GetMapping("/shares")
    public Result<Page<Object>> getShares(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getShares(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件分享")
    @GetMapping("/shares/{id}")
    public Result<Object> getShareById(@PathVariable Long id) {
        return mediaServiceClient.getShareById(id);
    }
    
    @Operation(summary = "取消文件分享")
    @PostMapping("/shares/{id}/cancel")
    public Result<Void> cancelShare(@PathVariable Long id) {
        return mediaServiceClient.cancelShare(id);
    }
    
    @Operation(summary = "删除文件分享")
    @DeleteMapping("/shares/{id}")
    public Result<Void> deleteShare(@PathVariable Long id) {
        return mediaServiceClient.deleteShare(id);
    }

    // ==================== 文件评论管理 ====================
    
    @Operation(summary = "获取文件评论列表")
    @GetMapping("/comments")
    public Result<Page<Object>> getComments(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getComments(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件评论")
    @GetMapping("/comments/{id}")
    public Result<Object> getCommentById(@PathVariable Long id) {
        return mediaServiceClient.getCommentById(id);
    }
    
    @Operation(summary = "删除文件评论")
    @DeleteMapping("/comments/{id}")
    public Result<Void> deleteComment(@PathVariable Long id) {
        return mediaServiceClient.deleteComment(id);
    }

    // ==================== 文件标签管理 ====================
    
    @Operation(summary = "获取文件标签列表")
    @GetMapping("/tags")
    public Result<Page<Object>> getTags(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getTags(params, pageable);
    }
    
    @Operation(summary = "根据ID获取文件标签")
    @GetMapping("/tags/{id}")
    public Result<Object> getTagById(@PathVariable Long id) {
        return mediaServiceClient.getTagById(id);
    }
    
    @Operation(summary = "创建文件标签")
    @PostMapping("/tags")
    public Result<Object> createTag(@RequestBody Object request) {
        return mediaServiceClient.createTag(request);
    }
    
    @Operation(summary = "更新文件标签")
    @PutMapping("/tags/{id}")
    public Result<Object> updateTag(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateTag(id, request);
    }
    
    @Operation(summary = "删除文件标签")
    @DeleteMapping("/tags/{id}")
    public Result<Void> deleteTag(@PathVariable Long id) {
        return mediaServiceClient.deleteTag(id);
    }

    // ==================== 访问日志管理 ====================
    
    @Operation(summary = "获取访问日志列表")
    @GetMapping("/access-logs")
    public Result<Page<Object>> getAccessLogs(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getAccessLogs(params, pageable);
    }
    
    @Operation(summary = "根据ID获取访问日志")
    @GetMapping("/access-logs/{id}")
    public Result<Object> getAccessLogById(@PathVariable Long id) {
        return mediaServiceClient.getAccessLogById(id);
    }

    // ==================== 分析统计管理 ====================
    
    @Operation(summary = "获取分析统计列表")
    @GetMapping("/analytics")
    public Result<Page<Object>> getAnalytics(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getAnalytics(params, pageable);
    }
    
    @Operation(summary = "根据ID获取分析统计")
    @GetMapping("/analytics/{id}")
    public Result<Object> getAnalyticsById(@PathVariable Long id) {
        return mediaServiceClient.getAnalyticsById(id);
    }

    // ==================== 病毒扫描管理 ====================
    
    @Operation(summary = "获取病毒扫描列表")
    @GetMapping("/virus-scan")
    public Result<Page<Object>> getVirusScans(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getVirusScans(params, pageable);
    }
    
    @Operation(summary = "根据ID获取病毒扫描")
    @GetMapping("/virus-scan/{id}")
    public Result<Object> getVirusScanById(@PathVariable Long id) {
        return mediaServiceClient.getVirusScanById(id);
    }
    
    @Operation(summary = "重新扫描文件")
    @PostMapping("/virus-scan/{id}/rescan")
    public Result<Void> rescanFile(@PathVariable Long id) {
        return mediaServiceClient.rescanFile(id);
    }

    // ==================== 表情包管理 ====================
    
    @Operation(summary = "获取表情包列表")
    @GetMapping("/stickers")
    public Result<Page<Object>> getStickers(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getStickers(params, pageable);
    }
    
    @Operation(summary = "根据ID获取表情包")
    @GetMapping("/stickers/{id}")
    public Result<Object> getStickerById(@PathVariable Long id) {
        return mediaServiceClient.getStickerById(id);
    }
    
    @Operation(summary = "创建表情包")
    @PostMapping("/stickers")
    public Result<Object> createSticker(@RequestBody Object request) {
        return mediaServiceClient.createSticker(request);
    }
    
    @Operation(summary = "更新表情包")
    @PutMapping("/stickers/{id}")
    public Result<Object> updateSticker(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateSticker(id, request);
    }
    
    @Operation(summary = "删除表情包")
    @DeleteMapping("/stickers/{id}")
    public Result<Void> deleteSticker(@PathVariable Long id) {
        return mediaServiceClient.deleteSticker(id);
    }

    // ==================== 表情包套装管理 ====================
    
    @Operation(summary = "获取表情包套装列表")
    @GetMapping("/sticker-packs")
    public Result<Page<Object>> getStickerPacks(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getStickerPacks(params, pageable);
    }
    
    @Operation(summary = "根据ID获取表情包套装")
    @GetMapping("/sticker-packs/{id}")
    public Result<Object> getStickerPackById(@PathVariable Long id) {
        return mediaServiceClient.getStickerPackById(id);
    }
    
    @Operation(summary = "创建表情包套装")
    @PostMapping("/sticker-packs")
    public Result<Object> createStickerPack(@RequestBody Object request) {
        return mediaServiceClient.createStickerPack(request);
    }
    
    @Operation(summary = "更新表情包套装")
    @PutMapping("/sticker-packs/{id}")
    public Result<Object> updateStickerPack(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateStickerPack(id, request);
    }
    
    @Operation(summary = "删除表情包套装")
    @DeleteMapping("/sticker-packs/{id}")
    public Result<Void> deleteStickerPack(@PathVariable Long id) {
        return mediaServiceClient.deleteStickerPack(id);
    }

    // ==================== 表情包购买管理 ====================
    
    @Operation(summary = "获取表情包购买列表")
    @GetMapping("/sticker-purchases")
    public Result<Page<Object>> getStickerPurchases(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getStickerPurchases(params, pageable);
    }
    
    @Operation(summary = "根据ID获取表情包购买")
    @GetMapping("/sticker-purchases/{id}")
    public Result<Object> getStickerPurchaseById(@PathVariable Long id) {
        return mediaServiceClient.getStickerPurchaseById(id);
    }

    // ==================== 商店分类管理 ====================
    
    @Operation(summary = "获取商店分类列表")
    @GetMapping("/store-categories")
    public Result<Page<Object>> getStoreCategories(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getStoreCategories(params, pageable);
    }
    
    @Operation(summary = "根据ID获取商店分类")
    @GetMapping("/store-categories/{id}")
    public Result<Object> getStoreCategoryById(@PathVariable Long id) {
        return mediaServiceClient.getStoreCategoryById(id);
    }
    
    @Operation(summary = "创建商店分类")
    @PostMapping("/store-categories")
    public Result<Object> createStoreCategory(@RequestBody Object request) {
        return mediaServiceClient.createStoreCategory(request);
    }
    
    @Operation(summary = "更新商店分类")
    @PutMapping("/store-categories/{id}")
    public Result<Object> updateStoreCategory(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateStoreCategory(id, request);
    }
    
    @Operation(summary = "删除商店分类")
    @DeleteMapping("/store-categories/{id}")
    public Result<Void> deleteStoreCategory(@PathVariable Long id) {
        return mediaServiceClient.deleteStoreCategory(id);
    }

    // ==================== 商店商品管理 ====================
    
    @Operation(summary = "获取商店商品列表")
    @GetMapping("/store-items")
    public Result<Page<Object>> getStoreItems(@RequestParam Map<String, Object> params, Pageable pageable) {
        return mediaServiceClient.getStoreItems(params, pageable);
    }
    
    @Operation(summary = "根据ID获取商店商品")
    @GetMapping("/store-items/{id}")
    public Result<Object> getStoreItemById(@PathVariable Long id) {
        return mediaServiceClient.getStoreItemById(id);
    }
    
    @Operation(summary = "创建商店商品")
    @PostMapping("/store-items")
    public Result<Object> createStoreItem(@RequestBody Object request) {
        return mediaServiceClient.createStoreItem(request);
    }
    
    @Operation(summary = "更新商店商品")
    @PutMapping("/store-items/{id}")
    public Result<Object> updateStoreItem(@PathVariable Long id, @RequestBody Object request) {
        return mediaServiceClient.updateStoreItem(id, request);
    }
    
    @Operation(summary = "删除商店商品")
    @DeleteMapping("/store-items/{id}")
    public Result<Void> deleteStoreItem(@PathVariable Long id) {
        return mediaServiceClient.deleteStoreItem(id);
    }
}
