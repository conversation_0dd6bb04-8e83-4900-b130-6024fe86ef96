# =============================================
# Admin Service Unified Configuration
# Combines bootstrap and application configuration
# =============================================

server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: admin-service

  main:
    web-application-type: servlet

  cloud:
    consul:
      enabled: true
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}

      # Service Discovery Configuration
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}:${server.port}
        health-check-path: /actuator/health
        health-check-interval: 15s
        health-check-timeout: 10s
        prefer-ip-address: false
        health-check-critical-timeout: 120s
        hostname: ${spring.application.name}
        # 查询配置
        query-passing: true
        # 心跳配置
        heartbeat:
          enabled: true
          ttl-value: 30
          interval-ratio: 0.67
        # Configuration Management
      config:
        enabled: true
        format: yaml
        prefixes: config
        default-context: application
        data-key: data
        watch:
          enabled: true
          delay: 1000
        fail-fast: false
  # R2DBC Configuration for reactive database access
  r2dbc:
    url: ${USER_DB_R2DBC_URL:r2dbc:postgresql://localhost:5432/user_db}
    username: ${USER_DB_USERNAME:postgres}
    password: ${USER_DB_PASSWORD:postgres123}
    pool:
      enabled: true
      initial-size: 5
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1