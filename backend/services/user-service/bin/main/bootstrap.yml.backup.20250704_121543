# =============================================
# User Service Unified Configuration
# Combines bootstrap and application configuration
# =============================================

server:
  port: ${SERVER_PORT:8089}

spring:
  application:
    name: user-service

  main:
    web-application-type: servlet

  cloud:
    consul:
      enabled: true
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}

      # Service Discovery Configuration - 增强版本
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}:${server.port}:${random.value}
        health-check-path: /actuator/health
        health-check-interval: 15s
        health-check-timeout: 10s
        health-check-critical-timeout: 120s
        # 关键：使用主机名而不是IP地址
        prefer-ip-address: false
        hostname: ${spring.application.name}
        # 心跳配置
        heartbeat:
          enabled: true
          ttl-value: 30
          interval-ratio: 0.67
        # 故障转移配置
        fail-fast: false
        register: true
        deregister: true

      # Configuration Management
      config:
        enabled: true
        format: yaml
        prefixes: config
        default-context: application
        data-key: data
        watch:
          enabled: true
          delay: 1000
        fail-fast: false
