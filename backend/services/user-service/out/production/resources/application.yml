# =============================================
# user-service Bootstrap Configuration
# Minimal local config - Business logic in Consul
# =============================================

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}


# Cache Configuration
cache:
  admin-permissions:
    ttl: 3600s # 1 hour
    max-size: 1000

  system-metrics:
    ttl: 60s # 1 minute
    max-size: 10000

  user-statistics:
    ttl: 300s # 5 minutes
    max-size: 50000

  audit-logs:
    ttl: 86400s # 24 hours
    max-size: 100000

# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      media-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
      notification-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
      realtime-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
      social-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10

# Scheduled Tasks Configuration
scheduling:
  system-health-check:
    enabled: true
    cron: "0 */5 * * * *" # Every 5 minutes

  metrics-collection:
    enabled: true
    cron: "0 */1 * * * *" # Every minute

  audit-log-cleanup:
    enabled: true
    cron: "0 0 3 * * *" # Daily at 3 AM

  user-statistics:
    enabled: true
    cron: "0 0 0 * * *" # Daily at midnight

# Security Configuration
security:
  admin-access:
    ip-whitelist: ${ADMIN_IP_WHITELIST:}
    require-mfa: true
    session-timeout: 1800s # 30 minutes

  audit-logging:
    enabled: true
    log-all-operations: true
    retention-days: 365

---
# Development Environment Overrides
spring:
  config:
    activate:
      on-profile: dev

# =============================================
# SpringDoc OpenAPI配置 - 仅提供API文档，不启用UI
# =============================================
springdoc:
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.implatform.user.controller, com.implatform.admin.controller

app:
  admin:
    monitoring:
      alerts:
        enabled: false # 开发环境禁用告警
    user-management:
      batch-operations:
        max-batch-size: 100 # 开发环境较小批次

security:
  admin-access:
    require-mfa: false # 开发环境禁用MFA

logging:
  level:
    com.implatform.admin: DEBUG
  # 启用异步日志
  async:
    enabled: true
  # 启用日志事件发布
  events:
    enabled: true
  # 启用性能日志
  performance:
    enabled: true
  audit:
    enabled: true  # 管理服务必须启用审计日志
  # 管理服务特殊配置
  admin-events:
    enabled: true
    log-admin-operations: true
    log-configuration-changes: true

---
# Production Environment Overrides
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    consul:
      host: ${CONSUL_HOST:consul}
      discovery:
        ip-address: ${HOSTNAME:user-service}

app:
  admin:
    monitoring:
      alerts:
        enabled: true # 生产环境启用告警

security:
  admin-access:
    require-mfa: true # 生产环境必须MFA
