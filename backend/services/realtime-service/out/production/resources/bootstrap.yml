# =============================================
# Realtime Service Unified Configuration
# Combines bootstrap and application configuration
# =============================================

server:
  port: ${SERVER_PORT:8087}

spring:
  application:
    name: realtime-service

  # Mail Configuration for Email Services
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-mail-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 30000
          timeout: 30000
          writetimeout: 30000
        debug: false

  main:
    web-application-type: reactive

  cloud:
    consul:
      enabled: true
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}

      # Service Discovery Configuration
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}:${server.port}:${random.value}
        health-check-path: /actuator/health
        health-check-interval: 10s
        health-check-timeout: 5s
        # 关键：使用hostname进行注册，支持内网IP变化时自动恢复
        prefer-ip-address: false
        hostname: ${spring.application.name}
        ip-address: ${spring.cloud.client.ip-address:localhost}
        health-check-critical-timeout: 60s
        # 自动重新注册配置
        register: true
        deregister: true
        # 查询配置
        query-passing: true
        fail-fast: false
      config:
        enabled: true
        format: yaml
        prefixes: config
        default-context: application
        data-key: data
        watch:
          enabled: true
          delay: 1000
        fail-fast: false

  # R2DBC Configuration for reactive database access
  r2dbc:
    url: ${REALTIME_DB_R2DBC_URL:r2dbc:postgresql://localhost:5432/realtime_db}
    username: ${REALTIME_DB_USERNAME:postgres}
    password: ${REALTIME_DB_PASSWORD:postgres123}
    pool:
      enabled: true
      initial-size: 5
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1