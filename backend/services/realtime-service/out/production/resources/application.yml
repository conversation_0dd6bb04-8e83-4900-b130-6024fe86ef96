# =============================================
# realtime-service Bootstrap Configuration
# Minimal local config - Business logic in Consul
# =============================================

app:
  # Payment Gateway Configuration
  payment:
    # 支付宝配置
    alipay:
      enabled: ${ALIPAY_ENABLED:true}
      app-id: ${ALIPAY_APP_ID:}
      private-key: ${ALIPAY_PRIVATE_KEY:}
      public-key: ${ALIPAY_PUBLIC_KEY:}
      gateway-url: ${ALIPAY_GATEWAY_URL:https://openapi.alipay.com/gateway.do}
      notify-url: ${ALIPAY_NOTIFY_URL:}
      return-url: ${ALIPAY_RETURN_URL:}

    # 微信支付配置
    wechat:
      enabled: ${WECHAT_PAY_ENABLED:true}
      app-id: ${WECHAT_APP_ID:}
      mch-id: ${WECHAT_MCH_ID:}
      api-key: ${WECHAT_API_KEY:}
      cert-path: ${WECHAT_CERT_PATH:}
      notify-url: ${WECHAT_NOTIFY_URL:}

    # PayPal配置
    paypal:
      enabled: ${PAYPAL_ENABLED:false}
      client-id: ${PAYPAL_CLIENT_ID:}
      client-secret: ${PAYPAL_CLIENT_SECRET:}
      mode: ${PAYPAL_MODE:sandbox}

    # 交易限制
    limits:
      min-amount: 0.01
      max-amount: 50000.00
      daily-limit: 100000.00
      monthly-limit: 1000000.00

    # 安全配置
    security:
      encryption-key: ${PAYMENT_ENCRYPTION_KEY:}
      signature-algorithm: RSA2
      timeout: 30s
      retry-attempts: 3

    # 手续费配置
    fees:
      alipay-rate: 0.006
      wechat-rate: 0.006
      paypal-rate: 0.029

  # =============================================
  # 红包系统配置
  # =============================================
  red-packet:
    # 红包基本配置
    basic:
      # 红包过期时间（小时）
      expiry-hours: 24
      # 最小红包金额
      min-amount: 0.01
      # 最大红包金额
      max-amount: 200000.00
      # 最大红包个数
      max-count: 100
      # 拼手气红包最大倍数
      lucky-max-ratio: 2.0

    # 红包限制配置
    limits:
      # 单日发送红包数量限制
      daily-send-limit: 100
      # 单日发送红包金额限制
      daily-send-amount-limit: 50000.00
      # 单个红包最大金额
      single-max-amount: 10000.00
      # 群红包最大个数
      group-max-count: 100
      # 私聊红包最大个数
      private-max-count: 1

    # 定时任务配置
    scheduled:
      # 是否启用定时任务
      enabled: true
      # 过期红包处理间隔（毫秒）
      expired-process-interval: 600000
      # 统计更新间隔（毫秒）
      statistics-update-interval: 3600000

    # 风控配置
    risk:
      # 是否启用风控
      enabled: true
      # 单用户单日领取限制
      daily-claim-limit: 1000
      # 异常行为检测
      anomaly-detection: true
      # IP限制
      ip-limit-enabled: true
      # 单IP单日领取限制
      ip-daily-limit: 100

    # 通知配置
    notification:
      # 是否启用红包通知
      enabled: true
      # 红包创建通知
      create-notification: true
      # 红包领取通知
      claim-notification: true
      # 红包过期通知
      expire-notification: true
      timeout: 5000

    security-service:
      url: http://security-service:8090
      timeout: 5000
  # Push通知配置
  push:
    enabled: ${PUSH_NOTIFICATION_ENABLED:true}
    # 移动设备Push配置
    mobile:
      # FCM (Firebase Cloud Messaging) for Android
      fcm:
        server-key: ${FCM_SERVER_KEY:}
        project-id: ${FCM_PROJECT_ID:}
        enabled: ${FCM_ENABLED:false}
        api-url: https://fcm.googleapis.com/fcm/send
        timeout: 10000ms
      # APNs (Apple Push Notification service) for iOS
      apns:
        key-id: ${APNS_KEY_ID:}
        team-id: ${APNS_TEAM_ID:}
        bundle-id: ${APNS_BUNDLE_ID:}
        private-key-path: ${APNS_PRIVATE_KEY_PATH:}
        enabled: ${APNS_ENABLED:false}
        production: ${APNS_PRODUCTION:false}
        timeout: 10000ms
      # JPush (极光推送) for Android & iOS
      jpush:
        app-key: ${JPUSH_APP_KEY:b6142d6ac84333800e640457}
        master-secret: ${JPUSH_MASTER_SECRET:743f849a92c244c42a3599a1}
        enabled: ${JPUSH_ENABLED:true}
        production: ${JPUSH_PRODUCTION:false}
        timeout: 10000
        # 厂商推送通道配置
        vendor-channels:
          xiaomi:
            enabled: ${JPUSH_XIAOMI_ENABLED:true}
          huawei:
            enabled: ${JPUSH_HUAWEI_ENABLED:true}
          oppo:
            enabled: ${JPUSH_OPPO_ENABLED:true}
          vivo:
            enabled: ${JPUSH_VIVO_ENABLED:true}
          meizu:
            enabled: ${JPUSH_MEIZU_ENABLED:true}
        # 推送统计配置
        statistics:
          enabled: ${JPUSH_STATISTICS_ENABLED:true}
          report-interval: ${JPUSH_REPORT_INTERVAL:300000} # 5分钟
    # Web Push配置
    web:
      vapid:
        public-key: ${WEB_PUSH_VAPID_PUBLIC_KEY:}
        private-key: ${WEB_PUSH_VAPID_PRIVATE_KEY:}
        subject: ${WEB_PUSH_VAPID_SUBJECT:mailto:<EMAIL>}
      enabled: ${WEB_PUSH_ENABLED:false}
      timeout: 10000ms
    # 实时消息配置
    realtime:
      enabled: ${REALTIME_PUSH_ENABLED:true}
      websocket-endpoint: /ws/notifications
      heartbeat-interval: 30000ms
      max-connections-per-user: 5
    # 批量处理配置
    batch:
      max-size: ${PUSH_BATCH_MAX_SIZE:1000}
      timeout: ${PUSH_BATCH_TIMEOUT:30000}
      retry-attempts: ${PUSH_BATCH_RETRY_ATTEMPTS:3}
    # 设备管理配置
    device:
      token-expiry-days: ${PUSH_DEVICE_TOKEN_EXPIRY_DAYS:90}
      max-devices-per-user: ${PUSH_MAX_DEVICES_PER_USER:10}
      cleanup-interval: ${PUSH_DEVICE_CLEANUP_INTERVAL:24h}

  # 厂商推送通道配置
  vendor:
    # 小米推送
    xiaomi:
      enabled: ${XIAOMI_PUSH_ENABLED:false}
      app-id: ${XIAOMI_APP_ID:}
      app-key: ${XIAOMI_APP_KEY:}
      app-secret: ${XIAOMI_APP_SECRET:}
      package-name: ${XIAOMI_PACKAGE_NAME:}
      production: ${XIAOMI_PRODUCTION:false}
    # 华为推送
    huawei:
      enabled: ${HUAWEI_PUSH_ENABLED:false}
      app-id: ${HUAWEI_APP_ID:}
      app-secret: ${HUAWEI_APP_SECRET:}
      client-id: ${HUAWEI_CLIENT_ID:}
      client-secret: ${HUAWEI_CLIENT_SECRET:}
      production: ${HUAWEI_PRODUCTION:false}
    # OPPO推送
    oppo:
      enabled: ${OPPO_PUSH_ENABLED:false}
      app-key: ${OPPO_APP_KEY:}
      app-secret: ${OPPO_APP_SECRET:}
      master-secret: ${OPPO_MASTER_SECRET:}
      production: ${OPPO_PRODUCTION:false}
    # vivo推送
    vivo:
      enabled: ${VIVO_PUSH_ENABLED:false}
      app-id: ${VIVO_APP_ID:}
      app-key: ${VIVO_APP_KEY:}
      app-secret: ${VIVO_APP_SECRET:}
      production: ${VIVO_PRODUCTION:false}
    # 魅族推送
    meizu:
      enabled: ${MEIZU_PUSH_ENABLED:false}
      app-id: ${MEIZU_APP_ID:}
      app-key: ${MEIZU_APP_KEY:}
      app-secret: ${MEIZU_APP_SECRET:}
      production: ${MEIZU_PRODUCTION:false}

  # 邮件通知配置
  email:
    enabled: ${EMAIL_NOTIFICATION_ENABLED:true}
    template-path: classpath:templates/email/
    default-from: ${MAIL_USERNAME:}
    default-from-name: ${MAIL_FROM_NAME:IM Platform}

  # 短信配置
  sms:
    enabled: ${SMS_ENABLED:false}
    aliyun:
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
      sign-name: ${SMS_SIGN_NAME:IM平台}
      region: ${SMS_REGION:cn-hangzhou}
      endpoint: dysmsapi.aliyuncs.com

  # 异步处理配置
  async:
    core-pool-size: ${NOTIFICATION_ASYNC_CORE_POOL_SIZE:5}
    max-pool-size: ${NOTIFICATION_ASYNC_MAX_POOL_SIZE:20}
    queue-capacity: ${NOTIFICATION_ASYNC_QUEUE_CAPACITY:100}
    thread-name-prefix: notification-

  # Netty网关集成配置
  netty:
    gateway:
      url: ${NETTY_GATEWAY_URL:http://netty-gateway-service:8080}
      notification-endpoint: /api/v1/gateway/notification
      health-endpoint: /health
      timeout: 5000ms

  # 通知处理配置
  processing:
    batch-size: ${NOTIFICATION_BATCH_SIZE:100}
    retry-max-attempts: ${NOTIFICATION_RETRY_MAX_ATTEMPTS:3}
    retry-delay: ${NOTIFICATION_RETRY_DELAY:5000}
    cleanup-days: ${NOTIFICATION_CLEANUP_DAYS:30}

  # 限流配置
  rate-limit:
    enabled: ${NOTIFICATION_RATE_LIMIT_ENABLED:true}
    per-user-per-minute: ${NOTIFICATION_RATE_LIMIT_PER_USER:60}
    per-user-per-hour: ${NOTIFICATION_RATE_LIMIT_PER_HOUR:1000}
  # Message Queue Configuration
  message-queue:
    enabled: true

    # Redis Pub/Sub
    redis:
      enabled: true
      channels:
        - "user-messages"
        - "group-messages"
        - "system-notifications"
        - "presence-updates"

    # Message Processing
    processing:
      batch-size: 100
      flush-interval: 100ms
      max-retry-attempts: 3
  session:
    timeout:
      default: 7200s # 2 hours for development
    limits:
      max-concurrent-sessions: 10 # 开发环境更宽松
    security:
      secure-cookie: false # 开发环境HTTP
  # Presence Management
  presence:
    enabled: true
    update-interval: 30s
    offline-timeout: 300s # 5 minutes

    # Status Types
    statuses:
      - online
      - away
      - busy
      - invisible
      - offline

  # Connection Pool Configuration
  connection-pool:
    core-size: 10
    max-size: 100
    queue-capacity: 1000
    keep-alive: 60s

# =============================================
# Agora声网音视频通话配置
# 企业级音视频通话解决方案配置
# =============================================
agora:
  # 基础认证配置
  app-id: ${AGORA_APP_ID:********************************}
  app-certificate: ${AGORA_APP_CERTIFICATE:********************************}

  # 令牌管理配置
  token:
    # 令牌有效期（秒）
    expire-time: ${AGORA_TOKEN_EXPIRE_TIME:3600}
    # 权限有效期（秒）
    privilege-expire-time: ${AGORA_PRIVILEGE_EXPIRE_TIME:86400}
    # 是否启用令牌验证
    enabled: ${AGORA_TOKEN_ENABLED:true}

  # RTC实时通信配置
  rtc:
    # 是否启用双流模式
    enable-dual-stream: ${AGORA_RTC_DUAL_STREAM:true}
    # 视频配置文件 (120P, 180P, 240P, 360P, 480P, 720P, 1080P)
    video-profile: ${AGORA_RTC_VIDEO_PROFILE:720P}
    # 音频配置文件 (SPEECH_LOW_QUALITY, SPEECH_STANDARD, MUSIC_STANDARD, MUSIC_HIGH_QUALITY)
    audio-profile: ${AGORA_RTC_AUDIO_PROFILE:MUSIC_STANDARD}
    # 音频场景 (DEFAULT, CHATROOM_ENTERTAINMENT, EDUCATION, GAME_STREAMING)
    audio-scenario: ${AGORA_RTC_AUDIO_SCENARIO:DEFAULT}

  # 录制配置
  recording:
    # 是否启用录制功能
    enabled: ${AGORA_RECORDING_ENABLED:false}
    # 存储厂商 (QINIU, AMAZON_S3, ALIBABA_CLOUD, TENCENT_CLOUD)
    storage-vendor: ${AGORA_RECORDING_STORAGE:QINIU}
    # 录制模式 (INDIVIDUAL, COMPOSITE)
    recording-mode: ${AGORA_RECORDING_MODE:COMPOSITE}
    # 录制文件格式 (MP4, M3U8)
    file-format: ${AGORA_RECORDING_FORMAT:MP4}

  # 区域配置
  region:
    # 服务器区域 (CN, US, EU, AP)
    area: ${AGORA_REGION_AREA:CN}
    # 是否启用区域负载均衡
    enable-load-balancing: ${AGORA_REGION_LOAD_BALANCING:true}

  # REST API配置
  rest-api:
    # API基础URL
    base-url: ${AGORA_REST_API_URL:https://api.agora.io}
    # 连接超时（毫秒）
    connect-timeout: ${AGORA_REST_CONNECT_TIMEOUT:5000}
    # 读取超时（毫秒）
    read-timeout: ${AGORA_REST_READ_TIMEOUT:10000}
    # 最大重试次数
    max-retry: ${AGORA_REST_MAX_RETRY:3}

  # 质量监控配置
  quality:
    # 是否启用质量监控
    enabled: ${AGORA_QUALITY_ENABLED:true}
    # 统计数据收集间隔（秒）
    collect-interval: ${AGORA_QUALITY_INTERVAL:30}
    # 质量报告保留天数
    retention-days: ${AGORA_QUALITY_RETENTION:30}

# Cache Configuration
cache:
  active-connections:
    ttl: 300s # 5 minutes
    max-size: 100000

  user-presence:
    ttl: 60s # 1 minute
    max-size: 500000

  message-queue:
    ttl: 30s # 30 seconds
    max-size: 1000000

  connection-metadata:
    ttl: 3600s # 1 hour
    max-size: 200000

# Monitoring Configuration
monitoring:
  metrics:
    enabled: true
    connection-count: true
    message-throughput: true
    latency: true

  health-check:
    enabled: true
    interval: 30s

# Performance Tuning
performance:
  # Netty Configuration
  netty:
    boss-threads: 2
    worker-threads: 8
    so-backlog: 1024
    so-keepalive: true
    tcp-nodelay: true

    # TCP服务器配置
    tcp:
      enabled: true
      port: 8100
      idle-timeout: 300
      max-frame-size: 65536

    # WebSocket服务器配置
    websocket:
      enabled: true
      port: 8111
      path: "/ws"
      idle-timeout: 300
      max-frame-size: 65536


spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

---
# Development Environment Overrides
spring:
  config:
    activate:
      on-profile: dev



# =============================================
# SpringDoc OpenAPI配置 - 仅提供API文档，不启用UI
# =============================================
springdoc:
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.implatform.realtime.controller

app:
  websocket:
    connection:
      max-connections: 1000 # 开发环境限制连接数
    auth:
      required: true # 生产环境必须认证
    gateway-only: false
    gateway:
      secret: im-platform-gateway # 网关标识密钥
      signature-secret: im-platform-gateway-secret-2024 # 签名密钥（生产环境应该更复杂）
      timestamp-tolerance: 300000 # 时间戳容差（5分钟）

# Netty WebSocket服务器配置
performance:
  netty:
    websocket:
      host: ${WEBSOCKET_HOST:0.0.0.0}  # 修改为监听所有地址，允许外部访问
      port: ${WEBSOCKET_PORT:8111}
      path: /ws
      enabled: true
      idle-timeout: 300
      max-frame-size: 65536

# 开发环境Agora配置
agora:
  token:
    expire-time: 1800 # 开发环境30分钟过期
    enabled: false # 开发环境可禁用令牌验证
  rtc:
    video-profile: 480P # 开发环境使用较低分辨率
  recording:
    enabled: false # 开发环境禁用录制
  quality:
    enabled: false # 开发环境禁用质量监控

logging:
  level:
    com.implatform.realtime: ${LOG_LEVEL_REALTIME_SERVICE:DEBUG}
    io.netty: WARN
  performance:
    slow-threshold: 500
  connection-events:
    enabled: true
    log-connections: true
    log-disconnections: true
    log-heartbeats: true
---
# Production Environment Overrides
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    consul:
      host: ${CONSUL_HOST:consul}
      discovery:
        ip-address: ${HOSTNAME:realtime-service}

app:
  websocket:
    connection:
      max-connections: 100000 # 生产环境支持更多连接
    auth:
      required: true # 生产环境必须认证

# 生产环境Agora配置
agora:
  token:
    expire-time: 7200 # 生产环境2小时过期
    enabled: true # 生产环境必须启用令牌验证
  rtc:
    video-profile: 1080P # 生产环境使用高清分辨率
    enable-dual-stream: true # 生产环境启用双流模式
  recording:
    enabled: true # 生产环境启用录制
    storage-vendor: ALIBABA_CLOUD # 生产环境使用阿里云存储
  quality:
    enabled: true # 生产环境启用质量监控
    collect-interval: 60 # 生产环境1分钟收集一次
  rest-api:
    max-retry: 5 # 生产环境增加重试次数

# ========================================
# 语音转写服务配置
# ========================================
implatform:
  transcription:
    # ========================================
    # 基础配置
    # ========================================
    # 是否启用转写服务
    enabled: false

    # 默认提供商 (aliyun, tencent, baidu, azure)
    default-provider: aliyun

    # 最大音频时长（秒）- 5分钟
    max-audio-duration-seconds: 300

    # 最大音频文件大小（字节）- 50MB
    max-audio-size-bytes: 52428800

    # 请求超时时间
    request-timeout: PT10M

    # ========================================
    # 重试配置
    # ========================================
    retry:
      # 是否启用重试
      enabled: true
      # 最大重试次数
      max-attempts: 3
      # 初始延迟时间
      initial-delay: PT1S
      # 延迟倍数
      multiplier: 2.0
      # 最大延迟时间
      max-delay: PT1M

    # ========================================
    # 配额配置
    # ========================================
    quota:
      # 是否启用配额管理
      enabled: true
      # 默认每日配额（秒）- 1小时
      default-daily-quota-seconds: 3600
      # VIP每日配额（秒）- 2小时
      vip-daily-quota-seconds: 7200
      # 是否允许超额使用
      allow-overage: false
      # 超额费率（每秒费用倍数）
      overage-rate: 0.1

    # ========================================
    # 缓存配置
    # ========================================
    cache:
      # 是否启用结果缓存
      enabled: true
      # 缓存生存时间 - 7天
      ttl: P7D
      # 最大缓存条目数
      max-size: 10000
      # 是否压缩缓存结果
      compress-results: true

    # ========================================
    # 服务提供商配置
    # ========================================
    providers:
      # ========================================
      # 阿里云语音识别配置
      # ========================================
      aliyun:
        # 是否启用阿里云服务
        enabled: true
        # 服务优先级（数字越小优先级越高）
        priority: 1
        # 请求超时时间
        timeout: PT5M
        # 服务端点
        endpoint: https://nls-meta.cn-shanghai.aliyuncs.com
        # 服务区域
        region: cn-shanghai
        # 阿里云应用Key
        app-key: ${ALIYUN_NLS_APP_KEY:test-app-key}
        # 阿里云Access Key ID
        access-key-id: ${ALIYUN_ACCESS_KEY_ID:test-access-key-id}
        # 阿里云Access Key Secret
        access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:test-access-key-secret}
        # 自定义属性
        custom-properties:
          # 识别模型
          model: generic
          # 语言模型
          language-model: mandarin
          # 是否启用标点符号
          enable-punctuation: true
          # 是否启用时间戳
          enable-timestamps: false

      # ========================================
      # 腾讯云语音识别配置
      # ========================================
      tencent:
        # 是否启用腾讯云服务
        enabled: false
        # 服务优先级
        priority: 2
        # 请求超时时间
        timeout: PT5M
        # 服务端点
        endpoint: asr.tencentcloudapi.com
        # 服务区域
        region: ap-beijing
        # 腾讯云Secret ID
        secret-id: ${TENCENT_SECRET_ID:}
        # 腾讯云Secret Key
        secret-key: ${TENCENT_SECRET_KEY:}
        # 自定义属性
        custom-properties:
          # 引擎模型类型
          engine-model-type: 16k_zh
          # 音频格式
          voice-format: 1
          # 热词ID
          hotword-id: ""
          # 自学习模型ID
          customization-id: ""

      # ========================================
      # 百度AI语音识别配置
      # ========================================
      baidu:
        # 是否启用百度AI服务
        enabled: false
        # 服务优先级
        priority: 3
        # 请求超时时间
        timeout: PT5M
        # 服务端点
        endpoint: https://vop.baidu.com/server_api
        # 百度AI API Key
        api-key: ${BAIDU_API_KEY:}
        # 百度AI Secret Key
        secret-key: ${BAIDU_SECRET_KEY:}
        # 自定义属性
        custom-properties:
          # 产品ID（1537=普通话搜索模型）
          dev-pid: 1537
          # 用户唯一标识
          cuid: im-platform
          # 采样率
          rate: 16000
          # 音频格式
          format: wav

      # ========================================
      # Azure Speech语音识别配置
      # ========================================
      azure:
        # 是否启用Azure Speech服务
        enabled: false
        # 服务优先级
        priority: 4
        # 请求超时时间
        timeout: PT5M
        # 服务端点
        endpoint: https://eastus.api.cognitive.microsoft.com/sts/v1.0/issuetoken
        # 服务区域
        region: eastus
        # Azure订阅密钥
        subscription-key: ${AZURE_SPEECH_KEY:}
        # 自定义属性
        custom-properties:
          # 识别语言
          language: zh-CN
          # 输出格式
          format: detailed
          # 是否启用分段
          enable-segmentation: true
          # 是否启用词级时间戳
          enable-word-level-timestamps: false