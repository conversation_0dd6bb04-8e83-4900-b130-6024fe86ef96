dependencies {
    // Common Modules (排除JPA依赖，使用R2DBC)
    implementation project(':common:common-core')
    implementation project(':common:common-webflux')
    implementation project(':common:common-messaging')
    implementation project(':common:common-api')
    implementation project(':common:common-security')
    implementation project(':common:common-protobuf')
    implementation(project(':common:common-data')) {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-jpa'
        exclude group: 'com.zaxxer', module: 'HikariCP'
    }

    // Service Discovery
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

    // Load Balancer
    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'

    // Netty for TCP connections (WebFlux已包含Netty，但我们需要完整的Netty功能)
    implementation 'io.netty:netty-all'

    // Spring Retry for transcription service
    implementation 'org.springframework.retry:spring-retry'
    implementation 'org.springframework:spring-aspects'

    // Spring Boot Actuator for health checks and monitoring
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // Validation
    implementation 'org.springframework.boot:spring-boot-starter-validation'
}