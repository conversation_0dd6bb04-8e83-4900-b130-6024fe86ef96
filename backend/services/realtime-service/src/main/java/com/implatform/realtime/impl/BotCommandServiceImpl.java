package com.implatform.realtime.impl;

import com.implatform.realtime.entity.BotCommand;
import com.implatform.realtime.repository.BotCommandRepository;
import com.implatform.realtime.service.BotCommandService;
import com.implatform.common.core.enums.BotErrorCode;
import com.implatform.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机器人命令服务实现类
 * 提供机器人命令管理和执行功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BotCommandServiceImpl implements BotCommandService {
    
    private final BotCommandRepository commandRepository;
    
    // 命令配置
    private static final int MAX_COMMANDS_PER_BOT = 50;
    private static final int MAX_COMMAND_LENGTH = 32;
    private static final long HIGH_USAGE_THRESHOLD = 1000L;
    
    /**
     * 创建机器人命令
     */
    @Override
    @Transactional
    @CacheEvict(value = {"bot-commands", "command-stats"}, allEntries = true)
    public BotCommand createCommand(BotCommand command) {
        log.debug("Creating command: {} for bot: {}", command.getCommand(), command.getBotId());
        
        // 验证命令数据
        validateCommand(command);
        
        // 检查机器人命令数量限制
        long botCommandCount = commandRepository.countByBotIdAndIsActiveTrue(command.getBotId());
        if (botCommandCount >= MAX_COMMANDS_PER_BOT) {
            throw new IllegalStateException("Bot has reached maximum command limit: " + MAX_COMMANDS_PER_BOT);
        }
        
        // 检查命令是否已存在
        if (commandRepository.existsByBotIdAndCommandAndIsActiveTrue(command.getBotId(), command.getCommand())) {
            throw new BusinessException(BotErrorCode.COMMAND_ALREADY_EXISTS);
        }
        
        // 设置默认值
        if (command.getIsActive() == null) {
            command.setIsActive(true);
        }
        if (command.getUsageCount() == null) {
            command.setUsageCount(0L);
        }
        if (command.getIsAdminOnly() == null) {
            command.setIsAdminOnly(false);
        }
        if (command.getIsGroupOnly() == null) {
            command.setIsGroupOnly(false);
        }
        if (command.getIsPrivateOnly() == null) {
            command.setIsPrivateOnly(false);
        }
        
        BotCommand savedCommand = commandRepository.save(command);
        log.debug("Created command with ID: {} for bot: {}", savedCommand.getId(), command.getBotId());
        
        return savedCommand;
    }
    
    /**
     * 批量创建命令
     */
    @Override
    @Transactional
    @CacheEvict(value = {"bot-commands", "command-stats"}, allEntries = true)
    public List<BotCommand> createCommands(List<BotCommand> commands) {
        log.info("Creating {} bot commands", commands.size());
        
        List<BotCommand> validCommands = commands.stream()
            .filter(this::validateCommandSilently)
            .collect(Collectors.toList());
        
        return commandRepository.saveAll(validCommands);
    }
    
    /**
     * 更新命令
     */
    @Override
    @Transactional
    @CacheEvict(value = {"bot-commands", "command-details"}, allEntries = true)
    public BotCommand updateCommand(String commandId, BotCommand commandUpdate) {
        BotCommand existingCommand = commandRepository.findById(commandId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.COMMAND_NOT_FOUND));
        
        // 更新允许的字段
        if (StringUtils.hasText(commandUpdate.getDescription())) {
            existingCommand.setDescription(commandUpdate.getDescription());
        }
        if (StringUtils.hasText(commandUpdate.getResponseText())) {
            existingCommand.setResponseText(commandUpdate.getResponseText());
        }
        if (commandUpdate.getIsAdminOnly() != null) {
            existingCommand.setIsAdminOnly(commandUpdate.getIsAdminOnly());
        }
        if (commandUpdate.getIsGroupOnly() != null) {
            existingCommand.setIsGroupOnly(commandUpdate.getIsGroupOnly());
        }
        if (commandUpdate.getIsPrivateOnly() != null) {
            existingCommand.setIsPrivateOnly(commandUpdate.getIsPrivateOnly());
        }
        if (commandUpdate.getParameters() != null) {
            existingCommand.setParameters(commandUpdate.getParameters());
        }
        
        BotCommand updatedCommand = commandRepository.save(existingCommand);
        log.debug("Updated command: {}", commandId);
        
        return updatedCommand;
    }
    
    /**
     * 获取机器人的所有命令
     */
    @Override
    @Cacheable(value = "bot-commands", key = "#botId")
    public List<BotCommand> getBotCommands(Long botId) {
        return commandRepository.findByBotIdAndIsActiveTrueOrderByCommandAsc(botId);
    }
    
    /**
     * 获取机器人的公开命令
     */
    @Override
    @Cacheable(value = "bot-public-commands", key = "#botId")
    public List<BotCommand> getBotPublicCommands(Long botId) {
        return commandRepository.findPublicCommands(botId, true);
    }
    
    /**
     * 根据命令名获取命令
     */
    @Override
    @Cacheable(value = "command-by-name", key = "#botId + '_' + #commandName")
    public Optional<BotCommand> getCommandByName(Long botId, String commandName) {
        return commandRepository.findByBotIdAndCommandAndIsActiveTrue(botId, commandName);
    }
    
    /**
     * 搜索命令
     */
    @Override
    @Cacheable(value = "command-search", key = "#botId + '_' + #keyword")
    public List<BotCommand> searchCommands(Long botId, String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return getBotCommands(botId);
        }
        return commandRepository.searchCommands(botId, keyword);
    }
    
    /**
     * 获取管理员命令
     */
    @Override
    @Cacheable(value = "admin-commands", key = "#botId")
    public List<BotCommand> getAdminCommands(Long botId) {
        return commandRepository.findAdminCommands(botId, true, true);
    }
    
    /**
     * 获取群组命令
     */
    @Override
    @Cacheable(value = "group-commands", key = "#botId")
    public List<BotCommand> getGroupCommands(Long botId) {
        return commandRepository.findGroupCommands(botId, true, true);
    }
    
    /**
     * 获取私聊命令
     */
    @Override
    @Cacheable(value = "private-commands", key = "#botId")
    public List<BotCommand> getPrivateCommands(Long botId) {
        return commandRepository.findPrivateCommands(botId, true, true);
    }
    
    /**
     * 获取热门命令
     */
    @Override
    @Cacheable(value = "popular-commands", key = "#botId")
    public List<BotCommand> getPopularCommands(Long botId) {
        return commandRepository.findPopularCommands(botId, HIGH_USAGE_THRESHOLD, true);
    }

    /**
     * 验证命令数据
     */
    private void validateCommand(BotCommand command) {
        if (command.getBotId() == null) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_ID);
        }
        if (!StringUtils.hasText(command.getCommand())) {
            throw new BusinessException(BotErrorCode.INVALID_COMMAND_FORMAT);
        }
        if (command.getCommand().length() > MAX_COMMAND_LENGTH) {
            throw new BusinessException(BotErrorCode.INVALID_COMMAND_FORMAT);
        }
        if (!command.getCommand().startsWith("/")) {
            throw new BusinessException(BotErrorCode.INVALID_COMMAND_FORMAT);
        }
        if (!command.getCommand().matches("^/[a-zA-Z0-9_]+$")) {
            throw new BusinessException(BotErrorCode.INVALID_COMMAND_FORMAT);
        }
    }
    
    /**
     * 静默验证命令
     */
    private boolean validateCommandSilently(BotCommand command) {
        try {
            validateCommand(command);
            return true;
        } catch (Exception e) {
            log.warn("Invalid command: {}", e.getMessage());
            return false;
        }
    }
}
