package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.GroupErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.entity.GroupSettings;
import com.implatform.realtime.repository.GroupSettingsRepository;
import com.implatform.service.GroupSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群组设置服务实现
 * 提供群组配置管理、验证和优化建议功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(isolation = Isolation.READ_COMMITTED)
public class GroupSettingsServiceImpl implements GroupSettingsService {

    private final GroupSettingsRepository groupSettingsRepository;

    @Override
    public GroupSettings createDefaultSettings(@NotNull Long groupId) {
        log.info("Creating default settings for group {}", groupId);
        
        if (groupSettingsRepository.existsByGroupId(groupId)) {
            throw new IllegalStateException(String.format("[GRP-SET-001] Settings already exist for group %d", groupId));
        }
        
        GroupSettings settings = GroupSettings.createDefault(groupId);
        GroupSettings savedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully created default settings for group {}", groupId);
        return savedSettings;
    }

    @Override
    public Optional<GroupSettings> getGroupSettings(@NotNull Long groupId) {
        return groupSettingsRepository.findByGroupId(groupId);
    }

    @Override
    public GroupSettings updateBasicSettings(@NotNull Long groupId, Integer memberLimit, 
                                           Boolean joinApprovalRequired, Boolean allowMemberInvite, 
                                           @NotNull Long modifierId) {
        log.info("Updating basic settings for group {}: memberLimit={}, joinApproval={}, allowInvite={}", 
                groupId, memberLimit, joinApprovalRequired, allowMemberInvite);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (memberLimit != null) {
            validateMemberLimit(memberLimit);
            settings.setMemberLimit(memberLimit);
        }
        
        if (joinApprovalRequired != null) {
            settings.setJoinApprovalRequired(joinApprovalRequired);
        }
        
        if (allowMemberInvite != null) {
            settings.setAllowMemberInvite(allowMemberInvite);
        }
        
        settings.updateSettings(modifierId, "Basic settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated basic settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateMessageSettings(@NotNull Long groupId, Boolean messageModerationEnabled,
                                             GroupSettings.ModerationMode messageModerationMode,
                                             Boolean messageRecallEnabled, Integer messageRecallTimeLimit,
                                             @NotNull Long modifierId) {
        log.info("Updating message settings for group {}: moderation={}, mode={}, recall={}, timeLimit={}", 
                groupId, messageModerationEnabled, messageModerationMode, messageRecallEnabled, messageRecallTimeLimit);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (messageModerationEnabled != null) {
            settings.setMessageModerationEnabled(messageModerationEnabled);
        }
        
        if (messageModerationMode != null) {
            settings.setMessageModerationMode(messageModerationMode);
        }
        
        if (messageRecallEnabled != null) {
            settings.setMessageRecallEnabled(messageRecallEnabled);
        }
        
        if (messageRecallTimeLimit != null) {
            validateMessageRecallTimeLimit(messageRecallTimeLimit);
            settings.setMessageRecallTimeLimit(messageRecallTimeLimit);
        }
        
        settings.updateSettings(modifierId, "Message settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated message settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateFileSharingSettings(@NotNull Long groupId, Boolean fileSharingEnabled,
                                                 Integer fileSizeLimitMb, String allowedFileTypes,
                                                 @NotNull Long modifierId) {
        log.info("Updating file sharing settings for group {}: enabled={}, sizeLimit={}, types={}", 
                groupId, fileSharingEnabled, fileSizeLimitMb, allowedFileTypes);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (fileSharingEnabled != null) {
            settings.setFileSharingEnabled(fileSharingEnabled);
        }
        
        if (fileSizeLimitMb != null) {
            validateFileSizeLimit(fileSizeLimitMb);
            settings.setFileSizeLimitMb(fileSizeLimitMb);
        }
        
        if (StringUtils.hasText(allowedFileTypes)) {
            settings.setAllowedFileTypes(allowedFileTypes);
        }
        
        settings.updateSettings(modifierId, "File sharing settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated file sharing settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updatePermissionSettings(@NotNull Long groupId, Boolean mentionAllEnabled,
                                                 Integer mentionAllPermissionLevel, @NotNull Long modifierId) {
        log.info("Updating permission settings for group {}: mentionAll={}, level={}", 
                groupId, mentionAllEnabled, mentionAllPermissionLevel);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (mentionAllEnabled != null) {
            settings.setMentionAllEnabled(mentionAllEnabled);
        }
        
        if (mentionAllPermissionLevel != null) {
            validatePermissionLevel(mentionAllPermissionLevel);
            settings.setMentionAllPermissionLevel(mentionAllPermissionLevel);
        }
        
        settings.updateSettings(modifierId, "Permission settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated permission settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateBotSettings(@NotNull Long groupId, Boolean botEnabled, String botConfiguration,
                                         @NotNull Long modifierId) {
        log.info("Updating bot settings for group {}: enabled={}", groupId, botEnabled);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (botEnabled != null) {
            settings.setBotEnabled(botEnabled);
        }
        
        if (StringUtils.hasText(botConfiguration)) {
            settings.setBotConfiguration(botConfiguration);
        }
        
        settings.updateSettings(modifierId, "Bot settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated bot settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateNotificationSettings(@NotNull Long groupId, 
                                                   GroupSettings.NotificationSetting notificationSetting,
                                                   @NotNull Long modifierId) {
        log.info("Updating notification settings for group {}: setting={}", groupId, notificationSetting);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (notificationSetting != null) {
            settings.setNotificationSetting(notificationSetting);
        }
        
        settings.updateSettings(modifierId, "Notification settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated notification settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateEncryptionSettings(@NotNull Long groupId, Boolean messageEncryptionEnabled,
                                                 String encryptionAlgorithm, @NotNull Long modifierId) {
        log.info("Updating encryption settings for group {}: enabled={}, algorithm={}", 
                groupId, messageEncryptionEnabled, encryptionAlgorithm);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (messageEncryptionEnabled != null) {
            settings.setMessageEncryptionEnabled(messageEncryptionEnabled);
        }
        
        if (StringUtils.hasText(encryptionAlgorithm)) {
            settings.setEncryptionAlgorithm(encryptionAlgorithm);
        }
        
        settings.updateSettings(modifierId, "Encryption settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated encryption settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateHistorySettings(@NotNull Long groupId, Boolean messageHistoryEnabled,
                                              Integer messageHistoryRetentionDays, @NotNull Long modifierId) {
        log.info("Updating history settings for group {}: enabled={}, retention={}", 
                groupId, messageHistoryEnabled, messageHistoryRetentionDays);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (messageHistoryEnabled != null) {
            settings.setMessageHistoryEnabled(messageHistoryEnabled);
        }
        
        if (messageHistoryRetentionDays != null) {
            validateHistoryRetentionDays(messageHistoryRetentionDays);
            settings.setMessageHistoryRetentionDays(messageHistoryRetentionDays);
        }
        
        settings.updateSettings(modifierId, "History settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated history settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateSensitiveWordSettings(@NotNull Long groupId, Boolean sensitiveWordFilterEnabled,
                                                    GroupSettings.SensitiveWordAction sensitiveWordAction,
                                                    String customSensitiveWords, @NotNull Long modifierId) {
        log.info("Updating sensitive word settings for group {}: enabled={}, action={}", 
                groupId, sensitiveWordFilterEnabled, sensitiveWordAction);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (sensitiveWordFilterEnabled != null) {
            settings.setSensitiveWordFilterEnabled(sensitiveWordFilterEnabled);
        }
        
        if (sensitiveWordAction != null) {
            settings.setSensitiveWordAction(sensitiveWordAction);
        }
        
        if (StringUtils.hasText(customSensitiveWords)) {
            settings.setCustomSensitiveWords(customSensitiveWords);
        }
        
        settings.updateSettings(modifierId, "Sensitive word settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated sensitive word settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateAntiSpamSettings(@NotNull Long groupId, Boolean antiSpamEnabled,
                                               Integer antiSpamTimeWindow, Integer antiSpamMessageLimit,
                                               @NotNull Long modifierId) {
        log.info("Updating anti-spam settings for group {}: enabled={}, window={}, limit={}", 
                groupId, antiSpamEnabled, antiSpamTimeWindow, antiSpamMessageLimit);
        
        GroupSettings settings = getOrCreateSettings(groupId);
        
        if (antiSpamEnabled != null) {
            settings.setAntiSpamEnabled(antiSpamEnabled);
        }
        
        if (antiSpamTimeWindow != null) {
            validateAntiSpamTimeWindow(antiSpamTimeWindow);
            settings.setAntiSpamTimeWindow(antiSpamTimeWindow);
        }
        
        if (antiSpamMessageLimit != null) {
            validateAntiSpamMessageLimit(antiSpamMessageLimit);
            settings.setAntiSpamMessageLimit(antiSpamMessageLimit);
        }
        
        settings.updateSettings(modifierId, "Anti-spam settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);
        
        log.info("Successfully updated anti-spam settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateVisibilitySettings(@NotNull Long groupId,
                                                 GroupSettings.GroupVisibility groupVisibility,
                                                 Boolean searchable, @NotNull Long modifierId) {
        log.info("Updating visibility settings for group {}: visibility={}, searchable={}",
                groupId, groupVisibility, searchable);

        GroupSettings settings = getOrCreateSettings(groupId);

        if (groupVisibility != null) {
            settings.setGroupVisibility(groupVisibility);
        }

        if (searchable != null) {
            settings.setSearchable(searchable);
        }

        settings.updateSettings(modifierId, "Visibility settings updated");
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);

        log.info("Successfully updated visibility settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateGroupTags(@NotNull Long groupId, String groupTags, @NotNull Long modifierId) {
        log.info("Updating group tags for group {}", groupId);

        GroupSettings settings = getOrCreateSettings(groupId);
        settings.setGroupTags(groupTags);
        settings.updateSettings(modifierId, "Group tags updated");

        GroupSettings updatedSettings = groupSettingsRepository.save(settings);

        log.info("Successfully updated group tags for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings updateCustomSettings(@NotNull Long groupId, String customSettings, @NotNull Long modifierId) {
        log.info("Updating custom settings for group {}", groupId);

        GroupSettings settings = getOrCreateSettings(groupId);
        settings.setCustomSettings(customSettings);
        settings.updateSettings(modifierId, "Custom settings updated");

        GroupSettings updatedSettings = groupSettingsRepository.save(settings);

        log.info("Successfully updated custom settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public boolean isFileTypeAllowed(@NotNull Long groupId, @NotNull String fileType) {
        Optional<GroupSettings> settings = groupSettingsRepository.findByGroupId(groupId);
        return settings.map(s -> s.isFileTypeAllowed(fileType)).orElse(false);
    }

    @Override
    public boolean isFileSizeValid(@NotNull Long groupId, long fileSizeBytes) {
        Optional<GroupSettings> settings = groupSettingsRepository.findByGroupId(groupId);
        return settings.map(s -> s.isFileSizeValid(fileSizeBytes)).orElse(false);
    }

    @Override
    public boolean isModerationEnabled(@NotNull Long groupId) {
        Optional<GroupSettings> settings = groupSettingsRepository.findByGroupId(groupId);
        return settings.map(GroupSettings::isModerationEnabled).orElse(false);
    }

    @Override
    public SettingsValidationResult validateSettings(@NotNull Long groupId) {
        log.info("Validating settings for group {}", groupId);

        Optional<GroupSettings> settingsOpt = groupSettingsRepository.findByGroupId(groupId);
        if (settingsOpt.isEmpty()) {
            return new SettingsValidationResult(false,
                    List.of("Group settings not found"),
                    List.of("Create default settings for the group"));
        }

        GroupSettings settings = settingsOpt.get();
        List<String> issues = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 验证配置一致性
        if (settings.getMessageModerationEnabled() &&
            settings.getMessageModerationMode() == GroupSettings.ModerationMode.NONE) {
            issues.add("Message moderation is enabled but mode is set to NONE");
        }

        if (settings.getMessageEncryptionEnabled() &&
            !StringUtils.hasText(settings.getEncryptionAlgorithm())) {
            issues.add("Message encryption is enabled but no algorithm specified");
        }

        if (settings.getBotEnabled() &&
            !StringUtils.hasText(settings.getBotConfiguration())) {
            warnings.add("Bot is enabled but no configuration provided");
        }

        // 验证安全设置
        if (settings.getGroupVisibility() == GroupSettings.GroupVisibility.PUBLIC &&
            !settings.getJoinApprovalRequired() &&
            settings.getMemberLimit() > 500) {
            warnings.add("Large public group without join approval may have security risks");
        }

        if (!settings.getSensitiveWordFilterEnabled() &&
            !settings.getMessageModerationEnabled()) {
            warnings.add("No content filtering enabled, consider enabling sensitive word filter or moderation");
        }

        boolean isValid = issues.isEmpty();

        log.info("Settings validation for group {} completed: valid={}, issues={}, warnings={}",
                groupId, isValid, issues.size(), warnings.size());

        return new SettingsValidationResult(isValid, issues, warnings);
    }

    @Override
    public List<SettingsOptimizationSuggestion> getOptimizationSuggestions(@NotNull Long groupId) {
        log.info("Getting optimization suggestions for group {}", groupId);

        Optional<GroupSettings> settingsOpt = groupSettingsRepository.findByGroupId(groupId);
        if (settingsOpt.isEmpty()) {
            return List.of(new SettingsOptimizationSuggestion(
                    "Setup", "Create group settings", "Group has no settings configured", "HIGH"));
        }

        GroupSettings settings = settingsOpt.get();
        List<SettingsOptimizationSuggestion> suggestions = new ArrayList<>();

        // 性能优化建议
        if (settings.getMemberLimit() > 1000 && !settings.getMessageModerationEnabled()) {
            suggestions.add(new SettingsOptimizationSuggestion(
                    "Performance", "Enable message moderation",
                    "Large groups benefit from message moderation", "MEDIUM"));
        }

        // 安全优化建议
        if (!settings.getAntiSpamEnabled()) {
            suggestions.add(new SettingsOptimizationSuggestion(
                    "Security", "Enable anti-spam protection",
                    "Prevent spam and abuse in the group", "HIGH"));
        }

        // 功能优化建议
        if (!settings.getFileSharingEnabled()) {
            suggestions.add(new SettingsOptimizationSuggestion(
                    "Features", "Consider enabling file sharing",
                    "File sharing enhances group collaboration", "LOW"));
        }

        log.info("Generated {} optimization suggestions for group {}", suggestions.size(), groupId);
        return suggestions;
    }

    /**
     * 获取或创建群组设置
     */
    private GroupSettings getOrCreateSettings(Long groupId) {
        return groupSettingsRepository.findByGroupId(groupId)
                .orElseGet(() -> createDefaultSettings(groupId));
    }

    /**
     * 验证成员数量限制
     */
    private void validateMemberLimit(Integer memberLimit) {
        if (memberLimit < 1 || memberLimit > 100000) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证消息撤回时间限制
     */
    private void validateMessageRecallTimeLimit(Integer timeLimit) {
        if (timeLimit < 1 || timeLimit > 1440) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证文件大小限制
     */
    private void validateFileSizeLimit(Integer fileSizeLimitMb) {
        if (fileSizeLimitMb < 1 || fileSizeLimitMb > 1024) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证权限级别
     */
    private void validatePermissionLevel(Integer level) {
        if (level < 1 || level > 10) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证历史记录保留天数
     */
    private void validateHistoryRetentionDays(Integer days) {
        if (days < 1 || days > 3650) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证防刷屏时间窗口
     */
    private void validateAntiSpamTimeWindow(Integer timeWindow) {
        if (timeWindow < 1 || timeWindow > 3600) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证防刷屏消息限制
     */
    private void validateAntiSpamMessageLimit(Integer messageLimit) {
        if (messageLimit < 1 || messageLimit > 1000) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    @Override
    public GroupSettings applyRecommendedSettings(@NotNull Long groupId,
                                                 @NotNull SettingsTemplate settingsTemplate,
                                                 @NotNull Long modifierId) {
        log.info("Applying recommended settings for group {}: template={}", groupId, settingsTemplate);

        GroupSettings settings = getOrCreateSettings(groupId);

        // 根据模板应用设置
        switch (settingsTemplate) {
            case SMALL_GROUP:
                settings.setMemberLimit(50);
                settings.setJoinApprovalRequired(false);
                settings.setMessageModerationEnabled(false);
                settings.setAntiSpamEnabled(true);
                settings.setAntiSpamMessageLimit(20);
                break;

            case MEDIUM_GROUP:
                settings.setMemberLimit(200);
                settings.setJoinApprovalRequired(true);
                settings.setMessageModerationEnabled(true);
                settings.setMessageModerationMode(GroupSettings.ModerationMode.AUTO);
                settings.setAntiSpamEnabled(true);
                settings.setAntiSpamMessageLimit(15);
                break;

            case LARGE_GROUP:
                settings.setMemberLimit(500);
                settings.setJoinApprovalRequired(true);
                settings.setMessageModerationEnabled(true);
                settings.setMessageModerationMode(GroupSettings.ModerationMode.HYBRID);
                settings.setAntiSpamEnabled(true);
                settings.setAntiSpamMessageLimit(10);
                settings.setSensitiveWordFilterEnabled(true);
                break;

            case ENTERPRISE_GROUP:
                settings.setMemberLimit(1000);
                settings.setJoinApprovalRequired(true);
                settings.setMessageModerationEnabled(true);
                settings.setMessageModerationMode(GroupSettings.ModerationMode.MANUAL);
                settings.setMessageEncryptionEnabled(true);
                settings.setEncryptionAlgorithm("AES-256");
                settings.setAntiSpamEnabled(true);
                settings.setAntiSpamMessageLimit(5);
                settings.setSensitiveWordFilterEnabled(true);
                break;

            case PUBLIC_GROUP:
                settings.setGroupVisibility(GroupSettings.GroupVisibility.PUBLIC);
                settings.setSearchable(true);
                settings.setJoinApprovalRequired(false);
                settings.setMessageModerationEnabled(true);
                settings.setMessageModerationMode(GroupSettings.ModerationMode.AUTO);
                settings.setAntiSpamEnabled(true);
                break;

            case PRIVATE_GROUP:
                settings.setGroupVisibility(GroupSettings.GroupVisibility.PRIVATE);
                settings.setSearchable(false);
                settings.setJoinApprovalRequired(true);
                settings.setAllowMemberInvite(false);
                break;

            default:
                log.warn("Unknown settings template: {}", settingsTemplate);
                break;
        }

        settings.updateSettings(modifierId, "Applied recommended settings: " + settingsTemplate);
        GroupSettings updatedSettings = groupSettingsRepository.save(settings);

        log.info("Successfully applied recommended settings for group {}", groupId);
        return updatedSettings;
    }

    @Override
    public GroupSettings resetToDefaultSettings(@NotNull Long groupId, @NotNull Long modifierId) {
        log.info("Resetting settings to default for group {}", groupId);

        // 删除现有设置
        groupSettingsRepository.deleteByGroupId(groupId);

        // 创建新的默认设置
        GroupSettings defaultSettings = createDefaultSettings(groupId);
        defaultSettings.updateSettings(modifierId, "Reset to default settings");

        GroupSettings savedSettings = groupSettingsRepository.save(defaultSettings);

        log.info("Successfully reset settings to default for group {}", groupId);
        return savedSettings;
    }

    @Override
    public String exportSettings(@NotNull Long groupId) {
        log.info("Exporting settings for group {}", groupId);

        Optional<GroupSettings> settingsOpt = groupSettingsRepository.findByGroupId(groupId);
        if (settingsOpt.isEmpty()) {
            throw new BusinessException(GroupErrorCode.GROUP_NOT_FOUND);
        }

        GroupSettings settings = settingsOpt.get();

        // 构建JSON配置（简化实现）
        StringBuilder json = new StringBuilder();
        json.append("{")
            .append("\"groupId\":").append(groupId).append(",")
            .append("\"memberLimit\":").append(settings.getMemberLimit()).append(",")
            .append("\"joinApprovalRequired\":").append(settings.getJoinApprovalRequired()).append(",")
            .append("\"allowMemberInvite\":").append(settings.getAllowMemberInvite()).append(",")
            .append("\"messageModerationEnabled\":").append(settings.getMessageModerationEnabled()).append(",")
            .append("\"messageModerationMode\":\"").append(settings.getMessageModerationMode()).append("\",")
            .append("\"fileSharingEnabled\":").append(settings.getFileSharingEnabled()).append(",")
            .append("\"fileSizeLimitMb\":").append(settings.getFileSizeLimitMb()).append(",")
            .append("\"allowedFileTypes\":\"").append(settings.getAllowedFileTypes()).append("\",")
            .append("\"antiSpamEnabled\":").append(settings.getAntiSpamEnabled()).append(",")
            .append("\"groupVisibility\":\"").append(settings.getGroupVisibility()).append("\",")
            .append("\"searchable\":").append(settings.getSearchable())
            .append("}");

        log.info("Exported settings for group {}", groupId);
        return json.toString();
    }

    @Override
    @Transactional
    public GroupSettings importSettings(@NotNull Long groupId, @NotNull String settingsJson,
                                      @NotNull Long modifierId) {
        log.info("Importing settings for group {}", groupId);

        // TODO: 实现JSON解析和设置导入
        // 这里应该解析JSON并更新设置

        GroupSettings settings = getOrCreateSettings(groupId);
        settings.updateSettings(modifierId, "Settings imported from JSON");

        GroupSettings savedSettings = groupSettingsRepository.save(settings);

        log.warn("Settings import not fully implemented yet for group {}", groupId);
        return savedSettings;
    }

    @Override
    public SettingsStatistics getSettingsStatistics() {
        Object[] stats = groupSettingsRepository.getSettingsStatistics();

        long totalGroups = ((Number) stats[0]).longValue();
        long groupsWithApproval = ((Number) stats[1]).longValue();
        long groupsWithModeration = ((Number) stats[2]).longValue();
        long groupsWithFileSharing = ((Number) stats[3]).longValue();
        long groupsWithBots = ((Number) stats[4]).longValue();

        return new SettingsStatistics(totalGroups, groupsWithApproval, groupsWithModeration,
                groupsWithFileSharing, groupsWithBots);
    }

    @Override
    public Map<GroupSettings.GroupVisibility, Long> getVisibilityDistribution() {
        List<Object[]> distribution = groupSettingsRepository.getVisibilityDistribution();

        return distribution.stream()
                .collect(Collectors.toMap(
                        row -> (GroupSettings.GroupVisibility) row[0],
                        row -> ((Number) row[1]).longValue()));
    }

    @Override
    public Map<GroupSettings.ModerationMode, Long> getModerationModeDistribution() {
        List<Object[]> distribution = groupSettingsRepository.getModerationModeDistribution();

        return distribution.stream()
                .collect(Collectors.toMap(
                        row -> (GroupSettings.ModerationMode) row[0],
                        row -> ((Number) row[1]).longValue()));
    }

    @Override
    public Map<GroupSettings.NotificationSetting, Long> getNotificationSettingDistribution() {
        List<Object[]> distribution = groupSettingsRepository.getNotificationSettingDistribution();

        return distribution.stream()
                .collect(Collectors.toMap(
                        row -> (GroupSettings.NotificationSetting) row[0],
                        row -> ((Number) row[1]).longValue()));
    }

    @Override
    public MemberLimitDistribution getMemberLimitDistribution() {
        Object[] distribution = groupSettingsRepository.getMemberLimitDistribution();

        long small = ((Number) distribution[0]).longValue();
        long medium = ((Number) distribution[1]).longValue();
        long large = ((Number) distribution[2]).longValue();
        long xlarge = ((Number) distribution[3]).longValue();

        return new MemberLimitDistribution(small, medium, large, xlarge);
    }

    @Override
    public FileSizeLimitDistribution getFileSizeLimitDistribution() {
        Object[] distribution = groupSettingsRepository.getFileSizeLimitDistribution();

        long small = ((Number) distribution[0]).longValue();
        long medium = ((Number) distribution[1]).longValue();
        long large = ((Number) distribution[2]).longValue();
        long xlarge = ((Number) distribution[3]).longValue();

        return new FileSizeLimitDistribution(small, medium, large, xlarge);
    }

    @Override
    public AdvancedFeatureUsageRates getAdvancedFeatureUsageRates() {
        Object[] rates = groupSettingsRepository.getAdvancedFeatureUsageRates();

        double encryptionRate = ((Number) rates[0]).doubleValue();
        double botRate = ((Number) rates[1]).doubleValue();
        double moderationRate = ((Number) rates[2]).doubleValue();

        return new AdvancedFeatureUsageRates(encryptionRate, botRate, moderationRate);
    }

    @Override
    public List<GroupSettings> getGroupsNeedingOptimization() {
        return groupSettingsRepository.findGroupsNeedingOptimization();
    }

    @Override
    public List<GroupSettings> getGroupsWithWeakSecurity() {
        return groupSettingsRepository.findGroupsWithWeakSecurity();
    }

    @Override
    public List<GroupSettings> getGroupsWithConfigurationIssues() {
        return groupSettingsRepository.findGroupsWithConfigurationIssues();
    }

    @Override
    @Transactional
    public int batchUpdateMemberLimit(@NotNull List<Long> groupIds, @NotNull Integer memberLimit,
                                    @NotNull Long modifierId) {
        log.info("Batch updating member limit for {} groups: limit={}", groupIds.size(), memberLimit);

        validateMemberLimit(memberLimit);

        int updated = groupSettingsRepository.batchUpdateMemberLimit(groupIds, memberLimit, modifierId);

        log.info("Successfully updated member limit for {} groups", updated);
        return updated;
    }

    @Override
    @Transactional
    public int batchUpdateFileSharingSetting(@NotNull List<Long> groupIds, @NotNull Boolean fileSharingEnabled,
                                           @NotNull Long modifierId) {
        log.info("Batch updating file sharing setting for {} groups: enabled={}", groupIds.size(), fileSharingEnabled);

        int updated = groupSettingsRepository.batchUpdateFileSharingSetting(groupIds, fileSharingEnabled, modifierId);

        log.info("Successfully updated file sharing setting for {} groups", updated);
        return updated;
    }

    @Override
    @Transactional
    public int batchUpdateModerationSettings(@NotNull List<Long> groupIds, @NotNull Boolean moderationEnabled,
                                           @NotNull GroupSettings.ModerationMode moderationMode,
                                           @NotNull Long modifierId) {
        log.info("Batch updating moderation settings for {} groups: enabled={}, mode={}",
                groupIds.size(), moderationEnabled, moderationMode);

        int updated = groupSettingsRepository.batchUpdateModerationSettings(
                groupIds, moderationEnabled, moderationMode, modifierId);

        log.info("Successfully updated moderation settings for {} groups", updated);
        return updated;
    }

    @Override
    @Transactional
    public boolean deleteGroupSettings(@NotNull Long groupId) {
        log.info("Deleting settings for group {}", groupId);

        int deleted = groupSettingsRepository.deleteByGroupId(groupId);
        boolean success = deleted > 0;

        if (success) {
            log.info("Successfully deleted settings for group {}", groupId);
        } else {
            log.warn("No settings found to delete for group {}", groupId);
        }

        return success;
    }

}
