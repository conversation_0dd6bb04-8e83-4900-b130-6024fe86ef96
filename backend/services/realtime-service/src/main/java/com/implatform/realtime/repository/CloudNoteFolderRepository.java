package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CloudNoteFolder;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 云笔记文件夹Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CloudNoteFolderRepository extends R2dbcRepository<CloudNoteFolder, Long> {

    /**
     * 根据用户ID查找文件夹
     */
    @Query("SELECT * FROM cloud_note_folders WHERE user_id = :userId AND folder_status = :folderStatus ORDER BY is_pinned DESC, sort_order ASC, folder_name ASC")
    Flux<CloudNoteFolder> findByUserIdAndFolderStatusOrderByIsPinnedDescSortOrderAscFolderNameAsc(@Param("userId") Long userId, @Param("folderStatus") String folderStatus);

    /**
     * 分页查找用户文件夹
     */
    @Query("SELECT * FROM cloud_note_folders WHERE user_id = :userId AND folder_status = :folderStatus ORDER BY is_pinned DESC, sort_order ASC, folder_name ASC LIMIT :limit OFFSET :offset")
    Flux<CloudNoteFolder> findByUserIdAndFolderStatusOrderByIsPinnedDescSortOrderAscFolderNameAsc(@Param("userId") Long userId, @Param("folderStatus") String folderStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据父文件夹ID查找子文件夹
     */
    @Query("SELECT * FROM cloud_note_folders WHERE user_id = :userId AND parent_id = :parentId AND folder_status = :folderStatus ORDER BY is_pinned DESC, sort_order ASC, folder_name ASC")
    Flux<CloudNoteFolder> findByUserIdAndParentIdAndFolderStatusOrderByIsPinnedDescSortOrderAscFolderNameAsc(@Param("userId") Long userId, @Param("parentId") Long parentId, @Param("folderStatus") String folderStatus);

    /**
     * 查找根文件夹
     */
    @Query("SELECT * FROM cloud_note_folders WHERE user_id = :userId AND parent_id IS NULL AND folder_status = :folderStatus ORDER BY is_pinned DESC, sort_order ASC, folder_name ASC")
    Flux<CloudNoteFolder> findByUserIdAndParentIdIsNullAndFolderStatusOrderByIsPinnedDescSortOrderAscFolderNameAsc(@Param("userId") Long userId, @Param("folderStatus") String folderStatus);

    /**
     * 根据文件夹类型查找文件夹
     */
    @Query("SELECT * FROM cloud_note_folders WHERE user_id = :userId AND folder_type = :folderType AND folder_status = :folderStatus ORDER BY folder_name ASC")
    Flux<CloudNoteFolder> findByUserIdAndFolderTypeAndFolderStatusOrderByFolderNameAsc(@Param("userId") Long userId, @Param("folderType") String folderType, @Param("folderStatus") String folderStatus);

    /**
     * 查找置顶文件夹
     */
    List<CloudNoteFolder> findByUserIdAndIsPinnedTrueAndFolderStatusOrderBySortOrderAscFolderNameAsc(Long userId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 查找收藏文件夹
     */
    List<CloudNoteFolder> findByUserIdAndIsFavoriteTrueAndFolderStatusOrderBySortOrderAscFolderNameAsc(Long userId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 查找公开文件夹
     */
    List<CloudNoteFolder> findByUserIdAndIsPublicTrueAndFolderStatusOrderByFolderNameAsc(Long userId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 搜索文件夹
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "(cnf.folderName LIKE %:keyword% OR cnf.description LIKE %:keyword% OR cnf.tags LIKE %:keyword%) " +
           "ORDER BY cnf.isPinned DESC, cnf.sortOrder ASC, cnf.folderName ASC")
    List<CloudNoteFolder> searchFolders(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("keyword") String keyword);

    /**
     * 分页搜索文件夹
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "(cnf.folderName LIKE %:keyword% OR cnf.description LIKE %:keyword% OR cnf.tags LIKE %:keyword%) " +
           "ORDER BY cnf.isPinned DESC, cnf.sortOrder ASC, cnf.folderName ASC")
    Page<CloudNoteFolder> searchFolders(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据标签搜索文件夹
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND cnf.tags LIKE %:tag% " +
           "ORDER BY cnf.isPinned DESC, cnf.sortOrder ASC, cnf.folderName ASC")
    List<CloudNoteFolder> findByTag(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("tag") String tag);

    /**
     * 查找空文件夹
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "cnf.noteCount = 0 AND cnf.subfolderCount = 0 ORDER BY cnf.folderName ASC")
    List<CloudNoteFolder> findEmptyFolders(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 查找最近访问的文件夹
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "cnf.lastAccessAt >= :since ORDER BY cnf.lastAccessAt DESC")
    List<CloudNoteFolder> findRecentlyAccessedFolders(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("since") LocalDateTime since);

    /**
     * 查找待同步的文件夹
     */
    List<CloudNoteFolder> findByUserIdAndSyncStatus(Long userId, CloudNoteFolder.SyncStatus syncStatus);

    /**
     * 根据深度级别查找文件夹
     */
    List<CloudNoteFolder> findByUserIdAndDepthLevelAndFolderStatusOrderByFolderNameAsc(Long userId, Integer depthLevel, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 查找文件夹路径
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderPath = :folderPath AND cnf.folderStatus = :folderStatus")
    Optional<CloudNoteFolder> findByUserIdAndFolderPath(@Param("userId") Long userId, @Param("folderPath") String folderPath, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 统计用户文件夹数量
     */
    long countByUserIdAndFolderStatus(Long userId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 统计子文件夹数量
     */
    long countByUserIdAndParentIdAndFolderStatus(Long userId, Long parentId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 统计根文件夹数量
     */
    long countByUserIdAndParentIdIsNullAndFolderStatus(Long userId, CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 统计各类型文件夹数量
     */
    @Query("SELECT cnf.folderType, COUNT(cnf) FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus GROUP BY cnf.folderType")
    List<Object[]> countByFolderType(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 统计各状态文件夹数量
     */
    @Query("SELECT cnf.folderStatus, COUNT(cnf) FROM CloudNoteFolder cnf WHERE cnf.userId = :userId GROUP BY cnf.folderStatus")
    List<Object[]> countByFolderStatus(@Param("userId") Long userId);

    /**
     * 获取用户文件夹统计
     */
    @Query("SELECT COUNT(cnf), SUM(cnf.noteCount), SUM(cnf.subfolderCount), AVG(cnf.depthLevel) " +
           "FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus")
    List<Object[]> getUserFolderStatistics(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 获取热门标签
     */
    @Query("SELECT cnf.tags FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND cnf.tags IS NOT NULL")
    List<String> findAllTags(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 批量更新文件夹状态
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.folderStatus = :status, cnf.updatedAt = :now WHERE cnf.userId = :userId AND cnf.id IN :folderIds")
    int batchUpdateFolderStatus(@Param("userId") Long userId, @Param("folderIds") List<Long> folderIds, 
                               @Param("status") CloudNoteFolder.FolderStatus status, @Param("now") LocalDateTime now);

    /**
     * 批量移动文件夹
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.parentId = :parentId, cnf.updatedAt = :now WHERE cnf.userId = :userId AND cnf.id IN :folderIds")
    int batchMoveFolders(@Param("userId") Long userId, @Param("folderIds") List<Long> folderIds, 
                        @Param("parentId") Long parentId, @Param("now") LocalDateTime now);

    /**
     * 批量置顶/取消置顶文件夹
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.isPinned = :pinned, cnf.updatedAt = :now WHERE cnf.userId = :userId AND cnf.id IN :folderIds")
    int batchPinFolders(@Param("userId") Long userId, @Param("folderIds") List<Long> folderIds, 
                       @Param("pinned") Boolean pinned, @Param("now") LocalDateTime now);

    /**
     * 批量收藏/取消收藏文件夹
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.isFavorite = :favorite, cnf.updatedAt = :now WHERE cnf.userId = :userId AND cnf.id IN :folderIds")
    int batchFavoriteFolders(@Param("userId") Long userId, @Param("folderIds") List<Long> folderIds, 
                            @Param("favorite") Boolean favorite, @Param("now") LocalDateTime now);

    /**
     * 更新文件夹排序
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.sortOrder = :sortOrder, cnf.updatedAt = :now WHERE cnf.id = :folderId AND cnf.userId = :userId")
    int updateFolderSortOrder(@Param("userId") Long userId, @Param("folderId") Long folderId, 
                             @Param("sortOrder") Integer sortOrder, @Param("now") LocalDateTime now);

    /**
     * 更新文件夹笔记数量
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.noteCount = cnf.noteCount + :delta, cnf.updatedAt = :now WHERE cnf.id = :folderId AND cnf.userId = :userId")
    int updateNoteCount(@Param("userId") Long userId, @Param("folderId") Long folderId,
                       @Param("delta") Integer delta, @Param("now") Instant now);

    /**
     * 更新文件夹子文件夹数量
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.subfolderCount = cnf.subfolderCount + :delta, cnf.updatedAt = :now WHERE cnf.id = :folderId AND cnf.userId = :userId")
    int updateSubfolderCount(@Param("userId") Long userId, @Param("folderId") Long folderId, 
                            @Param("delta") Integer delta, @Param("now") LocalDateTime now);

    /**
     * 更新同步状态
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET cnf.syncStatus = :syncStatus, cnf.lastSyncAt = :now WHERE cnf.userId = :userId AND cnf.id IN :folderIds")
    int updateSyncStatus(@Param("userId") Long userId, @Param("folderIds") List<Long> folderIds, 
                        @Param("syncStatus") CloudNoteFolder.SyncStatus syncStatus, @Param("now") LocalDateTime now);

    /**
     * 重新计算文件夹统计
     */
    @Modifying
    @Query("UPDATE CloudNoteFolder cnf SET " +
           "cnf.noteCount = (SELECT COUNT(cn) FROM CloudNote cn WHERE cn.folderId = cnf.id AND cn.noteStatus = 'ACTIVE'), " +
           "cnf.subfolderCount = (SELECT COUNT(subcnf) FROM CloudNoteFolder subcnf WHERE subcnf.parentId = cnf.id AND subcnf.folderStatus = 'ACTIVE'), " +
           "cnf.updatedAt = :now " +
           "WHERE cnf.userId = :userId AND cnf.id = :folderId")
    int recalculateFolderStatistics(@Param("userId") Long userId, @Param("folderId") Long folderId, @Param("now") LocalDateTime now);

    /**
     * 物理删除已删除的文件夹
     */
    @Modifying
    @Query("DELETE FROM CloudNoteFolder cnf WHERE cnf.folderStatus = 'DELETED' AND cnf.updatedAt < :cutoffTime")
    int physicalDeleteOldFolders(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取文件夹层次结构
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus " +
           "ORDER BY cnf.depthLevel ASC, cnf.folderPath ASC")
    List<CloudNoteFolder> getFolderHierarchy(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 获取文件夹树（从指定文件夹开始）
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "(cnf.id = :rootFolderId OR cnf.folderPath LIKE CONCAT((SELECT root.folderPath FROM CloudNoteFolder root WHERE root.id = :rootFolderId), '/%')) " +
           "ORDER BY cnf.depthLevel ASC, cnf.folderPath ASC")
    List<CloudNoteFolder> getFolderTree(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("rootFolderId") Long rootFolderId);

    /**
     * 获取文件夹面包屑路径
     */
    @Query("SELECT cnf FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus AND " +
           "cnf.folderPath IN (SELECT DISTINCT SUBSTRING(target.folderPath, 1, LOCATE('/', target.folderPath, 2) - 1) " +
           "FROM CloudNoteFolder target WHERE target.id = :folderId) " +
           "ORDER BY cnf.depthLevel ASC")
    List<CloudNoteFolder> getFolderBreadcrumb(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus, @Param("folderId") Long folderId);

    /**
     * 检查文件夹是否存在
     */
    boolean existsByUserIdAndId(Long userId, Long folderId);

    /**
     * 检查文件夹名称是否重复
     */
    boolean existsByUserIdAndParentIdAndFolderNameAndFolderStatusAndIdNot(Long userId, Long parentId, String folderName, CloudNoteFolder.FolderStatus folderStatus, Long excludeId);

    /**
     * 检查根文件夹名称是否重复
     */
    boolean existsByUserIdAndParentIdIsNullAndFolderNameAndFolderStatusAndIdNot(Long userId, String folderName, CloudNoteFolder.FolderStatus folderStatus, Long excludeId);

    /**
     * 获取下一个排序顺序
     */
    @Query("SELECT COALESCE(MAX(cnf.sortOrder), 0) + 1 FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.parentId = :parentId")
    Integer getNextSortOrder(@Param("userId") Long userId, @Param("parentId") Long parentId);

    /**
     * 获取下一个排序顺序（根目录）
     */
    @Query("SELECT COALESCE(MAX(cnf.sortOrder), 0) + 1 FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.parentId IS NULL")
    Integer getNextSortOrderForRoot(@Param("userId") Long userId);

    /**
     * 获取用户文件夹概览
     */
    @Query("SELECT COUNT(cnf), " +
           "COUNT(CASE WHEN cnf.isPinned = true THEN 1 END), " +
           "COUNT(CASE WHEN cnf.isFavorite = true THEN 1 END), " +
           "COUNT(CASE WHEN cnf.isPublic = true THEN 1 END), " +
           "COUNT(CASE WHEN cnf.isEncrypted = true THEN 1 END) " +
           "FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus")
    List<Object[]> getUserFolderOverview(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);

    /**
     * 获取最大深度级别
     */
    @Query("SELECT COALESCE(MAX(cnf.depthLevel), 0) FROM CloudNoteFolder cnf WHERE cnf.userId = :userId AND cnf.folderStatus = :folderStatus")
    Integer getMaxDepthLevel(@Param("userId") Long userId, @Param("folderStatus") CloudNoteFolder.FolderStatus folderStatus);
}
