package com.implatform.realtime.repository;

import com.implatform.realtime.entity.MessageReaction;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 消息表情回应Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface MessageReactionRepository extends R2dbcRepository<MessageReaction, Long> {

    /**
     * 根据消息ID查找所有表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE message_id = :messageId ORDER BY created_at ASC")
    Flux<MessageReaction> findByMessageIdOrderByCreatedAtAsc(@Param("messageId") Long messageId);

    /**
     * 根据消息ID和用户ID查找表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE message_id = :messageId AND user_id = :userId")
    Flux<MessageReaction> findByMessageIdAndUserId(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 查找用户对消息的特定表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE message_id = :messageId AND user_id = :userId " +
           "AND ((reaction_type = 'EMOJI' AND emoji_unicode = :emojiUnicode) " +
           "OR (reaction_type = 'STICKER' AND sticker_id = :stickerId) " +
           "OR (reaction_type = 'CUSTOM' AND custom_reaction_id = :customReactionId))")
    Mono<MessageReaction> findSpecificReaction(@Param("messageId") Long messageId,
                                                  @Param("userId") Long userId,
                                                  @Param("emojiUnicode") String emojiUnicode,
                                                  @Param("stickerId") Long stickerId,
                                                  @Param("customReactionId") Long customReactionId);

    /**
     * 检查用户是否对消息有特定表情回应
     */
    @Query("SELECT COUNT(*) > 0 FROM message_reactions WHERE message_id = :messageId AND user_id = :userId " +
           "AND ((reaction_type = 'EMOJI' AND emoji_unicode = :emojiUnicode) " +
           "OR (reaction_type = 'STICKER' AND sticker_id = :stickerId) " +
           "OR (reaction_type = 'CUSTOM' AND custom_reaction_id = :customReactionId))")
    Mono<Boolean> existsSpecificReaction(@Param("messageId") Long messageId,
                                  @Param("userId") Long userId,
                                  @Param("emojiUnicode") String emojiUnicode,
                                  @Param("stickerId") Long stickerId,
                                  @Param("customReactionId") Long customReactionId);

    /**
     * 统计消息的总表情回应数量
     */
    @Query("SELECT COUNT(*) FROM message_reactions WHERE message_id = :messageId")
    Mono<Long> countByMessageId(@Param("messageId") Long messageId);

    /**
     * 统计消息的独立用户表情回应数量
     */
    @Query("SELECT COUNT(DISTINCT user_id) FROM message_reactions WHERE message_id = :messageId")
    Mono<Long> countDistinctUsersByMessageId(@Param("messageId") Long messageId);

    /**
     * 根据表情类型查找表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE message_id = :messageId AND reaction_type = :reactionType")
    Flux<MessageReaction> findByMessageIdAndReactionType(@Param("messageId") Long messageId, @Param("reactionType") String reactionType);

    /**
     * 查找用户的表情回应历史
     */
    @Query("SELECT * FROM message_reactions WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<MessageReaction> findUserReactionHistory(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户最近使用的表情
     */
    @Query("SELECT emoji_unicode, COUNT(*) as count FROM message_reactions " +
           "WHERE user_id = :userId AND reaction_type = 'EMOJI' AND emoji_unicode IS NOT NULL " +
           "AND created_at >= :since " +
           "GROUP BY emoji_unicode ORDER BY count DESC, MAX(created_at) DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findUserRecentEmojis(@Param("userId") Long userId, @Param("since") LocalDateTime since, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找热门表情回应
     */
    @Query("SELECT emoji_unicode, COUNT(*) as count FROM message_reactions " +
           "WHERE reaction_type = 'EMOJI' AND emoji_unicode IS NOT NULL " +
           "AND created_at >= :since " +
           "GROUP BY emoji_unicode ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findPopularEmojis(@Param("since") LocalDateTime since, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找消息的表情回应统计
     */
    @Query("SELECT emoji_unicode, sticker_id, custom_reaction_id, COUNT(*) as count " +
           "FROM message_reactions WHERE message_id = :messageId " +
           "GROUP BY emoji_unicode, sticker_id, custom_reaction_id " +
           "ORDER BY count DESC")
    Flux<Object[]> getMessageReactionStats(@Param("messageId") Long messageId);

    /**
     * 删除用户对消息的所有表情回应
     */
    @Modifying
    @Query("DELETE FROM message_reactions WHERE message_id = :messageId AND user_id = :userId")
    Mono<Integer> deleteByMessageIdAndUserId(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 删除消息的所有表情回应
     */
    @Modifying
    @Query("DELETE FROM message_reactions WHERE message_id = :messageId")
    Mono<Integer> deleteByMessageId(@Param("messageId") Long messageId);

    /**
     * 查找指定时间段内的表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE created_at BETWEEN :startTime AND :endTime " +
           "ORDER BY created_at DESC")
    Flux<MessageReaction> findReactionsBetweenTime(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查找使用特定贴纸的表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE sticker_id = :stickerId ORDER BY created_at DESC")
    Flux<MessageReaction> findByStickerIdOrderByCreatedAtDesc(@Param("stickerId") Long stickerId);

    /**
     * 查找使用特定自定义表情的表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE custom_reaction_id = :customReactionId ORDER BY created_at DESC")
    Flux<MessageReaction> findByCustomReactionIdOrderByCreatedAtDesc(@Param("customReactionId") Long customReactionId);

    /**
     * 统计用户的表情回应总数
     */
    @Query("SELECT COUNT(*) FROM message_reactions WHERE user_id = :userId")
    Mono<Long> countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户在指定时间段内的表情回应数量
     */
    @Query("SELECT COUNT(*) FROM message_reactions WHERE user_id = :userId " +
           "AND created_at BETWEEN :startTime AND :endTime")
    Mono<Long> countByUserIdAndTimeBetween(@Param("userId") Long userId,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查找活跃的表情回应用户
     */
    @Query("SELECT user_id, COUNT(*) as count FROM message_reactions " +
           "WHERE created_at >= :since " +
           "GROUP BY user_id ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findActiveReactionUsers(@Param("since") LocalDateTime since, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找消息的最新表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE message_id = :messageId " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<MessageReaction> findLatestReactionsByMessage(@Param("messageId") Long messageId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户对多条消息的表情回应
     */
    @Query("SELECT * FROM message_reactions WHERE user_id = :userId AND message_id = ANY(:messageIds) " +
           "ORDER BY created_at DESC")
    Flux<MessageReaction> findUserReactionsForMessages(@Param("userId") Long userId,
                                                      @Param("messageIds") Long[] messageIds);

    /**
     * 统计表情回应的使用频率
     */
    @Query("SELECT " +
           "CASE " +
           "  WHEN reaction_type = 'EMOJI' THEN CONCAT('emoji:', emoji_unicode) " +
           "  WHEN reaction_type = 'STICKER' THEN CONCAT('sticker:', sticker_id) " +
           "  WHEN reaction_type = 'CUSTOM' THEN CONCAT('custom:', custom_reaction_id) " +
           "END as reaction_key, " +
           "COUNT(*) as count " +
           "FROM message_reactions " +
           "WHERE created_at >= :since " +
           "GROUP BY reaction_key " +
           "ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> getReactionUsageStats(@Param("since") LocalDateTime since, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 清理旧的表情回应记录
     */
    @Modifying
    @Query("DELETE FROM message_reactions WHERE created_at < :cutoffTime")
    Mono<Integer> deleteOldReactions(@Param("cutoffTime") LocalDateTime cutoffTime);
}
