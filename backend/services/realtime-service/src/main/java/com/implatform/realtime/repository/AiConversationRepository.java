package com.implatform.realtime.repository;

import com.implatform.realtime.entity.AiConversation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * AI对话数据访问层 - R2DBC响应式版本
 *
 * <p><strong>功能概述</strong>：
 * 提供AI对话的完整数据访问功能，包括基础CRUD、高级查询、统计分析等。
 * 专门针对AI对话的特殊需求进行优化，支持多会话管理、令牌统计、费用计算等。
 *
 * <p><strong>核心特性</strong>：
 * <ul>
 *   <li><strong>多会话查询</strong>：支持用户与助手的多对话查询</li>
 *   <li><strong>智能排序</strong>：支持按时间、费用、令牌等多维度排序</li>
 *   <li><strong>状态管理</strong>：完整的对话状态查询和更新</li>
 *   <li><strong>统计分析</strong>：提供丰富的使用统计和趋势分析</li>
 *   <li><strong>性能优化</strong>：针对AI对话场景的查询优化</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface AiConversationRepository extends R2dbcRepository<AiConversation, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据用户ID查询活跃对话
     */
    @Query("SELECT * FROM ai_conversations WHERE user_id = :userId AND status = 'ACTIVE' " +
           "ORDER BY is_pinned DESC, last_message_at DESC LIMIT :limit OFFSET :offset")
    Flux<AiConversation> findActiveConversationsByUserId(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据用户ID和助手ID查询对话
     */
    @Query("SELECT * FROM ai_conversations WHERE user_id = :userId AND assistant_id = :assistantId " +
           "AND ac.status != 'DELETED' ORDER BY ac.lastMessageAt DESC")
    Page<AiConversation> findConversationsByUserAndAssistant(@Param("userId") Long userId, 
                                                             @Param("assistantId") Long assistantId, 
                                                             Pageable pageable);

    /**
     * 根据助手ID查询所有对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.assistantId = :assistantId AND ac.status != 'DELETED' " +
           "ORDER BY ac.lastMessageAt DESC")
    Page<AiConversation> findConversationsByAssistantId(@Param("assistantId") Long assistantId, Pageable pageable);

    /**
     * 查询用户的置顶对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.isPinned = true " +
           "AND ac.status = 'ACTIVE' ORDER BY ac.pinnedAt DESC")
    List<AiConversation> findPinnedConversationsByUserId(@Param("userId") Long userId);

    /**
     * 查询用户的收藏对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.isFavorite = true " +
           "AND ac.status != 'DELETED' ORDER BY ac.favoritedAt DESC")
    Page<AiConversation> findFavoriteConversationsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据状态查询对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.status = :status " +
           "ORDER BY ac.lastMessageAt DESC")
    Page<AiConversation> findConversationsByUserIdAndStatus(@Param("userId") Long userId, 
                                                           @Param("status") AiConversation.ConversationStatus status, 
                                                           Pageable pageable);

    /**
     * 搜索对话（按标题和摘要）
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.status != 'DELETED' " +
           "AND (LOWER(ac.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(ac.summary) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(ac.lastMessageContent) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY ac.lastMessageAt DESC")
    Page<AiConversation> searchConversations(@Param("userId") Long userId, 
                                           @Param("keyword") String keyword, 
                                           Pageable pageable);

    // ==================== 统计查询 ====================

    /**
     * 统计用户的对话总数
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status != 'DELETED'")
    long countConversationsByUserId(@Param("userId") Long userId);

    /**
     * 统计助手的对话总数
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.assistantId = :assistantId AND ac.status != 'DELETED'")
    long countConversationsByAssistantId(@Param("assistantId") Long assistantId);

    /**
     * 统计用户的活跃对话数
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status = 'ACTIVE'")
    long countActiveConversationsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总消息数
     */
    @Query("SELECT COALESCE(SUM(ac.messageCount), 0) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status != 'DELETED'")
    long sumMessageCountByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总令牌数
     */
    @Query("SELECT COALESCE(SUM(ac.totalTokens), 0) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status != 'DELETED'")
    long sumTokensByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总费用
     */
    @Query("SELECT COALESCE(SUM(ac.totalCost), 0) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status != 'DELETED'")
    long sumCostByUserId(@Param("userId") Long userId);

    /**
     * 统计助手的总使用次数
     */
    @Query("SELECT COALESCE(SUM(ac.messageCount), 0) FROM AiConversation ac WHERE ac.assistantId = :assistantId AND ac.status != 'DELETED'")
    long sumMessageCountByAssistantId(@Param("assistantId") Long assistantId);

    /**
     * 获取用户最近使用的助手ID列表
     */
    @Query("SELECT DISTINCT ac.assistantId FROM AiConversation ac WHERE ac.userId = :userId " +
           "AND ac.status = 'ACTIVE' AND ac.lastMessageAt >= :since " +
           "ORDER BY MAX(ac.lastMessageAt) DESC")
    List<Long> findRecentlyUsedAssistantIds(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 获取用户的对话使用趋势
     */
    @Query("SELECT DATE(ac.createdAt) as date, COUNT(ac) as count FROM AiConversation ac " +
           "WHERE ac.userId = :userId AND ac.createdAt >= :startTime " +
           "GROUP BY DATE(ac.createdAt) ORDER BY DATE(ac.createdAt)")
    List<Object[]> getUserConversationTrend(@Param("userId") Long userId, @Param("startTime") Instant startTime);

    /**
     * 获取助手的使用趋势
     */
    @Query("SELECT DATE(ac.createdAt) as date, COUNT(ac) as count FROM AiConversation ac " +
           "WHERE ac.assistantId = :assistantId AND ac.createdAt >= :startTime " +
           "GROUP BY DATE(ac.createdAt) ORDER BY DATE(ac.createdAt)")
    List<Object[]> getAssistantUsageTrend(@Param("assistantId") Long assistantId, @Param("startTime") Instant startTime);

    /**
     * 获取用户的费用趋势
     */
    @Query("SELECT DATE(ac.lastMessageAt) as date, SUM(ac.totalCost) as cost FROM AiConversation ac " +
           "WHERE ac.userId = :userId AND ac.lastMessageAt >= :startTime " +
           "GROUP BY DATE(ac.lastMessageAt) ORDER BY DATE(ac.lastMessageAt)")
    List<Object[]> getUserCostTrend(@Param("userId") Long userId, @Param("startTime") Instant startTime);

    // ==================== 更新操作 ====================

    /**
     * 批量置顶对话
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.isPinned = true, ac.pinnedAt = :pinnedAt " +
           "WHERE ac.id IN :conversationIds AND ac.userId = :userId")
    int pinConversations(@Param("conversationIds") List<Long> conversationIds, 
                        @Param("userId") Long userId, 
                        @Param("pinnedAt") Instant pinnedAt);

    /**
     * 批量取消置顶
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.isPinned = false, ac.pinnedAt = null " +
           "WHERE ac.id IN :conversationIds AND ac.userId = :userId")
    int unpinConversations(@Param("conversationIds") List<Long> conversationIds, @Param("userId") Long userId);

    /**
     * 批量收藏对话
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.isFavorite = true, ac.favoritedAt = :favoritedAt " +
           "WHERE ac.id IN :conversationIds AND ac.userId = :userId")
    int favoriteConversations(@Param("conversationIds") List<Long> conversationIds, 
                             @Param("userId") Long userId, 
                             @Param("favoritedAt") Instant favoritedAt);

    /**
     * 批量取消收藏
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.isFavorite = false, ac.favoritedAt = null " +
           "WHERE ac.id IN :conversationIds AND ac.userId = :userId")
    int unfavoriteConversations(@Param("conversationIds") List<Long> conversationIds, @Param("userId") Long userId);

    /**
     * 批量更新对话状态
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.status = :status " +
           "WHERE ac.id IN :conversationIds AND ac.userId = :userId")
    int updateConversationStatus(@Param("conversationIds") List<Long> conversationIds, 
                                @Param("userId") Long userId, 
                                @Param("status") AiConversation.ConversationStatus status);

    /**
     * 更新对话的最后消息信息
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.lastMessageId = :messageId, " +
           "ac.lastMessageContent = :content, ac.lastMessageRole = :role, " +
           "ac.lastMessageAt = :messageAt, ac.messageCount = ac.messageCount + 1 " +
           "WHERE ac.id = :conversationId")
    int updateLastMessage(@Param("conversationId") Long conversationId,
                         @Param("messageId") Long messageId,
                         @Param("content") String content,
                         @Param("role") String role,
                         @Param("messageAt") Instant messageAt);

    /**
     * 更新对话的令牌使用量
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.totalInputTokens = ac.totalInputTokens + :inputTokens, " +
           "ac.totalOutputTokens = ac.totalOutputTokens + :outputTokens, " +
           "ac.totalTokens = ac.totalTokens + :totalTokens " +
           "WHERE ac.id = :conversationId")
    int updateTokenUsage(@Param("conversationId") Long conversationId,
                        @Param("inputTokens") Long inputTokens,
                        @Param("outputTokens") Long outputTokens,
                        @Param("totalTokens") Long totalTokens);

    /**
     * 更新对话费用
     */
    @Modifying
    @Query("UPDATE AiConversation ac SET ac.totalCost = ac.totalCost + :cost WHERE ac.id = :conversationId")
    int updateCost(@Param("conversationId") Long conversationId, @Param("cost") Long cost);

    /**
     * 清理过期的已删除对话
     */
    @Modifying
    @Query("DELETE FROM AiConversation ac WHERE ac.status = 'DELETED' AND ac.updatedAt < :expiredTime")
    int cleanupDeletedConversations(@Param("expiredTime") Instant expiredTime);

    // ==================== 高级查询 ====================

    /**
     * 查找相似对话（基于标题和摘要的相似度）
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.assistantId = :assistantId " +
           "AND ac.status = 'ACTIVE' AND ac.id != :excludeId " +
           "AND (LOWER(ac.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(ac.summary) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY ac.lastMessageAt DESC")
    List<AiConversation> findSimilarConversations(@Param("userId") Long userId,
                                                 @Param("assistantId") Long assistantId,
                                                 @Param("excludeId") Long excludeId,
                                                 @Param("keyword") String keyword,
                                                 Pageable pageable);

    /**
     * 查询高费用对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.totalCost >= :minCost " +
           "AND ac.status != 'DELETED' ORDER BY ac.totalCost DESC")
    List<AiConversation> findHighCostConversations(@Param("userId") Long userId, 
                                                  @Param("minCost") Long minCost, 
                                                  Pageable pageable);

    /**
     * 查询长对话（消息数多的对话）
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.messageCount >= :minMessages " +
           "AND ac.status != 'DELETED' ORDER BY ac.messageCount DESC")
    List<AiConversation> findLongConversations(@Param("userId") Long userId, 
                                             @Param("minMessages") Integer minMessages, 
                                             Pageable pageable);

    /**
     * 查询最近活跃的对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.lastMessageAt >= :since " +
           "AND ac.status = 'ACTIVE' ORDER BY ac.lastMessageAt DESC")
    List<AiConversation> findRecentActiveConversations(@Param("userId") Long userId, 
                                                      @Param("since") Instant since, 
                                                      Pageable pageable);

    /**
     * 查询用户与特定助手的最新对话
     */
    @Query("SELECT ac FROM AiConversation ac WHERE ac.userId = :userId AND ac.assistantId = :assistantId " +
           "AND ac.status = 'ACTIVE' ORDER BY ac.lastMessageAt DESC")
    Optional<AiConversation> findLatestConversationByUserAndAssistant(@Param("userId") Long userId, 
                                                                     @Param("assistantId") Long assistantId);

    /**
     * 检查用户是否有与助手的活跃对话
     */
    @Query("SELECT COUNT(ac) > 0 FROM AiConversation ac WHERE ac.userId = :userId AND ac.assistantId = :assistantId " +
           "AND ac.status = 'ACTIVE'")
    boolean hasActiveConversationWithAssistant(@Param("userId") Long userId, @Param("assistantId") Long assistantId);

    // ==================== Protobuf支持的额外查询方法 ====================

    /**
     * 根据用户ID查询非指定状态的对话
     */
    Page<AiConversation> findByUserIdAndStatusNot(Long userId, AiConversation.ConversationStatus status, Pageable pageable);

    /**
     * 根据用户ID查询非指定状态的对话（不分页）
     */
    List<AiConversation> findByUserIdAndStatusNot(Long userId, AiConversation.ConversationStatus status);

    /**
     * 根据ID列表和用户ID查询对话
     */
    List<AiConversation> findByIdInAndUserId(List<Long> ids, Long userId);

    /**
     * 根据用户ID、助手ID查询非指定状态的对话
     */
    Page<AiConversation> findByUserIdAndAssistantIdAndStatusNot(Long userId, Long assistantId,
                                                               AiConversation.ConversationStatus status,
                                                               Pageable pageable);

    /**
     * 根据用户ID查询置顶且非指定状态的对话
     */
    List<AiConversation> findByUserIdAndIsPinnedTrueAndStatusNotOrderByPinnedAtDesc(Long userId,
                                                                                   AiConversation.ConversationStatus status);

    /**
     * 根据用户ID查询收藏且非指定状态的对话
     */
    Page<AiConversation> findByUserIdAndIsFavoriteTrueAndStatusNot(Long userId,
                                                                  AiConversation.ConversationStatus status,
                                                                  Pageable pageable);

    /**
     * 根据用户ID、状态和最后消息时间不为空查询对话
     */
    Page<AiConversation> findByUserIdAndStatusAndLastMessageAtIsNotNull(Long userId,
                                                                        AiConversation.ConversationStatus status,
                                                                        Pageable pageable);

    /**
     * 根据用户ID、助手ID查询最新对话（非删除状态）
     */
    Optional<AiConversation> findFirstByUserIdAndAssistantIdAndStatusNotOrderByLastMessageAtDesc(Long userId,
                                                                                                 Long assistantId,
                                                                                                 AiConversation.ConversationStatus status);

    /**
     * 根据用户ID和时间范围查询对话
     */
    List<AiConversation> findByUserIdAndCreatedAtBetween(Long userId, Instant startTime, Instant endTime);

    /**
     * 检查用户是否有与助手的指定状态对话
     */
    boolean existsByUserIdAndAssistantIdAndStatus(Long userId, Long assistantId, AiConversation.ConversationStatus status);

    // ==================== 新增的统计方法 ====================

    /**
     * 根据用户ID和时间范围统计对话数量
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId " +
           "AND ac.createdAt BETWEEN :startTime AND :endTime")
    long countByUserIdAndCreatedAtBetween(@Param("userId") Long userId, 
                                         @Param("startTime") Instant startTime, 
                                         @Param("endTime") Instant endTime);

    /**
     * 根据用户ID和状态统计对话数量
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId AND ac.status = :status")
    long countByUserIdAndStatus(@Param("userId") Long userId, 
                               @Param("status") AiConversation.ConversationStatus status);

    /**
     * 根据用户ID和收藏状态统计对话数量
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId AND ac.isFavorite = :isFavorite")
    long countByUserIdAndIsFavorite(@Param("userId") Long userId, @Param("isFavorite") boolean isFavorite);

    /**
     * 统计用户已删除的对话数量
     */
    @Query("SELECT COUNT(ac) FROM AiConversation ac WHERE ac.userId = :userId AND ac.deletedAt IS NOT NULL")
    long countByUserIdAndDeletedAtIsNotNull(@Param("userId") Long userId);
}
