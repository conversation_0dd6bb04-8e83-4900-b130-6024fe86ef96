package com.implatform.realtime.service.impl;

import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.core.enums.SocialErrorCode;
import com.implatform.realtime.dto.CoinRuleDTO;
import com.implatform.realtime.entity.CoinRule;
import com.implatform.repository.CoinRuleRepository;
import com.implatform.realtime.service.CoinAccountService;
import com.implatform.realtime.service.CoinRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

/**
 * 金币规则服务实现类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class CoinRuleServiceImpl implements CoinRuleService {

    private final CoinRuleRepository coinRuleRepository;
    private final CoinAccountService coinAccountService;
    private final Random random = new Random();

    @Override
    @Transactional
    public CoinRule createRule(CoinRuleDTO.CreateRequest request) {
        log.info("创建金币规则: ruleName={}", request.getRuleName());

        // 检查规则名称是否已存在
        if (coinRuleRepository.existsByRuleName(request.getRuleName())) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }

        CoinRule rule = CoinRule.builder()
                .ruleName(request.getRuleName())
                .description(request.getDescription())
                .ruleType(request.getRuleType())
                .action(request.getAction())
                .rewardAmount(request.getRewardAmount())
                .minReward(request.getMinReward())
                .maxReward(request.getMaxReward())
                .priority(request.getPriority() != null ? request.getPriority() : 0)
                .dailyLimit(request.getDailyLimit())
                .weeklyLimit(request.getWeeklyLimit())
                .monthlyLimit(request.getMonthlyLimit())
                .totalLimit(request.getTotalLimit())
                .conditions(request.getConditions())
                .config(request.getConfig())
                .effectiveFrom(request.getEffectiveFrom())
                .effectiveUntil(request.getEffectiveUntil())
                .createdBy("SYSTEM") // 默认创建者，实际应该从控制器传入
                .build();

        return coinRuleRepository.save(rule);
    }

    @Override
    public Optional<CoinRule> getRule(Long ruleId) {
        return coinRuleRepository.findById(ruleId);
    }

    @Override
    @Transactional
    public CoinRule updateRule(Long ruleId, CoinRuleDTO.UpdateRequest request) {
        log.info("更新金币规则: ruleId={}", ruleId);
        
        CoinRule rule = coinRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));
        
        // 检查规则名称是否重复
        if (request.getRuleName() != null && 
            !request.getRuleName().equals(rule.getRuleName()) &&
            coinRuleRepository.existsByRuleNameAndIdNot(request.getRuleName(), ruleId)) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }
        
        // 更新字段
        if (request.getRuleName() != null) {
            rule.setRuleName(request.getRuleName());
        }
        if (request.getDescription() != null) {
            rule.setDescription(request.getDescription());
        }
        if (request.getRewardAmount() != null) {
            rule.setRewardAmount(request.getRewardAmount());
        }
        if (request.getMinReward() != null) {
            rule.setMinReward(request.getMinReward());
        }
        if (request.getMaxReward() != null) {
            rule.setMaxReward(request.getMaxReward());
        }
        if (request.getPriority() != null) {
            rule.setPriority(request.getPriority());
        }
        if (request.getDailyLimit() != null) {
            rule.setDailyLimit(request.getDailyLimit());
        }
        if (request.getWeeklyLimit() != null) {
            rule.setWeeklyLimit(request.getWeeklyLimit());
        }
        if (request.getMonthlyLimit() != null) {
            rule.setMonthlyLimit(request.getMonthlyLimit());
        }
        if (request.getTotalLimit() != null) {
            rule.setTotalLimit(request.getTotalLimit());
        }
        if (request.getConditions() != null) {
            rule.setConditions(request.getConditions());
        }
        if (request.getConfig() != null) {
            rule.setConfig(request.getConfig());
        }
        if (request.getEffectiveFrom() != null) {
            rule.setEffectiveFrom(request.getEffectiveFrom());
        }
        if (request.getEffectiveUntil() != null) {
            rule.setEffectiveUntil(request.getEffectiveUntil());
        }
        // UpdateRequest 没有 updatedBy 字段，使用默认值
        rule.setUpdatedBy("SYSTEM");
        
        return coinRuleRepository.save(rule);
    }

    @Override
    @Transactional
    public void deleteRule(Long ruleId, String operatorId) {
        log.info("删除金币规则: ruleId={}, operatorId={}", ruleId, operatorId);
        
        CoinRule rule = coinRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));
        
        rule.setRuleStatus(CoinRule.RuleStatus.DELETED);
        rule.setUpdatedBy(operatorId);
        coinRuleRepository.save(rule);
    }

    @Override
    @Transactional
    public void enableRule(Long ruleId, String operatorId) {
        log.info("启用金币规则: ruleId={}, operatorId={}", ruleId, operatorId);
        
        CoinRule rule = coinRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));
        
        rule.activate();
        rule.setUpdatedBy(operatorId);
        coinRuleRepository.save(rule);
    }

    @Override
    @Transactional
    public void disableRule(Long ruleId, String operatorId) {
        log.info("禁用金币规则: ruleId={}, operatorId={}", ruleId, operatorId);
        
        CoinRule rule = coinRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));
        
        rule.deactivate();
        rule.setUpdatedBy(operatorId);
        coinRuleRepository.save(rule);
    }

    @Override
    public Page<CoinRule> getAllRules(CoinRule.RuleStatus status, Pageable pageable) {
        log.debug("分页查询金币规则: status={}", status);
        
        if (status != null) {
            return coinRuleRepository.findByRuleStatus(status, pageable);
        } else {
            return coinRuleRepository.findAll(pageable);
        }
    }

    @Override
    public List<CoinRule> getRulesByType(CoinRule.RuleType ruleType) {
        log.debug("根据类型查询规则: ruleType={}", ruleType);
        
        return coinRuleRepository.findByRuleType(ruleType);
    }

    @Override
    public List<CoinRule> getRulesByAction(String action) {
        log.debug("根据动作查询规则: action={}", action);
        
        return coinRuleRepository.findByAction(action);
    }

    @Override
    public Page<CoinRule> searchRules(String keyword, Pageable pageable) {
        log.debug("搜索金币规则: keyword={}", keyword);
        
        return coinRuleRepository.searchRules(keyword, pageable);
    }

    @Override
    public List<CoinRule> getActiveRules() {
        log.debug("获取活跃规则");
        
        return coinRuleRepository.findActiveRules();
    }

    @Override
    public List<CoinRule> getApplicableRules(Long userId, String action) {
        log.debug("获取用户适用规则: userId={}, action={}", userId, action);
        
        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction(action);
        List<CoinRule> applicableRules = new ArrayList<>();
        
        for (CoinRule rule : rules) {
            if (rule.canExecute() && checkRuleConditions(userId, rule, null)) {
                applicableRules.add(rule);
            }
        }
        
        return applicableRules;
    }

    @Override
    @Transactional
    public BigDecimal executeRule(Long userId, String action, String context) {
        log.info("执行金币规则: userId={}, action={}", userId, action);
        
        List<CoinRule> applicableRules = getApplicableRules(userId, action);
        
        if (applicableRules.isEmpty()) {
            log.debug("没有找到适用的规则: userId={}, action={}", userId, action);
            return BigDecimal.ZERO;
        }
        
        // 选择优先级最高的规则
        CoinRule rule = applicableRules.get(0);
        
        // 验证规则限制
        if (!validateRuleLimit(userId, rule)) {
            log.debug("规则限制验证失败: userId={}, ruleId={}", userId, rule.getId());
            return BigDecimal.ZERO;
        }
        
        // 计算奖励
        BigDecimal rewardAmount = calculateReward(userId, action, context);
        
        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 增加用户金币
            coinAccountService.addCoins(userId, rewardAmount, "RULE_REWARD", 
                "规则奖励: " + rule.getRuleName());
            
            // 更新规则统计
            rule.incrementExecution(rewardAmount);
            coinRuleRepository.save(rule);
            
            log.info("规则执行成功: userId={}, ruleId={}, reward={}", userId, rule.getId(), rewardAmount);
        }
        
        return rewardAmount;
    }

    @Override
    @Transactional
    public List<BigDecimal> batchExecuteRules(List<Long> userIds, String action, String context) {
        log.info("批量执行金币规则: userCount={}, action={}", userIds.size(), action);
        
        List<BigDecimal> results = new ArrayList<>();
        
        for (Long userId : userIds) {
            try {
                BigDecimal reward = executeRule(userId, action, context);
                results.add(reward);
            } catch (Exception e) {
                log.error("批量执行规则失败: userId={}, action={}", userId, action, e);
                results.add(BigDecimal.ZERO);
            }
        }
        
        return results;
    }

    @Override
    public BigDecimal calculateReward(Long userId, String action, String context) {
        log.debug("计算规则奖励: userId={}, action={}", userId, action);
        
        List<CoinRule> applicableRules = getApplicableRules(userId, action);
        
        if (applicableRules.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        CoinRule rule = applicableRules.get(0);
        
        switch (rule.getRuleType()) {
            case FIXED:
                return rule.getRewardAmount();
            case RANDOM:
                return calculateRandomReward(rule);
            case PROGRESSIVE:
                return calculateProgressiveReward(userId, rule);
            case CONDITIONAL:
                return calculateConditionalReward(userId, rule, context);
            case MULTIPLIER:
                return calculateMultiplierReward(userId, rule, context);
            default:
                return rule.getRewardAmount();
        }
    }

    /**
     * 计算随机奖励
     */
    private BigDecimal calculateRandomReward(CoinRule rule) {
        if (rule.getMinReward() != null && rule.getMaxReward() != null) {
            double min = rule.getMinReward().doubleValue();
            double max = rule.getMaxReward().doubleValue();
            double randomValue = min + (max - min) * random.nextDouble();
            return BigDecimal.valueOf(randomValue);
        }
        return rule.getRewardAmount();
    }

    /**
     * 计算递增奖励
     */
    private BigDecimal calculateProgressiveReward(Long userId, CoinRule rule) {
        // 简单实现：基础奖励 + 执行次数 * 0.1
        long executionCount = rule.getExecutionCount();
        BigDecimal bonus = BigDecimal.valueOf(executionCount * 0.1);
        return rule.getRewardAmount().add(bonus);
    }

    /**
     * 计算条件奖励
     */
    private BigDecimal calculateConditionalReward(Long userId, CoinRule rule, String context) {
        // 简单实现：根据条件返回不同奖励
        return rule.getRewardAmount();
    }

    /**
     * 计算倍数奖励
     */
    private BigDecimal calculateMultiplierReward(Long userId, CoinRule rule, String context) {
        // 简单实现：基础奖励 * 2
        return rule.getRewardAmount().multiply(BigDecimal.valueOf(2));
    }

    @Override
    public boolean checkRuleConditions(Long userId, CoinRule rule, String context) {
        log.debug("检查规则条件: userId={}, ruleId={}", userId, rule.getId());

        // 检查规则是否可执行
        if (!rule.canExecute()) {
            return false;
        }

        // 简单实现：总是返回true
        // 实际实现中应该根据rule.getConditions()解析JSON条件进行验证
        return true;
    }

    @Override
    public boolean validateRuleLimit(Long userId, CoinRule rule) {
        log.debug("验证规则限制: userId={}, ruleId={}", userId, rule.getId());

        // 简单实现：检查总限制
        if (rule.getTotalLimit() != null && rule.getExecutionCount() >= rule.getTotalLimit()) {
            return false;
        }

        // 实际实现中应该检查用户的每日、每周、每月限制
        return true;
    }

    @Override
    public Optional<CoinRule> getCheckinRule() {
        log.debug("获取签到规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("CHECKIN");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public Optional<CoinRule> getInviteRule() {
        log.debug("获取邀请规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("INVITE");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public Optional<CoinRule> getShareRule() {
        log.debug("获取分享规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("SHARE");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public Optional<CoinRule> getCommentRule() {
        log.debug("获取评论规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("COMMENT");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public Optional<CoinRule> getLikeRule() {
        log.debug("获取点赞规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("LIKE");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public Optional<CoinRule> getPublishRule() {
        log.debug("获取发布规则");

        List<CoinRule> rules = coinRuleRepository.findActiveRulesByAction("PUBLISH");
        return rules.isEmpty() ? Optional.empty() : Optional.of(rules.get(0));
    }

    @Override
    public CoinRuleDTO.RuleStatistics getRuleStatistics() {
        log.debug("获取规则统计信息");

        long totalRules = coinRuleRepository.count();
        long activeRules = coinRuleRepository.countByRuleStatus(CoinRule.RuleStatus.ACTIVE);
        long inactiveRules = coinRuleRepository.countByRuleStatus(CoinRule.RuleStatus.INACTIVE);
        long expiredRules = coinRuleRepository.countByRuleStatus(CoinRule.RuleStatus.EXPIRED);

        Object[] executionStats = coinRuleRepository.getExecutionStatistics();
        Long totalExecutions = executionStats != null && executionStats[0] != null ?
            ((Number) executionStats[0]).longValue() : 0L;
        BigDecimal totalRewards = executionStats != null && executionStats[1] != null ?
            (BigDecimal) executionStats[1] : BigDecimal.ZERO;

        return CoinRuleDTO.RuleStatistics.builder()
                .totalRules(totalRules)
                .activeRules(activeRules)
                .suspendedRules(inactiveRules) // 使用正确的字段名
                .expiredRules(expiredRules)
                .totalExecutions(totalExecutions)
                .totalRewards(totalRewards)
                .averageReward(totalExecutions > 0 ?
                    totalRewards.divide(BigDecimal.valueOf(totalExecutions), 2, BigDecimal.ROUND_HALF_UP) :
                    BigDecimal.ZERO)
                .build();
    }

    @Override
    public CoinRuleDTO.ExecutionStatistics getExecutionStatistics(Long ruleId) {
        log.debug("获取规则执行统计: ruleId={}", ruleId);

        CoinRule rule = coinRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));

        return CoinRuleDTO.ExecutionStatistics.builder()
                .ruleId(ruleId)
                .executionCount(rule.getExecutionCount())
                .successCount(rule.getExecutionCount()) // 假设所有执行都成功
                .failedCount(0L)
                .totalReward(rule.getTotalReward())
                .averageReward(rule.getExecutionCount() > 0 ?
                    rule.getTotalReward().divide(BigDecimal.valueOf(rule.getExecutionCount()), 2, BigDecimal.ROUND_HALF_UP) :
                    BigDecimal.ZERO)
                .maxReward(rule.getTotalReward())
                .minReward(rule.getTotalReward())
                .successRate(100.0)
                .lastExecutionAt(rule.getUpdatedAt())
                .build();
    }

    @Override
    public List<CoinRuleDTO.UsageTrend> getRuleUsageTrend(Long ruleId, int days) {
        log.debug("获取规则使用趋势: ruleId={}, days={}", ruleId, days);

        // 简单实现：返回空列表
        // 实际实现中应该查询规则执行历史数据
        return new ArrayList<>();
    }

    @Override
    public CoinRuleDTO.UserRuleStatistics getUserRuleStatistics(Long userId) {
        log.debug("获取用户规则统计: userId={}", userId);

        // 简单实现：返回默认统计
        // 实际实现中应该查询用户的规则执行历史
        return CoinRuleDTO.UserRuleStatistics.builder()
                .userId(userId)
                .participatedRules(0L)
                .totalExecutions(0L)
                .totalRewards(BigDecimal.ZERO)
                .averageReward(BigDecimal.ZERO)
                .favoriteRule("无")
                .lastParticipationAt(null)
                .build();
    }

    @Override
    public List<CoinRuleDTO.RuleTemplate> getRuleTemplates() {
        log.debug("获取规则模板");

        List<CoinRuleDTO.RuleTemplate> templates = new ArrayList<>();

        // 添加一些预定义模板
        templates.add(CoinRuleDTO.RuleTemplate.builder()
                .templateId("CHECKIN_TEMPLATE")
                .templateName("签到奖励模板")
                .description("用户每日签到获得金币奖励")
                .ruleType(CoinRule.RuleType.FIXED)
                .defaultConfig(Map.of("action", "CHECKIN", "defaultReward", BigDecimal.valueOf(10)))
                .parameters(new ArrayList<>())
                .build());

        templates.add(CoinRuleDTO.RuleTemplate.builder()
                .templateId("INVITE_TEMPLATE")
                .templateName("邀请奖励模板")
                .description("用户邀请好友获得金币奖励")
                .ruleType(CoinRule.RuleType.FIXED)
                .defaultConfig(Map.of("action", "INVITE", "defaultReward", BigDecimal.valueOf(50)))
                .parameters(new ArrayList<>())
                .build());

        return templates;
    }

    @Override
    @Transactional
    public CoinRule createRuleFromTemplate(String templateId, CoinRuleDTO.TemplateParams params) {
        log.info("根据模板创建规则: templateId={}", templateId);

        // 简单实现：根据模板ID创建基础规则
        // 从参数映射中获取值
        Map<String, Object> paramMap = params.getParams();
        String ruleName = (String) paramMap.get("ruleName");
        String description = (String) paramMap.get("description");
        BigDecimal rewardAmount = (BigDecimal) paramMap.get("rewardAmount");
        String createdBy = (String) paramMap.getOrDefault("createdBy", "SYSTEM");

        // 由于 CoinRule 实体可能没有 @Builder 注解，使用构造函数创建
        CoinRule rule = new CoinRule();
        rule.setRuleName(ruleName);
        rule.setDescription(description);
        rule.setRewardAmount(rewardAmount);
        rule.setCreatedBy(createdBy);

        switch (templateId) {
            case "CHECKIN_TEMPLATE":
                rule.setRuleType(CoinRule.RuleType.FIXED);
                rule.setAction("CHECKIN");
                rule.setDailyLimit(1);
                break;
            case "INVITE_TEMPLATE":
                rule.setRuleType(CoinRule.RuleType.FIXED);
                rule.setAction("INVITE");
                break;
            default:
                throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }

        return coinRuleRepository.save(rule);
    }

    @Override
    public String exportRuleConfig() {
        log.debug("导出规则配置");

        // 简单实现：返回JSON格式的规则配置
        List<CoinRule> rules = coinRuleRepository.findAll();
        // 实际实现中应该将规则序列化为JSON
        return "{}"; // 占位符
    }

    @Override
    @Transactional
    public void importRuleConfig(String configData) {
        log.info("导入规则配置");

        // 简单实现：解析JSON并创建规则
        // 实际实现中应该解析configData并批量创建规则
        log.info("规则配置导入完成");
    }

    @Override
    public CoinRuleDTO.ValidationResult validateRuleConfig(CoinRuleDTO.CreateRequest request) {
        log.debug("验证规则配置: ruleName={}", request.getRuleName());

        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 验证规则名称
        if (request.getRuleName() == null || request.getRuleName().trim().isEmpty()) {
            errors.add("规则名称不能为空");
        } else if (coinRuleRepository.existsByRuleName(request.getRuleName())) {
            errors.add("规则名称已存在");
        }

        // 验证奖励金额
        if (request.getRewardAmount() == null || request.getRewardAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("奖励金额必须大于0");
        }

        // 验证动作
        if (request.getAction() == null || request.getAction().trim().isEmpty()) {
            errors.add("触发动作不能为空");
        }

        // 验证时间范围
        if (request.getEffectiveFrom() != null && request.getEffectiveUntil() != null &&
            request.getEffectiveFrom().isAfter(request.getEffectiveUntil())) {
            errors.add("生效时间不能晚于失效时间");
        }

        // 验证随机奖励范围
        if (request.getRuleType() == CoinRule.RuleType.RANDOM) {
            if (request.getMinReward() == null || request.getMaxReward() == null) {
                errors.add("随机奖励类型必须设置最小和最大奖励金额");
            } else if (request.getMinReward().compareTo(request.getMaxReward()) > 0) {
                errors.add("最小奖励不能大于最大奖励");
            }
        }

        return CoinRuleDTO.ValidationResult.builder()
                .isValid(errors.isEmpty())
                .errors(errors)
                .warnings(warnings)
                .build();
    }

    @Override
    public List<CoinRule> checkRuleConflicts(CoinRule rule) {
        log.debug("检查规则冲突: ruleId={}", rule.getId());

        return coinRuleRepository.findConflictingRules(
            rule.getAction(),
            rule.getPriority(),
            rule.getId() != null ? rule.getId() : -1L
        );
    }

    @Override
    public CoinRuleDTO.TestResult testRuleExecution(Long ruleId, Long userId, String context) {
        log.debug("测试规则执行: ruleId={}, userId={}", ruleId, userId);

        long startTime = System.currentTimeMillis();

        try {
            CoinRule rule = coinRuleRepository.findById(ruleId)
                    .orElseThrow(() -> new BusinessException(SocialErrorCode.INVALID_PARAMETER));

            // 检查规则条件
            boolean conditionsPass = checkRuleConditions(userId, rule, context);
            if (!conditionsPass) {
                return CoinRuleDTO.TestResult.builder()
                        .success(false)
                        .rewardAmount(BigDecimal.ZERO)
                        .executionTime(System.currentTimeMillis() - startTime)
                        .errorMessage("规则条件不满足")
                        .build();
            }

            // 验证规则限制
            boolean limitPass = validateRuleLimit(userId, rule);
            if (!limitPass) {
                return CoinRuleDTO.TestResult.builder()
                        .success(false)
                        .rewardAmount(BigDecimal.ZERO)
                        .executionTime(System.currentTimeMillis() - startTime)
                        .errorMessage("规则限制验证失败")
                        .build();
            }

            // 计算奖励
            BigDecimal rewardAmount = calculateReward(userId, rule.getAction(), context);

            return CoinRuleDTO.TestResult.builder()
                    .success(true)
                    .rewardAmount(rewardAmount)
                    .executionTime(System.currentTimeMillis() - startTime)
                    .errorMessage(null)
                    .details(Map.of("message", "测试成功"))
                    .build();

        } catch (Exception e) {
            log.error("测试规则执行失败", e);
            return CoinRuleDTO.TestResult.builder()
                    .success(false)
                    .rewardAmount(BigDecimal.ZERO)
                    .executionTime(System.currentTimeMillis() - startTime)
                    .errorMessage("测试失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    @Transactional
    public int cleanupExpiredRules() {
        log.info("清理过期规则");

        Instant now = Instant.now();
        List<CoinRule> expiredRules = coinRuleRepository.findExpiredRules(now);

        int cleanedCount = 0;
        for (CoinRule rule : expiredRules) {
            rule.setRuleStatus(CoinRule.RuleStatus.EXPIRED);
            coinRuleRepository.save(rule);
            cleanedCount++;
        }

        log.info("过期规则清理完成: 处理了{}个规则", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional
    public void repairRuleData() {
        log.info("修复规则数据");

        List<CoinRule> allRules = coinRuleRepository.findAll();

        for (CoinRule rule : allRules) {
            try {
                // 检查并修复规则状态
                if (rule.getEffectiveUntil() != null && rule.getEffectiveUntil().isBefore(Instant.now())) {
                    if (rule.getRuleStatus() == CoinRule.RuleStatus.ACTIVE) {
                        rule.setRuleStatus(CoinRule.RuleStatus.EXPIRED);
                        coinRuleRepository.save(rule);
                    }
                }

                // 其他数据修复逻辑...

            } catch (Exception e) {
                log.error("修复规则数据失败: ruleId={}", rule.getId(), e);
            }
        }

        log.info("规则数据修复完成");
    }

    @Override
    @Transactional
    public void recalculateRuleStatistics() {
        log.info("重新计算规则统计");

        // 简单实现：重置所有规则的统计数据
        List<CoinRule> allRules = coinRuleRepository.findAll();

        for (CoinRule rule : allRules) {
            try {
                coinRuleRepository.resetRuleStatistics(rule.getId());
            } catch (Exception e) {
                log.error("重置规则统计失败: ruleId={}", rule.getId(), e);
            }
        }

        log.info("规则统计重新计算完成");
    }

    @Override
    @Transactional
    public void syncRuleStatus() {
        log.info("同步规则状态");

        Instant now = Instant.now();
        List<CoinRule> rulesNeedingSync = coinRuleRepository.findRulesNeedingStatusSync(now);

        int syncedCount = 0;
        for (CoinRule rule : rulesNeedingSync) {
            try {
                if (rule.getEffectiveFrom() != null && rule.getEffectiveFrom().isAfter(now)) {
                    // 规则还未生效
                    rule.setRuleStatus(CoinRule.RuleStatus.INACTIVE);
                } else if (rule.getEffectiveUntil() != null && rule.getEffectiveUntil().isBefore(now)) {
                    // 规则已过期
                    rule.setRuleStatus(CoinRule.RuleStatus.EXPIRED);
                }

                coinRuleRepository.save(rule);
                syncedCount++;

            } catch (Exception e) {
                log.error("同步规则状态失败: ruleId={}", rule.getId(), e);
            }
        }

        log.info("规则状态同步完成: 同步了{}个规则", syncedCount);
    }
}
