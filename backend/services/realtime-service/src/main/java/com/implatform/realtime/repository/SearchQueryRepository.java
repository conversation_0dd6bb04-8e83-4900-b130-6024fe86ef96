package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchQuery;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 搜索查询Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchQueryRepository extends R2dbcRepository<SearchQuery, Long> {

    /**
     * 根据查询哈希值查找查询
     */
    @Query("SELECT * FROM search_queries WHERE query_hash = :queryHash")
    Mono<SearchQuery> findByQueryHash(@Param("queryHash") String queryHash);

    /**
     * 根据用户ID查找查询历史
     */
    @Query("SELECT * FROM search_queries WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<SearchQuery> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据搜索类型查找查询
     */
    @Query("SELECT * FROM search_queries WHERE search_type = :searchType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<SearchQuery> findBySearchTypeOrderByCreatedAtDesc(@Param("searchType") String searchType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据搜索范围查找查询
     */
    @Query("SELECT * FROM search_queries WHERE search_scope = :searchScope ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<SearchQuery> findBySearchScopeOrderByCreatedAtDesc(@Param("searchScope") String searchScope, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据查询状态查找查询
     */
    @Query("SELECT * FROM search_queries WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<SearchQuery> findByStatusOrderByCreatedAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);
    
    /**
     * 查找慢查询
     */
    @Query("SELECT * FROM search_queries WHERE execution_time_ms > :threshold ORDER BY execution_time_ms DESC")
    Flux<SearchQuery> findSlowQueries(@Param("threshold") Long threshold);

    /**
     * 查找失败的查询
     */
    @Query("SELECT * FROM search_queries WHERE status = :status ORDER BY created_at DESC")
    Flux<SearchQuery> findByStatusOrderByCreatedAtDesc(@Param("status") String status);

    /**
     * 根据时间范围查找查询
     */
    @Query("SELECT * FROM search_queries WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<SearchQuery> findByTimeRange(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 查找热门查询文本
     */
    @Query("SELECT query_text, COUNT(*) as query_count FROM search_queries WHERE created_at >= :since GROUP BY query_text HAVING COUNT(*) >= :threshold ORDER BY query_count DESC")
    Flux<Object[]> findPopularQueries(@Param("since") Instant since, @Param("threshold") long threshold);

    /**
     * 查找用户的频繁查询
     */
    @Query("SELECT query_text, COUNT(*) as query_count FROM search_queries WHERE user_id = :userId AND created_at >= :since GROUP BY query_text HAVING COUNT(*) >= :threshold ORDER BY query_count DESC")
    Flux<Object[]> findUserFrequentQueries(
        @Param("userId") Long userId,
        @Param("since") Instant since,
        @Param("threshold") long threshold);

    /**
     * 统计查询性能
     */
    @Query("SELECT AVG(execution_time_ms), MIN(execution_time_ms), MAX(execution_time_ms), COUNT(*) FROM search_queries WHERE status = :status AND created_at >= :since")
    Mono<Object[]> getQueryPerformanceStats(
        @Param("status") String status,
        @Param("since") Instant since);

    /**
     * 统计各搜索类型的使用情况
     */
    @Query("SELECT search_type, COUNT(*) FROM search_queries WHERE created_at >= :since GROUP BY search_type ORDER BY COUNT(*) DESC")
    Flux<Object[]> getSearchTypeUsage(@Param("since") Instant since);
    
    /**
     * 统计各搜索范围的使用情况
     */
    @Query("SELECT search_scope, COUNT(*) FROM search_queries WHERE created_at >= :since GROUP BY search_scope ORDER BY COUNT(*) DESC")
    Flux<Object[]> getSearchScopeUsage(@Param("since") Instant since);

    /**
     * 统计各搜索引擎的使用情况
     */
    @Query("SELECT search_engine, COUNT(*), AVG(execution_time_ms) FROM search_queries WHERE created_at >= :since GROUP BY search_engine ORDER BY COUNT(*) DESC")
    Flux<Object[]> getSearchEngineUsage(@Param("since") Instant since);

    /**
     * 查找无结果查询
     */
    @Query("SELECT * FROM search_queries WHERE total_results = 0 AND status = :status ORDER BY created_at DESC")
    Flux<SearchQuery> findNoResultQueries(@Param("status") String status);

    /**
     * 查找高结果量查询
     */
    @Query("SELECT * FROM search_queries WHERE total_results > :threshold ORDER BY total_results DESC")
    Flux<SearchQuery> findHighVolumeQueries(@Param("threshold") Long threshold);

    /**
     * 查找缓存命中率统计
     */
    @Query("SELECT COUNT(CASE WHEN cache_hit = true THEN 1 END) * 100.0 / COUNT(*) as hit_rate, COUNT(*) as total_queries FROM search_queries WHERE use_cache = true AND created_at >= :since")
    Mono<Object[]> getCacheHitRate(@Param("since") Instant since);

    /**
     * 查找用户搜索模式
     */
    @Query("SELECT user_id, COUNT(*) as query_count, AVG(execution_time_ms) as avg_time, COUNT(DISTINCT query_text) as unique_queries FROM search_queries WHERE user_id IS NOT NULL AND created_at >= :since GROUP BY user_id HAVING COUNT(*) >= :threshold ORDER BY query_count DESC")
    Flux<Object[]> getUserSearchPatterns(@Param("since") Instant since, @Param("threshold") long threshold);

    /**
     * 查找相似查询
     */
    @Query("SELECT * FROM search_queries WHERE query_text LIKE CONCAT('%', :queryText, '%') AND id != :excludeId ORDER BY created_at DESC")
    Flux<SearchQuery> findSimilarQueries(@Param("queryText") String queryText, @Param("excludeId") Long excludeId);

    /**
     * 查找IP地址的查询活动
     */
    @Query("SELECT * FROM search_queries WHERE ip_address = :ipAddress ORDER BY created_at DESC")
    Flux<SearchQuery> findByIpAddressOrderByCreatedAtDesc(@Param("ipAddress") String ipAddress);

    /**
     * 查找会话的查询活动
     */
    @Query("SELECT * FROM search_queries WHERE session_id = :sessionId ORDER BY created_at DESC")
    Flux<SearchQuery> findBySessionIdOrderByCreatedAtDesc(@Param("sessionId") String sessionId);
    
    /**
     * 统计查询成功率
     */
    @Query("SELECT COUNT(CASE WHEN status = :successStatus THEN 1 END) * 100.0 / COUNT(*) as success_rate FROM search_queries WHERE created_at >= :since")
    Mono<Double> getQuerySuccessRate(@Param("successStatus") String successStatus, @Param("since") Instant since);

    /**
     * 查找需要优化的查询
     */
    @Query("SELECT * FROM search_queries WHERE (execution_time_ms > :timeThreshold OR total_results > :resultThreshold) AND status = :status ORDER BY execution_time_ms DESC")
    Flux<SearchQuery> findQueriesNeedingOptimization(
        @Param("timeThreshold") Long timeThreshold,
        @Param("resultThreshold") Long resultThreshold,
        @Param("status") String status);

    /**
     * 获取查询趋势数据
     */
    @Query("SELECT DATE(created_at), COUNT(*), AVG(execution_time_ms) FROM search_queries WHERE created_at >= :since GROUP BY DATE(created_at) ORDER BY DATE(created_at)")
    Flux<Object[]> getQueryTrends(@Param("since") Instant since);

    /**
     * 查找错误查询模式
     */
    @Query("SELECT error_message, COUNT(*) FROM search_queries WHERE status = :failedStatus AND error_message IS NOT NULL AND created_at >= :since GROUP BY error_message ORDER BY COUNT(*) DESC")
    Flux<Object[]> getErrorPatterns(@Param("failedStatus") String failedStatus, @Param("since") Instant since);

    /**
     * 查找超时查询
     */
    @Query("SELECT * FROM search_queries WHERE status = :status AND execution_time_ms > :timeoutThreshold ORDER BY execution_time_ms DESC")
    Flux<SearchQuery> findByStatusAndExecutionTimeMsGreaterThanOrderByExecutionTimeMsDesc(
        @Param("status") String status, @Param("timeoutThreshold") Long timeoutThreshold);

    /**
     * 统计用户查询活跃度
     */
    @Query("SELECT DATE(created_at), COUNT(DISTINCT user_id) as active_users FROM search_queries WHERE user_id IS NOT NULL AND created_at >= :since GROUP BY DATE(created_at) ORDER BY DATE(created_at)")
    Flux<Object[]> getUserActivityTrends(@Param("since") Instant since);

    /**
     * 查找重复查询
     */
    @Query("SELECT query_hash, COUNT(*) as duplicate_count FROM search_queries WHERE created_at >= :since GROUP BY query_hash HAVING COUNT(*) > 1 ORDER BY duplicate_count DESC")
    Flux<Object[]> findDuplicateQueries(@Param("since") Instant since);
    
    /**
     * 获取查询复杂度分析
     */
    @Query("SELECT search_type, AVG(execution_time_ms) as avg_time, AVG(total_results) as avg_results FROM search_queries WHERE status = :status AND created_at >= :since GROUP BY search_type ORDER BY avg_time DESC")
    Flux<Object[]> getQueryComplexityAnalysis(
        @Param("status") String status,
        @Param("since") Instant since);

    /**
     * 删除过期查询记录
     */
    @Query("DELETE FROM search_queries WHERE created_at < :cutoffTime")
    Mono<Void> deleteByCreatedAtBefore(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 检查查询是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM search_queries WHERE query_hash = :queryHash AND created_at > :since")
    Mono<Boolean> existsByQueryHashAndCreatedAtAfter(@Param("queryHash") String queryHash, @Param("since") Instant since);

    /**
     * 统计总查询数
     */
    @Query("SELECT COUNT(*) FROM search_queries WHERE created_at >= :since")
    Mono<Long> countQueriesSince(@Param("since") Instant since);

    /**
     * 获取最近的查询
     */
    @Query("SELECT * FROM search_queries ORDER BY created_at DESC LIMIT 10")
    Flux<SearchQuery> findTop10ByOrderByCreatedAtDesc();
}
