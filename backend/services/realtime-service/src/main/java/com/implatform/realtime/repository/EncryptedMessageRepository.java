package com.implatform.realtime.repository;

import com.implatform.realtime.entity.EncryptedMessage;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 加密消息Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface EncryptedMessageRepository extends R2dbcRepository<EncryptedMessage, Long> {

    /**
     * 根据消息ID查找加密消息
     */
    @Query("SELECT * FROM encrypted_messages WHERE message_id = :messageId")
    Mono<EncryptedMessage> findByMessageId(@Param("messageId") Long messageId);

    /**
     * 根据会话ID查找加密消息
     */
    @Query("SELECT * FROM encrypted_messages WHERE session_id = :sessionId ORDER BY created_at DESC")
    Flux<EncryptedMessage> findBySessionIdOrderByCreatedAtDesc(@Param("sessionId") String sessionId);

    /**
     * 分页查找会话的加密消息
     */
    @Query("SELECT * FROM encrypted_messages WHERE session_id = :sessionId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<EncryptedMessage> findBySessionIdOrderByCreatedAtDesc(@Param("sessionId") String sessionId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找发送者的加密消息
     */
    @Query("SELECT * FROM encrypted_messages WHERE sender_id = :senderId ORDER BY created_at DESC")
    Flux<EncryptedMessage> findBySenderIdOrderByCreatedAtDesc(@Param("senderId") Long senderId);

    /**
     * 查找接收者的加密消息
     */
    @Query("SELECT * FROM encrypted_messages WHERE receiver_id = :receiverId ORDER BY created_at DESC")
    Flux<EncryptedMessage> findByReceiverIdOrderByCreatedAtDesc(@Param("receiverId") Long receiverId);

    /**
     * 查找用户的所有加密消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.senderId = :userId OR em.receiverId = :userId ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findByUserId(@Param("userId") Long userId);

    /**
     * 分页查找用户的加密消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.senderId = :userId OR em.receiverId = :userId ORDER BY em.createdAt DESC")
    Page<EncryptedMessage> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找未送达的消息
     */
    List<EncryptedMessage> findByIsDeliveredFalseOrderByCreatedAtAsc();

    /**
     * 查找未读的消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.receiverId = :userId AND em.isRead = false ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findUnreadMessagesByUserId(@Param("userId") Long userId);

    /**
     * 查找指定时间范围内的加密消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.createdAt BETWEEN :startTime AND :endTime ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找支持前向安全的消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.ratchetPublicKey IS NOT NULL ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findForwardSecureMessages();

    /**
     * 查找需要重新加密的消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.messageKeyIndex > :maxKeyIndex ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findMessagesNeedingReencryption(@Param("maxKeyIndex") Integer maxKeyIndex);

    /**
     * 统计会话的消息数量
     */
    @Query("SELECT COUNT(em) FROM EncryptedMessage em WHERE em.sessionId = :sessionId")
    Long countBySessionId(@Param("sessionId") String sessionId);

    /**
     * 统计用户的消息数量
     */
    @Query("SELECT COUNT(em) FROM EncryptedMessage em WHERE em.senderId = :userId OR em.receiverId = :userId")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 统计未读消息数量
     */
    @Query("SELECT COUNT(em) FROM EncryptedMessage em WHERE em.receiverId = :userId AND em.isRead = false")
    Long countUnreadMessagesByUserId(@Param("userId") Long userId);

    /**
     * 统计未送达消息数量
     */
    @Query("SELECT COUNT(em) FROM EncryptedMessage em WHERE em.isDelivered = false")
    Long countUndeliveredMessages();

    /**
     * 批量标记为已送达
     */
    @Modifying
    @Query("UPDATE EncryptedMessage em SET em.isDelivered = true, em.updatedAt = CURRENT_TIMESTAMP WHERE em.id IN :messageIds")
    int markAsDelivered(@Param("messageIds") List<Long> messageIds);

    /**
     * 批量标记为已读
     */
    @Modifying
    @Query("UPDATE EncryptedMessage em SET em.isRead = true, em.isDelivered = true, em.updatedAt = CURRENT_TIMESTAMP WHERE em.id IN :messageIds")
    int markAsRead(@Param("messageIds") List<Long> messageIds);

    /**
     * 标记用户的所有消息为已读
     */
    @Modifying
    @Query("UPDATE EncryptedMessage em SET em.isRead = true, em.isDelivered = true, em.updatedAt = CURRENT_TIMESTAMP WHERE em.receiverId = :userId AND em.isRead = false")
    int markAllAsReadByUserId(@Param("userId") Long userId);

    /**
     * 标记会话的所有消息为已读
     */
    @Modifying
    @Query("UPDATE EncryptedMessage em SET em.isRead = true, em.isDelivered = true, em.updatedAt = CURRENT_TIMESTAMP WHERE em.sessionId = :sessionId AND em.receiverId = :userId AND em.isRead = false")
    int markSessionMessagesAsRead(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 查找消息统计信息
     */
    @Query("SELECT " +
           "COUNT(em) as totalMessages, " +
           "COUNT(CASE WHEN em.isDelivered = true THEN 1 END) as deliveredMessages, " +
           "COUNT(CASE WHEN em.isRead = true THEN 1 END) as readMessages, " +
           "COUNT(CASE WHEN em.ratchetPublicKey IS NOT NULL THEN 1 END) as forwardSecureMessages " +
           "FROM EncryptedMessage em WHERE em.sessionId = :sessionId")
    Object[] getSessionMessageStatistics(@Param("sessionId") String sessionId);

    /**
     * 查找用户消息统计信息
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN em.senderId = :userId THEN 1 END) as sentMessages, " +
           "COUNT(CASE WHEN em.receiverId = :userId THEN 1 END) as receivedMessages, " +
           "COUNT(CASE WHEN em.receiverId = :userId AND em.isRead = false THEN 1 END) as unreadMessages " +
           "FROM EncryptedMessage em WHERE em.senderId = :userId OR em.receiverId = :userId")
    Object[] getUserMessageStatistics(@Param("userId") Long userId);

    /**
     * 查找最近的加密消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.createdAt >= :since ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findRecentMessages(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查找会话的最新消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.sessionId = :sessionId ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findLatestMessageBySessionId(@Param("sessionId") String sessionId, Pageable pageable);

    /**
     * 查找用户的最新消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE em.senderId = :userId OR em.receiverId = :userId ORDER BY em.createdAt DESC")
    List<EncryptedMessage> findLatestMessageByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找消息创建统计
     */
    @Query("SELECT DATE(em.createdAt) as date, COUNT(em) as count FROM EncryptedMessage em WHERE em.createdAt >= :since GROUP BY DATE(em.createdAt) ORDER BY date")
    List<Object[]> getMessageCreationStatistics(@Param("since") LocalDateTime since);

    /**
     * 查找加密级别统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN em.ratchetPublicKey IS NOT NULL THEN 1 END) as forwardSecure, " +
           "COUNT(CASE WHEN em.ratchetPublicKey IS NULL THEN 1 END) as standard " +
           "FROM EncryptedMessage em")
    Object[] getEncryptionLevelStatistics();

    /**
     * 删除旧的加密消息
     */
    @Modifying
    @Query("DELETE FROM EncryptedMessage em WHERE em.createdAt < :cutoffTime")
    int deleteOldMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 检查消息是否存在
     */
    @Query("SELECT COUNT(em) > 0 FROM EncryptedMessage em WHERE em.messageId = :messageId")
    boolean existsByMessageId(@Param("messageId") Long messageId);

    /**
     * 查找大消息
     */
    @Query("SELECT em FROM EncryptedMessage em WHERE LENGTH(em.encryptedContent) > :sizeThreshold ORDER BY LENGTH(em.encryptedContent) DESC")
    List<EncryptedMessage> findLargeMessages(@Param("sizeThreshold") Integer sizeThreshold, Pageable pageable);

    /**
     * 统计消息大小分布
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN LENGTH(em.encryptedContent) <= 1000 THEN 1 END) as small, " +
           "COUNT(CASE WHEN LENGTH(em.encryptedContent) BETWEEN 1001 AND 10000 THEN 1 END) as medium, " +
           "COUNT(CASE WHEN LENGTH(em.encryptedContent) > 10000 THEN 1 END) as large " +
           "FROM EncryptedMessage em")
    Object[] getMessageSizeDistribution();
}
