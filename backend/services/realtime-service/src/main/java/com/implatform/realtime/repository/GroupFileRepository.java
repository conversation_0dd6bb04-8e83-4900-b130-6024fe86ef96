package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupFile;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 群文件Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupFileRepository extends R2dbcRepository<GroupFile, Long> {

    /**
     * 根据群组ID查找文件
     */
    @Query("SELECT * FROM group_files WHERE group_id = :groupId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupFile> findByGroupIdAndStatusOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据群组ID和文件夹ID查找文件
     */
    @Query("SELECT * FROM group_files WHERE group_id = :groupId AND folder_id = :folderId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupFile> findByGroupIdAndFolderIdAndStatusOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("folderId") Long folderId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据群组ID查找根目录文件
     */
    @Query("SELECT * FROM group_files WHERE group_id = :groupId AND folder_id IS NULL AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupFile> findByGroupIdAndFolderIdIsNullAndStatusOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据文件类型查找文件
     */
    @Query("SELECT * FROM group_files WHERE group_id = :groupId AND file_type = :fileType AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupFile> findByGroupIdAndFileTypeAndStatusOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("fileType") String fileType, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据上传者查找文件
     */
    @Query("SELECT * FROM group_files WHERE group_id = :groupId AND uploader_id = :uploaderId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupFile> findByGroupIdAndUploaderIdAndStatusOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("uploaderId") Long uploaderId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 搜索群文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "(gf.fileName LIKE %:keyword% OR gf.originalName LIKE %:keyword% OR gf.description LIKE %:keyword%) " +
           "ORDER BY gf.createdAt DESC")
    Page<GroupFile> searchFiles(@Param("groupId") Long groupId, 
                               @Param("status") GroupFile.FileStatus status,
                               @Param("keyword") String keyword, 
                               Pageable pageable);

    /**
     * 根据文件MD5查找文件
     */
    List<GroupFile> findByGroupIdAndFileMd5AndStatus(Long groupId, String fileMd5, GroupFile.FileStatus status);

    /**
     * 查找大文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "gf.fileSize > :minSize ORDER BY gf.fileSize DESC")
    List<GroupFile> findLargeFiles(@Param("groupId") Long groupId, 
                                  @Param("status") GroupFile.FileStatus status,
                                  @Param("minSize") Long minSize, 
                                  Pageable pageable);

    /**
     * 查找热门文件（下载次数多）
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status " +
           "ORDER BY gf.downloadCount DESC")
    List<GroupFile> findPopularFiles(@Param("groupId") Long groupId, 
                                    @Param("status") GroupFile.FileStatus status, 
                                    Pageable pageable);

    /**
     * 查找最近上传的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "gf.createdAt >= :since ORDER BY gf.createdAt DESC")
    List<GroupFile> findRecentFiles(@Param("groupId") Long groupId, 
                                   @Param("status") GroupFile.FileStatus status,
                                   @Param("since") LocalDateTime since);

    /**
     * 查找即将过期的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = 'ACTIVE' AND " +
           "gf.expiryTime IS NOT NULL AND gf.expiryTime BETWEEN :now AND :threshold")
    List<GroupFile> findExpiringSoonFiles(@Param("groupId") Long groupId,
                                         @Param("now") LocalDateTime now, 
                                         @Param("threshold") LocalDateTime threshold);

    /**
     * 查找已过期的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.status = 'ACTIVE' AND gf.expiryTime IS NOT NULL AND " +
           "gf.expiryTime < :now")
    List<GroupFile> findExpiredFiles(@Param("now") LocalDateTime now);

    /**
     * 统计群组文件数量
     */
    long countByGroupIdAndStatus(Long groupId, GroupFile.FileStatus status);

    /**
     * 统计群组文件夹内文件数量
     */
    long countByGroupIdAndFolderIdAndStatus(Long groupId, Long folderId, GroupFile.FileStatus status);

    /**
     * 统计群组文件类型数量
     */
    long countByGroupIdAndFileTypeAndStatus(Long groupId, GroupFile.FileType fileType, GroupFile.FileStatus status);

    /**
     * 计算群组文件总大小
     */
    @Query("SELECT COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status")
    Long calculateTotalFileSize(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 计算文件夹内文件总大小
     */
    @Query("SELECT COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.folderId = :folderId AND gf.status = :status")
    Long calculateFolderFileSize(@Param("groupId") Long groupId, @Param("folderId") Long folderId, @Param("status") GroupFile.FileStatus status);

    /**
     * 更新文件下载计数
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.downloadCount = gf.downloadCount + 1 WHERE gf.id = :fileId")
    void incrementDownloadCount(@Param("fileId") Long fileId);

    /**
     * 批量更新文件状态
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.status = :status WHERE gf.groupId = :groupId AND gf.id IN :fileIds")
    void batchUpdateStatus(@Param("groupId") Long groupId, 
                          @Param("fileIds") List<Long> fileIds, 
                          @Param("status") GroupFile.FileStatus status);

    /**
     * 批量移动文件到文件夹
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.folderId = :folderId WHERE gf.groupId = :groupId AND gf.id IN :fileIds")
    void batchMoveToFolder(@Param("groupId") Long groupId, 
                          @Param("fileIds") List<Long> fileIds, 
                          @Param("folderId") Long folderId);

    /**
     * 自动过期文件
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.status = 'EXPIRED' WHERE gf.status = 'ACTIVE' AND " +
           "gf.expiryTime IS NOT NULL AND gf.expiryTime < :now")
    int autoExpireFiles(@Param("now") LocalDateTime now);

    /**
     * 获取文件类型统计
     */
    @Query("SELECT gf.fileType, COUNT(gf), COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId AND gf.status = :status GROUP BY gf.fileType ORDER BY COUNT(gf) DESC")
    List<Object[]> getFileTypeStatistics(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 获取文件状态统计
     */
    @Query("SELECT gf.status, COUNT(gf), COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId GROUP BY gf.status")
    List<Object[]> getFileStatusStatistics(@Param("groupId") Long groupId);

    /**
     * 获取上传者统计
     */
    @Query("SELECT gf.uploaderId, COUNT(gf), COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId AND gf.status = :status GROUP BY gf.uploaderId ORDER BY COUNT(gf) DESC")
    List<Object[]> getUploaderStatistics(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 获取文件上传趋势
     */
    @Query("SELECT DATE(gf.createdAt), COUNT(gf), COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId AND gf.createdAt >= :startDate " +
           "GROUP BY DATE(gf.createdAt) ORDER BY DATE(gf.createdAt)")
    List<Object[]> getFileUploadTrend(@Param("groupId") Long groupId, @Param("startDate") LocalDateTime startDate);

    /**
     * 获取文件下载趋势
     */
    @Query("SELECT DATE(gf.updatedAt), SUM(gf.downloadCount) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId AND gf.updatedAt >= :startDate " +
           "GROUP BY DATE(gf.updatedAt) ORDER BY DATE(gf.updatedAt)")
    List<Object[]> getFileDownloadTrend(@Param("groupId") Long groupId, @Param("startDate") LocalDateTime startDate);

    /**
     * 查找重复文件（相同MD5）
     */
    @Query("SELECT gf.fileMd5, COUNT(gf) FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "gf.fileMd5 IS NOT NULL GROUP BY gf.fileMd5 HAVING COUNT(gf) > 1")
    List<Object[]> findDuplicateFiles(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 查找孤立文件（文件夹被删除但文件还在）
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "gf.folderId IS NOT NULL AND gf.folderId NOT IN " +
           "(SELECT gfo.id FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = 'ACTIVE')")
    List<GroupFile> findOrphanedFiles(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 查找长时间未下载的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status AND " +
           "gf.downloadCount = 0 AND gf.createdAt < :threshold ORDER BY gf.createdAt ASC")
    List<GroupFile> findUndownloadedFiles(@Param("groupId") Long groupId, 
                                         @Param("status") GroupFile.FileStatus status,
                                         @Param("threshold") LocalDateTime threshold);

    /**
     * 删除群组的所有文件
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.status = 'DELETED' WHERE gf.groupId = :groupId")
    void softDeleteGroupFiles(@Param("groupId") Long groupId);

    /**
     * 删除文件夹内的所有文件
     */
    @Modifying
    @Query("UPDATE GroupFile gf SET gf.status = 'DELETED' WHERE gf.groupId = :groupId AND gf.folderId = :folderId")
    void softDeleteFolderFiles(@Param("groupId") Long groupId, @Param("folderId") Long folderId);

    /**
     * 物理删除已删除状态的文件记录
     */
    @Modifying
    @Query("DELETE FROM GroupFile gf WHERE gf.status = 'DELETED' AND gf.updatedAt < :cutoffTime")
    int physicalDeleteOldFiles(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取群组文件概览
     */
    @Query("SELECT COUNT(gf), COALESCE(SUM(gf.fileSize), 0), COALESCE(SUM(gf.downloadCount), 0), " +
           "COUNT(CASE WHEN gf.fileType = 'IMAGE' THEN 1 END), " +
           "COUNT(CASE WHEN gf.fileType = 'DOCUMENT' THEN 1 END), " +
           "COUNT(CASE WHEN gf.fileType = 'VIDEO' THEN 1 END) " +
           "FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status")
    List<Object[]> getGroupFileOverview(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 获取平均文件大小
     */
    @Query("SELECT AVG(gf.fileSize) FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status")
    Double getAverageFileSize(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 获取平均下载次数
     */
    @Query("SELECT AVG(gf.downloadCount) FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status")
    Double getAverageDownloadCount(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);

    /**
     * 查找最大的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status " +
           "ORDER BY gf.fileSize DESC")
    List<GroupFile> findLargestFiles(@Param("groupId") Long groupId, 
                                    @Param("status") GroupFile.FileStatus status, 
                                    Pageable pageable);

    /**
     * 查找最小的文件
     */
    @Query("SELECT gf FROM GroupFile gf WHERE gf.groupId = :groupId AND gf.status = :status " +
           "ORDER BY gf.fileSize ASC")
    List<GroupFile> findSmallestFiles(@Param("groupId") Long groupId, 
                                     @Param("status") GroupFile.FileStatus status, 
                                     Pageable pageable);

    /**
     * 清理过期的上传中文件
     */
    @Modifying
    @Query("DELETE FROM GroupFile gf WHERE gf.status = 'UPLOADING' AND gf.createdAt < :cutoffTime")
    int cleanupExpiredUploading(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取文件扩展名统计
     */
    @Query("SELECT gf.fileExtension, COUNT(gf) FROM GroupFile gf " +
           "WHERE gf.groupId = :groupId AND gf.status = :status AND gf.fileExtension IS NOT NULL " +
           "GROUP BY gf.fileExtension ORDER BY COUNT(gf) DESC")
    List<Object[]> getFileExtensionStatistics(@Param("groupId") Long groupId, @Param("status") GroupFile.FileStatus status);
}
