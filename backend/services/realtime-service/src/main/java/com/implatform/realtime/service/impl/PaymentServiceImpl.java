package com.implatform.realtime.service.impl;

import com.implatform.realtime.dto.PaymentDTO;
import com.implatform.realtime.entity.Payment;
import com.implatform.realtime.entity.Transaction;
import com.implatform.repository.PaymentRepository;
import com.implatform.repository.TransactionRepository;
import com.implatform.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付服务实现
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final PaymentRepository paymentRepository;
    private final TransactionRepository transactionRepository;

    @Override
    @Transactional
    public PaymentDTO.PaymentInfo createPayment(PaymentDTO.CreatePaymentRequest request) {
        log.info("创建支付订单: userId={}, amount={}, method={}", 
                request.getUserId(), request.getAmount(), request.getPaymentMethod());

        // 生成支付订单号
        String paymentOrderNo = generatePaymentOrderNo();

        // 创建支付记录
        Payment payment = new Payment();
        payment.setPaymentOrderNo(paymentOrderNo);
        payment.setUserId(request.getUserId());
        payment.setAmount(request.getAmount());
        payment.setCurrency(request.getCurrency());
        payment.setPaymentMethod(request.getPaymentMethod());
        payment.setPaymentProvider(request.getPaymentProvider());
        payment.setPaymentType(request.getPaymentType());
        payment.setBusinessOrderId(request.getBusinessOrderId());
        payment.setBusinessType(request.getBusinessType());
        payment.setDescription(request.getDescription());
        payment.setNotifyUrl(request.getNotifyUrl());
        payment.setReturnUrl(request.getReturnUrl());
        payment.setStatus(Payment.PaymentStatus.PENDING);

        // 设置过期时间
        if (request.getExpirationMinutes() != null) {
            payment.setExpiresAt(Instant.now().plus(request.getExpirationMinutes(), java.time.temporal.ChronoUnit.MINUTES));
        }

        Payment savedPayment = paymentRepository.save(payment);
        log.info("支付订单创建成功: paymentOrderNo={}", paymentOrderNo);

        return PaymentDTO.PaymentInfo.fromEntity(savedPayment);
    }

    @Override
    @Transactional
    public PaymentDTO.PaymentResult processPayment(String paymentOrderNo, PaymentDTO.ProcessPaymentRequest request) {
        log.info("处理支付: paymentOrderNo={}", paymentOrderNo);

        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        if (payment.getStatus() != Payment.PaymentStatus.PENDING) {
            throw new RuntimeException("支付订单状态不正确: " + payment.getStatus());
        }

        if (payment.isExpired()) {
            payment.setStatus(Payment.PaymentStatus.EXPIRED);
            paymentRepository.save(payment);
            throw new RuntimeException("支付订单已过期");
        }

        try {
            // 更新状态为处理中
            payment.setStatus(Payment.PaymentStatus.PROCESSING);
            paymentRepository.save(payment);

            // 根据支付提供商处理支付
            PaymentDTO.PaymentResult result = processPaymentByProvider(payment, request);

            if (result.isSuccess()) {
                payment.markAsSuccess();
                payment.setExternalTransactionId(result.getPaymentOrderNo());
            } else {
                payment.markAsFailed(result.getMessage());
            }

            paymentRepository.save(payment);
            return result;

        } catch (Exception e) {
            log.error("支付处理失败: paymentOrderNo={}", paymentOrderNo, e);
            payment.markAsFailed(e.getMessage());
            paymentRepository.save(payment);
            
            return PaymentDTO.PaymentResult.builder()
                    .success(false)
                    .message("支付处理失败: " + e.getMessage())
                    .paymentOrderNo(paymentOrderNo)
                    .build();
        }
    }

    @Override
    @Transactional
    public PaymentDTO.PaymentInfo confirmPayment(String paymentOrderNo, String externalTransactionId) {
        log.info("确认支付: paymentOrderNo={}, externalTransactionId={}", paymentOrderNo, externalTransactionId);

        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        payment.markAsSuccess();
        payment.setExternalTransactionId(externalTransactionId);
        Payment savedPayment = paymentRepository.save(payment);

        log.info("支付确认成功: paymentOrderNo={}", paymentOrderNo);
        return PaymentDTO.PaymentInfo.fromEntity(savedPayment);
    }

    @Override
    @Transactional
    public void cancelPayment(String paymentOrderNo, String reason) {
        log.info("取消支付: paymentOrderNo={}, reason={}", paymentOrderNo, reason);

        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        payment.setStatus(Payment.PaymentStatus.CANCELLED);
        payment.setErrorMessage(reason);
        paymentRepository.save(payment);

        log.info("支付取消成功: paymentOrderNo={}", paymentOrderNo);
    }

    @Override
    public void handlePaymentCallback(String provider, Map<String, Object> callbackData) {
        log.info("处理支付回调: provider={}", provider);
        
        // 根据不同的支付提供商处理回调
        switch (provider.toUpperCase()) {
            case "ALIPAY":
                handleAlipayCallback(callbackData);
                break;
            case "WECHAT":
                handleWechatCallback(callbackData);
                break;
            case "STRIPE":
                handleStripeCallback(callbackData);
                break;
            default:
                log.warn("未知的支付提供商: {}", provider);
        }
    }

    @Override
    public PaymentDTO.PaymentInfo getPaymentByOrderNo(String paymentOrderNo) {
        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));
        return PaymentDTO.PaymentInfo.fromEntity(payment);
    }

    @Override
    public PaymentDTO.PaymentInfo getPaymentById(Long paymentId) {
        Payment payment = paymentRepository.findById(paymentId)
                .orElseThrow(() -> new RuntimeException("支付记录不存在: " + paymentId));
        return PaymentDTO.PaymentInfo.fromEntity(payment);
    }

    @Override
    public Page<PaymentDTO.PaymentInfo> getUserPayments(Long userId, Pageable pageable) {
        Page<Payment> payments = paymentRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return payments.map(PaymentDTO.PaymentInfo::fromEntity);
    }

    @Override
    public Page<PaymentDTO.PaymentInfo> getUserPaymentsByStatus(Long userId, Payment.PaymentStatus status, Pageable pageable) {
        Page<Payment> payments = paymentRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, status, pageable);
        return payments.map(PaymentDTO.PaymentInfo::fromEntity);
    }

    @Override
    public Page<PaymentDTO.PaymentInfo> getPayments(PaymentDTO.PaymentQueryRequest request, Pageable pageable) {
        // 这里应该使用 Specification 或者 QueryDSL 来构建动态查询
        // 为了简化，这里只返回所有支付记录
        Page<Payment> payments = paymentRepository.findAll(pageable);
        return payments.map(PaymentDTO.PaymentInfo::fromEntity);
    }

    @Override
    public PaymentDTO.PaymentStatistics getPaymentStatistics(Instant startDate, Instant endDate) {
        // 实现支付统计逻辑
        Long totalCount = paymentRepository.countSuccessfulPaymentsByDateRange(startDate, endDate);
        BigDecimal totalAmount = paymentRepository.sumSuccessfulPaymentsByDateRange(startDate, endDate);
        
        return PaymentDTO.PaymentStatistics.builder()
                .totalCount(totalCount)
                .totalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                .successCount(totalCount)
                .successAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                .failedCount(0L)
                .failedAmount(BigDecimal.ZERO)
                .successRate(BigDecimal.valueOf(100))
                .totalFee(BigDecimal.ZERO)
                .totalRefunded(BigDecimal.ZERO)
                .build();
    }

    @Override
    public PaymentDTO.UserPaymentStatistics getUserPaymentStatistics(Long userId) {
        BigDecimal totalAmount = paymentRepository.sumSuccessfulPaymentsByUserId(userId);
        
        return PaymentDTO.UserPaymentStatistics.builder()
                .userId(userId)
                .totalPayments(0L)
                .totalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                .totalRefunded(BigDecimal.ZERO)
                .build();
    }

    @Override
    public List<PaymentDTO.PaymentMethodStatistics> getPaymentMethodStatistics() {
        List<Object[]> statistics = paymentRepository.getPaymentMethodStatistics();
        return statistics.stream()
                .map(stat -> PaymentDTO.PaymentMethodStatistics.builder()
                        .method((Payment.PaymentMethod) stat[0])
                        .methodDescription(((Payment.PaymentMethod) stat[0]).getDescription())
                        .count((Long) stat[1])
                        .amount((BigDecimal) stat[2])
                        .build())
                .collect(Collectors.toList());
    }

    // 私有方法

    private String generatePaymentOrderNo() {
        return "PAY" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }

    private String generateRefundOrderNo() {
        return "REF" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }

    private PaymentDTO.PaymentResult processPaymentByProvider(Payment payment, PaymentDTO.ProcessPaymentRequest request) {
        // 根据不同的支付提供商处理支付
        switch (payment.getPaymentProvider()) {
            case ALIPAY:
                return processAlipayPayment(payment, request);
            case WECHAT:
                return processWechatPayment(payment, request);
            case STRIPE:
                return processStripePayment(payment, request);
            case INTERNAL:
                return processInternalPayment(payment, request);
            default:
                throw new RuntimeException("不支持的支付提供商: " + payment.getPaymentProvider());
        }
    }

    private PaymentDTO.PaymentResult processAlipayPayment(Payment payment, PaymentDTO.ProcessPaymentRequest request) {
        // 支付宝支付处理逻辑
        log.info("处理支付宝支付: {}", payment.getPaymentOrderNo());
        
        // 模拟支付处理
        return PaymentDTO.PaymentResult.builder()
                .success(true)
                .message("支付宝支付处理成功")
                .paymentOrderNo(payment.getPaymentOrderNo())
                .paymentUrl("https://openapi.alipay.com/gateway.do?...")
                .build();
    }

    private PaymentDTO.PaymentResult processWechatPayment(Payment payment, PaymentDTO.ProcessPaymentRequest request) {
        // 微信支付处理逻辑
        log.info("处理微信支付: {}", payment.getPaymentOrderNo());
        
        return PaymentDTO.PaymentResult.builder()
                .success(true)
                .message("微信支付处理成功")
                .paymentOrderNo(payment.getPaymentOrderNo())
                .qrCode("weixin://wxpay/bizpayurl?...")
                .build();
    }

    private PaymentDTO.PaymentResult processStripePayment(Payment payment, PaymentDTO.ProcessPaymentRequest request) {
        // Stripe支付处理逻辑
        log.info("处理Stripe支付: {}", payment.getPaymentOrderNo());
        
        return PaymentDTO.PaymentResult.builder()
                .success(true)
                .message("Stripe支付处理成功")
                .paymentOrderNo(payment.getPaymentOrderNo())
                .paymentUrl("https://checkout.stripe.com/...")
                .build();
    }

    private PaymentDTO.PaymentResult processInternalPayment(Payment payment, PaymentDTO.ProcessPaymentRequest request) {
        // 内部支付处理逻辑（余额支付等）
        log.info("处理内部支付: {}", payment.getPaymentOrderNo());
        
        return PaymentDTO.PaymentResult.builder()
                .success(true)
                .message("内部支付处理成功")
                .paymentOrderNo(payment.getPaymentOrderNo())
                .build();
    }

    private void handleAlipayCallback(Map<String, Object> callbackData) {
        // 处理支付宝回调
        log.info("处理支付宝回调: {}", callbackData);
    }

    private void handleWechatCallback(Map<String, Object> callbackData) {
        // 处理微信回调
        log.info("处理微信回调: {}", callbackData);
    }

    private void handleStripeCallback(Map<String, Object> callbackData) {
        // 处理Stripe回调
        log.info("处理Stripe回调: {}", callbackData);
    }

    // 其他方法的简化实现...
    
    @Override
    @Transactional
    public PaymentDTO.RefundInfo requestRefund(String paymentOrderNo, PaymentDTO.RefundRequest request) {
        log.info("申请退款: paymentOrderNo={}, refundAmount={}", paymentOrderNo, request.getRefundAmount());

        // 查找支付记录
        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        // 验证支付状态
        if (payment.getStatus() != Payment.PaymentStatus.SUCCESS) {
            throw new RuntimeException("只有成功的支付才能申请退款");
        }

        // 验证退款金额
        BigDecimal refundAmount = request.getRefundAmount();
        BigDecimal maxRefundAmount = payment.getAmount().subtract(payment.getRefundedAmount());

        if (refundAmount.compareTo(maxRefundAmount) > 0) {
            throw new RuntimeException("退款金额不能超过可退款金额: " + maxRefundAmount);
        }

        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("退款金额必须大于0");
        }

        // 创建退款交易记录
        Transaction refundTransaction = new Transaction();
        refundTransaction.setTransactionNo(generateRefundOrderNo());
        refundTransaction.setUserId(payment.getUserId());
        refundTransaction.setAmount(refundAmount);
        refundTransaction.setTransactionType(Transaction.TransactionType.REFUND);
        refundTransaction.setDirection(Transaction.Direction.IN);
        refundTransaction.setStatus(Transaction.TransactionStatus.PENDING);
        refundTransaction.setBusinessType(payment.getBusinessType());
        refundTransaction.setBusinessOrderId(payment.getBusinessOrderId());
        refundTransaction.setPaymentId(payment.getId());
        refundTransaction.setDescription("退款: " + request.getReason());
        refundTransaction.setRefundStatus(Transaction.RefundStatus.REQUESTED);
        refundTransaction.setRefundAmount(refundAmount);
        refundTransaction.setRefundReason(request.getReason());
        refundTransaction.setRefundRequestedAt(Instant.now());

        // 保存退款交易记录
        refundTransaction = transactionRepository.save(refundTransaction);

        // 更新支付记录状态
        payment.setStatus(Payment.PaymentStatus.REFUNDING);
        paymentRepository.save(payment);

        // 构建退款信息
        return PaymentDTO.RefundInfo.builder()
                .id(refundTransaction.getId())
                .refundNo(refundTransaction.getTransactionNo())
                .paymentOrderNo(paymentOrderNo)
                .refundAmount(refundAmount)
                .reason(request.getReason())
                .status(refundTransaction.getRefundStatus().name())
                .statusDescription(refundTransaction.getRefundStatus().getDescription())
                .createdAt(refundTransaction.getCreatedAt())
                .build();
    }

    @Override
    @Transactional
    public PaymentDTO.RefundInfo processRefund(Long refundId, PaymentDTO.ProcessRefundRequest request) {
        log.info("处理退款: refundId={}, approved={}", refundId, request.isApproved());

        // 查找退款交易记录
        Transaction refundTransaction = transactionRepository.findById(refundId)
                .orElseThrow(() -> new RuntimeException("退款记录不存在: " + refundId));

        // 验证退款状态
        if (refundTransaction.getRefundStatus() != Transaction.RefundStatus.REQUESTED) {
            throw new RuntimeException("退款状态不正确，当前状态: " + refundTransaction.getRefundStatus());
        }

        if (request.isApproved()) {
            // 批准退款
            refundTransaction.setRefundStatus(Transaction.RefundStatus.APPROVED);
            // Note: Transaction entity may not have these fields, using description instead
            refundTransaction.setDescription("退款已批准: " + request.getReason());
            refundTransaction.setStatus(Transaction.TransactionStatus.SUCCESS);

            // 更新原支付记录状态
            if (refundTransaction.getPaymentId() != null) {
                Payment originalPayment = paymentRepository.findById(refundTransaction.getPaymentId())
                        .orElseThrow(() -> new RuntimeException("原支付记录不存在"));

                // 检查是否全额退款
                BigDecimal totalRefunded = originalPayment.getRefundedAmount().add(refundTransaction.getRefundAmount());
                if (totalRefunded.compareTo(originalPayment.getAmount()) >= 0) {
                    originalPayment.setStatus(Payment.PaymentStatus.REFUNDED);
                } else {
                    originalPayment.setStatus(Payment.PaymentStatus.PARTIAL_REFUNDED);
                }
                originalPayment.setRefundedAmount(totalRefunded);
                paymentRepository.save(originalPayment);
            }

        } else {
            // 拒绝退款
            refundTransaction.setRefundStatus(Transaction.RefundStatus.REJECTED);
            // Note: Transaction entity may not have these fields, using description instead
            refundTransaction.setDescription("退款已拒绝: " + request.getReason());
            refundTransaction.setStatus(Transaction.TransactionStatus.FAILED);

            // 恢复原支付记录状态
            if (refundTransaction.getPaymentId() != null) {
                Payment originalPayment = paymentRepository.findById(refundTransaction.getPaymentId())
                        .orElseThrow(() -> new RuntimeException("原支付记录不存在"));
                originalPayment.setStatus(Payment.PaymentStatus.SUCCESS);
                paymentRepository.save(originalPayment);
            }
        }

        // 保存退款交易记录
        refundTransaction = transactionRepository.save(refundTransaction);

        // 构建退款信息
        return PaymentDTO.RefundInfo.builder()
                .id(refundTransaction.getId())
                .refundNo(refundTransaction.getTransactionNo())
                .paymentOrderNo(refundTransaction.getPaymentId() != null ?
                    paymentRepository.findById(refundTransaction.getPaymentId())
                        .map(Payment::getPaymentOrderNo).orElse("") : "")
                .refundAmount(refundTransaction.getRefundAmount())
                .reason(refundTransaction.getRefundReason())
                .status(refundTransaction.getRefundStatus().name())
                .statusDescription(refundTransaction.getRefundStatus().getDescription())
                .createdAt(refundTransaction.getCreatedAt())
                .build();
    }

    @Override
    public PaymentDTO.RefundInfo getRefundInfo(Long refundId) {
        log.debug("获取退款信息: refundId={}", refundId);

        // 查找退款交易记录
        Transaction refundTransaction = transactionRepository.findById(refundId)
                .orElseThrow(() -> new RuntimeException("退款记录不存在: " + refundId));

        // 验证是否为退款交易
        if (refundTransaction.getTransactionType() != Transaction.TransactionType.REFUND) {
            throw new RuntimeException("记录不是退款交易: " + refundId);
        }

        // 获取原支付订单号
        String paymentOrderNo = "";
        if (refundTransaction.getPaymentId() != null) {
            paymentOrderNo = paymentRepository.findById(refundTransaction.getPaymentId())
                    .map(Payment::getPaymentOrderNo)
                    .orElse("");
        }

        // 构建退款信息
        return PaymentDTO.RefundInfo.builder()
                .id(refundTransaction.getId())
                .refundNo(refundTransaction.getTransactionNo())
                .paymentOrderNo(paymentOrderNo)
                .refundAmount(refundTransaction.getRefundAmount())
                .reason(refundTransaction.getRefundReason())
                .status(refundTransaction.getRefundStatus().name())
                .statusDescription(refundTransaction.getRefundStatus().getDescription())
                .createdAt(refundTransaction.getCreatedAt())
                .build();
    }

    @Override
    public List<PaymentDTO.RefundInfo> getPaymentRefunds(String paymentOrderNo) {
        log.debug("获取支付退款记录: paymentOrderNo={}", paymentOrderNo);

        // 查找支付记录
        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        // 查找相关的退款交易记录 (使用简单查询方法)
        List<Transaction> refundTransactions = transactionRepository.findAll().stream()
                .filter(t -> t.getPaymentId() != null && t.getPaymentId().equals(payment.getId())
                        && t.getTransactionType() == Transaction.TransactionType.REFUND)
                .collect(Collectors.toList());

        // 转换为退款信息
        return refundTransactions.stream()
                .map(transaction -> PaymentDTO.RefundInfo.builder()
                        .id(transaction.getId())
                        .refundNo(transaction.getTransactionNo())
                        .paymentOrderNo(paymentOrderNo)
                        .refundAmount(transaction.getRefundAmount())
                        .reason(transaction.getRefundReason())
                        .status(transaction.getRefundStatus().name())
                        .statusDescription(transaction.getRefundStatus().getDescription())
                        .createdAt(transaction.getCreatedAt())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDTO.DailyStatistics> getDailyPaymentStatistics(Instant startDate, int days) {
        // TODO: 实现每日支付统计逻辑
        return new ArrayList<>();
    }

    @Override
    public List<PaymentDTO.MonthlyStatistics> getMonthlyPaymentStatistics(Instant startDate, int months) {
        // TODO: 实现月度支付统计逻辑
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void updatePaymentStatus(String paymentOrderNo, Payment.PaymentStatus status, String reason) {
        log.info("更新支付状态: paymentOrderNo={}, status={}, reason={}", paymentOrderNo, status, reason);

        // 查找支付记录
        Payment payment = paymentRepository.findByPaymentOrderNo(paymentOrderNo)
                .orElseThrow(() -> new RuntimeException("支付订单不存在: " + paymentOrderNo));

        // 记录原状态
        Payment.PaymentStatus oldStatus = payment.getStatus();

        // 更新状态
        payment.setStatus(status);
        if (reason != null && !reason.trim().isEmpty()) {
            payment.setErrorMessage(reason);
        }

        // 根据状态设置相应的时间戳
        switch (status) {
            case SUCCESS:
                payment.setPaidAt(Instant.now());
                break;
            case FAILED:
            case CANCELLED:
                payment.setErrorMessage(reason);
                break;
            default:
                break;
        }

        // 保存更新
        paymentRepository.save(payment);

        log.info("支付状态已更新: paymentOrderNo={}, {} -> {}", paymentOrderNo, oldStatus, status);
    }

    @Override
    public int processExpiredPayments() {
        // TODO: 实现处理过期支付逻辑
        return 0;
    }

    @Override
    public void syncPaymentStatus(String paymentOrderNo) {
        // TODO: 实现同步支付状态逻辑
    }

    @Override
    public PaymentDTO.PaymentResult retryPayment(String paymentOrderNo) {
        // TODO: 实现重试支付逻辑
        throw new UnsupportedOperationException("重试支付功能待实现");
    }

    @Override
    public PaymentDTO.ValidationResult validatePaymentRequest(PaymentDTO.CreatePaymentRequest request) {
        log.debug("验证支付请求: userId={}, amount={}", request.getUserId(), request.getAmount());

        List<String> errors = new ArrayList<>();

        // 验证用户ID
        if (request.getUserId() == null || request.getUserId() <= 0) {
            errors.add("用户ID不能为空或无效");
        }

        // 验证金额
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("支付金额必须大于0");
        } else if (request.getAmount().compareTo(new BigDecimal("1000000")) > 0) {
            errors.add("单笔支付金额不能超过100万");
        }

        // 验证业务类型
        if (request.getBusinessType() == null || request.getBusinessType().trim().isEmpty()) {
            errors.add("业务类型不能为空");
        }

        // 验证业务订单ID
        if (request.getBusinessOrderId() == null || request.getBusinessOrderId() <= 0) {
            errors.add("业务订单ID不能为空或无效");
        }

        // 验证支付方式
        if (request.getPaymentMethod() == null) {
            errors.add("支付方式不能为空");
        }

        // 验证支付提供商
        if (request.getPaymentProvider() == null) {
            errors.add("支付提供商不能为空");
        }

        // 检查重复支付
        if (errors.isEmpty() && checkDuplicatePayment(request.getUserId(), request.getBusinessType(), request.getBusinessOrderId())) {
            errors.add("检测到重复支付，请勿重复提交");
        }

        boolean isValid = errors.isEmpty();
        String message = isValid ? "验证通过" : String.join("; ", errors);

        return PaymentDTO.ValidationResult.builder()
                .valid(isValid)
                .message(message)
                .build();
    }

    @Override
    public PaymentDTO.RiskAssessment assessPaymentRisk(Long userId, BigDecimal amount, Payment.PaymentMethod method) {
        log.debug("评估支付风险: userId={}, amount={}, method={}", userId, amount, method);

        int riskScore = 0;
        List<String> riskFactors = new ArrayList<>();

        // 金额风险评估
        if (amount.compareTo(new BigDecimal("10000")) > 0) {
            riskScore += 20;
            riskFactors.add("大额支付");
        }
        if (amount.compareTo(new BigDecimal("50000")) > 0) {
            riskScore += 30;
            riskFactors.add("超大额支付");
        }

        // 用户历史风险评估
        try {
            // 检查用户最近24小时的支付失败次数
            Instant yesterday = Instant.now().minus(24, ChronoUnit.HOURS);
            long recentFailedCount = paymentRepository.countFailedPaymentsByUserSince(userId, yesterday);

            if (recentFailedCount > 5) {
                riskScore += 30;
                riskFactors.add("频繁支付失败");
            } else if (recentFailedCount > 2) {
                riskScore += 15;
                riskFactors.add("支付失败较多");
            }

            // 检查用户总支付金额（作为信用评估）
            BigDecimal totalPaymentAmount = paymentRepository.sumSuccessfulPaymentsByUserId(userId);
            if (totalPaymentAmount == null || totalPaymentAmount.compareTo(new BigDecimal("1000")) < 0) {
                riskScore += 20;
                riskFactors.add("新用户或低信用");
            }

        } catch (Exception e) {
            log.warn("获取用户支付历史失败: userId={}", userId, e);
            riskScore += 10;
            riskFactors.add("历史数据异常");
        }

        // 支付方式风险评估
        if (method == Payment.PaymentMethod.BALANCE) {
            // 余额支付风险较低
            riskScore += 0;
        } else if (method == Payment.PaymentMethod.BANK_CARD) {
            riskScore += 5;
            riskFactors.add("银行卡支付风险");
        } else if (method == Payment.PaymentMethod.POINTS) {
            riskScore += 0;
            riskFactors.add("积分支付");
        } else {
            // 第三方支付（支付宝、微信等）
            riskScore += 2;
            riskFactors.add("第三方支付");
        }

        // 确定风险等级
        String riskLevel;
        boolean requiresManualReview = false;

        if (riskScore >= 70) {
            riskLevel = "HIGH";
            requiresManualReview = true;
        } else if (riskScore >= 40) {
            riskLevel = "MEDIUM";
            requiresManualReview = amount.compareTo(new BigDecimal("50000")) > 0;
        } else {
            riskLevel = "LOW";
        }

        return PaymentDTO.RiskAssessment.builder()
                .riskLevel(riskLevel)
                .riskScore(riskScore)
                .requiresManualReview(requiresManualReview)
                .riskFactors(riskFactors)
                .build();
    }

    @Override
    public boolean checkDuplicatePayment(Long userId, String businessType, Long businessOrderId) {
        log.debug("检查重复支付: userId={}, businessType={}, businessOrderId={}", userId, businessType, businessOrderId);

        if (userId == null || businessType == null || businessOrderId == null) {
            return false;
        }

        try {
            // 查找已成功支付的记录
            List<Payment> existingPayments = paymentRepository.findDuplicatePayments(userId, businessType, businessOrderId);

            boolean hasDuplicate = !existingPayments.isEmpty();
            if (hasDuplicate) {
                log.warn("发现重复支付: userId={}, businessType={}, businessOrderId={}, 已存在{}条成功支付记录",
                    userId, businessType, businessOrderId, existingPayments.size());
            }

            return hasDuplicate;

        } catch (Exception e) {
            log.error("检查重复支付失败: userId={}, businessType={}, businessOrderId={}",
                userId, businessType, businessOrderId, e);
            // 出现异常时，为了安全起见，返回true阻止支付
            return true;
        }
    }

    @Override
    public PaymentDTO.PaymentLimits getPaymentLimits(Long userId, Payment.PaymentMethod method) {
        // TODO: 实现获取支付限额逻辑
        return PaymentDTO.PaymentLimits.builder()
                .singleLimit(new BigDecimal("10000"))
                .dailyLimit(new BigDecimal("50000"))
                .monthlyLimit(new BigDecimal("500000"))
                .build();
    }

    @Override
    public PaymentDTO.PaymentConfig getPaymentConfig(Payment.PaymentProvider provider) {
        // TODO: 实现获取支付配置逻辑
        throw new UnsupportedOperationException("获取支付配置功能待实现");
    }

    @Override
    public void updatePaymentConfig(Payment.PaymentProvider provider, PaymentDTO.PaymentConfig config) {
        // TODO: 实现更新支付配置逻辑
    }

    @Override
    public PaymentDTO.ConfigTestResult testPaymentConfig(Payment.PaymentProvider provider) {
        // TODO: 实现测试支付配置逻辑
        return PaymentDTO.ConfigTestResult.builder()
                .success(true)
                .message("配置测试成功")
                .responseTime(100L)
                .build();
    }

    @Override
    public PaymentDTO.FinancialReport generateFinancialReport(Instant startDate, Instant endDate) {
        // TODO: 实现生成财务报表逻辑
        throw new UnsupportedOperationException("生成财务报表功能待实现");
    }

    @Override
    public String exportPaymentRecords(PaymentDTO.ExportRequest request) {
        // TODO: 实现导出支付记录逻辑
        throw new UnsupportedOperationException("导出支付记录功能待实现");
    }

    @Override
    public List<PaymentDTO.RevenueTrend> getRevenueTrend(Instant startDate, Instant endDate, String period) {
        // TODO: 实现获取收入趋势逻辑
        return new ArrayList<>();
    }

    @Override
    public PaymentDTO.BatchProcessResult batchProcessPayments(List<String> paymentOrderNos, String action) {
        // TODO: 实现批量处理支付逻辑
        return PaymentDTO.BatchProcessResult.builder()
                .totalCount(paymentOrderNos.size())
                .successCount(0)
                .failedCount(paymentOrderNos.size())
                .build();
    }

    @Override
    public PaymentDTO.BatchRefundResult batchRefund(List<String> paymentOrderNos, String reason) {
        // TODO: 实现批量退款逻辑
        return PaymentDTO.BatchRefundResult.builder()
                .totalCount(paymentOrderNos.size())
                .successCount(0)
                .failedCount(paymentOrderNos.size())
                .build();
    }

    @Override
    public int batchUpdateStatus(List<String> paymentOrderNos, Payment.PaymentStatus status) {
        // TODO: 实现批量更新状态逻辑
        return 0;
    }

    @Override
    public int cleanupExpiredData(int days) {
        // TODO: 实现清理过期数据逻辑
        return 0;
    }

    @Override
    public int repairAnomalousData() {
        // TODO: 实现修复异常数据逻辑
        return 0;
    }

    @Override
    public void recalculateStatistics() {
        // TODO: 实现重新计算统计数据逻辑
    }

    @Override
    public PaymentDTO.ConsistencyCheckResult checkDataConsistency() {
        // TODO: 实现数据一致性检查逻辑
        return PaymentDTO.ConsistencyCheckResult.builder()
                .consistent(true)
                .checkTime(Instant.now())
                .build();
    }
}
