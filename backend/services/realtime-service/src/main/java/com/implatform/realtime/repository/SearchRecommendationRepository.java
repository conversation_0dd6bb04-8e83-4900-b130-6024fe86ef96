package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchRecommendation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 搜索推荐Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchRecommendationRepository extends R2dbcRepository<SearchRecommendation, Long> {
    
    /**
     * 根据用户ID查找有效推荐
     */
    List<SearchRecommendation> findByUserIdAndIsActiveTrueAndExpiresAtAfterOrderByRecommendationScoreDesc(
        Long userId, Instant currentTime);
    
    /**
     * 根据用户ID和内容类型查找推荐
     */
    List<SearchRecommendation> findByUserIdAndContentTypeAndIsActiveTrueOrderByRecommendationScoreDesc(
        Long userId, SearchRecommendation.ContentType contentType);
    
    /**
     * 根据推荐类型查找推荐
     */
    List<SearchRecommendation> findByRecommendationTypeAndIsActiveTrueOrderByRecommendationScoreDesc(
        SearchRecommendation.RecommendationType recommendationType);
    
    /**
     * 查找高质量推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.isActive = true AND r.recommendationScore >= :scoreThreshold AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findHighQualityRecommendations(
        @Param("scoreThreshold") Double scoreThreshold,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找用户的个性化推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.recommendationType = :personalizedType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findPersonalizedRecommendations(
        @Param("userId") Long userId,
        @Param("personalizedType") SearchRecommendation.RecommendationType personalizedType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找热门推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.recommendationType = :trendingType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findTrendingRecommendations(
        @Param("trendingType") SearchRecommendation.RecommendationType trendingType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找协同过滤推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.recommendationType = :collaborativeType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findCollaborativeFilteringRecommendations(
        @Param("userId") Long userId,
        @Param("collaborativeType") SearchRecommendation.RecommendationType collaborativeType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找基于内容的推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.recommendationType = :contentBasedType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findContentBasedRecommendations(
        @Param("userId") Long userId,
        @Param("contentBasedType") SearchRecommendation.RecommendationType contentBasedType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找混合推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.recommendationType = :hybridType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findHybridRecommendations(
        @Param("userId") Long userId,
        @Param("hybridType") SearchRecommendation.RecommendationType hybridType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找已过期的推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.expiresAt IS NOT NULL AND r.expiresAt <= :currentTime")
    List<SearchRecommendation> findExpiredRecommendations(@Param("currentTime") Instant currentTime);
    
    /**
     * 查找未查看的推荐
     */
    List<SearchRecommendation> findByUserIdAndIsViewedFalseAndIsActiveTrueOrderByRecommendationScoreDesc(Long userId);
    
    /**
     * 查找已点击的推荐
     */
    List<SearchRecommendation> findByUserIdAndIsClickedTrueOrderByClickedAtDesc(Long userId);
    
    /**
     * 统计用户推荐点击率
     */
    @Query("SELECT COUNT(CASE WHEN r.isClicked = true THEN 1 END) * 100.0 / COUNT(r) as clickRate FROM SearchRecommendation r WHERE r.userId = :userId AND r.isViewed = true")
    Double getUserClickThroughRate(@Param("userId") Long userId);
    
    /**
     * 统计推荐类型的点击率
     */
    @Query("SELECT r.recommendationType, COUNT(CASE WHEN r.isClicked = true THEN 1 END) * 100.0 / COUNT(r) as clickRate FROM SearchRecommendation r WHERE r.isViewed = true GROUP BY r.recommendationType")
    List<Object[]> getClickThroughRateByType();
    
    /**
     * 统计内容类型的推荐效果
     */
    @Query("SELECT r.contentType, COUNT(r), AVG(r.recommendationScore), COUNT(CASE WHEN r.isClicked = true THEN 1 END) FROM SearchRecommendation r GROUP BY r.contentType")
    List<Object[]> getRecommendationEffectivenessByContentType();
    
    /**
     * 统计算法模型的性能
     */
    @Query("SELECT r.algorithmModel, COUNT(r), AVG(r.recommendationScore), AVG(r.confidence), COUNT(CASE WHEN r.isClicked = true THEN 1 END) FROM SearchRecommendation r WHERE r.algorithmModel IS NOT NULL GROUP BY r.algorithmModel")
    List<Object[]> getAlgorithmPerformance();
    
    /**
     * 查找高置信度推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.confidence >= :confidenceThreshold AND r.isActive = true ORDER BY r.confidence DESC")
    List<SearchRecommendation> findHighConfidenceRecommendations(@Param("confidenceThreshold") Double confidenceThreshold);
    
    /**
     * 记录推荐展示
     */
    @Modifying
    @Query("UPDATE SearchRecommendation r SET r.impressionCount = r.impressionCount + 1, r.isViewed = CASE WHEN r.isViewed = false THEN true ELSE r.isViewed END, r.viewedAt = CASE WHEN r.viewedAt IS NULL THEN :viewedAt ELSE r.viewedAt END WHERE r.id = :recommendationId")
    int recordImpression(@Param("recommendationId") Long recommendationId, @Param("viewedAt") Instant viewedAt);
    
    /**
     * 记录推荐点击
     */
    @Modifying
    @Query("UPDATE SearchRecommendation r SET r.clickCount = r.clickCount + 1, r.isClicked = true, r.clickedAt = :clickedAt, r.impressionCount = r.impressionCount + 1, r.isViewed = true, r.viewedAt = CASE WHEN r.viewedAt IS NULL THEN :clickedAt ELSE r.viewedAt END WHERE r.id = :recommendationId")
    int recordClick(@Param("recommendationId") Long recommendationId, @Param("clickedAt") Instant clickedAt);
    
    /**
     * 批量停用推荐
     */
    @Modifying
    @Query("UPDATE SearchRecommendation r SET r.isActive = false WHERE r.id IN :recommendationIds")
    int batchDeactivate(@Param("recommendationIds") List<Long> recommendationIds);
    
    /**
     * 批量激活推荐
     */
    @Modifying
    @Query("UPDATE SearchRecommendation r SET r.isActive = true WHERE r.id IN :recommendationIds")
    int batchActivate(@Param("recommendationIds") List<Long> recommendationIds);
    
    /**
     * 查找相似用户的推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId IN :similarUserIds AND r.recommendationType = :socialType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findSocialRecommendations(
        @Param("similarUserIds") List<Long> similarUserIds,
        @Param("socialType") SearchRecommendation.RecommendationType socialType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找最近查看的推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.isViewed = true ORDER BY r.viewedAt DESC")
    List<SearchRecommendation> findRecentlyViewedRecommendations(@Param("userId") Long userId);
    
    /**
     * 查找频繁访问的推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.recommendationType = :frequentType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findFrequentlyAccessedRecommendations(
        @Param("frequentType") SearchRecommendation.RecommendationType frequentType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 查找上下文相关推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.userId = :userId AND r.recommendationType = :contextualType AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime) ORDER BY r.recommendationScore DESC")
    List<SearchRecommendation> findContextualRecommendations(
        @Param("userId") Long userId,
        @Param("contextualType") SearchRecommendation.RecommendationType contextualType,
        @Param("currentTime") Instant currentTime);
    
    /**
     * 获取推荐多样性统计
     */
    @Query("SELECT r.userId, COUNT(DISTINCT r.contentType) as contentDiversity, COUNT(DISTINCT r.recommendationType) as typeDiversity FROM SearchRecommendation r WHERE r.isActive = true GROUP BY r.userId")
    List<Object[]> getRecommendationDiversityStats();
    
    /**
     * 查找低表现推荐
     */
    @Query("SELECT r FROM SearchRecommendation r WHERE r.impressionCount > :impressionThreshold AND (r.clickCount * 100.0 / r.impressionCount) < :clickRateThreshold")
    List<SearchRecommendation> findLowPerformingRecommendations(
        @Param("impressionThreshold") Integer impressionThreshold,
        @Param("clickRateThreshold") Double clickRateThreshold);
    
    /**
     * 删除过期推荐
     */
    @Modifying
    @Query("DELETE FROM SearchRecommendation r WHERE r.expiresAt IS NOT NULL AND r.expiresAt < :cutoffTime")
    int deleteExpiredRecommendations(@Param("cutoffTime") Instant cutoffTime);
    
    /**
     * 检查用户是否有推荐
     */
    boolean existsByUserIdAndIsActiveTrueAndExpiresAtAfter(Long userId, Instant currentTime);
    
    /**
     * 统计用户推荐数量
     */
    @Query("SELECT COUNT(r) FROM SearchRecommendation r WHERE r.userId = :userId AND r.isActive = true AND (r.expiresAt IS NULL OR r.expiresAt > :currentTime)")
    long countActiveRecommendationsForUser(@Param("userId") Long userId, @Param("currentTime") Instant currentTime);
    
    /**
     * 获取推荐趋势数据
     */
    @Query("SELECT DATE(r.createdAt), COUNT(r), AVG(r.recommendationScore) FROM SearchRecommendation r WHERE r.createdAt >= :since GROUP BY DATE(r.createdAt) ORDER BY DATE(r.createdAt)")
    List<Object[]> getRecommendationTrends(@Param("since") Instant since);
    
    /**
     * 查找最新推荐
     */
    List<SearchRecommendation> findTop20ByIsActiveTrueOrderByCreatedAtDesc();
    
    /**
     * 分页查找用户推荐
     */
    Page<SearchRecommendation> findByUserIdAndIsActiveTrueOrderByRecommendationScoreDesc(Long userId, Pageable pageable);
}
