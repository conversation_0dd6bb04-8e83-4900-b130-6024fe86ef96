package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupAnnouncement;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 群公告Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupAnnouncementRepository extends R2dbcRepository<GroupAnnouncement, Long> {

    /**
     * 根据群组ID查找有效公告
     */
    @Query("SELECT * FROM group_announcements WHERE group_id = :groupId AND status = 'ACTIVE' " +
           "ORDER BY is_pinned DESC, priority DESC, created_at DESC")
    Flux<GroupAnnouncement> findActiveAnnouncementsByGroupId(@Param("groupId") Long groupId);

    /**
     * 分页查找群组公告
     */
    @Query("SELECT * FROM group_announcements WHERE group_id = :groupId AND status = :status ORDER BY is_pinned DESC, priority DESC, created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupAnnouncement> findByGroupIdAndStatusOrderByIsPinnedDescPriorityDescCreatedAtDesc(
            @Param("groupId") Long groupId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据群组ID和状态查找公告
     */
    @Query("SELECT * FROM group_announcements WHERE group_id = :groupId AND status = :status ORDER BY is_pinned DESC, priority DESC, created_at DESC")
    Flux<GroupAnnouncement> findByGroupIdAndStatusOrderByIsPinnedDescPriorityDescCreatedAtDesc(
            @Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 查找置顶公告
     */
    @Query("SELECT * FROM group_announcements WHERE group_id = :groupId AND is_pinned = true AND status = :status ORDER BY priority DESC, created_at DESC")
    Flux<GroupAnnouncement> findByGroupIdAndIsPinnedTrueAndStatusOrderByPriorityDescCreatedAtDesc(
            @Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 根据优先级查找公告
     */
    List<GroupAnnouncement> findByGroupIdAndPriorityAndStatusOrderByCreatedAtDesc(
            Long groupId, GroupAnnouncement.Priority priority, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 根据公告类型查找公告
     */
    List<GroupAnnouncement> findByGroupIdAndAnnouncementTypeAndStatusOrderByCreatedAtDesc(
            Long groupId, GroupAnnouncement.AnnouncementType announcementType, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 搜索群组公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = :status AND " +
           "(ga.title LIKE %:keyword% OR ga.content LIKE %:keyword%) " +
           "ORDER BY ga.isPinned DESC, ga.priority DESC, ga.createdAt DESC")
    Page<GroupAnnouncement> searchAnnouncements(@Param("groupId") Long groupId, 
                                               @Param("status") GroupAnnouncement.AnnouncementStatus status,
                                               @Param("keyword") String keyword, 
                                               Pageable pageable);

    /**
     * 查找需要确认的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "ga.requireConfirmation = true ORDER BY ga.priority DESC, ga.createdAt DESC")
    List<GroupAnnouncement> findConfirmationRequiredAnnouncements(@Param("groupId") Long groupId);

    /**
     * 查找即将过期的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.status = 'ACTIVE' AND ga.expiryTime IS NOT NULL AND " +
           "ga.expiryTime BETWEEN :now AND :threshold")
    List<GroupAnnouncement> findExpiringSoonAnnouncements(@Param("now") LocalDateTime now, 
                                                          @Param("threshold") LocalDateTime threshold);

    /**
     * 查找已过期的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.status = 'ACTIVE' AND ga.expiryTime IS NOT NULL AND " +
           "ga.expiryTime < :now")
    List<GroupAnnouncement> findExpiredAnnouncements(@Param("now") LocalDateTime now);

    /**
     * 统计群组公告数量
     */
    long countByGroupIdAndStatus(Long groupId, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 统计群组置顶公告数量
     */
    long countByGroupIdAndIsPinnedTrueAndStatus(Long groupId, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 统计群组需要确认的公告数量
     */
    long countByGroupIdAndRequireConfirmationTrueAndStatus(Long groupId, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 更新公告阅读计数
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.readCount = ga.readCount + 1 WHERE ga.id = :announcementId")
    void incrementReadCount(@Param("announcementId") Long announcementId);

    /**
     * 更新公告确认计数
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.confirmedCount = ga.confirmedCount + 1 WHERE ga.id = :announcementId")
    void incrementConfirmedCount(@Param("announcementId") Long announcementId);

    /**
     * 批量更新公告状态
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.status = :status WHERE ga.groupId = :groupId AND ga.id IN :announcementIds")
    void batchUpdateStatus(@Param("groupId") Long groupId, 
                          @Param("announcementIds") List<Long> announcementIds, 
                          @Param("status") GroupAnnouncement.AnnouncementStatus status);

    /**
     * 批量设置置顶状态
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.isPinned = :pinned WHERE ga.groupId = :groupId AND ga.id IN :announcementIds")
    void batchUpdatePinned(@Param("groupId") Long groupId, 
                          @Param("announcementIds") List<Long> announcementIds, 
                          @Param("pinned") Boolean pinned);

    /**
     * 自动过期公告
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.status = 'EXPIRED' WHERE ga.status = 'ACTIVE' AND " +
           "ga.expiryTime IS NOT NULL AND ga.expiryTime < :now")
    int autoExpireAnnouncements(@Param("now") LocalDateTime now);

    /**
     * 获取群组公告统计
     */
    @Query("SELECT ga.status, COUNT(ga) FROM GroupAnnouncement ga WHERE ga.groupId = :groupId GROUP BY ga.status")
    List<Object[]> getAnnouncementStatistics(@Param("groupId") Long groupId);

    /**
     * 获取公告类型统计
     */
    @Query("SELECT ga.announcementType, COUNT(ga) FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' " +
           "GROUP BY ga.announcementType ORDER BY COUNT(ga) DESC")
    List<Object[]> getAnnouncementTypeStatistics(@Param("groupId") Long groupId);

    /**
     * 获取公告优先级统计
     */
    @Query("SELECT ga.priority, COUNT(ga) FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' " +
           "GROUP BY ga.priority ORDER BY ga.priority DESC")
    List<Object[]> getAnnouncementPriorityStatistics(@Param("groupId") Long groupId);

    /**
     * 查找最受欢迎的公告（阅读数最多）
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' " +
           "ORDER BY ga.readCount DESC")
    List<GroupAnnouncement> findMostPopularAnnouncements(@Param("groupId") Long groupId, Pageable pageable);

    /**
     * 查找最新公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "ga.createdAt >= :since ORDER BY ga.createdAt DESC")
    List<GroupAnnouncement> findRecentAnnouncements(@Param("groupId") Long groupId, 
                                                   @Param("since") LocalDateTime since);

    /**
     * 查找长期有效的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "(ga.expiryTime IS NULL OR ga.expiryTime > :futureTime) ORDER BY ga.createdAt DESC")
    List<GroupAnnouncement> findLongTermAnnouncements(@Param("groupId") Long groupId, 
                                                     @Param("futureTime") LocalDateTime futureTime);

    /**
     * 查找低阅读率的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "ga.readCount < :minReadCount ORDER BY ga.readCount ASC, ga.createdAt DESC")
    List<GroupAnnouncement> findLowReadRateAnnouncements(@Param("groupId") Long groupId, 
                                                        @Param("minReadCount") Integer minReadCount);

    /**
     * 查找需要提醒的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "ga.requireConfirmation = true AND ga.expiryTime IS NOT NULL AND " +
           "ga.expiryTime BETWEEN :now AND :reminderTime AND ga.confirmedCount < ga.readCount")
    List<GroupAnnouncement> findAnnouncementsNeedingReminder(@Param("groupId") Long groupId,
                                                            @Param("now") LocalDateTime now,
                                                            @Param("reminderTime") LocalDateTime reminderTime);

    /**
     * 获取公告创建趋势
     */
    @Query("SELECT DATE(ga.createdAt), COUNT(ga) FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND " +
           "ga.createdAt >= :startDate GROUP BY DATE(ga.createdAt) ORDER BY DATE(ga.createdAt)")
    List<Object[]> getAnnouncementCreationTrend(@Param("groupId") Long groupId, 
                                               @Param("startDate") LocalDateTime startDate);

    /**
     * 获取公告阅读趋势
     */
    @Query("SELECT DATE(ga.updatedAt), SUM(ga.readCount) FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND " +
           "ga.updatedAt >= :startDate GROUP BY DATE(ga.updatedAt) ORDER BY DATE(ga.updatedAt)")
    List<Object[]> getAnnouncementReadTrend(@Param("groupId") Long groupId, 
                                          @Param("startDate") LocalDateTime startDate);

    /**
     * 查找创建者的公告
     */
    List<GroupAnnouncement> findByGroupIdAndCreatorIdAndStatusOrderByCreatedAtDesc(
            Long groupId, Long creatorId, GroupAnnouncement.AnnouncementStatus status);

    /**
     * 删除群组的所有公告
     */
    @Modifying
    @Query("UPDATE GroupAnnouncement ga SET ga.status = 'DELETED' WHERE ga.groupId = :groupId")
    void softDeleteGroupAnnouncements(@Param("groupId") Long groupId);

    /**
     * 物理删除已删除状态的公告
     */
    @Modifying
    @Query("DELETE FROM GroupAnnouncement ga WHERE ga.status = 'DELETED' AND ga.updatedAt < :cutoffTime")
    int physicalDeleteOldAnnouncements(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取平均阅读率
     */
    @Query("SELECT AVG(CASE WHEN :totalMembers > 0 THEN ga.readCount * 100.0 / :totalMembers ELSE 0 END) " +
           "FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE'")
    Double getAverageReadRate(@Param("groupId") Long groupId, @Param("totalMembers") Integer totalMembers);

    /**
     * 获取平均确认率
     */
    @Query("SELECT AVG(CASE WHEN :totalMembers > 0 AND ga.requireConfirmation = true THEN ga.confirmedCount * 100.0 / :totalMembers ELSE 0 END) " +
           "FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND ga.requireConfirmation = true")
    Double getAverageConfirmationRate(@Param("groupId") Long groupId, @Param("totalMembers") Integer totalMembers);

    /**
     * 查找重复标题的公告
     */
    @Query("SELECT ga FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE' AND " +
           "ga.title = :title AND ga.id != :excludeId")
    List<GroupAnnouncement> findDuplicateTitleAnnouncements(@Param("groupId") Long groupId, 
                                                           @Param("title") String title, 
                                                           @Param("excludeId") Long excludeId);

    /**
     * 清理过期的草稿公告
     */
    @Modifying
    @Query("DELETE FROM GroupAnnouncement ga WHERE ga.status = 'DRAFT' AND ga.createdAt < :cutoffTime")
    int cleanupExpiredDrafts(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取群组公告概览
     */
    @Query("SELECT COUNT(ga), SUM(ga.readCount), SUM(ga.confirmedCount), " +
           "COUNT(CASE WHEN ga.isPinned = true THEN 1 END), " +
           "COUNT(CASE WHEN ga.requireConfirmation = true THEN 1 END) " +
           "FROM GroupAnnouncement ga WHERE ga.groupId = :groupId AND ga.status = 'ACTIVE'")
    List<Object[]> getGroupAnnouncementOverview(@Param("groupId") Long groupId);
}
