package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BroadcastRecipient;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 群发接收记录Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BroadcastRecipientRepository extends R2dbcRepository<BroadcastRecipient, Long> {

    /**
     * 根据群发ID查找所有接收记录
     */
    @Query("SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId ORDER BY created_at ASC")
    Flux<BroadcastRecipient> findByBroadcastIdOrderByCreatedAtAsc(@Param("broadcastId") Long broadcastId);

    /**
     * 分页查找群发的接收记录
     */
    @Query("SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId ORDER BY created_at ASC LIMIT :limit OFFSET :offset")
    Flux<BroadcastRecipient> findByBroadcastIdOrderByCreatedAtAsc(@Param("broadcastId") Long broadcastId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据群发ID和状态查找接收记录
     */
    @Query("SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId AND send_status = :sendStatus ORDER BY created_at ASC")
    Flux<BroadcastRecipient> findByBroadcastIdAndSendStatusOrderByCreatedAtAsc(@Param("broadcastId") Long broadcastId, @Param("sendStatus") String sendStatus);

    /**
     * 分页查找指定状态的接收记录
     */
    @Query("SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId AND send_status = :sendStatus ORDER BY created_at ASC LIMIT :limit OFFSET :offset")
    Flux<BroadcastRecipient> findByBroadcastIdAndSendStatusOrderByCreatedAtAsc(@Param("broadcastId") Long broadcastId, @Param("sendStatus") String sendStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找特定的接收记录
     */
    @Query("SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId AND recipient_id = :recipientId")
    Mono<BroadcastRecipient> findByBroadcastIdAndRecipientId(@Param("broadcastId") Long broadcastId, @Param("recipientId") Long recipientId);

    /**
     * 查找需要发送的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sendStatus = 'PENDING' AND br.isBlocked = false ORDER BY br.createdAt ASC")
    List<BroadcastRecipient> findPendingRecipients(@Param("broadcastId") Long broadcastId);

    /**
     * 查找需要重试的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sendStatus = 'FAILED' AND br.retryCount < br.maxRetryCount AND " +
           "br.nextRetryTime <= :currentTime AND br.isBlocked = false ORDER BY br.nextRetryTime ASC")
    List<BroadcastRecipient> findRecipientsToRetry(@Param("broadcastId") Long broadcastId, @Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找所有需要重试的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.sendStatus = 'FAILED' AND " +
           "br.retryCount < br.maxRetryCount AND br.nextRetryTime <= :currentTime AND br.isBlocked = false " +
           "ORDER BY br.nextRetryTime ASC")
    List<BroadcastRecipient> findAllRecipientsToRetry(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 统计群发的接收记录数量
     */
    long countByBroadcastId(Long broadcastId);

    /**
     * 统计群发指定状态的接收记录数量
     */
    long countByBroadcastIdAndSendStatus(Long broadcastId, BroadcastRecipient.SendStatus sendStatus);

    /**
     * 根据群发ID和状态查找接收记录（兼容方法）
     */
    default List<BroadcastRecipient> findByBroadcastIdAndStatus(Long broadcastId, BroadcastRecipient.SendStatus status) {
        return findByBroadcastIdAndSendStatusOrderByCreatedAtAsc(broadcastId, status);
    }

    /**
     * 更新状态（兼容方法）
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.sendStatus = :status WHERE br.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") BroadcastRecipient.SendStatus status);

    /**
     * 统计群发的发送成功数量
     */
    @Query("SELECT COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sendStatus IN ('SENT', 'DELIVERED', 'READ')")
    long countSuccessfulSends(@Param("broadcastId") Long broadcastId);

    /**
     * 统计群发的发送失败数量
     */
    @Query("SELECT COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sendStatus IN ('FAILED', 'BLOCKED')")
    long countFailedSends(@Param("broadcastId") Long broadcastId);

    /**
     * 统计群发的阅读数量
     */
    @Query("SELECT COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sendStatus = 'READ'")
    long countReadRecipients(@Param("broadcastId") Long broadcastId);

    /**
     * 批量更新发送状态
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.sendStatus = :status WHERE br.broadcastId = :broadcastId AND br.id IN :recipientIds")
    void batchUpdateSendStatus(@Param("broadcastId") Long broadcastId, @Param("recipientIds") List<Long> recipientIds, @Param("status") BroadcastRecipient.SendStatus status);

    /**
     * 批量标记为已屏蔽
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.sendStatus = 'BLOCKED', br.isBlocked = true, br.blockReason = :reason " +
           "WHERE br.broadcastId = :broadcastId AND br.recipientId IN :recipientIds")
    void batchBlockRecipients(@Param("broadcastId") Long broadcastId, @Param("recipientIds") List<Long> recipientIds, @Param("reason") String reason);

    /**
     * 重置失败的接收记录
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.sendStatus = 'PENDING', br.retryCount = 0, " +
           "br.nextRetryTime = null, br.failureReason = null WHERE br.broadcastId = :broadcastId AND br.sendStatus = 'FAILED'")
    void resetFailedRecipients(@Param("broadcastId") Long broadcastId);

    /**
     * 查找用户接收的群发消息
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.recipientId = :userId AND " +
           "br.sendStatus IN ('SENT', 'DELIVERED', 'READ') ORDER BY br.sentAt DESC")
    Page<BroadcastRecipient> findUserReceivedBroadcasts(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找用户未读的群发消息
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.recipientId = :userId AND " +
           "br.sendStatus IN ('SENT', 'DELIVERED') ORDER BY br.sentAt DESC")
    List<BroadcastRecipient> findUserUnreadBroadcasts(@Param("userId") Long userId);

    /**
     * 标记用户的群发消息为已读
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.sendStatus = 'READ', br.readAt = :readTime " +
           "WHERE br.recipientId = :userId AND br.messageId = :messageId AND br.sendStatus IN ('SENT', 'DELIVERED')")
    void markAsReadByUser(@Param("userId") Long userId, @Param("messageId") Long messageId, @Param("readTime") LocalDateTime readTime);

    /**
     * 获取群发的发送统计
     */
    @Query("SELECT br.sendStatus, COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId GROUP BY br.sendStatus")
    List<Object[]> getBroadcastSendStatistics(@Param("broadcastId") Long broadcastId);

    /**
     * 获取群发的阅读统计
     */
    @Query("SELECT COUNT(CASE WHEN br.readAt IS NOT NULL THEN 1 END) as readCount, " +
           "COUNT(CASE WHEN br.readAt IS NULL AND br.sendStatus IN ('SENT', 'DELIVERED') THEN 1 END) as unreadCount " +
           "FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId")
    List<Object[]> getBroadcastReadStatistics(@Param("broadcastId") Long broadcastId);

    /**
     * 获取群发的时间分布统计
     */
    @Query("SELECT HOUR(br.sentAt), COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND " +
           "br.sentAt IS NOT NULL GROUP BY HOUR(br.sentAt) ORDER BY HOUR(br.sentAt)")
    List<Object[]> getBroadcastTimeDistribution(@Param("broadcastId") Long broadcastId);

    /**
     * 查找发送延迟最高的接收记录
     */
    @Query(value = "SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId AND sent_at IS NOT NULL " +
           "ORDER BY (EXTRACT(EPOCH FROM sent_at) - EXTRACT(EPOCH FROM created_at)) DESC", nativeQuery = true)
    List<BroadcastRecipient> findHighestDelayRecipients(@Param("broadcastId") Long broadcastId, Pageable pageable);

    /**
     * 查找快速阅读的接收记录
     */
    @Query(value = "SELECT * FROM broadcast_recipients WHERE broadcast_id = :broadcastId AND read_at IS NOT NULL AND " +
           "(EXTRACT(EPOCH FROM read_at) - EXTRACT(EPOCH FROM sent_at)) / 60 <= 5 ORDER BY read_at ASC", nativeQuery = true)
    List<BroadcastRecipient> findQuickReadRecipients(@Param("broadcastId") Long broadcastId);

    /**
     * 查找长时间未读的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId AND br.readAt IS NULL AND " +
           "br.sendStatus IN ('SENT', 'DELIVERED') AND br.sentAt < :threshold ORDER BY br.sentAt ASC")
    List<BroadcastRecipient> findLongUnreadRecipients(@Param("broadcastId") Long broadcastId, @Param("threshold") LocalDateTime threshold);

    /**
     * 获取接收者类型统计
     */
    @Query("SELECT br.recipientType, COUNT(br) FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId GROUP BY br.recipientType")
    List<Object[]> getRecipientTypeStatistics(@Param("broadcastId") Long broadcastId);

    /**
     * 查找重试次数最多的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.broadcastId = :broadcastId ORDER BY br.retryCount DESC")
    List<BroadcastRecipient> findMostRetriedRecipients(@Param("broadcastId") Long broadcastId, Pageable pageable);

    /**
     * 查找被屏蔽的接收者
     */
    List<BroadcastRecipient> findByBroadcastIdAndIsBlockedTrueOrderByUpdatedAtDesc(Long broadcastId);

    /**
     * 取消屏蔽接收者
     */
    @Modifying
    @Query("UPDATE BroadcastRecipient br SET br.isBlocked = false, br.blockReason = null, br.sendStatus = 'PENDING' " +
           "WHERE br.broadcastId = :broadcastId AND br.recipientId IN :recipientIds AND br.isBlocked = true")
    void unblockRecipients(@Param("broadcastId") Long broadcastId, @Param("recipientIds") List<Long> recipientIds);

    /**
     * 获取平均发送延迟（秒）
     */
    @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM sent_at) - EXTRACT(EPOCH FROM created_at)) FROM broadcast_recipients " +
           "WHERE broadcast_id = :broadcastId AND sent_at IS NOT NULL", nativeQuery = true)
    Double getAverageSendDelay(@Param("broadcastId") Long broadcastId);

    /**
     * 获取平均阅读延迟（分钟）
     */
    @Query(value = "SELECT AVG((EXTRACT(EPOCH FROM read_at) - EXTRACT(EPOCH FROM sent_at)) / 60) FROM broadcast_recipients " +
           "WHERE broadcast_id = :broadcastId AND read_at IS NOT NULL AND sent_at IS NOT NULL", nativeQuery = true)
    Double getAverageReadDelayMinutes(@Param("broadcastId") Long broadcastId);

    /**
     * 查找需要立即重试的接收记录
     */
    @Query("SELECT br FROM BroadcastRecipient br WHERE br.sendStatus = 'FAILED' AND " +
           "br.retryCount < br.maxRetryCount AND br.isBlocked = false AND " +
           "(br.nextRetryTime IS NULL OR br.nextRetryTime <= :currentTime) " +
           "ORDER BY br.retryCount ASC, br.updatedAt ASC")
    List<BroadcastRecipient> findImmediateRetryRecipients(@Param("currentTime") LocalDateTime currentTime, Pageable pageable);

    /**
     * 删除群发的所有接收记录
     */
    void deleteByBroadcastId(Long broadcastId);

    /**
     * 清理旧的接收记录
     */
    @Modifying
    @Query("DELETE FROM BroadcastRecipient br WHERE br.createdAt < :cutoffTime AND " +
           "br.sendStatus IN ('COMPLETED', 'CANCELLED', 'FAILED')")
    int cleanupOldRecipients(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取用户的群发接收统计
     */
    @Query("SELECT br.sendStatus, COUNT(br) FROM BroadcastRecipient br WHERE br.recipientId = :userId GROUP BY br.sendStatus")
    List<Object[]> getUserReceiveStatistics(@Param("userId") Long userId);

    /**
     * 查找活跃的接收者（经常阅读群发消息）
     */
    @Query(value = "SELECT recipient_id, COUNT(*), AVG((EXTRACT(EPOCH FROM read_at) - EXTRACT(EPOCH FROM sent_at)) / 60) " +
           "FROM broadcast_recipients WHERE read_at IS NOT NULL AND sent_at IS NOT NULL AND " +
           "created_at >= :since GROUP BY recipient_id HAVING COUNT(*) >= :minReadCount " +
           "ORDER BY COUNT(*) DESC", nativeQuery = true)
    List<Object[]> findActiveRecipients(@Param("since") LocalDateTime since, @Param("minReadCount") Long minReadCount);

    /**
     * 查找不活跃的接收者（很少阅读群发消息）
     */
    @Query("SELECT br.recipientId, COUNT(br), COUNT(CASE WHEN br.readAt IS NOT NULL THEN 1 END) " +
           "FROM BroadcastRecipient br WHERE br.sendStatus IN ('SENT', 'DELIVERED', 'READ') AND " +
           "br.createdAt >= :since GROUP BY br.recipientId " +
           "HAVING COUNT(CASE WHEN br.readAt IS NOT NULL THEN 1 END) * 100.0 / COUNT(br) < :maxReadRate " +
           "ORDER BY COUNT(CASE WHEN br.readAt IS NOT NULL THEN 1 END) * 100.0 / COUNT(br) ASC")
    List<Object[]> findInactiveRecipients(@Param("since") LocalDateTime since, @Param("maxReadRate") Double maxReadRate);

    /**
     * 获取群发消息的热力图数据
     */
    @Query("SELECT DATE(br.sentAt), HOUR(br.sentAt), COUNT(br) FROM BroadcastRecipient br " +
           "WHERE br.sentAt >= :startDate GROUP BY DATE(br.sentAt), HOUR(br.sentAt)")
    List<Object[]> getBroadcastHeatmapData(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找重复的接收记录
     */
    @Query("SELECT br1 FROM BroadcastRecipient br1, BroadcastRecipient br2 WHERE br1.id < br2.id AND " +
           "br1.broadcastId = br2.broadcastId AND br1.recipientId = br2.recipientId")
    List<BroadcastRecipient> findDuplicateRecipients();

    /**
     * 清理重复的接收记录
     */
    @Modifying
    @Query("DELETE FROM BroadcastRecipient br WHERE br.id IN " +
           "(SELECT br2.id FROM BroadcastRecipient br1, BroadcastRecipient br2 WHERE br1.id < br2.id AND " +
           "br1.broadcastId = br2.broadcastId AND br1.recipientId = br2.recipientId)")
    int cleanupDuplicateRecipients();
}
