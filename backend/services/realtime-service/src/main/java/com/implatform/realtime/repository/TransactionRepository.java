package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Transaction;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 交易Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface TransactionRepository extends R2dbcRepository<Transaction, Long> {

    /**
     * 根据交易号查找交易记录
     */
    @Query("SELECT * FROM transactions WHERE transaction_no = :transactionNo")
    Mono<Transaction> findByTransactionNo(@Param("transactionNo") String transactionNo);

    /**
     * 根据交易编号查找交易记录
     */
    @Query("SELECT * FROM transactions WHERE transaction_number = :transactionNumber")
    Mono<Transaction> findByTransactionNumber(@Param("transactionNumber") String transactionNumber);

    /**
     * 根据网关交易ID查找交易记录
     */
    @Query("SELECT * FROM transactions WHERE gateway_transaction_id = :gatewayTransactionId")
    Mono<Transaction> findByGatewayTransactionId(@Param("gatewayTransactionId") String gatewayTransactionId);

    /**
     * 根据支付ID查找交易记录
     */
    @Query("SELECT * FROM transactions WHERE payment_id = :paymentId ORDER BY created_at DESC")
    Flux<Transaction> findByPaymentIdOrderByCreatedAtDesc(@Param("paymentId") Long paymentId);

    /**
     * 根据用户ID查找交易记录（不分页）
     */
    @Query("SELECT * FROM transactions WHERE user_id = :userId ORDER BY created_at DESC")
    Flux<Transaction> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据用户ID查找交易记录（分页）
     */
    Page<Transaction> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和交易类型查找交易记录（不分页）
     */
    List<Transaction> findByUserIdAndTransactionTypeOrderByCreatedAtDesc(Long userId, Transaction.TransactionType transactionType);

    /**
     * 根据用户ID和交易类型查找交易记录（分页）
     */
    Page<Transaction> findByUserIdAndTransactionTypeOrderByCreatedAtDesc(Long userId, Transaction.TransactionType transactionType, Pageable pageable);

    /**
     * 根据用户ID和交易方向查找交易记录
     */
    Page<Transaction> findByUserIdAndDirectionOrderByCreatedAtDesc(Long userId, Transaction.Direction direction, Pageable pageable);

    /**
     * 根据用户ID和状态查找交易记录
     */
    Page<Transaction> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Transaction.TransactionStatus status, Pageable pageable);

    /**
     * 根据交易类型查找交易记录
     */
    Page<Transaction> findByTransactionTypeOrderByCreatedAtDesc(Transaction.TransactionType transactionType, Pageable pageable);

    /**
     * 根据状态查找交易记录
     */
    Page<Transaction> findByStatusOrderByCreatedAtDesc(Transaction.TransactionStatus status, Pageable pageable);

    /**
     * 根据状态查找交易记录（按创建时间升序）
     */
    List<Transaction> findByStatusOrderByCreatedAtAsc(Transaction.TransactionStatus status);

    /**
     * 根据用户ID和支付方式查找交易记录
     */
    List<Transaction> findByUserIdAndPaymentMethodOrderByCreatedAtDesc(Long userId, String paymentMethod);

    /**
     * 根据外部交易ID查找交易记录
     */
    Optional<Transaction> findByExternalTransactionId(String externalTransactionId);

    /**
     * 统计用户收入总额
     */
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.userId = :userId AND t.direction = 'IN' AND t.status = 'SUCCESS'")
    BigDecimal sumIncomeByUserId(@Param("userId") Long userId);

    /**
     * 统计用户支出总额
     */
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.userId = :userId AND t.direction = 'OUT' AND t.status = 'SUCCESS'")
    BigDecimal sumExpenseByUserId(@Param("userId") Long userId);

    /**
     * 计算用户余额
     */
    @Query("SELECT " +
           "SUM(CASE WHEN t.direction = 'IN' THEN t.amount ELSE 0 END) - " +
           "SUM(CASE WHEN t.direction = 'OUT' THEN t.amount ELSE 0 END) " +
           "FROM Transaction t WHERE t.userId = :userId AND t.status = 'SUCCESS'")
    BigDecimal calculateUserBalance(@Param("userId") Long userId);

    /**
     * 统计指定时间范围内的交易金额
     */
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.status = 'SUCCESS' AND t.transactionTime BETWEEN :startDate AND :endDate")
    BigDecimal sumTransactionsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计指定时间范围内的交易数量
     */
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.status = 'SUCCESS' AND t.transactionTime BETWEEN :startDate AND :endDate")
    Long countTransactionsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 根据业务类型和业务订单ID查找交易记录
     */
    List<Transaction> findByBusinessTypeAndBusinessOrderId(String businessType, Long businessOrderId);

    /**
     * 查找待处理的交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.status = 'PENDING' AND t.createdAt < :threshold")
    List<Transaction> findPendingTransactions(@Param("threshold") LocalDateTime threshold);

    /**
     * 统计各交易类型的使用情况
     */
    @Query("SELECT t.transactionType, COUNT(t), SUM(t.amount) FROM Transaction t WHERE t.status = 'SUCCESS' GROUP BY t.transactionType")
    List<Object[]> getTransactionTypeStatistics();

    /**
     * 获取每日交易统计
     */
    @Query("SELECT DATE(t.transactionTime) as transactionDate, COUNT(t) as transactionCount, SUM(t.amount) as totalAmount " +
           "FROM Transaction t WHERE t.status = 'SUCCESS' AND t.transactionTime >= :startDate " +
           "GROUP BY DATE(t.transactionTime) ORDER BY transactionDate DESC")
    List<Object[]> getDailyTransactionStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取月度交易统计
     */
    @Query("SELECT YEAR(t.transactionTime) as year, MONTH(t.transactionTime) as month, COUNT(t) as transactionCount, SUM(t.amount) as totalAmount " +
           "FROM Transaction t WHERE t.status = 'SUCCESS' AND t.transactionTime >= :startDate " +
           "GROUP BY YEAR(t.transactionTime), MONTH(t.transactionTime) ORDER BY year DESC, month DESC")
    List<Object[]> getMonthlyTransactionStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找用户最近的交易记录
     */
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId AND t.status = 'SUCCESS' ORDER BY t.transactionTime DESC")
    Page<Transaction> findRecentTransactionsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据金额范围查找交易记录
     */
    @Query("SELECT t FROM Transaction t WHERE t.amount BETWEEN :minAmount AND :maxAmount AND t.status = 'SUCCESS' ORDER BY t.transactionTime DESC")
    Page<Transaction> findTransactionsByAmountRange(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, Pageable pageable);

    /**
     * 查找大额交易记录
     */
    @Query("SELECT t FROM Transaction t WHERE t.amount >= :threshold AND t.status = 'SUCCESS' ORDER BY t.amount DESC")
    Page<Transaction> findLargeTransactions(@Param("threshold") BigDecimal threshold, Pageable pageable);

    /**
     * 查找用户之间的转账记录
     */
    @Query("SELECT t FROM Transaction t WHERE " +
           "(t.userId = :userId1 AND t.counterpartUserId = :userId2) OR " +
           "(t.userId = :userId2 AND t.counterpartUserId = :userId1) " +
           "AND t.transactionType = 'TRANSFER' ORDER BY t.transactionTime DESC")
    Page<Transaction> findTransfersBetweenUsers(@Param("userId1") Long userId1, @Param("userId2") Long userId2, Pageable pageable);

    /**
     * 统计用户交易频率
     */
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.userId = :userId AND t.status = 'SUCCESS' AND t.transactionTime >= :since")
    Long countUserTransactionsSince(@Param("userId") Long userId, @Param("since") LocalDateTime since);

    /**
     * 查找异常交易记录
     */
    @Query("SELECT t FROM Transaction t WHERE " +
           "(t.status = 'SUCCESS' AND t.completedAt IS NULL) OR " +
           "(t.status = 'FAILED' AND t.errorMessage IS NULL) OR " +
           "(t.balanceAfter IS NULL AND t.status = 'SUCCESS')")
    List<Transaction> findAnomalousTransactions();

    /**
     * 获取交易成功率统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN t.status = 'SUCCESS' THEN 1 END) as successCount, " +
           "COUNT(CASE WHEN t.status = 'FAILED' THEN 1 END) as failedCount, " +
           "COUNT(t) as totalCount " +
           "FROM Transaction t WHERE t.createdAt >= :startDate")
    Object[] getTransactionSuccessRateStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找重复交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId AND t.businessType = :businessType AND t.businessOrderId = :businessOrderId AND t.status = 'SUCCESS'")
    List<Transaction> findDuplicateTransactions(@Param("userId") Long userId, @Param("businessType") String businessType, @Param("businessOrderId") Long businessOrderId);

    /**
     * 统计手续费收入
     */
    @Query("SELECT SUM(t.feeAmount) FROM Transaction t WHERE t.status = 'SUCCESS' AND t.transactionTime BETWEEN :startDate AND :endDate")
    BigDecimal sumFeesByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找需要对账的交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.status = 'SUCCESS' AND t.externalTransactionId IS NOT NULL AND t.transactionTime >= :startDate")
    List<Transaction> findTransactionsForReconciliation(@Param("startDate") LocalDateTime startDate);

    /**
     * 统计用户交易行为
     */
    @Query("SELECT t.transactionType, t.direction, COUNT(t), AVG(t.amount), SUM(t.amount) " +
           "FROM Transaction t WHERE t.userId = :userId AND t.status = 'SUCCESS' " +
           "GROUP BY t.transactionType, t.direction")
    List<Object[]> getUserTransactionBehavior(@Param("userId") Long userId);

    /**
     * 查找可疑交易模式
     */
    @Query("SELECT t.userId, COUNT(t), SUM(t.amount) FROM Transaction t " +
           "WHERE t.transactionType = :type AND t.status = 'SUCCESS' AND t.transactionTime >= :since " +
           "GROUP BY t.userId HAVING COUNT(t) > :threshold OR SUM(t.amount) > :amountThreshold")
    List<Object[]> findSuspiciousTransactionPatterns(@Param("type") Transaction.TransactionType type,
                                                     @Param("since") LocalDateTime since,
                                                     @Param("threshold") Long threshold,
                                                     @Param("amountThreshold") BigDecimal amountThreshold);

    // ==================== TransactionService需要的方法 ====================

    /**
     * 查找成功的交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId AND t.status = :status")
    List<Transaction> findSuccessfulTransactions(@Param("userId") Long userId, @Param("status") Transaction.TransactionStatus status);

    /**
     * 查找失败的交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId AND t.status = :status")
    List<Transaction> findFailedTransactions(@Param("userId") Long userId, @Param("status") Transaction.TransactionStatus status);

    /**
     * 查找过期的交易
     */
    @Query("SELECT t FROM Transaction t WHERE t.status = :status AND t.expiresAt < :now")
    List<Transaction> findExpiredTransactions(@Param("status") Transaction.TransactionStatus status, @Param("now") Instant now);

    /**
     * 统计各状态的交易数量
     */
    @Query("SELECT t.status, COUNT(t) FROM Transaction t GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * 统计各交易类型的数量
     */
    @Query("SELECT t.transactionType, COUNT(t) FROM Transaction t GROUP BY t.transactionType")
    List<Object[]> countByTransactionType();

    /**
     * 统计各支付方式的数量
     */
    @Query("SELECT t.paymentMethod, COUNT(t) FROM Transaction t GROUP BY t.paymentMethod")
    List<Object[]> countByPaymentMethod();

    /**
     * 获取收入统计
     */
    @Query("SELECT SUM(t.amount), COUNT(t) FROM Transaction t WHERE t.status = 'SUCCESS'")
    Object[] getRevenueStatistics();

    /**
     * 获取退款统计
     */
    @Query("SELECT SUM(t.refundAmount), COUNT(t) FROM Transaction t WHERE t.refundStatus = 'COMPLETED'")
    Object[] getRefundStatistics();

    /**
     * 获取交易趋势
     */
    @Query("SELECT DATE(t.createdAt), COUNT(t), SUM(t.amount) FROM Transaction t WHERE t.createdAt >= :since GROUP BY DATE(t.createdAt) ORDER BY DATE(t.createdAt)")
    List<Object[]> getTransactionTrends(@Param("since") Instant since);
}
