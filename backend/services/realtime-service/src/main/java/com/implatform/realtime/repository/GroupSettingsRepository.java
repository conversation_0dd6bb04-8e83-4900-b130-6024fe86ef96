package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupSettings;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 群组设置数据访问层 - R2DBC响应式版本
 * 提供群组设置相关的数据库操作方法
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupSettingsRepository extends R2dbcRepository<GroupSettings, Long> {

    /**
     * 根据群组ID查询设置
     *
     * @param groupId 群组ID
     * @return 群组设置
     */
    @Query("SELECT * FROM group_settings WHERE group_id = :groupId")
    Mono<GroupSettings> findByGroupId(@Param("groupId") Long groupId);

    /**
     * 检查群组设置是否存在
     *
     * @param groupId 群组ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM group_settings WHERE group_id = :groupId")
    Mono<Boolean> existsByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询需要加群审批的群组
     *
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 需要审批的群组分页结果
     */
    @Query("SELECT * FROM group_settings WHERE join_approval_required = true ORDER BY updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupSettings> findByJoinApprovalRequiredTrueOrderByUpdatedAtDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查询启用消息审核的群组
     * 
     * @param pageable 分页参数
     * @return 启用审核的群组分页结果
     */
    Page<GroupSettings> findByMessageModerationEnabledTrueOrderByUpdatedAtDesc(Pageable pageable);

    /**
     * 查询禁用文件分享的群组
     * 
     * @param pageable 分页参数
     * @return 禁用文件分享的群组分页结果
     */
    Page<GroupSettings> findByFileSharingEnabledFalseOrderByUpdatedAtDesc(Pageable pageable);

    /**
     * 查询指定可见性的群组
     * 
     * @param visibility 群组可见性
     * @param pageable 分页参数
     * @return 指定可见性的群组分页结果
     */
    Page<GroupSettings> findByGroupVisibilityOrderByUpdatedAtDesc(
            GroupSettings.GroupVisibility visibility, Pageable pageable);

    /**
     * 查询可搜索的群组
     * 
     * @param pageable 分页参数
     * @return 可搜索的群组分页结果
     */
    Page<GroupSettings> findBySearchableTrueOrderByUpdatedAtDesc(Pageable pageable);

    /**
     * 查询启用机器人的群组
     * 
     * @param pageable 分页参数
     * @return 启用机器人的群组分页结果
     */
    Page<GroupSettings> findByBotEnabledTrueOrderByUpdatedAtDesc(Pageable pageable);

    /**
     * 查询成员数量限制大于指定值的群组
     * 
     * @param minMemberLimit 最小成员限制
     * @param pageable 分页参数
     * @return 群组分页结果
     */
    Page<GroupSettings> findByMemberLimitGreaterThanEqualOrderByMemberLimitDesc(
            Integer minMemberLimit, Pageable pageable);

    /**
     * 查询文件大小限制大于指定值的群组
     * 
     * @param minFileSizeLimit 最小文件大小限制（MB）
     * @param pageable 分页参数
     * @return 群组分页结果
     */
    Page<GroupSettings> findByFileSizeLimitMbGreaterThanEqualOrderByFileSizeLimitMbDesc(
            Integer minFileSizeLimit, Pageable pageable);

    /**
     * 查询指定消息审核模式的群组
     * 
     * @param moderationMode 审核模式
     * @return 群组设置列表
     */
    List<GroupSettings> findByMessageModerationMode(GroupSettings.ModerationMode moderationMode);

    /**
     * 查询指定通知设置的群组
     * 
     * @param notificationSetting 通知设置
     * @return 群组设置列表
     */
    List<GroupSettings> findByNotificationSetting(GroupSettings.NotificationSetting notificationSetting);

    /**
     * 查询启用消息加密的群组
     * 
     * @return 启用加密的群组列表
     */
    List<GroupSettings> findByMessageEncryptionEnabledTrue();

    /**
     * 查询启用敏感词过滤的群组
     * 
     * @return 启用过滤的群组列表
     */
    List<GroupSettings> findBySensitiveWordFilterEnabledTrue();

    /**
     * 查询启用防刷屏的群组
     * 
     * @return 启用防刷屏的群组列表
     */
    List<GroupSettings> findByAntiSpamEnabledTrue();

    /**
     * 查询群组设置统计信息
     * 
     * @return 统计信息
     */
    @Query("SELECT COUNT(gs), " +
           "COUNT(CASE WHEN gs.joinApprovalRequired = true THEN 1 END), " +
           "COUNT(CASE WHEN gs.messageModerationEnabled = true THEN 1 END), " +
           "COUNT(CASE WHEN gs.fileSharingEnabled = true THEN 1 END), " +
           "COUNT(CASE WHEN gs.botEnabled = true THEN 1 END) " +
           "FROM GroupSettings gs")
    Object[] getSettingsStatistics();

    /**
     * 查询可见性分布统计
     * 
     * @return 可见性统计结果
     */
    @Query("SELECT gs.groupVisibility, COUNT(gs) FROM GroupSettings gs " +
           "GROUP BY gs.groupVisibility ORDER BY COUNT(gs) DESC")
    List<Object[]> getVisibilityDistribution();

    /**
     * 查询审核模式分布统计
     * 
     * @return 审核模式统计结果
     */
    @Query("SELECT gs.messageModerationMode, COUNT(gs) FROM GroupSettings gs " +
           "GROUP BY gs.messageModerationMode ORDER BY COUNT(gs) DESC")
    List<Object[]> getModerationModeDistribution();

    /**
     * 查询通知设置分布统计
     * 
     * @return 通知设置统计结果
     */
    @Query("SELECT gs.notificationSetting, COUNT(gs) FROM GroupSettings gs " +
           "GROUP BY gs.notificationSetting ORDER BY COUNT(gs) DESC")
    List<Object[]> getNotificationSettingDistribution();

    /**
     * 查询成员限制分布统计
     * 
     * @return 成员限制分布
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN gs.memberLimit <= 50 THEN 1 END) as small, " +
           "COUNT(CASE WHEN gs.memberLimit BETWEEN 51 AND 200 THEN 1 END) as medium, " +
           "COUNT(CASE WHEN gs.memberLimit BETWEEN 201 AND 500 THEN 1 END) as large, " +
           "COUNT(CASE WHEN gs.memberLimit > 500 THEN 1 END) as xlarge " +
           "FROM GroupSettings gs")
    Object[] getMemberLimitDistribution();

    /**
     * 查询文件大小限制分布统计
     * 
     * @return 文件大小限制分布
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN gs.fileSizeLimitMb <= 10 THEN 1 END) as small, " +
           "COUNT(CASE WHEN gs.fileSizeLimitMb BETWEEN 11 AND 50 THEN 1 END) as medium, " +
           "COUNT(CASE WHEN gs.fileSizeLimitMb BETWEEN 51 AND 100 THEN 1 END) as large, " +
           "COUNT(CASE WHEN gs.fileSizeLimitMb > 100 THEN 1 END) as xlarge " +
           "FROM GroupSettings gs")
    Object[] getFileSizeLimitDistribution();

    /**
     * 查询平均成员限制
     * 
     * @return 平均成员限制
     */
    @Query("SELECT AVG(gs.memberLimit) FROM GroupSettings gs")
    Double getAverageMemberLimit();

    /**
     * 查询平均文件大小限制
     * 
     * @return 平均文件大小限制
     */
    @Query("SELECT AVG(gs.fileSizeLimitMb) FROM GroupSettings gs")
    Double getAverageFileSizeLimit();

    /**
     * 查询最大成员限制
     * 
     * @return 最大成员限制
     */
    @Query("SELECT MAX(gs.memberLimit) FROM GroupSettings gs")
    Integer getMaxMemberLimit();

    /**
     * 查询最小成员限制
     * 
     * @return 最小成员限制
     */
    @Query("SELECT MIN(gs.memberLimit) FROM GroupSettings gs")
    Integer getMinMemberLimit();

    /**
     * 批量更新成员限制
     * 
     * @param groupIds 群组ID列表
     * @param memberLimit 新的成员限制
     * @param modifierId 修改人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupSettings gs SET gs.memberLimit = :memberLimit, " +
           "gs.lastModifiedBy = :modifierId, gs.modificationNotes = 'Batch update member limit' " +
           "WHERE gs.groupId IN :groupIds")
    int batchUpdateMemberLimit(@Param("groupIds") List<Long> groupIds, 
                              @Param("memberLimit") Integer memberLimit, 
                              @Param("modifierId") Long modifierId);

    /**
     * 批量更新文件分享设置
     * 
     * @param groupIds 群组ID列表
     * @param fileSharingEnabled 是否启用文件分享
     * @param modifierId 修改人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupSettings gs SET gs.fileSharingEnabled = :fileSharingEnabled, " +
           "gs.lastModifiedBy = :modifierId, gs.modificationNotes = 'Batch update file sharing' " +
           "WHERE gs.groupId IN :groupIds")
    int batchUpdateFileSharingSetting(@Param("groupIds") List<Long> groupIds, 
                                     @Param("fileSharingEnabled") Boolean fileSharingEnabled, 
                                     @Param("modifierId") Long modifierId);

    /**
     * 批量更新消息审核设置
     * 
     * @param groupIds 群组ID列表
     * @param moderationEnabled 是否启用审核
     * @param moderationMode 审核模式
     * @param modifierId 修改人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupSettings gs SET gs.messageModerationEnabled = :moderationEnabled, " +
           "gs.messageModerationMode = :moderationMode, gs.lastModifiedBy = :modifierId, " +
           "gs.modificationNotes = 'Batch update message moderation' " +
           "WHERE gs.groupId IN :groupIds")
    int batchUpdateModerationSettings(@Param("groupIds") List<Long> groupIds, 
                                     @Param("moderationEnabled") Boolean moderationEnabled,
                                     @Param("moderationMode") GroupSettings.ModerationMode moderationMode,
                                     @Param("modifierId") Long modifierId);

    /**
     * 查询需要配置优化的群组
     * 
     * @return 需要优化的群组设置列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE " +
           "(gs.memberLimit > 1000 AND gs.messageModerationEnabled = false) OR " +
           "(gs.fileSizeLimitMb > 500 AND gs.antiSpamEnabled = false) OR " +
           "(gs.groupVisibility = 'PUBLIC' AND gs.joinApprovalRequired = false AND gs.memberLimit > 500)")
    List<GroupSettings> findGroupsNeedingOptimization();

    /**
     * 查询安全配置较弱的群组
     * 
     * @return 安全配置弱的群组设置列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE " +
           "gs.messageModerationEnabled = false AND " +
           "gs.sensitiveWordFilterEnabled = false AND " +
           "gs.antiSpamEnabled = false AND " +
           "gs.joinApprovalRequired = false")
    List<GroupSettings> findGroupsWithWeakSecurity();

    /**
     * 查询高级功能使用率
     * 
     * @return 高级功能使用统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN gs.messageEncryptionEnabled = true THEN 1 END) * 100.0 / COUNT(gs) as encryptionRate, " +
           "COUNT(CASE WHEN gs.botEnabled = true THEN 1 END) * 100.0 / COUNT(gs) as botRate, " +
           "COUNT(CASE WHEN gs.messageModerationEnabled = true THEN 1 END) * 100.0 / COUNT(gs) as moderationRate " +
           "FROM GroupSettings gs")
    Object[] getAdvancedFeatureUsageRates();

    /**
     * 查询最近修改的群组设置
     * 
     * @param pageable 分页参数
     * @return 最近修改的设置分页结果
     */
    @Query("SELECT gs FROM GroupSettings gs ORDER BY gs.updatedAt DESC")
    Page<GroupSettings> findRecentlyModified(Pageable pageable);

    /**
     * 查询指定修改人的群组设置
     * 
     * @param modifierId 修改人ID
     * @param pageable 分页参数
     * @return 修改人的设置分页结果
     */
    Page<GroupSettings> findByLastModifiedByOrderByUpdatedAtDesc(Long modifierId, Pageable pageable);

    /**
     * 查询有自定义设置的群组
     * 
     * @return 有自定义设置的群组列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE gs.customSettings IS NOT NULL " +
           "AND LENGTH(gs.customSettings) > 0")
    List<GroupSettings> findGroupsWithCustomSettings();

    /**
     * 查询有群组标签的群组
     * 
     * @return 有标签的群组列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE gs.groupTags IS NOT NULL " +
           "AND LENGTH(gs.groupTags) > 0")
    List<GroupSettings> findGroupsWithTags();

    /**
     * 根据标签搜索群组
     * 
     * @param tag 标签关键词
     * @return 匹配标签的群组列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE gs.groupTags LIKE %:tag%")
    List<GroupSettings> findGroupsByTag(@Param("tag") String tag);

    /**
     * 查询配置完整性检查
     * 
     * @return 配置问题的群组列表
     */
    @Query("SELECT gs FROM GroupSettings gs WHERE " +
           "(gs.messageModerationEnabled = true AND gs.messageModerationMode = 'NONE') OR " +
           "(gs.messageEncryptionEnabled = true AND gs.encryptionAlgorithm IS NULL) OR " +
           "(gs.botEnabled = true AND gs.botConfiguration IS NULL)")
    List<GroupSettings> findGroupsWithConfigurationIssues();

    /**
     * 删除群组设置
     * 
     * @param groupId 群组ID
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM GroupSettings gs WHERE gs.groupId = :groupId")
    int deleteByGroupId(@Param("groupId") Long groupId);
}
