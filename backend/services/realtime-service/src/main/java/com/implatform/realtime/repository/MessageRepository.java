package com.implatform.realtime.repository;

import java.time.Instant;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.realtime.entity.Message;
import com.implatform.realtime.entity.Message.MessageStatus;

/**
 * 消息数据访问层 - IM平台消息数据库操作接口（R2DBC响应式版本）
 *
 * <p><strong>Repository概述</strong>：
 * 本接口提供完整的消息数据访问功能，负责消息实体的CRUD操作、复杂查询、统计分析等数据库交互。
 * 采用Spring Data R2DBC技术，提供高性能的响应式数据访问能力，支持分页查询、条件筛选、聚合统计等功能。
 *
 * <p><strong>核心数据操作</strong>：
 * <ul>
 *   <li><strong>基础CRUD</strong>：消息的创建、读取、更新、删除操作（响应式）</li>
 *   <li><strong>条件查询</strong>：基于会话、发送者、时间、状态等条件的复杂查询</li>
 *   <li><strong>分页查询</strong>：支持大数据量的分页加载和排序</li>
 *   <li><strong>聚合统计</strong>：消息数量、未读统计、活跃度分析</li>
 *   <li><strong>全文搜索</strong>：消息内容的模糊搜索和关键词匹配</li>
 *   <li><strong>特殊功能</strong>：自毁消息、消息反应、转发等特殊查询</li>
 * </ul>
 *
 * <p><strong>数据库表结构</strong>：
 * 对应数据库表：messages
 * <ul>
 *   <li><strong>主键</strong>：id (BIGINT, AUTO_INCREMENT)</li>
 *   <li><strong>外键</strong>：conversation_id → conversations.id</li>
 *   <li><strong>外键</strong>：sender_id → users.id</li>
 *   <li><strong>索引</strong>：conversation_id, sender_id, created_at, status</li>
 *   <li><strong>复合索引</strong>：(conversation_id, created_at), (sender_id, created_at)</li>
 * </ul>
 *
 * <p><strong>响应式查询性能优化</strong>：
 * <ul>
 *   <li><strong>索引策略</strong>：基于查询模式设计的复合索引</li>
 *   <li><strong>分页优化</strong>：使用游标分页避免深度分页性能问题</li>
 *   <li><strong>流式处理</strong>：大数据量查询使用Flux流式处理</li>
 *   <li><strong>背压处理</strong>：支持响应式背压机制，避免内存溢出</li>
 *   <li><strong>非阻塞IO</strong>：所有数据库操作均为非阻塞响应式</li>
 * </ul>
 *
 * <p><strong>数据一致性保证</strong>：
 * <ul>
 *   <li><strong>事务支持</strong>：关键操作使用响应式事务保证数据一致性</li>
 *   <li><strong>外键约束</strong>：确保消息与会话、用户的引用完整性</li>
 *   <li><strong>状态管理</strong>：消息状态变更的原子性操作</li>
 *   <li><strong>软删除</strong>：使用状态标记实现软删除，保留数据完整性</li>
 * </ul>
 *
 * <p><strong>特殊功能支持</strong>：
 * <ul>
 *   <li><strong>自毁消息</strong>：支持定时自毁和阅读后自毁的查询</li>
 *   <li><strong>消息反应</strong>：支持消息点赞、表情反应的关联查询</li>
 *   <li><strong>消息转发</strong>：支持转发消息的链式查询</li>
 *   <li><strong>媒体消息</strong>：支持图片、文件、语音等媒体消息查询</li>
 * </ul>
 *
 * <p><strong>响应式缓存策略</strong>：
 * <ul>
 *   <li><strong>查询缓存</strong>：会话最新消息、未读计数等热点数据缓存</li>
 *   <li><strong>流式缓存</strong>：消息列表流式结果缓存</li>
 *   <li><strong>统计缓存</strong>：消息统计数据定期更新缓存</li>
 *   <li><strong>缓存失效</strong>：消息变更时相关缓存的及时失效</li>
 * </ul>
 *
 * <p><strong>监控和维护</strong>：
 * <ul>
 *   <li><strong>查询监控</strong>：慢查询监控和性能分析</li>
 *   <li><strong>数据清理</strong>：过期消息和垃圾数据的定期清理</li>
 *   <li><strong>索引维护</strong>：索引使用情况监控和优化</li>
 *   <li><strong>容量管理</strong>：数据增长监控和分区策略</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 * @version 2.0.0 (R2DBC响应式版本)
 */
@Repository
public interface MessageRepository extends R2dbcRepository<Message, Long> {
    
    /**
     * 根据会话ID分页查询消息（按创建时间倒序）
     *
     * <p><strong>查询功能</strong>：
     * 获取指定会话的所有消息，按创建时间倒序排列（最新消息在前），支持分页加载。
     * 这是消息列表展示的核心查询方法，用于聊天界面的消息历史加载。
     *
     * <p><strong>数据库操作</strong>：
     * <pre>{@code
     * SELECT * FROM messages
     * WHERE conversation_id = ?
     * ORDER BY created_at DESC
     * LIMIT ? OFFSET ?
     * }</pre>
     *
     * <p><strong>索引使用</strong>：
     * 使用复合索引 (conversation_id, created_at DESC) 优化查询性能，
     * 避免文件排序操作，确保大数据量下的查询效率。
     *
     * <p><strong>响应式性能特征</strong>：
     * <ul>
     *   <li><strong>查询复杂度</strong>：O(log n + k)，其中k为返回记录数</li>
     *   <li><strong>内存使用</strong>：流式处理，内存使用可控</li>
     *   <li><strong>缓存友好</strong>：查询结果适合缓存，提升重复访问性能</li>
     *   <li><strong>并发安全</strong>：只读查询，支持高并发访问</li>
     *   <li><strong>背压支持</strong>：支持响应式背压机制</li>
     * </ul>
     *
     * <p><strong>使用场景</strong>：
     * <ul>
     *   <li>聊天界面消息历史加载</li>
     *   <li>消息列表分页展示</li>
     *   <li>会话消息导出功能</li>
     *   <li>消息统计和分析</li>
     * </ul>
     *
     * @param conversationId 会话ID，必须为有效的会话标识符
     * @param limit 限制返回的消息数量
     * @param offset 偏移量，用于分页
     * @return Flux&lt;Message&gt; 消息流，按创建时间倒序
     *
     * @implNote
     * 建议限制数量设置为20-50条，避免单次查询数据量过大。
     * 对于历史消息较多的会话，考虑使用游标分页优化性能。
     *
     * @since 2.0.0 (R2DBC版本)
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findByConversationIdOrderByCreatedAtDesc(@Param("conversationId") Long conversationId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据会话ID和状态分页查询消息（排除特定状态）
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId AND status != :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findByConversationIdAndStatusNotOrderByCreatedAtDesc(@Param("conversationId") Long conversationId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据会话ID、内容和状态搜索消息
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId AND LOWER(content) LIKE LOWER(CONCAT('%', :content, '%')) AND status != :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findByConversationIdAndContentContainingIgnoreCaseAndStatusNot(@Param("conversationId") Long conversationId, @Param("content") String content, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查询用户未读消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE receiver_id = :userId AND is_read = false")
    Mono<Long> countUnreadMessages(@Param("userId") Long userId);

    /**
     * 查询用户在指定会话中的未读消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE conversation_id = :conversationId AND receiver_id = :receiverId AND is_read = false")
    Mono<Long> countByConversationIdAndReceiverIdAndIsReadFalse(@Param("conversationId") Long conversationId, @Param("receiverId") Long receiverId);

    /**
     * 查询用户所有未读消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE receiver_id = :receiverId AND is_read = false")
    Mono<Long> countByReceiverIdAndIsReadFalse(@Param("receiverId") Long receiverId);

    /**
     * 查询会话中指定接收者的未读消息
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId AND receiver_id = :receiverId AND is_read = false")
    Flux<Message> findByConversationIdAndReceiverIdAndIsReadFalse(@Param("conversationId") Long conversationId, @Param("receiverId") Long receiverId);

    /**
     * 根据发送者和接收者查询消息
     */
    @Query("SELECT * FROM messages WHERE sender_id = :senderId AND receiver_id = :receiverId ORDER BY created_at DESC")
    Flux<Message> findBySenderIdAndReceiverIdOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("receiverId") Long receiverId);

    /**
     * 查找所有自毁消息
     */
    @Query("SELECT * FROM messages WHERE is_self_destruct = true AND is_destructed = false")
    Flux<Message> findAllSelfDestructMessages();

    /**
     * 查找过期的自毁消息
     */
    @Query("SELECT * FROM messages WHERE is_self_destruct = true AND is_destructed = false " +
           "AND self_destruct_at IS NOT NULL AND self_destruct_at <= :cutoffTime " +
           "LIMIT :limit OFFSET :offset")
    Flux<Message> findExpiredSelfDestructMessages(@Param("cutoffTime") Instant cutoffTime, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户的自毁消息
     */
    @Query("SELECT * FROM messages WHERE sender_id = :userId AND is_self_destruct = true " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findUserSelfDestructMessages(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找会话中的自毁消息
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId AND is_self_destruct = true " +
           "AND is_destructed = false ORDER BY created_at DESC")
    Flux<Message> findConversationSelfDestructMessages(@Param("conversationId") Long conversationId);

    /**
     * 查找会话中有自毁时间的消息
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId " +
           "AND self_destruct_time IS NOT NULL ORDER BY created_at DESC")
    Flux<Message> findByConversationIdAndSelfDestructTimeIsNotNull(@Param("conversationId") Long conversationId);

    /**
     * 统计自毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE is_self_destruct = true")
    Mono<Long> countSelfDestructMessages();

    /**
     * 统计已销毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE is_self_destruct = true AND is_destructed = true")
    Mono<Long> countDestructedMessages();

    /**
     * 统计用户发送的自毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE sender_id = :userId AND is_self_destruct = true")
    Mono<Long> countUserSelfDestructMessages(@Param("userId") Long userId);

    /**
     * 查找即将到期的自毁消息（用于提醒）
     */
    @Query("SELECT * FROM messages WHERE is_self_destruct = true AND is_destructed = false " +
           "AND self_destruct_at IS NOT NULL AND self_destruct_at BETWEEN :startTime AND :endTime")
    Flux<Message> findSoonToExpireMessages(@Param("startTime") Instant startTime,
                                          @Param("endTime") Instant endTime);

    /**
     * 查找阅读后自毁的消息
     */
    @Query("SELECT * FROM messages WHERE is_self_destruct = true AND is_destructed = false " +
           "AND self_destruct_time = -1")
    Flux<Message> findReadOnceMessages();

    /**
     * 按自毁原因统计
     */
    @Query("SELECT destruct_reason, COUNT(*) FROM messages WHERE is_destructed = true " +
           "GROUP BY destruct_reason")
    Flux<Object[]> countByDestructReason();

    /**
     * 查找需要清理的已销毁消息
     */
    @Query("SELECT * FROM messages WHERE is_destructed = true " +
           "AND updated_at < :cutoffTime LIMIT :limit OFFSET :offset")
    Flux<Message> findDestructedMessagesForCleanup(@Param("cutoffTime") Instant cutoffTime, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找已编辑的消息
     */
    @Query("SELECT * FROM messages WHERE is_edited = true ORDER BY last_edit_time DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findEditedMessages(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户编辑过的消息
     */
    @Query("SELECT * FROM messages WHERE sender_id = :userId AND is_edited = true " +
           "ORDER BY last_edit_time DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findUserEditedMessages(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计已编辑消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE is_edited = true")
    Mono<Long> countEditedMessages();

    /**
     * 统计用户编辑过的消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE sender_id = :userId AND is_edited = true")
    Mono<Long> countUserEditedMessages(@Param("userId") Long userId);

    /**
     * 查找编辑次数最多的消息
     */
    @Query("SELECT * FROM messages WHERE edit_count > 0 ORDER BY edit_count DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findMostEditedMessages(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找最近编辑的消息
     */
    @Query("SELECT * FROM messages WHERE last_edit_time >= :since ORDER BY last_edit_time DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findRecentlyEditedMessages(@Param("since") Instant since, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计编辑活动
     */
    @Query("SELECT " +
           "COUNT(*) as total_messages, " +
           "COUNT(CASE WHEN is_edited = true THEN 1 END) as edited_messages, " +
           "AVG(edit_count) as avg_edits_per_message " +
           "FROM messages WHERE created_at >= :since")
    Mono<Object[]> getEditActivity(@Param("since") Instant since);

    /**
     * 统计用户接收的自毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE receiver_id = :receiverId AND self_destruct_at IS NOT NULL")
    Mono<Long> countByReceiverIdAndSelfDestructAtIsNotNull(@Param("receiverId") Long receiverId);

    /**
     * 统计用户发送的已销毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE sender_id = :senderId AND self_destruct_at IS NOT NULL AND self_destruct_at < :destructTime")
    Mono<Long> countBySenderIdAndSelfDestructAtIsNotNullAndSelfDestructAtBefore(@Param("senderId") Long senderId, @Param("destructTime") Instant destructTime);

    /**
     * 统计用户接收的已销毁消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE receiver_id = :receiverId AND self_destruct_at IS NOT NULL AND self_destruct_at < :destructTime")
    Mono<Long> countByReceiverIdAndSelfDestructAtIsNotNullAndSelfDestructAtBefore(@Param("receiverId") Long receiverId, @Param("destructTime") Instant destructTime);

    // ==================== 管理接口查询方法 ====================

    /**
     * 根据发送者ID查询消息（按创建时间倒序）
     */
    @Query("SELECT * FROM messages WHERE sender_id = :senderId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findBySenderIdOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据内容搜索消息（忽略大小写）
     */
    @Query("SELECT * FROM messages WHERE LOWER(content) LIKE LOWER(CONCAT('%', :content, '%')) LIMIT :limit OFFSET :offset")
    Flux<Message> findByContentContainingIgnoreCase(@Param("content") String content, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计指定时间后创建的消息数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE created_at > :createdAt")
    Mono<Long> countByCreatedAtAfter(@Param("createdAt") Instant createdAt);

    /**
     * 根据会话ID和消息类型查询消息（按创建时间倒序）
     */
    @Query("SELECT * FROM messages WHERE conversation_id = :conversationId AND message_type = :messageType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findByConversationIdAndMessageTypeOrderByCreatedAtDesc(@Param("conversationId") Long conversationId, @Param("messageType") String messageType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据发送者ID和状态查询消息（按创建时间倒序）
     */
    @Query("SELECT * FROM messages WHERE sender_id = :senderId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findBySenderIdAndStatusOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据内容和消息类型搜索消息（忽略大小写）
     */
    @Query("SELECT * FROM messages WHERE LOWER(content) LIKE LOWER(CONCAT('%', :content, '%')) AND message_type = :messageType LIMIT :limit OFFSET :offset")
    Flux<Message> findByContentContainingIgnoreCaseAndMessageType(@Param("content") String content, @Param("messageType") String messageType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计指定时间后活跃发送者数量
     */
    @Query("SELECT COUNT(DISTINCT sender_id) FROM messages WHERE created_at > :createdAt")
    Mono<Long> countDistinctSendersByCreatedAtAfter(@Param("createdAt") Instant createdAt);

    /**
     * 查询热门消息（根据反应数量排序）
     */
    @Query("SELECT m.* FROM messages m LEFT JOIN message_reaction_stats mrs ON m.id = mrs.message_id " +
           "WHERE m.status = 'SENT' ORDER BY COALESCE(mrs.total_reactions, 0) DESC, m.created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findPopularMessages(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查询敏感消息（被标记或包含敏感词的消息）
     */
    @Query("SELECT * FROM messages WHERE status = 'SENT' AND " +
           "(extra LIKE '%sensitive%' OR extra LIKE '%violation%' OR extra LIKE '%spam%') " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findSensitiveMessages(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取指定日期范围内的消息趋势统计
     */
    @Query("SELECT DATE(created_at), COUNT(*) FROM messages " +
           "WHERE created_at BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(created_at) ORDER BY DATE(created_at)")
    Flux<Object[]> getMessageTrendsByDateRange(@Param("startDate") Instant startDate,
                                               @Param("endDate") Instant endDate);

    /**
     * 查询用户的系统通知消息
     */
    @Query("SELECT * FROM messages WHERE receiver_id = :userId AND sender_id = :systemUserId " +
           "AND message_type = 'SYSTEM' ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Message> findSystemNotificationsByUserId(@Param("userId") Long userId,
                                                  @Param("systemUserId") Long systemUserId,
                                                  @Param("limit") int limit,
                                                  @Param("offset") long offset);

    /**
     * 统计用户未读系统通知数量
     */
    @Query("SELECT COUNT(*) FROM messages WHERE receiver_id = :userId AND sender_id = :systemUserId " +
           "AND message_type = 'SYSTEM' AND status != 'READ'")
    Mono<Long> countUnreadSystemNotifications(@Param("userId") Long userId,
                                       @Param("systemUserId") Long systemUserId);

    /**
     * 批量标记消息为已读
     */
    @Query("UPDATE messages SET status = 'read' WHERE id = ANY(:messageIds) AND receiver_id = :userId")
    Mono<Void> markMessagesAsRead(@Param("messageIds") Long[] messageIds, @Param("userId") Long userId);
}