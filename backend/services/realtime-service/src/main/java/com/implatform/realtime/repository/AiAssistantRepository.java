package com.implatform.realtime.repository;

import com.implatform.realtime.entity.AiAssistant;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * AI助手数据访问层 - IM平台AI助手数据库操作接口
 *
 * <p><strong>Repository概述</strong>：
 * 本接口提供完整的AI助手数据访问功能，负责AI助手实体的CRUD操作、助手搜索、
 * 能力管理、使用统计等数据库交互。支持多种AI提供商和助手类型的统一管理。
 *
 * <p><strong>核心数据操作</strong>：
 * <ul>
 *   <li><strong>助手管理</strong>：AI助手的创建、配置、启用/禁用、删除</li>
 *   <li><strong>助手搜索</strong>：按名称、提供商、能力、标签的搜索查询</li>
 *   <li><strong>权限控制</strong>：公开/私有助手的访问权限管理</li>
 *   <li><strong>使用统计</strong>：助手使用频率、对话数量、用户评价统计</li>
 *   <li><strong>能力查询</strong>：按助手能力和特性的筛选查询</li>
 * </ul>
 *
 * <p><strong>数据库表结构</strong>：
 * 对应数据库表：ai_assistants
 * <ul>
 *   <li><strong>主键</strong>：id (BIGINT, AUTO_INCREMENT)</li>
 *   <li><strong>外键</strong>：creator_id → users.id</li>
 *   <li><strong>唯一索引</strong>：(name, creator_id) 创建者内助手名称唯一</li>
 *   <li><strong>普通索引</strong>：provider, is_public, is_enabled, created_at</li>
 *   <li><strong>复合索引</strong>：(is_public, is_enabled), (provider, is_enabled)</li>
 *   <li><strong>全文索引</strong>：name, description, capabilities (用于助手搜索)</li>
 * </ul>
 *
 * <p><strong>AI提供商支持</strong>：
 * <ul>
 *   <li><strong>OPENAI</strong>：OpenAI GPT系列模型</li>
 *   <li><strong>ANTHROPIC</strong>：Anthropic Claude系列模型</li>
 *   <li><strong>GOOGLE</strong>：Google Bard/Gemini模型</li>
 *   <li><strong>BAIDU</strong>：百度文心一言模型</li>
 *   <li><strong>ALIBABA</strong>：阿里通义千问模型</li>
 *   <li><strong>CUSTOM</strong>：自定义AI模型接入</li>
 * </ul>
 *
 * <p><strong>助手能力分类</strong>：
 * <ul>
 *   <li><strong>文本生成</strong>：文章写作、内容创作、文本润色</li>
 *   <li><strong>代码编程</strong>：代码生成、调试、代码审查</li>
 *   <li><strong>数据分析</strong>：数据处理、图表生成、统计分析</li>
 *   <li><strong>翻译服务</strong>：多语言翻译、本地化服务</li>
 *   <li><strong>创意设计</strong>：创意构思、设计建议、艺术创作</li>
 *   <li><strong>专业咨询</strong>：法律、医疗、教育等专业领域咨询</li>
 * </ul>
 *
 * <p><strong>查询性能优化</strong>：
 * <ul>
 *   <li><strong>索引策略</strong>：基于查询模式设计的复合索引</li>
 *   <li><strong>搜索优化</strong>：全文索引支持的快速助手搜索</li>
 *   <li><strong>分页优化</strong>：助手列表的高效分页查询</li>
 *   <li><strong>缓存策略</strong>：热门助手信息的Redis缓存</li>
 * </ul>
 *
 * <p><strong>权限控制机制</strong>：
 * <ul>
 *   <li><strong>公开助手</strong>：所有用户可见和使用的助手</li>
 *   <li><strong>私有助手</strong>：仅创建者可见和使用的助手</li>
 *   <li><strong>团队助手</strong>：特定团队或组织内共享的助手</li>
 *   <li><strong>付费助手</strong>：需要付费才能使用的高级助手</li>
 * </ul>
 *
 * <p><strong>统计分析支持</strong>：
 * <ul>
 *   <li><strong>使用统计</strong>：助手使用次数、对话时长统计</li>
 *   <li><strong>用户评价</strong>：用户对助手的评分和反馈</li>
 *   <li><strong>性能指标</strong>：响应时间、成功率等性能数据</li>
 *   <li><strong>趋势分析</strong>：助手使用趋势和热度分析</li>
 * </ul>
 *
 * <p><strong>数据一致性保证</strong>：
 * <ul>
 *   <li><strong>外键约束</strong>：确保助手与创建者的引用完整性</li>
 *   <li><strong>状态管理</strong>：助手启用/禁用状态的一致性</li>
 *   <li><strong>配置验证</strong>：助手配置参数的有效性验证</li>
 *   <li><strong>版本控制</strong>：助手配置变更的版本管理</li>
 * </ul>
 *
 * <p><strong>缓存策略</strong>：
 * <ul>
 *   <li><strong>助手信息缓存</strong>：基本助手信息的Redis缓存</li>
 *   <li><strong>搜索缓存</strong>：热门搜索结果的缓存</li>
 *   <li><strong>配置缓存</strong>：助手配置参数的缓存</li>
 *   <li><strong>统计缓存</strong>：使用统计数据的定期更新缓存</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 * @version 2.0.0 (R2DBC响应式版本)
 */
@Repository
public interface AiAssistantRepository extends R2dbcRepository<AiAssistant, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据名称查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE name = :name AND creator_id = :creatorId")
    Mono<AiAssistant> findByNameAndCreatorId(@Param("name") String name, @Param("creatorId") Long creatorId);

    /**
     * 根据创建者查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE creator_id = :creatorId LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByCreatorId(@Param("creatorId") Long creatorId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据创建者和启用状态查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE creator_id = :creatorId AND is_enabled = :isEnabled LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByCreatorIdAndIsEnabled(@Param("creatorId") Long creatorId, @Param("isEnabled") boolean isEnabled, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找公开的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByIsPublicTrueAndIsEnabledTrue(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据提供商查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE provider = :provider AND is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByProviderAndIsPublicTrueAndIsEnabledTrue(@Param("provider") String provider, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据助手类型查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE assistant_type = :assistantType AND is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByAssistantTypeAndIsPublicTrueAndIsEnabledTrue(@Param("assistantType") String assistantType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据能力查找助手
     */
    @Query("SELECT * FROM ai_assistants WHERE capabilities @> CAST(:capability AS jsonb) AND is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByCapabilityAndIsPublicTrueAndIsEnabledTrue(@Param("capability") String capability, @Param("limit") int limit, @Param("offset") long offset);

    // ==================== 搜索查询 ====================

    /**
     * 搜索助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true " +
           "AND (LOWER(name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> searchAssistants(@Param("keyword") String keyword, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 高级搜索助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_enabled = true " +
           "AND (:keyword IS NULL OR LOWER(name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "AND (:provider IS NULL OR provider = :provider) " +
           "AND (:assistantType IS NULL OR assistant_type = :assistantType) " +
           "AND (:isPublic IS NULL OR is_public = :isPublic) " +
           "AND (:creatorId IS NULL OR creator_id = :creatorId) LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> advancedSearchAssistants(@Param("keyword") String keyword,
                                              @Param("provider") String provider,
                                              @Param("assistantType") String assistantType,
                                              @Param("isPublic") Boolean isPublic,
                                              @Param("creatorId") Long creatorId,
                                              @Param("limit") int limit,
                                              @Param("offset") long offset);

    // ==================== 排序查询 ====================

    /**
     * 按使用次数排序的热门助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true ORDER BY usage_count DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByIsPublicTrueAndIsEnabledTrueOrderByUsageCountDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 按评分排序的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true ORDER BY rating DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByIsPublicTrueAndIsEnabledTrueOrderByRatingDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 按创建时间排序的最新助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByIsPublicTrueAndIsEnabledTrueOrderByCreatedAtDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 按自定义排序顺序的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true ORDER BY sort_order ASC, created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findByIsPublicTrueAndIsEnabledTrueOrderBySortOrderAscCreatedAtDesc(@Param("limit") int limit, @Param("offset") long offset);

    // ==================== 统计查询 ====================

    /**
     * 统计用户创建的助手数量
     */
    @Query("SELECT COUNT(*) FROM ai_assistants WHERE creator_id = :creatorId")
    Mono<Long> countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 统计用户启用的助手数量
     */
    @Query("SELECT COUNT(*) FROM ai_assistants WHERE creator_id = :creatorId AND is_enabled = true")
    Mono<Long> countByCreatorIdAndIsEnabledTrue(@Param("creatorId") Long creatorId);

    /**
     * 统计公开助手数量
     */
    @Query("SELECT COUNT(*) FROM ai_assistants WHERE is_public = true AND is_enabled = true")
    Mono<Long> countByIsPublicTrueAndIsEnabledTrue();

    /**
     * 统计各提供商的助手数量
     */
    @Query("SELECT provider, COUNT(*) FROM ai_assistants WHERE is_public = true AND is_enabled = true GROUP BY provider")
    Flux<Object[]> countAssistantsByProvider();

    /**
     * 统计各类型的助手数量
     */
    @Query("SELECT assistant_type, COUNT(*) FROM ai_assistants WHERE is_public = true AND is_enabled = true GROUP BY assistant_type")
    Flux<Object[]> countAssistantsByType();

    /**
     * 获取助手使用统计
     */
    @Query("SELECT SUM(usage_count) FROM ai_assistants WHERE creator_id = :creatorId")
    Mono<Long> getTotalUsageByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取助手平均评分
     */
    @Query("SELECT AVG(rating) FROM ai_assistants WHERE creator_id = :creatorId AND rating_count > 0")
    Mono<Double> getAverageRatingByCreatorId(@Param("creatorId") Long creatorId);

    // ==================== 推荐查询 ====================

    /**
     * 获取推荐助手（基于使用次数和评分）
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true " +
           "ORDER BY (usage_count * 0.3 + rating * rating_count * 0.7) DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findRecommendedAssistants(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取用户可能感兴趣的助手（基于类型）
     */
    @Query("SELECT * FROM ai_assistants WHERE is_public = true AND is_enabled = true " +
           "AND assistant_type = ANY(:preferredTypes) AND creator_id != :userId " +
           "ORDER BY rating DESC, usage_count DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findAssistantsByPreferredTypes(@Param("userId") Long userId,
                                                    @Param("preferredTypes") String[] preferredTypes,
                                                    @Param("limit") int limit,
                                                    @Param("offset") long offset);

    /**
     * 获取相似助手（基于能力）
     */
    @Query("SELECT a.*, COUNT(capability) as common_capabilities FROM ai_assistants a, " +
           "jsonb_array_elements_text(a.capabilities) as capability " +
           "WHERE capability = ANY(:capabilities) AND a.id != :assistantId AND a.is_public = true AND a.is_enabled = true " +
           "GROUP BY a.id ORDER BY common_capabilities DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findSimilarAssistantsByCapabilities(@Param("assistantId") Long assistantId,
                                                       @Param("capabilities") String[] capabilities,
                                                       @Param("limit") int limit,
                                                       @Param("offset") long offset);

    // ==================== 更新操作 ====================

    /**
     * 增加使用次数
     */
    @Modifying
    @Query("UPDATE ai_assistants SET usage_count = usage_count + 1 WHERE id = :assistantId")
    Mono<Integer> incrementUsageCount(@Param("assistantId") Long assistantId);

    /**
     * 更新评分
     */
    @Modifying
    @Query("UPDATE ai_assistants SET rating = :rating, rating_count = rating_count + 1 WHERE id = :assistantId")
    Mono<Integer> updateRating(@Param("assistantId") Long assistantId, @Param("rating") Double rating);

    /**
     * 批量启用助手
     */
    @Modifying
    @Query("UPDATE ai_assistants SET is_enabled = true WHERE id = ANY(:assistantIds) AND creator_id = :creatorId")
    Mono<Integer> batchEnableAssistants(@Param("assistantIds") Long[] assistantIds, @Param("creatorId") Long creatorId);

    /**
     * 批量禁用助手
     */
    @Modifying
    @Query("UPDATE ai_assistants SET is_enabled = false WHERE id = ANY(:assistantIds) AND creator_id = :creatorId")
    Mono<Integer> batchDisableAssistants(@Param("assistantIds") Long[] assistantIds, @Param("creatorId") Long creatorId);

    /**
     * 批量设置公开状态
     */
    @Modifying
    @Query("UPDATE ai_assistants SET is_public = :isPublic WHERE id = ANY(:assistantIds) AND creator_id = :creatorId")
    Mono<Integer> batchUpdatePublicStatus(@Param("assistantIds") Long[] assistantIds,
                               @Param("creatorId") Long creatorId,
                               @Param("isPublic") boolean isPublic);

    // ==================== 验证查询 ====================

    /**
     * 检查助手名称是否已存在（同一创建者）
     */
    @Query("SELECT COUNT(*) > 0 FROM ai_assistants WHERE name = :name AND creator_id = :creatorId")
    Mono<Boolean> existsByNameAndCreatorId(@Param("name") String name, @Param("creatorId") Long creatorId);

    /**
     * 检查助手是否属于用户
     */
    @Query("SELECT COUNT(*) > 0 FROM ai_assistants WHERE id = :assistantId AND creator_id = :creatorId")
    Mono<Boolean> existsByIdAndCreatorId(@Param("assistantId") Long assistantId, @Param("creatorId") Long creatorId);

    /**
     * 检查助手是否可用
     */
    @Query("SELECT COUNT(*) > 0 FROM ai_assistants WHERE id = :assistantId AND is_enabled = true " +
           "AND (is_public = true OR creator_id = :userId)")
    Mono<Boolean> isAssistantAvailableForUser(@Param("assistantId") Long assistantId, @Param("userId") Long userId);

    // ==================== 特殊查询 ====================

    /**
     * 获取用户最近使用的助手
     * TODO: 需要实现 AI 助手与会话的关联机制
     */
    // @Query("SELECT DISTINCT a FROM AiAssistant a JOIN Conversation c ON a.id = c.assistantId " +
    //        "WHERE c.creatorId = :userId ORDER BY c.lastMessageAt DESC")
    // List<AiAssistant> findRecentlyUsedAssistants(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户收藏的助手
     * TODO: 需要实现用户收藏助手的关联机制
     */
    // @Query("SELECT a FROM AiAssistant a JOIN UserFavoriteAssistant f ON a.id = f.assistantId " +
    //        "WHERE f.userId = :userId AND a.isEnabled = true ORDER BY f.createdAt DESC")
    // List<AiAssistant> findFavoriteAssistants(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取未使用的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE usage_count = 0 AND is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findUnusedAssistants(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取高评分助手
     */
    @Query("SELECT * FROM ai_assistants WHERE rating >= :minRating AND rating_count >= :minRatingCount " +
           "AND is_public = true AND is_enabled = true ORDER BY rating DESC LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findHighRatedAssistants(@Param("minRating") Double minRating,
                                             @Param("minRatingCount") Long minRatingCount,
                                             @Param("limit") int limit,
                                             @Param("offset") long offset);

    /**
     * 获取特定提供商的模型列表
     */
    @Query("SELECT DISTINCT model_name FROM ai_assistants WHERE provider = :provider AND is_enabled = true")
    Flux<String> findModelNamesByProvider(@Param("provider") String provider);

    /**
     * 获取助手的能力统计
     */
    @Query("SELECT capability, COUNT(*) FROM (" +
           "SELECT jsonb_array_elements_text(capabilities) as capability FROM ai_assistants " +
           "WHERE is_public = true AND is_enabled = true" +
           ") t GROUP BY capability ORDER BY COUNT(*) DESC")
    Flux<Object[]> getCapabilityStatistics();

    /**
     * 查找配置相似的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE provider = :provider AND model_name = :modelName " +
           "AND assistant_type = :assistantType AND id != :assistantId AND is_public = true AND is_enabled = true LIMIT :limit OFFSET :offset")
    Flux<AiAssistant> findSimilarConfigAssistants(@Param("assistantId") Long assistantId,
                                                  @Param("provider") String provider,
                                                  @Param("modelName") String modelName,
                                                  @Param("assistantType") String assistantType,
                                                  @Param("limit") int limit,
                                                  @Param("offset") long offset);

    /**
     * 获取助手的详细信息（包含权限检查）
     */
    @Query("SELECT * FROM ai_assistants WHERE id = :assistantId AND is_enabled = true " +
           "AND (is_public = true OR creator_id = :userId)")
    Mono<AiAssistant> findAssistantWithPermissionCheck(@Param("assistantId") Long assistantId, @Param("userId") Long userId);

    /**
     * 获取系统默认助手
     */
    @Query("SELECT * FROM ai_assistants WHERE creator_id IS NULL AND is_public = true AND is_enabled = true ORDER BY sort_order")
    Flux<AiAssistant> findSystemDefaultAssistants();

    /**
     * 检查API密钥是否已被使用
     */
    @Query("SELECT COUNT(*) > 0 FROM ai_assistants WHERE api_key = :apiKey AND id != :assistantId")
    Mono<Boolean> isApiKeyAlreadyUsed(@Param("apiKey") String apiKey, @Param("assistantId") Long assistantId);

    /**
     * 获取需要同步配置的助手
     */
    @Query("SELECT * FROM ai_assistants WHERE is_enabled = true AND updated_at > :lastSyncTime")
    Flux<AiAssistant> findAssistantsNeedingSync(@Param("lastSyncTime") java.time.Instant lastSyncTime);
}
