package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupMember;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群组成员数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupMemberRepository extends R2dbcRepository<GroupMember, Long> {

    /**
     * 查找群组成员
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND user_id = :userId AND status = :status")
    Mono<GroupMember> findByGroupIdAndUserIdAndStatus(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("status") String status);

    /**
     * 查找群组所有活跃成员
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND status = :status ORDER BY joined_at ASC LIMIT :limit OFFSET :offset")
    Flux<GroupMember> findByGroupIdAndStatusOrderByJoinedAtAsc(@Param("groupId") Long groupId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户加入的所有群组
     */
    @Query("SELECT * FROM group_members WHERE user_id = :userId AND status = :status")
    Flux<GroupMember> findByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 查找群组管理员（包括群主）
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND status = :status " +
           "AND member_role IN ('OWNER', 'ADMIN') ORDER BY member_role ASC, joined_at ASC")
    Flux<GroupMember> findGroupAdmins(@Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 查找群主
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND member_role = :role AND status = :status")
    Mono<GroupMember> findByGroupIdAndMemberRoleAndStatus(@Param("groupId") Long groupId, @Param("role") String role, @Param("status") String status);

    /**
     * 统计群组成员数量
     */
    @Query("SELECT COUNT(*) FROM group_members WHERE group_id = :groupId AND status = :status")
    Mono<Long> countByGroupIdAndStatus(@Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 统计用户加入的群组数量
     */
    @Query("SELECT COUNT(*) FROM group_members WHERE user_id = :userId AND status = :status")
    Mono<Long> countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 检查用户是否为群组成员
     */
    @Query("SELECT COUNT(*) > 0 FROM group_members WHERE group_id = :groupId AND user_id = :userId AND status = :status")
    Mono<Boolean> existsByGroupIdAndUserIdAndStatus(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("status") String status);

    /**
     * 检查用户是否为群组管理员
     */
    @Query("SELECT COUNT(*) > 0 FROM group_members WHERE group_id = :groupId AND user_id = :userId " +
           "AND status = :status AND member_role IN ('OWNER', 'ADMIN')")
    Mono<Boolean> isGroupAdmin(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("status") String status);

    /**
     * 检查用户是否为群主
     */
    @Query("SELECT COUNT(*) > 0 FROM group_members WHERE group_id = :groupId AND user_id = :userId AND member_role = :role AND status = :status")
    Mono<Boolean> existsByGroupIdAndUserIdAndMemberRoleAndStatus(@Param("groupId") Long groupId, @Param("userId") Long userId, @Param("role") String role, @Param("status") String status);

    /**
     * 查找被禁言的成员
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND status = :status " +
           "AND is_muted = true AND (muted_until IS NULL OR muted_until > :currentTime)")
    Flux<GroupMember> findMutedMembers(@Param("groupId") Long groupId,
                                      @Param("status") String status,
                                      @Param("currentTime") Instant currentTime);

    /**
     * 查找最近加入的成员
     */
    @Query("SELECT * FROM group_members WHERE group_id = :groupId AND status = :status " +
           "ORDER BY joined_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupMember> findRecentMembers(@Param("groupId") Long groupId,
                                       @Param("status") String status,
                                       @Param("limit") int limit,
                                       @Param("offset") long offset);

    /**
     * 查找指定时间段加入的成员
     */
    @Query("SELECT gm FROM GroupMember gm WHERE gm.groupId = :groupId AND gm.status = :status " +
           "AND gm.joinedAt BETWEEN :startTime AND :endTime ORDER BY gm.joinedAt DESC")
    List<GroupMember> findMembersByJoinTime(@Param("groupId") Long groupId,
                                           @Param("status") GroupMember.MemberStatus status,
                                           @Param("startTime") Instant startTime,
                                           @Param("endTime") Instant endTime);

    /**
     * 更新成员角色
     */
    @Modifying
    @Query("UPDATE GroupMember gm SET gm.memberRole = :role, gm.updatedAt = :updateTime " +
           "WHERE gm.groupId = :groupId AND gm.userId = :userId AND gm.status = :status")
    int updateMemberRole(@Param("groupId") Long groupId, 
                        @Param("userId") Long userId, 
                        @Param("role") GroupMember.MemberRole role,
                        @Param("status") GroupMember.MemberStatus status,
                        @Param("updateTime") Instant updateTime);

    /**
     * 更新成员状态
     */
    @Modifying
    @Query("UPDATE group_members SET status = :newStatus, left_at = :leftTime, updated_at = :updateTime " +
           "WHERE group_id = :groupId AND user_id = :userId AND status = :currentStatus")
    Mono<Integer> updateMemberStatus(@Param("groupId") Long groupId,
                          @Param("userId") Long userId,
                          @Param("currentStatus") String currentStatus,
                          @Param("newStatus") String newStatus,
                          @Param("leftTime") Instant leftTime,
                          @Param("updateTime") Instant updateTime);

    /**
     * 设置成员禁言
     */
    @Modifying
    @Query("UPDATE group_members SET is_muted = :muted, muted_until = :muteUntil, updated_at = :updateTime " +
           "WHERE group_id = :groupId AND user_id = :userId AND status = :status")
    Mono<Integer> updateMemberMuteStatus(@Param("groupId") Long groupId,
                              @Param("userId") Long userId,
                              @Param("muted") Boolean muted,
                              @Param("muteUntil") Instant muteUntil,
                              @Param("status") String status,
                              @Param("updateTime") Instant updateTime);

    /**
     * 批量更新成员状态
     */
    @Modifying
    @Query("UPDATE group_members SET status = :newStatus, left_at = :leftTime, updated_at = :updateTime " +
           "WHERE group_id = :groupId AND user_id = ANY(:userIds) AND status = :currentStatus")
    Mono<Integer> batchUpdateMemberStatus(@Param("groupId") Long groupId,
                               @Param("userIds") Long[] userIds,
                               @Param("currentStatus") String currentStatus,
                               @Param("newStatus") String newStatus,
                               @Param("leftTime") Instant leftTime,
                               @Param("updateTime") Instant updateTime);

    /**
     * 删除群组的所有成员记录
     */
    @Modifying
    @Query("UPDATE GroupMember gm SET gm.status = :status, gm.leftAt = :leftTime, gm.updatedAt = :updateTime " +
           "WHERE gm.groupId = :groupId AND gm.status = com.implatform.realtime.entity.GroupMember$MemberStatus.ACTIVE")
    int disbandGroupMembers(@Param("groupId") Long groupId,
                           @Param("status") GroupMember.MemberStatus status,
                           @Param("leftTime") Instant leftTime,
                           @Param("updateTime") Instant updateTime);

    /**
     * 查找需要自动解除禁言的成员
     */
    @Query("SELECT gm FROM GroupMember gm WHERE gm.isMuted = true AND gm.mutedUntil IS NOT NULL " +
           "AND gm.mutedUntil <= :currentTime AND gm.status = :status")
    List<GroupMember> findMembersToUnmute(@Param("currentTime") Instant currentTime, 
                                         @Param("status") GroupMember.MemberStatus status);

    /**
     * 自动解除过期禁言
     */
    @Modifying
    @Query("UPDATE GroupMember gm SET gm.isMuted = false, gm.mutedUntil = null, gm.updatedAt = :updateTime " +
           "WHERE gm.isMuted = true AND gm.mutedUntil IS NOT NULL AND gm.mutedUntil <= :currentTime")
    int autoUnmuteExpiredMembers(@Param("currentTime") Instant currentTime, @Param("updateTime") Instant updateTime);

    /**
     * 获取群组成员统计信息
     */
    @Query("SELECT new map(" +
           "COUNT(gm) as totalMembers, " +
           "SUM(CASE WHEN gm.memberRole = com.implatform.realtime.entity.GroupMember$MemberRole.OWNER THEN 1 ELSE 0 END) as ownerCount, " +
           "SUM(CASE WHEN gm.memberRole = com.implatform.realtime.entity.GroupMember$MemberRole.ADMIN THEN 1 ELSE 0 END) as adminCount, " +
           "SUM(CASE WHEN gm.memberRole = com.implatform.realtime.entity.GroupMember$MemberRole.MEMBER THEN 1 ELSE 0 END) as memberCount, " +
           "SUM(CASE WHEN gm.isMuted = true THEN 1 ELSE 0 END) as mutedCount" +
           ") FROM GroupMember gm WHERE gm.groupId = :groupId AND gm.status = :status")
    List<Object> getGroupMemberStatistics(@Param("groupId") Long groupId, @Param("status") GroupMember.MemberStatus status);

    /**
     * 检查用户是否有管理权限
     */
    boolean existsByGroupIdAndUserIdAndMemberRoleInAndStatus(Long groupId, Long userId,
                                                           java.util.List<GroupMember.MemberRole> roles,
                                                           GroupMember.MemberStatus status);

    /**
     * 根据角色统计成员数量
     */
    long countByGroupIdAndMemberRoleAndStatus(Long groupId, GroupMember.MemberRole memberRole, GroupMember.MemberStatus status);

    /**
     * 批量更新群组所有成员状态（群组解散时使用）
     */
    @Modifying
    @Query("UPDATE GroupMember gm SET gm.status = :newStatus, gm.leftAt = :leftAt, gm.updatedAt = :updateTime " +
           "WHERE gm.groupId = :groupId AND gm.status = :currentStatus")
    int updateMemberStatusByGroupId(@Param("groupId") Long groupId,
                                   @Param("currentStatus") GroupMember.MemberStatus currentStatus,
                                   @Param("newStatus") GroupMember.MemberStatus newStatus,
                                   @Param("leftAt") Instant leftAt,
                                   @Param("updateTime") Instant updateTime);

    /**
     * 查找群组成员（分页）
     */
    @Query("SELECT gm FROM GroupMember gm WHERE gm.groupId = :groupId AND gm.status = :status")
    Page<GroupMember> findByGroupIdAndStatus(@Param("groupId") Long groupId,
                                           @Param("status") GroupMember.MemberStatus status,
                                           Pageable pageable);

    /**
     * 根据群组ID和用户ID查找成员（不限状态）
     */
    Optional<GroupMember> findByGroupIdAndUserId(Long groupId, Long userId);

    /**
     * 根据用户ID和状态查找群组（分页）
     */
    Page<GroupMember> findByUserIdAndStatus(Long userId, GroupMember.MemberStatus status, Pageable pageable);

    /**
     * 删除旧的已退出成员记录
     */
    @Modifying
    @Query("DELETE FROM GroupMember gm WHERE gm.status IN ('LEFT', 'KICKED') " +
           "AND gm.leftAt IS NOT NULL AND gm.leftAt < :cutoffTime")
    int deleteOldLeftMembers(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 根据状态统计成员数量
     */
    long countByStatus(GroupMember.MemberStatus status);



}
