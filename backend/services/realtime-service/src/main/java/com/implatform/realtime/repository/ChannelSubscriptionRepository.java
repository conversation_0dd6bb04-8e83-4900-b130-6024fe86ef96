package com.implatform.realtime.repository;

import com.implatform.realtime.entity.ChannelSubscription;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 频道订阅数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ChannelSubscriptionRepository extends R2dbcRepository<ChannelSubscription, Long> {

    /**
     * 根据频道ID查找所有活跃订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true")
    Flux<ChannelSubscription> findByChannelIdAndIsActiveTrue(@Param("channelId") Long channelId);

    /**
     * 根据频道ID和状态查找订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscription_status = :status")
    Flux<ChannelSubscription> findByChannelIdAndIsActiveTrueAndSubscriptionStatusEquals(@Param("channelId") Long channelId, @Param("status") String status);

    /**
     * 根据频道ID和创建时间查找订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscribed_at > :subscribedAt")
    Flux<ChannelSubscription> findByChannelIdAndIsActiveTrueAndSubscribedAtAfter(@Param("channelId") Long channelId, @Param("subscribedAt") Instant subscribedAt);

    /**
     * 根据频道ID和角色查找订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscription_role = ANY(:roles)")
    Flux<ChannelSubscription> findByChannelIdAndIsActiveTrueAndSubscriptionRoleIn(@Param("channelId") Long channelId, @Param("roles") String[] roles);

    /**
     * 根据频道ID和用户ID查找订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND user_id = :userId AND is_active = true")
    Mono<ChannelSubscription> findByChannelIdAndUserIdAndIsActiveTrue(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 根据用户ID查找所有活跃订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE user_id = :userId AND is_active = true")
    Flux<ChannelSubscription> findByUserIdAndIsActiveTrue(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查找订阅
     */
    @Query("SELECT * FROM channel_subscriptions WHERE user_id = :userId AND is_active = true AND subscription_status = :status")
    Flux<ChannelSubscription> findByUserIdAndIsActiveTrueAndSubscriptionStatusEquals(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 统计频道的活跃订阅数量
     */
    @Query("SELECT COUNT(*) FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true")
    Mono<Long> countByChannelIdAndIsActiveTrue(@Param("channelId") Long channelId);

    /**
     * 统计频道的指定状态订阅数量
     */
    @Query("SELECT COUNT(*) FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscription_status = :status")
    Mono<Long> countByChannelIdAndIsActiveTrueAndSubscriptionStatusEquals(@Param("channelId") Long channelId, @Param("status") String status);

    /**
     * 统计频道的指定时间后订阅数量
     */
    @Query("SELECT COUNT(*) FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscribed_at > :subscribedAt")
    Mono<Long> countByChannelIdAndIsActiveTrueAndSubscribedAtAfter(@Param("channelId") Long channelId, @Param("subscribedAt") Instant subscribedAt);

    /**
     * 统计用户的活跃订阅数量
     */
    @Query("SELECT COUNT(*) FROM channel_subscriptions WHERE user_id = :userId AND is_active = true")
    Mono<Long> countByUserIdAndIsActiveTrue(@Param("userId") Long userId);

    /**
     * 检查用户是否订阅了频道
     */
    @Query("SELECT COUNT(*) > 0 FROM channel_subscriptions WHERE channel_id = :channelId AND user_id = :userId AND is_active = true")
    Mono<Boolean> existsByChannelIdAndUserIdAndIsActiveTrue(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 检查用户是否为频道管理员
     */
    @Query("SELECT COUNT(*) > 0 FROM channel_subscriptions WHERE channel_id = :channelId AND user_id = :userId AND is_active = true AND subscription_role IN ('OWNER', 'ADMIN')")
    Mono<Boolean> isChannelAdmin(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 获取频道的管理员列表
     */
    @Query("SELECT * FROM channel_subscriptions WHERE channel_id = :channelId AND is_active = true AND subscription_role IN ('OWNER', 'ADMIN') ORDER BY subscription_role ASC, subscribed_at ASC")
    Flux<ChannelSubscription> findChannelAdmins(@Param("channelId") Long channelId);

    /**
     * 获取频道的版主及以上角色列表
     */
    @Query("SELECT cs FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true AND cs.subscriptionRole IN ('OWNER', 'ADMIN', 'MODERATOR') ORDER BY cs.subscriptionRole ASC, cs.subscribedAt ASC")
    List<ChannelSubscription> findChannelModerators(@Param("channelId") Long channelId);

    /**
     * 获取用户在频道中的角色
     */
    @Query("SELECT cs.subscriptionRole FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.userId = :userId AND cs.isActive = true")
    Optional<ChannelSubscription.SubscriptionRole> findUserRoleInChannel(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 更新订阅状态
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.subscriptionStatus = :status WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void updateSubscriptionStatus(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("status") ChannelSubscription.SubscriptionStatus status);

    /**
     * 更新用户角色
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.subscriptionRole = :role WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void updateUserRole(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("role") ChannelSubscription.SubscriptionRole role);

    /**
     * 更新最后活跃时间
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.lastActiveAt = :lastActiveAt WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void updateLastActiveTime(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("lastActiveAt") Instant lastActiveAt);

    /**
     * 批量更新最后活跃时间
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.lastActiveAt = :lastActiveAt WHERE cs.channelId = :channelId AND cs.userId IN :userIds")
    void batchUpdateLastActiveTime(@Param("channelId") Long channelId, @Param("userIds") List<Long> userIds, @Param("lastActiveAt") Instant lastActiveAt);

    /**
     * 设置静音
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isMuted = :muted, cs.mutedUntil = :muteUntil WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void setMute(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("muted") Boolean muted, @Param("muteUntil") Instant muteUntil);

    /**
     * 设置置顶
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isPinned = :pinned WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void setPinned(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("pinned") Boolean pinned);

    /**
     * 设置通知设置
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.notificationSetting = :setting WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void setNotificationSetting(@Param("channelId") Long channelId, @Param("userId") Long userId, @Param("setting") ChannelSubscription.NotificationSetting setting);

    /**
     * 软删除订阅
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isActive = false WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void softDeleteSubscription(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 批量软删除订阅
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isActive = false WHERE cs.channelId = :channelId AND cs.userId IN :userIds")
    void batchSoftDeleteSubscriptions(@Param("channelId") Long channelId, @Param("userIds") List<Long> userIds);

    /**
     * 恢复订阅
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isActive = true, cs.subscriptionStatus = 'ACTIVE' WHERE cs.channelId = :channelId AND cs.userId = :userId")
    void restoreSubscription(@Param("channelId") Long channelId, @Param("userId") Long userId);

    /**
     * 获取频道的活跃成员统计
     */
    @Query("SELECT cs.subscriptionStatus, COUNT(cs) FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true GROUP BY cs.subscriptionStatus")
    List<Object[]> getChannelMemberStatistics(@Param("channelId") Long channelId);

    /**
     * 获取频道的角色分布统计
     */
    @Query("SELECT cs.subscriptionRole, COUNT(cs) FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true GROUP BY cs.subscriptionRole")
    List<Object[]> getChannelRoleStatistics(@Param("channelId") Long channelId);

    /**
     * 获取用户的订阅统计
     */
    @Query("SELECT cs.subscriptionStatus, COUNT(cs) FROM ChannelSubscription cs WHERE cs.userId = :userId AND cs.isActive = true GROUP BY cs.subscriptionStatus")
    List<Object[]> getUserSubscriptionStatistics(@Param("userId") Long userId);

    /**
     * 查找最近活跃的订阅
     */
    @Query("SELECT cs FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true AND cs.lastActiveAt > :since ORDER BY cs.lastActiveAt DESC")
    List<ChannelSubscription> findRecentActiveSubscriptions(@Param("channelId") Long channelId, @Param("since") Instant since);

    /**
     * 查找长时间未活跃的订阅
     */
    @Query("SELECT cs FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true AND (cs.lastActiveAt IS NULL OR cs.lastActiveAt < :before)")
    List<ChannelSubscription> findInactiveSubscriptions(@Param("channelId") Long channelId, @Param("before") Instant before);

    /**
     * 查找被静音的订阅
     */
    @Query("SELECT cs FROM ChannelSubscription cs WHERE cs.channelId = :channelId AND cs.isActive = true AND cs.isMuted = true AND (cs.mutedUntil IS NULL OR cs.mutedUntil > :now)")
    List<ChannelSubscription> findMutedSubscriptions(@Param("channelId") Long channelId, @Param("now") Instant now);

    /**
     * 清理过期的静音状态
     */
    @Modifying
    @Query("UPDATE ChannelSubscription cs SET cs.isMuted = false, cs.mutedUntil = null WHERE cs.isMuted = true AND cs.mutedUntil IS NOT NULL AND cs.mutedUntil <= :now")
    void cleanupExpiredMutes(@Param("now") Instant now);
}
