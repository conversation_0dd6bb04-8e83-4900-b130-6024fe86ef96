package com.implatform.realtime.service.impl;

import com.implatform.realtime.dto.CloudNoteDTO;
import com.implatform.realtime.entity.CloudNote;
import com.implatform.realtime.entity.CloudNoteFolder;
import com.implatform.realtime.repository.CloudNoteRepository;
import com.implatform.realtime.repository.CloudNoteFolderRepository;
import com.implatform.realtime.service.CloudNoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 云笔记服务实现
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CloudNoteServiceImpl implements CloudNoteService {

    private final CloudNoteRepository noteRepository;
    private final CloudNoteFolderRepository folderRepository;

    // ==================== 笔记管理 ====================

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo createNote(Long userId, CloudNoteDTO.CreateCloudNoteRequest request) {
        log.info("创建笔记: userId={}, title={}", userId, request.getTitle());

        // 验证文件夹权限
        if (request.getFolderId() != null) {
            validateFolderAccess(userId, request.getFolderId());
        }

        // 创建笔记
        CloudNote note = new CloudNote(userId, request.getTitle(), request.getContent(), request.getNoteType());
        note.setFolderId(request.getFolderId());
        note.setPriority(request.getPriority());
        note.setIsPinned(request.getIsPinned());
        note.setIsFavorite(request.getIsFavorite());
        note.setIsPublic(request.getIsPublic());
        note.setIsEncrypted(request.getIsEncrypted());
        if (request.getReminderAt() != null) {
            note.setReminderAt(request.getReminderAt().atZone(java.time.ZoneId.systemDefault()).toInstant());
        }
        if (request.getExpiresAt() != null) {
            note.setExpiresAt(request.getExpiresAt().atZone(java.time.ZoneId.systemDefault()).toInstant());
        }
        note.setSourceType(request.getSourceType());
        note.setSourceId(request.getSourceId());
        note.setSourceUrl(request.getSourceUrl());

        // 设置标签
        if (request.getTags() != null && request.getTags().length > 0) {
            note.setTagList(request.getTags());
        }

        // 设置排序顺序
        Integer sortOrder = request.getFolderId() != null 
            ? noteRepository.getNextSortOrder(userId, request.getFolderId())
            : noteRepository.getNextSortOrderForRoot(userId);
        note.setSortOrder(sortOrder);

        CloudNote savedNote = noteRepository.save(note);

        // 更新文件夹笔记数量
        if (request.getFolderId() != null) {
            folderRepository.updateNoteCount(userId, request.getFolderId(), 1, Instant.now());
        }

        log.info("笔记创建成功: noteId={}", savedNote.getId());
        return convertNoteToDTO(savedNote);
    }

    @Override
    public CloudNoteDTO.CloudNoteInfo getNoteDetail(Long noteId, Long userId) {
        log.debug("获取笔记详情: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限访问此笔记");
        }

        // 记录阅读
        note.recordRead();
        noteRepository.save(note);

        return convertNoteToDTO(note);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo updateNote(Long noteId, Long userId, CloudNoteDTO.UpdateCloudNoteRequest request) {
        log.info("更新笔记: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        // 检查文件夹变更
        Long oldFolderId = note.getFolderId();
        Long newFolderId = request.getFolderId();

        // 更新笔记信息
        if (request.getTitle() != null) {
            note.setTitle(request.getTitle());
        }
        if (request.getContent() != null) {
            note.setContent(request.getContent());
        }
        if (request.getNoteType() != null) {
            note.setNoteType(request.getNoteType());
        }
        if (request.getPriority() != null) {
            note.setPriority(request.getPriority());
        }
        if (request.getIsPinned() != null) {
            note.setIsPinned(request.getIsPinned());
        }
        if (request.getIsFavorite() != null) {
            note.setIsFavorite(request.getIsFavorite());
        }
        if (request.getIsPublic() != null) {
            note.setIsPublic(request.getIsPublic());
        }
        if (request.getIsEncrypted() != null) {
            note.setIsEncrypted(request.getIsEncrypted());
        }
        if (request.getReminderAt() != null) {
            note.setReminderAt(request.getReminderAt().atZone(java.time.ZoneId.systemDefault()).toInstant());
        }
        if (request.getExpiresAt() != null) {
            note.setExpiresAt(request.getExpiresAt().atZone(java.time.ZoneId.systemDefault()).toInstant());
        }
        if (request.getSourceType() != null) {
            note.setSourceType(request.getSourceType());
        }
        if (request.getSourceId() != null) {
            note.setSourceId(request.getSourceId());
        }
        if (request.getSourceUrl() != null) {
            note.setSourceUrl(request.getSourceUrl());
        }

        // 更新标签
        if (request.getTags() != null) {
            note.setTagList(request.getTags());
        }

        // 更新文件夹
        if (newFolderId != null && !Objects.equals(oldFolderId, newFolderId)) {
            validateFolderAccess(userId, newFolderId);
            note.setFolderId(newFolderId);
        }

        note.recordEdit();
        CloudNote savedNote = noteRepository.save(note);

        // 更新文件夹笔记数量
        if (!Objects.equals(oldFolderId, newFolderId)) {
            if (oldFolderId != null) {
                folderRepository.updateNoteCount(userId, oldFolderId, -1, Instant.now());
            }
            if (newFolderId != null) {
                folderRepository.updateNoteCount(userId, newFolderId, 1, Instant.now());
            }
        }

        log.info("笔记更新成功: noteId={}", savedNote.getId());
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public void deleteNote(Long noteId, Long userId) {
        log.info("删除笔记: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        Long folderId = note.getFolderId();
        note.delete();
        noteRepository.save(note);

        // 更新文件夹笔记数量
        if (folderId != null) {
            folderRepository.updateNoteCount(userId, folderId, -1, Instant.now());
        }

        log.info("笔记删除成功: noteId={}", noteId);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo restoreNote(Long noteId, Long userId) {
        log.info("恢复笔记: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        Long folderId = note.getFolderId();
        note.restore();
        CloudNote savedNote = noteRepository.save(note);

        // 更新文件夹笔记数量
        if (folderId != null) {
            folderRepository.updateNoteCount(userId, folderId, 1, Instant.now());
        }

        log.info("笔记恢复成功: noteId={}", noteId);
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public void archiveNote(Long noteId, Long userId) {
        log.info("归档笔记: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        Long folderId = note.getFolderId();
        note.archive();
        noteRepository.save(note);

        // 更新文件夹笔记数量
        if (folderId != null) {
            folderRepository.updateNoteCount(userId, folderId, -1, Instant.now());
        }

        log.info("笔记归档成功: noteId={}", noteId);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo unarchiveNote(Long noteId, Long userId) {
        log.info("取消归档笔记: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        Long folderId = note.getFolderId();
        note.unarchive();
        CloudNote savedNote = noteRepository.save(note);

        // 更新文件夹笔记数量
        if (folderId != null) {
            folderRepository.updateNoteCount(userId, folderId, 1, Instant.now());
        }

        log.info("笔记取消归档成功: noteId={}", noteId);
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo togglePinNote(Long noteId, Long userId) {
        log.info("切换笔记置顶状态: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        note.togglePin();
        CloudNote savedNote = noteRepository.save(note);

        log.info("笔记置顶状态切换成功: noteId={}, pinned={}", noteId, savedNote.getIsPinned());
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo toggleFavoriteNote(Long noteId, Long userId) {
        log.info("切换笔记收藏状态: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        note.toggleFavorite();
        CloudNote savedNote = noteRepository.save(note);

        log.info("笔记收藏状态切换成功: noteId={}, favorite={}", noteId, savedNote.getIsFavorite());
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo moveNoteToFolder(Long noteId, Long userId, Long targetFolderId) {
        log.info("移动笔记到文件夹: noteId={}, userId={}, targetFolderId={}", noteId, userId, targetFolderId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        // 验证目标文件夹
        if (targetFolderId != null) {
            validateFolderAccess(userId, targetFolderId);
        }

        Long oldFolderId = note.getFolderId();
        note.setFolderId(targetFolderId);
        note.recordEdit();
        CloudNote savedNote = noteRepository.save(note);

        // 更新文件夹笔记数量
        if (!Objects.equals(oldFolderId, targetFolderId)) {
            if (oldFolderId != null) {
                folderRepository.updateNoteCount(userId, oldFolderId, -1, Instant.now());
            }
            if (targetFolderId != null) {
                folderRepository.updateNoteCount(userId, targetFolderId, 1, Instant.now());
            }
        }

        log.info("笔记移动成功: noteId={}", noteId);
        return convertNoteToDTO(savedNote);
    }

    // ==================== 辅助方法 ====================

    private void validateFolderAccess(Long userId, Long folderId) {
        if (!folderRepository.existsByUserIdAndId(userId, folderId)) {
            throw new RuntimeException("文件夹不存在或无权限访问");
        }
    }

    private CloudNoteDTO.CloudNoteInfo convertNoteToDTO(CloudNote note) {
        CloudNoteDTO.CloudNoteInfo dto = CloudNoteDTO.CloudNoteInfo.fromEntity(note);
        
        // 设置文件夹名称
        if (note.getFolderId() != null) {
            folderRepository.findById(note.getFolderId())
                    .ifPresent(folder -> dto.setFolderName(folder.getFolderName()));
        }
        
        return dto;
    }

    private List<CloudNoteDTO.CloudNoteInfo> convertNotesToDTO(List<CloudNote> notes) {
        return notes.stream()
                .map(this::convertNoteToDTO)
                .collect(Collectors.toList());
    }

    private Page<CloudNoteDTO.CloudNoteInfo> convertNotesPageToDTO(Page<CloudNote> notes) {
        List<CloudNoteDTO.CloudNoteInfo> dtoList = convertNotesToDTO(notes.getContent());
        return new PageImpl<>(dtoList, notes.getPageable(), notes.getTotalElements());
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo copyNote(Long noteId, Long userId, Long targetFolderId) {
        log.info("复制笔记: noteId={}, userId={}, targetFolderId={}", noteId, userId, targetFolderId);

        CloudNote originalNote = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!originalNote.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        // 验证目标文件夹
        if (targetFolderId != null) {
            validateFolderAccess(userId, targetFolderId);
        }

        // 创建副本
        CloudNote copyNote = new CloudNote(userId, originalNote.getTitle() + " - 副本",
                originalNote.getContent(), originalNote.getNoteType());
        copyNote.setFolderId(targetFolderId);
        copyNote.setPriority(originalNote.getPriority());
        copyNote.setTags(originalNote.getTags());
        copyNote.setIsPublic(false); // 副本默认为私有
        copyNote.setIsEncrypted(originalNote.getIsEncrypted());

        // 设置排序顺序
        Integer sortOrder = targetFolderId != null
            ? noteRepository.getNextSortOrder(userId, targetFolderId)
            : noteRepository.getNextSortOrderForRoot(userId);
        copyNote.setSortOrder(sortOrder);

        CloudNote savedNote = noteRepository.save(copyNote);

        // 更新文件夹笔记数量
        if (targetFolderId != null) {
            folderRepository.updateNoteCount(userId, targetFolderId, 1, Instant.now());
        }

        log.info("笔记复制成功: originalId={}, copyId={}", noteId, savedNote.getId());
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public void recordNoteRead(Long noteId, Long userId) {
        log.debug("记录笔记阅读: noteId={}, userId={}", noteId, userId);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限访问此笔记");
        }

        note.recordRead();
        noteRepository.save(note);
    }

    // ==================== 笔记查询 ====================

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getUserNotes(Long userId, CloudNote.NoteStatus noteStatus) {
        log.debug("获取用户笔记: userId={}, status={}", userId, noteStatus);

        List<CloudNote> notes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, noteStatus);
        return convertNotesToDTO(notes);
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> getUserNotesPage(Long userId, CloudNote.NoteStatus noteStatus, Pageable pageable) {
        log.debug("分页获取用户笔记: userId={}, status={}", userId, noteStatus);

        Page<CloudNote> notes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, noteStatus, pageable);
        return convertNotesPageToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getFolderNotes(Long userId, Long folderId, CloudNote.NoteStatus noteStatus) {
        log.debug("获取文件夹笔记: userId={}, folderId={}, status={}", userId, folderId, noteStatus);

        List<CloudNote> notes = noteRepository.findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, folderId, noteStatus);
        return convertNotesToDTO(notes);
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> getFolderNotesPage(Long userId, Long folderId, CloudNote.NoteStatus noteStatus, Pageable pageable) {
        log.debug("分页获取文件夹笔记: userId={}, folderId={}, status={}", userId, folderId, noteStatus);

        Page<CloudNote> notes = noteRepository.findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, folderId, noteStatus, pageable);
        return convertNotesPageToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getRootNotes(Long userId, CloudNote.NoteStatus noteStatus) {
        log.debug("获取根目录笔记: userId={}, status={}", userId, noteStatus);

        List<CloudNote> notes = noteRepository.findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, noteStatus);
        return convertNotesToDTO(notes);
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> getRootNotesPage(Long userId, CloudNote.NoteStatus noteStatus, Pageable pageable) {
        log.debug("分页获取根目录笔记: userId={}, status={}", userId, noteStatus);

        Page<CloudNote> notes = noteRepository.findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, noteStatus, pageable);
        return convertNotesPageToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getPinnedNotes(Long userId) {
        log.debug("获取置顶笔记: userId={}", userId);

        List<CloudNote> notes = noteRepository.findByUserIdAndIsPinnedTrueAndNoteStatusOrderByUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getFavoriteNotes(Long userId) {
        log.debug("获取收藏笔记: userId={}", userId);

        List<CloudNote> notes = noteRepository.findByUserIdAndIsFavoriteTrueAndNoteStatusOrderByUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getPublicNotes(Long userId) {
        log.debug("获取公开笔记: userId={}", userId);

        List<CloudNote> notes = noteRepository.findByUserIdAndIsPublicTrueAndNoteStatusOrderByUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getNotesByType(Long userId, CloudNote.NoteType noteType) {
        log.debug("根据类型获取笔记: userId={}, noteType={}", userId, noteType);

        List<CloudNote> notes = noteRepository.findByUserIdAndNoteTypeAndNoteStatusOrderByUpdatedAtDesc(userId, noteType, CloudNote.NoteStatus.ACTIVE);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getNotesByPriority(Long userId, CloudNote.Priority priority) {
        log.debug("根据优先级获取笔记: userId={}, priority={}", userId, priority);

        List<CloudNote> notes = noteRepository.findByUserIdAndPriorityAndNoteStatusOrderByUpdatedAtDesc(userId, priority, CloudNote.NoteStatus.ACTIVE);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getRecentlyEditedNotes(Long userId, int days) {
        log.debug("获取最近编辑的笔记: userId={}, days={}", userId, days);

        Instant since = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);
        List<CloudNote> notes = noteRepository.findRecentlyEditedNotes(userId, CloudNote.NoteStatus.ACTIVE, since);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getRecentlyReadNotes(Long userId, int days) {
        log.debug("获取最近阅读的笔记: userId={}, days={}", userId, days);

        Instant since = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);
        List<CloudNote> notes = noteRepository.findRecentlyReadNotes(userId, CloudNote.NoteStatus.ACTIVE, since);
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getNotesNeedingReminder(Long userId) {
        log.debug("获取需要提醒的笔记: userId={}", userId);

        List<CloudNote> notes = noteRepository.findNotesNeedingReminder(userId, Instant.now());
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getExpiredNotes(Long userId) {
        log.debug("获取过期笔记: userId={}", userId);

        List<CloudNote> notes = noteRepository.findExpiredNotes(userId, Instant.now());
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getSimilarNotes(Long noteId, Long userId, int limit) {
        log.debug("获取相似笔记: noteId={}, userId={}, limit={}", noteId, userId, limit);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限访问此笔记");
        }

        // 使用标题和内容的关键词搜索相似笔记
        String keyword = extractKeywords(note.getTitle() + " " + note.getContent());
        List<CloudNote> similarNotes = noteRepository.findSimilarNotes(userId, CloudNote.NoteStatus.ACTIVE,
                noteId, keyword, org.springframework.data.domain.PageRequest.of(0, limit));

        return convertNotesToDTO(similarNotes);
    }

    // ==================== 搜索功能 ====================

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> searchNotes(Long userId, String keyword, CloudNote.NoteStatus noteStatus) {
        log.debug("搜索笔记: userId={}, keyword={}, status={}", userId, keyword, noteStatus);

        List<CloudNote> notes = noteRepository.searchNotes(userId, noteStatus, keyword);
        return convertNotesToDTO(notes);
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> searchNotesPage(Long userId, String keyword, CloudNote.NoteStatus noteStatus, Pageable pageable) {
        log.debug("分页搜索笔记: userId={}, keyword={}, status={}", userId, keyword, noteStatus);

        Page<CloudNote> notes = noteRepository.searchNotes(userId, noteStatus, keyword, pageable);
        return convertNotesPageToDTO(notes);
    }

    private String extractKeywords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }

        // 简单的关键词提取：取前几个词
        String[] words = text.trim().split("\\s+");
        return Arrays.stream(words)
                .limit(3)
                .collect(Collectors.joining(" "));
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> advancedSearchNotes(Long userId, CloudNoteDTO.SearchRequest request, Pageable pageable) {
        log.debug("高级搜索笔记: userId={}, request={}", userId, request);

        // 基础搜索
        Page<CloudNote> notes;
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            notes = noteRepository.searchNotes(userId, request.getNoteStatus(), request.getKeyword(), pageable);
        } else {
            notes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, request.getNoteStatus(), pageable);
        }

        // 应用过滤条件
        List<CloudNote> filteredNotes = notes.getContent().stream()
                .filter(note -> applySearchFilters(note, request))
                .collect(Collectors.toList());

        return new PageImpl<>(convertNotesToDTO(filteredNotes), pageable, notes.getTotalElements());
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> searchNotesByTag(Long userId, String tag) {
        log.debug("根据标签搜索笔记: userId={}, tag={}", userId, tag);

        List<CloudNote> notes = noteRepository.findByTag(userId, CloudNote.NoteStatus.ACTIVE, tag);
        return convertNotesToDTO(notes);
    }

    @Override
    public Page<CloudNoteDTO.CloudNoteInfo> fullTextSearchNotes(Long userId, String keyword, Pageable pageable) {
        log.debug("全文搜索笔记: userId={}, keyword={}", userId, keyword);

        Page<CloudNote> notes = noteRepository.searchNotes(userId, CloudNote.NoteStatus.ACTIVE, keyword, pageable);
        return convertNotesPageToDTO(notes);
    }

    // ==================== 批量操作 ====================

    @Override
    @Transactional
    public void batchOperateNotes(Long userId, CloudNoteDTO.BatchOperationRequest request) {
        log.info("批量操作笔记: userId={}, operation={}, count={}", userId, request.getOperation(), request.getIds().size());

        switch (request.getOperation().toUpperCase()) {
            case "PIN":
                batchPinNotes(userId, request.getIds(), true);
                break;
            case "UNPIN":
                batchPinNotes(userId, request.getIds(), false);
                break;
            case "FAVORITE":
                batchFavoriteNotes(userId, request.getIds(), true);
                break;
            case "UNFAVORITE":
                batchFavoriteNotes(userId, request.getIds(), false);
                break;
            case "ARCHIVE":
                batchArchiveNotes(userId, request.getIds());
                break;
            case "DELETE":
                batchDeleteNotes(userId, request.getIds());
                break;
            case "RESTORE":
                batchRestoreNotes(userId, request.getIds());
                break;
            case "MOVE":
                if (request.getTargetFolderId() != null) {
                    batchMoveNotes(userId, request.getIds(), request.getTargetFolderId());
                }
                break;
            default:
                throw new RuntimeException("不支持的操作类型: " + request.getOperation());
        }

        log.info("批量操作完成: userId={}, operation={}", userId, request.getOperation());
    }

    @Override
    @Transactional
    public void batchDeleteNotes(Long userId, List<Long> noteIds) {
        log.info("批量删除笔记: userId={}, count={}", userId, noteIds.size());

        // 验证权限并获取文件夹信息
        Map<Long, Integer> folderCountMap = new HashMap<>();
        for (Long noteId : noteIds) {
            CloudNote note = noteRepository.findById(noteId)
                    .orElseThrow(() -> new RuntimeException("笔记不存在: " + noteId));

            if (!note.getUserId().equals(userId)) {
                throw new RuntimeException("无权限操作笔记: " + noteId);
            }

            if (note.getFolderId() != null) {
                folderCountMap.merge(note.getFolderId(), 1, Integer::sum);
            }
        }

        // 批量更新状态
        noteRepository.batchUpdateNoteStatus(userId, noteIds, CloudNote.NoteStatus.DELETED, Instant.now());

        // 更新文件夹笔记数量
        for (Map.Entry<Long, Integer> entry : folderCountMap.entrySet()) {
            folderRepository.updateNoteCount(userId, entry.getKey(), -entry.getValue(), Instant.now());
        }

        log.info("批量删除笔记完成: userId={}, count={}", userId, noteIds.size());
    }

    @Override
    @Transactional
    public void batchArchiveNotes(Long userId, List<Long> noteIds) {
        log.info("批量归档笔记: userId={}, count={}", userId, noteIds.size());

        // 验证权限并获取文件夹信息
        Map<Long, Integer> folderCountMap = new HashMap<>();
        for (Long noteId : noteIds) {
            CloudNote note = noteRepository.findById(noteId)
                    .orElseThrow(() -> new RuntimeException("笔记不存在: " + noteId));

            if (!note.getUserId().equals(userId)) {
                throw new RuntimeException("无权限操作笔记: " + noteId);
            }

            if (note.getFolderId() != null && note.getNoteStatus() == CloudNote.NoteStatus.ACTIVE) {
                folderCountMap.merge(note.getFolderId(), 1, Integer::sum);
            }
        }

        // 批量更新状态
        noteRepository.batchUpdateNoteStatus(userId, noteIds, CloudNote.NoteStatus.ARCHIVED, Instant.now());

        // 更新文件夹笔记数量
        for (Map.Entry<Long, Integer> entry : folderCountMap.entrySet()) {
            folderRepository.updateNoteCount(userId, entry.getKey(), -entry.getValue(), Instant.now());
        }

        log.info("批量归档笔记完成: userId={}, count={}", userId, noteIds.size());
    }

    @Override
    @Transactional
    public void batchRestoreNotes(Long userId, List<Long> noteIds) {
        log.info("批量恢复笔记: userId={}, count={}", userId, noteIds.size());

        // 验证权限并获取文件夹信息
        Map<Long, Integer> folderCountMap = new HashMap<>();
        for (Long noteId : noteIds) {
            CloudNote note = noteRepository.findById(noteId)
                    .orElseThrow(() -> new RuntimeException("笔记不存在: " + noteId));

            if (!note.getUserId().equals(userId)) {
                throw new RuntimeException("无权限操作笔记: " + noteId);
            }

            if (note.getFolderId() != null && note.getNoteStatus() != CloudNote.NoteStatus.ACTIVE) {
                folderCountMap.merge(note.getFolderId(), 1, Integer::sum);
            }
        }

        // 批量更新状态
        noteRepository.batchUpdateNoteStatus(userId, noteIds, CloudNote.NoteStatus.ACTIVE, Instant.now());

        // 更新文件夹笔记数量
        for (Map.Entry<Long, Integer> entry : folderCountMap.entrySet()) {
            folderRepository.updateNoteCount(userId, entry.getKey(), entry.getValue(), Instant.now());
        }

        log.info("批量恢复笔记完成: userId={}, count={}", userId, noteIds.size());
    }

    @Override
    @Transactional
    public void batchMoveNotes(Long userId, List<Long> noteIds, Long targetFolderId) {
        log.info("批量移动笔记: userId={}, count={}, targetFolderId={}", userId, noteIds.size(), targetFolderId);

        // 验证目标文件夹
        if (targetFolderId != null) {
            validateFolderAccess(userId, targetFolderId);
        }

        // 验证权限并统计文件夹变化
        Map<Long, Integer> folderDeltaMap = new HashMap<>();
        for (Long noteId : noteIds) {
            CloudNote note = noteRepository.findById(noteId)
                    .orElseThrow(() -> new RuntimeException("笔记不存在: " + noteId));

            if (!note.getUserId().equals(userId)) {
                throw new RuntimeException("无权限操作笔记: " + noteId);
            }

            Long oldFolderId = note.getFolderId();
            if (!Objects.equals(oldFolderId, targetFolderId)) {
                if (oldFolderId != null) {
                    folderDeltaMap.merge(oldFolderId, -1, Integer::sum);
                }
                if (targetFolderId != null) {
                    folderDeltaMap.merge(targetFolderId, 1, Integer::sum);
                }
            }
        }

        // 批量移动
        noteRepository.batchMoveNotesToFolder(userId, noteIds, targetFolderId, Instant.now());

        // 更新文件夹笔记数量
        for (Map.Entry<Long, Integer> entry : folderDeltaMap.entrySet()) {
            folderRepository.updateNoteCount(userId, entry.getKey(), entry.getValue(), Instant.now());
        }

        log.info("批量移动笔记完成: userId={}, count={}", userId, noteIds.size());
    }

    @Override
    @Transactional
    public void batchPinNotes(Long userId, List<Long> noteIds, boolean pinned) {
        log.info("批量{}笔记: userId={}, count={}", pinned ? "置顶" : "取消置顶", userId, noteIds.size());

        // 验证权限
        for (Long noteId : noteIds) {
            if (!noteRepository.existsByUserIdAndId(userId, noteId)) {
                throw new RuntimeException("笔记不存在或无权限: " + noteId);
            }
        }

        noteRepository.batchPinNotes(userId, noteIds, pinned, Instant.now());
        log.info("批量{}笔记完成: userId={}, count={}", pinned ? "置顶" : "取消置顶", userId, noteIds.size());
    }

    @Override
    @Transactional
    public void batchFavoriteNotes(Long userId, List<Long> noteIds, boolean favorite) {
        log.info("批量{}笔记: userId={}, count={}", favorite ? "收藏" : "取消收藏", userId, noteIds.size());

        // 验证权限
        for (Long noteId : noteIds) {
            if (!noteRepository.existsByUserIdAndId(userId, noteId)) {
                throw new RuntimeException("笔记不存在或无权限: " + noteId);
            }
        }

        noteRepository.batchFavoriteNotes(userId, noteIds, favorite, Instant.now());
        log.info("批量{}笔记完成: userId={}, count={}", favorite ? "收藏" : "取消收藏", userId, noteIds.size());
    }

    private boolean applySearchFilters(CloudNote note, CloudNoteDTO.SearchRequest request) {
        // 文件夹过滤
        if (request.getFolderId() != null && !Objects.equals(note.getFolderId(), request.getFolderId())) {
            return false;
        }

        // 笔记类型过滤
        if (request.getNoteType() != null && note.getNoteType() != request.getNoteType()) {
            return false;
        }

        // 优先级过滤
        if (request.getPriority() != null && note.getPriority() != request.getPriority()) {
            return false;
        }

        // 标签过滤
        if (request.getTag() != null && !note.hasTag(request.getTag())) {
            return false;
        }

        // 置顶过滤
        if (request.getPinnedOnly() && !note.getIsPinned()) {
            return false;
        }

        // 收藏过滤
        if (request.getFavoriteOnly() && !note.getIsFavorite()) {
            return false;
        }

        // 公开过滤
        if (request.getPublicOnly() && !note.getIsPublic()) {
            return false;
        }

        // 时间范围过滤
        if (request.getStartTime() != null && note.getCreatedAt().isBefore(request.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant())) {
            return false;
        }

        if (request.getEndTime() != null && note.getCreatedAt().isAfter(request.getEndTime().atZone(java.time.ZoneId.systemDefault()).toInstant())) {
            return false;
        }

        return true;
    }

    // ==================== 排序功能 ====================

    @Override
    @Transactional
    public void updateNoteSortOrder(Long userId, CloudNoteDTO.SortRequest request) {
        log.info("更新笔记排序: userId={}, count={}", userId, request.getItems().size());

        for (CloudNoteDTO.SortRequest.SortItem item : request.getItems()) {
            noteRepository.updateNoteSortOrder(userId, item.getId(), item.getSortOrder(), Instant.now());
        }

        log.info("笔记排序更新完成: userId={}", userId);
    }

    @Override
    @Transactional
    public void autoSortNotes(Long userId, Long folderId, String sortBy) {
        log.info("自动排序笔记: userId={}, folderId={}, sortBy={}", userId, folderId, sortBy);

        List<CloudNote> notes;
        if (folderId != null) {
            notes = noteRepository.findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, folderId, CloudNote.NoteStatus.ACTIVE);
        } else {
            notes = noteRepository.findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        }

        // 根据排序方式重新排序
        switch (sortBy.toLowerCase()) {
            case "title":
                notes.sort(Comparator.comparing(CloudNote::getTitle));
                break;
            case "created":
                notes.sort(Comparator.comparing(CloudNote::getCreatedAt));
                break;
            case "updated":
                notes.sort(Comparator.comparing(CloudNote::getUpdatedAt).reversed());
                break;
            case "priority":
                notes.sort(Comparator.comparing(CloudNote::getPriority));
                break;
            case "wordcount":
                notes.sort(Comparator.comparing(CloudNote::getWordCount).reversed());
                break;
            default:
                log.warn("不支持的排序方式: {}", sortBy);
                return;
        }

        // 更新排序顺序
        for (int i = 0; i < notes.size(); i++) {
            CloudNote note = notes.get(i);
            note.setSortOrder(i + 1);
            noteRepository.save(note);
        }

        log.info("自动排序完成: userId={}, folderId={}, sortBy={}", userId, folderId, sortBy);
    }

    // ==================== 标签管理 ====================

    @Override
    public List<String> getUserTags(Long userId) {
        log.debug("获取用户标签: userId={}", userId);

        List<String> allTagStrings = noteRepository.findAllTags(userId, CloudNote.NoteStatus.ACTIVE);
        Set<String> uniqueTags = new HashSet<>();

        for (String tagString : allTagStrings) {
            if (tagString != null && !tagString.trim().isEmpty()) {
                String[] tags = tagString.split(",");
                for (String tag : tags) {
                    uniqueTags.add(tag.trim());
                }
            }
        }

        return new ArrayList<>(uniqueTags);
    }

    @Override
    public List<CloudNoteDTO.StatisticsInfo.TagStatistics> getPopularTags(Long userId, int limit) {
        log.debug("获取热门标签: userId={}, limit={}", userId, limit);

        List<String> allTagStrings = noteRepository.findAllTags(userId, CloudNote.NoteStatus.ACTIVE);
        Map<String, Long> tagCountMap = new HashMap<>();

        for (String tagString : allTagStrings) {
            if (tagString != null && !tagString.trim().isEmpty()) {
                String[] tags = tagString.split(",");
                for (String tag : tags) {
                    String trimmedTag = tag.trim();
                    tagCountMap.merge(trimmedTag, 1L, Long::sum);
                }
            }
        }

        long totalTags = tagCountMap.values().stream().mapToLong(Long::longValue).sum();

        return tagCountMap.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(limit)
                .map(entry -> {
                    CloudNoteDTO.StatisticsInfo.TagStatistics stat = new CloudNoteDTO.StatisticsInfo.TagStatistics();
                    stat.setTagName(entry.getKey());
                    stat.setCount(entry.getValue());
                    stat.setPercentage(totalTags > 0 ? (double) entry.getValue() / totalTags * 100 : 0.0);
                    return stat;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo addNoteTag(Long noteId, Long userId, String tag) {
        log.info("添加笔记标签: noteId={}, userId={}, tag={}", noteId, userId, tag);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        note.addTag(tag);
        CloudNote savedNote = noteRepository.save(note);

        log.info("笔记标签添加成功: noteId={}, tag={}", noteId, tag);
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public CloudNoteDTO.CloudNoteInfo removeNoteTag(Long noteId, Long userId, String tag) {
        log.info("移除笔记标签: noteId={}, userId={}, tag={}", noteId, userId, tag);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        note.removeTag(tag);
        CloudNote savedNote = noteRepository.save(note);

        log.info("笔记标签移除成功: noteId={}, tag={}", noteId, tag);
        return convertNoteToDTO(savedNote);
    }

    @Override
    @Transactional
    public void renameTag(Long userId, String oldTag, String newTag) {
        log.info("重命名标签: userId={}, oldTag={}, newTag={}", userId, oldTag, newTag);

        List<CloudNote> notes = noteRepository.findByTag(userId, CloudNote.NoteStatus.ACTIVE, oldTag);

        for (CloudNote note : notes) {
            note.removeTag(oldTag);
            note.addTag(newTag);
            noteRepository.save(note);
        }

        log.info("标签重命名完成: userId={}, oldTag={}, newTag={}, count={}", userId, oldTag, newTag, notes.size());
    }

    @Override
    @Transactional
    public void deleteTag(Long userId, String tag) {
        log.info("删除标签: userId={}, tag={}", userId, tag);

        List<CloudNote> notes = noteRepository.findByTag(userId, CloudNote.NoteStatus.ACTIVE, tag);

        for (CloudNote note : notes) {
            note.removeTag(tag);
            noteRepository.save(note);
        }

        log.info("标签删除完成: userId={}, tag={}, count={}", userId, tag, notes.size());
    }

    // ==================== 统计分析 ====================

    @Override
    public CloudNoteDTO.StatisticsInfo getUserNoteStatistics(Long userId) {
        log.debug("获取用户笔记统计: userId={}", userId);

        CloudNoteDTO.StatisticsInfo statistics = new CloudNoteDTO.StatisticsInfo();

        // 基础统计
        statistics.setTotalNotes(noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.ACTIVE));
        statistics.setActiveNotes(noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.ACTIVE));
        statistics.setArchivedNotes(noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.ARCHIVED));
        statistics.setDeletedNotes(noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.DELETED));
        statistics.setDraftNotes(noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.DRAFT));

        // 特殊统计
        List<CloudNote> activeNotes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        statistics.setPinnedNotes(activeNotes.stream().mapToLong(note -> note.getIsPinned() ? 1 : 0).sum());
        statistics.setFavoriteNotes(activeNotes.stream().mapToLong(note -> note.getIsFavorite() ? 1 : 0).sum());
        statistics.setPublicNotes(activeNotes.stream().mapToLong(note -> note.getIsPublic() ? 1 : 0).sum());
        statistics.setEncryptedNotes(activeNotes.stream().mapToLong(note -> note.getIsEncrypted() ? 1 : 0).sum());

        // 文件夹统计
        statistics.setTotalFolders(folderRepository.countByUserIdAndFolderStatus(userId, CloudNoteFolder.FolderStatus.ACTIVE));
        statistics.setActiveFolders(folderRepository.countByUserIdAndFolderStatus(userId, CloudNoteFolder.FolderStatus.ACTIVE));

        // 内容统计
        List<Object[]> contentStats = noteRepository.getUserNoteStatistics(userId, CloudNote.NoteStatus.ACTIVE);
        if (!contentStats.isEmpty()) {
            Object[] stats = contentStats.get(0);
            statistics.setTotalWords((Long) stats[1]);
            statistics.setTotalChars((Long) stats[2]);
            statistics.setAvgReadCount((Double) stats[3]);
            statistics.setAvgEditCount((Double) stats[4]);
        }

        // 类型统计
        List<Object[]> typeStats = noteRepository.countByNoteType(userId, CloudNote.NoteStatus.ACTIVE);
        List<CloudNoteDTO.StatisticsInfo.TypeStatistics> typeStatistics = typeStats.stream()
                .map(stat -> {
                    CloudNoteDTO.StatisticsInfo.TypeStatistics typeStat = new CloudNoteDTO.StatisticsInfo.TypeStatistics();
                    typeStat.setNoteType((CloudNote.NoteType) stat[0]);
                    typeStat.setCount((Long) stat[1]);
                    typeStat.setPercentage(statistics.getTotalNotes() > 0 ? (double) (Long) stat[1] / statistics.getTotalNotes() * 100 : 0.0);
                    return typeStat;
                })
                .collect(Collectors.toList());
        statistics.setTypeStatistics(typeStatistics);

        // 优先级统计
        List<Object[]> priorityStats = noteRepository.countByPriority(userId, CloudNote.NoteStatus.ACTIVE);
        List<CloudNoteDTO.StatisticsInfo.PriorityStatistics> priorityStatistics = priorityStats.stream()
                .map(stat -> {
                    CloudNoteDTO.StatisticsInfo.PriorityStatistics priorityStat = new CloudNoteDTO.StatisticsInfo.PriorityStatistics();
                    priorityStat.setPriority((CloudNote.Priority) stat[0]);
                    priorityStat.setCount((Long) stat[1]);
                    priorityStat.setPercentage(statistics.getTotalNotes() > 0 ? (double) (Long) stat[1] / statistics.getTotalNotes() * 100 : 0.0);
                    return priorityStat;
                })
                .collect(Collectors.toList());
        statistics.setPriorityStatistics(priorityStatistics);

        // 热门标签
        statistics.setPopularTags(getPopularTags(userId, 10));

        return statistics;
    }

    @Override
    public List<CloudNoteDTO.StatisticsInfo.TrendData> getNoteCreationTrend(Long userId, int days) {
        log.debug("获取笔记创建趋势: userId={}, days={}", userId, days);

        Instant startDate = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);
        List<Object[]> trendData = noteRepository.getNoteCreationTrend(userId, startDate);

        return trendData.stream()
                .map(data -> {
                    CloudNoteDTO.StatisticsInfo.TrendData trend = new CloudNoteDTO.StatisticsInfo.TrendData();
                    trend.setDate(data[0].toString());
                    trend.setCount((Long) data[1]);
                    return trend;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CloudNoteDTO.StatisticsInfo.TrendData> getNoteEditTrend(Long userId, int days) {
        log.debug("获取笔记编辑趋势: userId={}, days={}", userId, days);

        Instant startDate = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);
        List<Object[]> trendData = noteRepository.getNoteEditTrend(userId, startDate);

        return trendData.stream()
                .map(data -> {
                    CloudNoteDTO.StatisticsInfo.TrendData trend = new CloudNoteDTO.StatisticsInfo.TrendData();
                    trend.setDate(data[0].toString());
                    trend.setCount((Long) data[1]);
                    return trend;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getMostActiveNotes(Long userId, int limit) {
        log.debug("获取最活跃的笔记: userId={}, limit={}", userId, limit);

        List<CloudNote> notes = noteRepository.getMostActiveNotes(userId, CloudNote.NoteStatus.ACTIVE,
                org.springframework.data.domain.PageRequest.of(0, limit));
        return convertNotesToDTO(notes);
    }

    @Override
    public List<CloudNoteDTO.CloudNoteInfo> getLongestNotes(Long userId, int limit) {
        log.debug("获取最长的笔记: userId={}, limit={}", userId, limit);

        List<CloudNote> notes = noteRepository.getLongestNotes(userId, CloudNote.NoteStatus.ACTIVE,
                org.springframework.data.domain.PageRequest.of(0, limit));
        return convertNotesToDTO(notes);
    }

    // ==================== 同步功能 ====================

    @Override
    public CloudNoteDTO.SyncStatusInfo getSyncStatus(Long userId) {
        log.debug("获取同步状态: userId={}", userId);

        CloudNoteDTO.SyncStatusInfo syncStatus = new CloudNoteDTO.SyncStatusInfo();

        // 统计各种同步状态的笔记数量
        long totalItems = noteRepository.countByUserIdAndNoteStatus(userId, CloudNote.NoteStatus.ACTIVE);
        long syncedItems = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.SYNCED).size();
        long pendingItems = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.PENDING).size();
        long syncingItems = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.SYNCING).size();
        long failedItems = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.FAILED).size();
        long conflictItems = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.CONFLICT).size();

        syncStatus.setTotalItems(totalItems);
        syncStatus.setSyncedItems(syncedItems);
        syncStatus.setPendingItems(pendingItems);
        syncStatus.setSyncingItems(syncingItems);
        syncStatus.setFailedItems(failedItems);
        syncStatus.setConflictItems(conflictItems);

        // 计算同步进度
        double progress = totalItems > 0 ? (double) syncedItems / totalItems * 100 : 100.0;
        syncStatus.setSyncProgress(progress);

        // 设置状态描述
        if (syncingItems > 0) {
            syncStatus.setStatusDescription("正在同步中...");
            syncStatus.setIsSyncing(true);
        } else if (failedItems > 0 || conflictItems > 0) {
            syncStatus.setStatusDescription("同步存在问题");
        } else if (pendingItems > 0) {
            syncStatus.setStatusDescription("等待同步");
        } else {
            syncStatus.setStatusDescription("已同步");
        }

        // 设置最后同步时间
        List<CloudNote> recentSyncedNotes = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.SYNCED);
        if (!recentSyncedNotes.isEmpty()) {
            Instant lastSyncAt = recentSyncedNotes.stream()
                    .map(CloudNote::getLastSyncAt)
                    .filter(Objects::nonNull)
                    .max(Instant::compareTo)
                    .orElse(null);
            if (lastSyncAt != null) {
                syncStatus.setLastSyncAt(LocalDateTime.ofInstant(lastSyncAt, java.time.ZoneId.systemDefault()));
            }
        }

        return syncStatus;
    }

    @Override
    @Transactional
    public void syncNotes(Long userId) {
        log.info("同步笔记: userId={}", userId);

        List<CloudNote> pendingNotes = noteRepository.findByUserIdAndSyncStatus(userId, CloudNote.SyncStatus.PENDING);

        for (CloudNote note : pendingNotes) {
            try {
                // 模拟同步过程
                note.setSyncStatus(CloudNote.SyncStatus.SYNCING);
                noteRepository.save(note);

                // 这里应该是实际的同步逻辑
                Thread.sleep(100); // 模拟网络延迟

                note.setSyncStatus(CloudNote.SyncStatus.SYNCED);
                note.setLastSyncAt(Instant.now());
                noteRepository.save(note);

            } catch (Exception e) {
                log.error("同步笔记失败: noteId={}", note.getId(), e);
                note.setSyncStatus(CloudNote.SyncStatus.FAILED);
                noteRepository.save(note);
            }
        }

        log.info("笔记同步完成: userId={}, count={}", userId, pendingNotes.size());
    }

    @Override
    @Transactional
    public void forceSyncNotes(Long userId) {
        log.info("强制同步笔记: userId={}", userId);

        List<CloudNote> allNotes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);

        for (CloudNote note : allNotes) {
            note.setSyncStatus(CloudNote.SyncStatus.PENDING);
            noteRepository.save(note);
        }

        syncNotes(userId);
        log.info("强制同步完成: userId={}", userId);
    }

    @Override
    @Transactional
    public void resolveSyncConflict(Long noteId, Long userId, String resolution) {
        log.info("解决同步冲突: noteId={}, userId={}, resolution={}", noteId, userId, resolution);

        CloudNote note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        if (!note.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此笔记");
        }

        switch (resolution.toLowerCase()) {
            case "local":
                // 使用本地版本
                note.setSyncStatus(CloudNote.SyncStatus.SYNCED);
                note.setLastSyncAt(Instant.now());
                break;
            case "remote":
                // 使用远程版本（这里需要从远程获取数据）
                note.setSyncStatus(CloudNote.SyncStatus.SYNCED);
                note.setLastSyncAt(Instant.now());
                break;
            case "merge":
                // 合并版本（这里需要实现合并逻辑）
                note.setSyncStatus(CloudNote.SyncStatus.SYNCED);
                note.setLastSyncAt(Instant.now());
                break;
            default:
                throw new RuntimeException("不支持的冲突解决方案: " + resolution);
        }

        noteRepository.save(note);
        log.info("同步冲突解决完成: noteId={}, resolution={}", noteId, resolution);
    }

    // ==================== 导入导出 ====================

    @Override
    public String exportNotes(Long userId, CloudNoteDTO.ImportExportRequest request) {
        log.info("导出笔记: userId={}, format={}", userId, request.getFormat());

        List<CloudNote> notesToExport = new ArrayList<>();

        // 根据请求获取要导出的笔记
        if (request.getNoteIds() != null && !request.getNoteIds().isEmpty()) {
            for (Long noteId : request.getNoteIds()) {
                noteRepository.findById(noteId)
                        .filter(note -> note.getUserId().equals(userId))
                        .ifPresent(notesToExport::add);
            }
        } else if (request.getFolderIds() != null && !request.getFolderIds().isEmpty()) {
            for (Long folderId : request.getFolderIds()) {
                List<CloudNote> folderNotes = noteRepository.findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(
                        userId, folderId, CloudNote.NoteStatus.ACTIVE);
                notesToExport.addAll(folderNotes);
            }
        } else {
            // 导出所有笔记
            notesToExport = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);
        }

        // 根据格式导出
        switch (request.getFormat().toUpperCase()) {
            case "JSON":
                return exportToJson(notesToExport, request.getExportOptions());
            case "MARKDOWN":
                return exportToMarkdown(notesToExport, request.getExportOptions());
            case "HTML":
                return exportToHtml(notesToExport, request.getExportOptions());
            case "TXT":
                return exportToText(notesToExport, request.getExportOptions());
            default:
                throw new RuntimeException("不支持的导出格式: " + request.getFormat());
        }
    }

    @Override
    @Transactional
    public void importNotes(Long userId, String data, String format) {
        log.info("导入笔记: userId={}, format={}", userId, format);

        switch (format.toUpperCase()) {
            case "JSON":
                importFromJson(userId, data);
                break;
            case "MARKDOWN":
                importFromMarkdown(userId, data);
                break;
            case "TXT":
                importFromText(userId, data);
                break;
            default:
                throw new RuntimeException("不支持的导入格式: " + format);
        }

        log.info("笔记导入完成: userId={}, format={}", userId, format);
    }

    // ==================== 智能功能 ====================

    @Override
    public List<CloudNoteDTO.SmartRecommendation> getSmartRecommendations(Long userId) {
        log.debug("获取智能推荐: userId={}", userId);

        List<CloudNoteDTO.SmartRecommendation> recommendations = new ArrayList<>();

        // 推荐相关笔记
        List<CloudNote> recentNotes = getRecentlyEditedNotes(userId, 7).stream()
                .map(dto -> noteRepository.findById(dto.getId()).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!recentNotes.isEmpty()) {
            CloudNoteDTO.SmartRecommendation recommendation = new CloudNoteDTO.SmartRecommendation();
            recommendation.setRecommendationType("RELATED_NOTES");
            recommendation.setTitle("相关笔记推荐");
            recommendation.setDescription("基于您最近编辑的笔记，为您推荐相关内容");
            recommendation.setRecommendedNotes(convertNotesToDTO(recentNotes.subList(0, Math.min(5, recentNotes.size()))));
            recommendation.setConfidence(0.8);
            recommendations.add(recommendation);
        }

        // 推荐标签
        List<String> popularTags = getPopularTags(userId, 5).stream()
                .map(CloudNoteDTO.StatisticsInfo.TagStatistics::getTagName)
                .collect(Collectors.toList());

        if (!popularTags.isEmpty()) {
            CloudNoteDTO.SmartRecommendation recommendation = new CloudNoteDTO.SmartRecommendation();
            recommendation.setRecommendationType("POPULAR_TAGS");
            recommendation.setTitle("热门标签推荐");
            recommendation.setDescription("这些是您最常使用的标签");
            recommendation.setRecommendedTags(popularTags);
            recommendation.setConfidence(0.9);
            recommendations.add(recommendation);
        }

        return recommendations;
    }

    @Override
    @Transactional
    public void autoClassifyNotes(Long userId) {
        log.info("自动分类笔记: userId={}", userId);

        List<CloudNote> unclassifiedNotes = noteRepository.findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);

        for (CloudNote note : unclassifiedNotes) {
            // 简单的自动分类逻辑
            String content = (note.getTitle() + " " + note.getContent()).toLowerCase();

            if (content.contains("工作") || content.contains("项目") || content.contains("会议")) {
                // 查找或创建工作文件夹
                createOrMoveToFolder(userId, note, "工作");
            } else if (content.contains("学习") || content.contains("笔记") || content.contains("知识")) {
                createOrMoveToFolder(userId, note, "学习");
            } else if (content.contains("个人") || content.contains("日记") || content.contains("想法")) {
                createOrMoveToFolder(userId, note, "个人");
            }
        }

        log.info("自动分类完成: userId={}", userId);
    }

    @Override
    public List<String> autoExtractTags(String content) {
        log.debug("自动提取标签: content length={}", content != null ? content.length() : 0);

        if (content == null || content.trim().isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> tags = new HashSet<>();
        String lowerContent = content.toLowerCase();

        // 简单的关键词提取
        String[] keywords = {"工作", "学习", "项目", "会议", "想法", "计划", "总结", "笔记", "重要", "紧急"};

        for (String keyword : keywords) {
            if (lowerContent.contains(keyword)) {
                tags.add(keyword);
            }
        }

        return new ArrayList<>(tags);
    }

    @Override
    public String generateNoteSummary(String content) {
        log.debug("生成笔记摘要: content length={}", content != null ? content.length() : 0);

        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        // 简单的摘要生成：取前100个字符
        String summary = content.trim();
        if (summary.length() > 100) {
            summary = summary.substring(0, 100) + "...";
        }

        return summary;
    }

    // ==================== 清理维护 ====================

    @Override
    @Transactional
    public int cleanupExpiredNotes() {
        log.info("清理过期笔记");

        int cleanedCount = noteRepository.cleanupExpiredNotes(Instant.now());
        log.info("过期笔记清理完成: count={}", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional
    public int physicalDeleteOldNotes(int days) {
        log.info("物理删除旧笔记: days={}", days);

        Instant cutoffTime = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);
        int deletedCount = noteRepository.physicalDeleteOldNotes(cutoffTime);
        log.info("旧笔记物理删除完成: count={}", deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public void rebuildNoteIndex(Long userId) {
        log.info("重建笔记索引: userId={}", userId);

        List<CloudNote> allNotes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);

        for (CloudNote note : allNotes) {
            // 重新计算字数统计
            note.updateWordCount();
            noteRepository.save(note);
        }

        log.info("笔记索引重建完成: userId={}, count={}", userId, allNotes.size());
    }

    @Override
    @Transactional
    public void repairNoteData(Long userId) {
        log.info("修复笔记数据: userId={}", userId);

        List<CloudNote> allNotes = noteRepository.findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(userId, CloudNote.NoteStatus.ACTIVE);

        for (CloudNote note : allNotes) {
            // 修复数据
            if (note.getWordCount() == null || note.getWordCount() == 0) {
                note.updateWordCount();
            }

            if (note.getReadCount() == null) {
                note.setReadCount(0);
            }

            if (note.getEditCount() == null) {
                note.setEditCount(1);
            }

            if (note.getVersion() == null) {
                note.setVersion(1);
            }

            noteRepository.save(note);
        }

        log.info("笔记数据修复完成: userId={}, count={}", userId, allNotes.size());
    }

    // ==================== 私有辅助方法 ====================

    private String exportToJson(List<CloudNote> notes, CloudNoteDTO.ImportExportRequest.ExportOptions options) {
        // 简化的JSON导出实现
        StringBuilder json = new StringBuilder();
        json.append("{\n  \"notes\": [\n");

        for (int i = 0; i < notes.size(); i++) {
            CloudNote note = notes.get(i);
            json.append("    {\n");
            json.append("      \"title\": \"").append(escapeJson(note.getTitle())).append("\",\n");
            json.append("      \"content\": \"").append(escapeJson(note.getContent())).append("\",\n");
            json.append("      \"type\": \"").append(note.getNoteType()).append("\",\n");
            json.append("      \"tags\": \"").append(note.getTags() != null ? note.getTags() : "").append("\",\n");
            json.append("      \"created\": \"").append(note.getCreatedAt()).append("\"\n");
            json.append("    }");
            if (i < notes.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }

        json.append("  ]\n}");
        return json.toString();
    }

    private String exportToMarkdown(List<CloudNote> notes, CloudNoteDTO.ImportExportRequest.ExportOptions options) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("# 云笔记导出\n\n");

        for (CloudNote note : notes) {
            markdown.append("## ").append(note.getTitle()).append("\n\n");
            markdown.append(note.getContent()).append("\n\n");
            if (note.getTags() != null && !note.getTags().isEmpty()) {
                markdown.append("**标签**: ").append(note.getTags()).append("\n\n");
            }
            markdown.append("---\n\n");
        }

        return markdown.toString();
    }

    private String exportToHtml(List<CloudNote> notes, CloudNoteDTO.ImportExportRequest.ExportOptions options) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n<html>\n<head>\n<title>云笔记导出</title>\n</head>\n<body>\n");
        html.append("<h1>云笔记导出</h1>\n");

        for (CloudNote note : notes) {
            html.append("<h2>").append(escapeHtml(note.getTitle())).append("</h2>\n");
            html.append("<div>").append(escapeHtml(note.getContent())).append("</div>\n");
            if (note.getTags() != null && !note.getTags().isEmpty()) {
                html.append("<p><strong>标签</strong>: ").append(escapeHtml(note.getTags())).append("</p>\n");
            }
            html.append("<hr>\n");
        }

        html.append("</body>\n</html>");
        return html.toString();
    }

    private String exportToText(List<CloudNote> notes, CloudNoteDTO.ImportExportRequest.ExportOptions options) {
        StringBuilder text = new StringBuilder();
        text.append("云笔记导出\n");
        text.append("=".repeat(50)).append("\n\n");

        for (CloudNote note : notes) {
            text.append(note.getTitle()).append("\n");
            text.append("-".repeat(note.getTitle().length())).append("\n");
            text.append(note.getContent()).append("\n\n");
            if (note.getTags() != null && !note.getTags().isEmpty()) {
                text.append("标签: ").append(note.getTags()).append("\n\n");
            }
            text.append("\n");
        }

        return text.toString();
    }

    private void importFromJson(Long userId, String jsonData) {
        // 简化的JSON导入实现
        log.info("从JSON导入笔记: userId={}", userId);
        // 这里应该解析JSON并创建笔记
    }

    private void importFromMarkdown(Long userId, String markdownData) {
        // 简化的Markdown导入实现
        log.info("从Markdown导入笔记: userId={}", userId);
        // 这里应该解析Markdown并创建笔记
    }

    private void importFromText(Long userId, String textData) {
        // 简化的文本导入实现
        log.info("从文本导入笔记: userId={}", userId);

        String[] lines = textData.split("\n");
        StringBuilder content = new StringBuilder();
        String title = "导入的笔记";

        if (lines.length > 0 && !lines[0].trim().isEmpty()) {
            title = lines[0].trim();
            for (int i = 1; i < lines.length; i++) {
                content.append(lines[i]).append("\n");
            }
        } else {
            content.append(textData);
        }

        CloudNote note = new CloudNote(userId, title, content.toString().trim(), CloudNote.NoteType.TEXT);
        noteRepository.save(note);
    }

    private void createOrMoveToFolder(Long userId, CloudNote note, String folderName) {
        // 查找文件夹
        List<CloudNoteFolder> allFolders = folderRepository.findByUserIdAndFolderStatusOrderByIsPinnedDescSortOrderAscFolderNameAsc(
                userId, CloudNoteFolder.FolderStatus.ACTIVE);

        CloudNoteFolder folder = allFolders.stream()
                .filter(f -> folderName.equals(f.getFolderName()))
                .findFirst()
                .orElse(null);

        if (folder == null) {
            // 创建文件夹
            folder = new CloudNoteFolder(userId, folderName, CloudNoteFolder.FolderType.CUSTOM);
            folder = folderRepository.save(folder);
        }

        // 移动笔记
        note.setFolderId(folder.getId());
        noteRepository.save(note);

        // 更新文件夹统计
        folderRepository.updateNoteCount(userId, folder.getId(), 1, Instant.now());
    }

    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
    }

    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace("\"", "&quot;");
    }
}
