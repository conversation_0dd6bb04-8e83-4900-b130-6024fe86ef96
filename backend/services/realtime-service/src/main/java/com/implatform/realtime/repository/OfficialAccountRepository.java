package com.implatform.realtime.repository;

import com.implatform.realtime.entity.OfficialAccount;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 公众号数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface OfficialAccountRepository extends R2dbcRepository<OfficialAccount, String> {

    /**
     * 根据账号代码查找公众号
     */
    @Query("SELECT * FROM official_accounts WHERE account_code = :accountCode")
    Mono<OfficialAccount> findByAccountCode(@Param("accountCode") String accountCode);

    /**
     * 根据所有者ID查找公众号列表
     */
    @Query("SELECT * FROM official_accounts WHERE owner_id = :ownerId AND status = :status ORDER BY created_at DESC")
    Flux<OfficialAccount> findByOwnerIdAndStatusOrderByCreatedAtDesc(@Param("ownerId") String ownerId, @Param("status") String status);

    /**
     * 根据账号类型查找公众号
     */
    @Query("SELECT * FROM official_accounts WHERE account_type = :accountType AND status = :status ORDER BY follower_count DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findByAccountTypeAndStatusOrderByFollowerCountDesc(
            @Param("accountType") String accountType,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 根据认证状态查找公众号
     */
    @Query("SELECT * FROM official_accounts WHERE verification_status = :verificationStatus AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findByVerificationStatusAndStatusOrderByCreatedAtDesc(
            @Param("verificationStatus") String verificationStatus,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 搜索公众号
     */
    @Query("SELECT * FROM official_accounts WHERE " +
           "(account_name LIKE CONCAT('%', :keyword, '%') OR description LIKE CONCAT('%', :keyword, '%')) " +
           "AND status = :status " +
           "ORDER BY follower_count DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> searchAccounts(@Param("keyword") String keyword,
                                       @Param("status") String status,
                                       @Param("limit") int limit,
                                       @Param("offset") long offset);

    /**
     * 获取热门公众号
     */
    @Query("SELECT * FROM official_accounts WHERE " +
           "status = :status AND follower_count >= :minFollowers " +
           "ORDER BY follower_count DESC, total_read_count DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findPopularAccounts(@Param("status") String status,
                                            @Param("minFollowers") Long minFollowers,
                                            @Param("limit") int limit,
                                            @Param("offset") long offset);

    /**
     * 获取最新公众号
     */
    @Query("SELECT * FROM official_accounts WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findByStatusOrderByCreatedAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计公众号数量
     */
    @Query("SELECT COUNT(*) FROM official_accounts WHERE status = :status")
    Mono<Long> countByStatus(@Param("status") String status);

    /**
     * 统计用户拥有的公众号数量
     */
    @Query("SELECT COUNT(*) FROM official_accounts WHERE owner_id = :ownerId AND status = :status")
    Mono<Long> countByOwnerIdAndStatus(@Param("ownerId") String ownerId, @Param("status") String status);

    /**
     * 统计各类型公众号数量
     */
    @Query("SELECT account_type, COUNT(*) FROM official_accounts WHERE status = :status GROUP BY account_type")
    Flux<Object[]> countByAccountTypeAndStatus(@Param("status") String status);

    /**
     * 统计各认证状态公众号数量
     */
    @Query("SELECT verification_status, COUNT(*) FROM official_accounts WHERE status = :status GROUP BY verification_status")
    Flux<Object[]> countByVerificationStatusAndStatus(@Param("status") String status);

    /**
     * 更新关注者数量
     */
    @Modifying
    @Query("UPDATE official_accounts SET follower_count = :followerCount, updated_at = :updatedAt WHERE id = :accountId")
    Mono<Integer> updateFollowerCount(@Param("accountId") String accountId,
                           @Param("followerCount") Long followerCount,
                           @Param("updatedAt") Instant updatedAt);

    /**
     * 更新文章数量
     */
    @Modifying
    @Query("UPDATE official_accounts SET article_count = :articleCount, updated_at = :updatedAt WHERE id = :accountId")
    Mono<Integer> updateArticleCount(@Param("accountId") String accountId,
                          @Param("articleCount") Long articleCount,
                          @Param("updatedAt") Instant updatedAt);

    /**
     * 更新总阅读量
     */
    @Modifying
    @Query("UPDATE official_accounts SET total_read_count = total_read_count + :readCount, updated_at = :updatedAt WHERE id = :accountId")
    Mono<Integer> addReadCount(@Param("accountId") String accountId,
                    @Param("readCount") Long readCount,
                    @Param("updatedAt") Instant updatedAt);

    /**
     * 更新认证状态
     */
    @Modifying
    @Query("UPDATE official_accounts SET verification_status = :status, verification_info = :info, updated_at = :updatedAt WHERE id = :accountId")
    Mono<Integer> updateVerificationStatus(@Param("accountId") String accountId,
                               @Param("status") String status,
                               @Param("info") String info,
                               @Param("updatedAt") Instant updatedAt);

    /**
     * 更新菜单配置
     */
    @Modifying
    @Query("UPDATE official_accounts SET menu_config = :menuConfig, updated_at = :updatedAt WHERE id = :accountId")
    Mono<Integer> updateMenuConfig(@Param("accountId") String accountId,
                        @Param("menuConfig") String menuConfig,
                        @Param("updatedAt") Instant updatedAt);

    /**
     * 获取公众号统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as total_accounts, " +
           "COUNT(CASE WHEN verification_status = 'VERIFIED' THEN 1 END) as verified_accounts, " +
           "SUM(follower_count) as total_followers, " +
           "SUM(article_count) as total_articles, " +
           "SUM(total_read_count) as total_reads " +
           "FROM official_accounts WHERE status = :status")
    Mono<Object[]> getAccountStatistics(@Param("status") String status);

    /**
     * 获取时间范围内的新增公众号
     */
    @Query("SELECT COUNT(*) FROM official_accounts WHERE created_at >= :startTime AND created_at <= :endTime AND status = :status")
    Mono<Long> countNewAccountsInPeriod(@Param("startTime") Instant startTime,
                                 @Param("endTime") Instant endTime,
                                 @Param("status") String status);

    /**
     * 获取关注者数量排行榜
     */
    @Query("SELECT * FROM official_accounts WHERE status = :status ORDER BY follower_count DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findTopAccountsByFollowers(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取阅读量排行榜
     */
    @Query("SELECT * FROM official_accounts WHERE status = :status ORDER BY total_read_count DESC LIMIT :limit OFFSET :offset")
    Flux<OfficialAccount> findTopAccountsByReads(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 检查账号代码是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM official_accounts WHERE account_code = :accountCode")
    Mono<Boolean> existsByAccountCode(@Param("accountCode") String accountCode);

    /**
     * 检查用户是否已拥有指定类型的公众号
     */
    @Query("SELECT COUNT(*) > 0 FROM official_accounts WHERE owner_id = :ownerId AND account_type = :accountType AND status = :status")
    Mono<Boolean> existsByOwnerIdAndAccountTypeAndStatus(@Param("ownerId") String ownerId, @Param("accountType") String accountType, @Param("status") String status);
}
