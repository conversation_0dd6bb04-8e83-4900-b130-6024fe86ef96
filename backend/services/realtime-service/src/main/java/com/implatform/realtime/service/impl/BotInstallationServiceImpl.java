package com.implatform.realtime.service.impl;

import com.implatform.realtime.entity.BotInstallation;
import com.implatform.realtime.repository.BotInstallationRepository;
import com.implatform.realtime.service.BotInstallationService;
import com.implatform.service.BotService;
import com.implatform.common.core.enums.BotErrorCode;
import com.implatform.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 机器人安装服务实现类
 * 提供机器人安装和权限管理功能
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BotInstallationServiceImpl implements BotInstallationService {
    
    private final BotInstallationRepository installationRepository;
    private final BotService botService;
    
    // 安装配置
    private static final int MAX_INSTALLATIONS_PER_USER = 50;
    private static final int MAX_INSTALLATIONS_PER_CHAT = 20;
    private static final long INACTIVE_INSTALLATION_DAYS = 90;
    
    /**
     * 安装机器人
     */
    @Transactional
    @CacheEvict(value = {"user-installations", "chat-installations", "bot-installations"}, allEntries = true)
    public BotInstallation installBot(Long botId, String userId, String chatId,
                                    BotInstallation.InstallationType installationType,
                                    Map<String, Object> permissions) {
        log.debug("Installing bot: {} by user: {} in chat: {}", botId, userId, chatId);
        
        // 验证安装数据
        validateInstallation(botId, userId, chatId, installationType);
        
        // 检查是否已安装
        Optional<BotInstallation> existing = installationRepository
            .findByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(botId, chatId, installationType);
        
        if (existing.isPresent()) {
            log.warn("Bot {} already installed in chat: {}", botId, chatId);
            return existing.get();
        }
        
        // 检查安装数量限制
        validateInstallationLimits(userId, chatId);
        
        // 创建安装记录
        BotInstallation installation = BotInstallation.builder()
            .botId(botId)
            .userId(userId)
            .chatId(chatId)
            .installationType(installationType)
            .permissions(permissions)
            .isActive(true)
            .build();
        
        BotInstallation savedInstallation = installationRepository.save(installation);
        
        // 更新机器人安装数量
        botService.incrementInstallCount(botId);
        
        // 异步处理安装后操作
        processInstallationAsync(savedInstallation);
        
        log.debug("Installed bot with installation ID: {}", savedInstallation.getId());
        return savedInstallation;
    }
    
    /**
     * 卸载机器人
     */
    @Transactional
    @CacheEvict(value = {"user-installations", "chat-installations", "bot-installations"}, allEntries = true)
    public void uninstallBot(Long botId, String chatId, BotInstallation.InstallationType installationType,
                           String uninstalledBy) {
        log.debug("Uninstalling bot: {} from chat: {} by user: {}", botId, chatId, uninstalledBy);
        
        Optional<BotInstallation> installation = installationRepository
            .findByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(botId, chatId, installationType);
        
        if (installation.isEmpty()) {
            throw new BusinessException(BotErrorCode.BOT_NOT_FOUND);
        }

        BotInstallation inst = installation.get();

        // 验证权限
        if (!inst.getUserId().equals(uninstalledBy) && !isAdminUser(uninstalledBy, chatId)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }
        
        // 标记为不活跃
        inst.setIsActive(false);
        inst.setUninstalledAt(Instant.now());
        inst.setUninstalledBy(uninstalledBy);
        installationRepository.save(inst);
        
        // 更新机器人安装数量
        botService.decrementInstallCount(botId);
        
        // 异步处理卸载后操作
        processUninstallationAsync(inst);
        
        log.info("Uninstalled bot: {} from chat: {}", botId, chatId);
    }
    
    /**
     * 获取用户安装的机器人
     */
    @Cacheable(value = "user-installations", key = "#userId")
    public List<BotInstallation> getUserInstallations(String userId) {
        return installationRepository.findByUserIdAndIsActiveTrueOrderByInstalledAtDesc(userId);
    }
    
    /**
     * 获取聊天中的机器人
     */
    @Cacheable(value = "chat-installations", key = "#chatId")
    public List<BotInstallation> getChatInstallations(String chatId) {
        return installationRepository.findByChatIdAndIsActiveTrueOrderByInstalledAtDesc(chatId);
    }
    
    /**
     * 获取机器人的所有安装
     */
    @Cacheable(value = "bot-installations", key = "#botId")
    public List<BotInstallation> getBotInstallations(Long botId) {
        return installationRepository.findByBotIdAndIsActiveTrueOrderByInstalledAtDesc(botId);
    }
    
    /**
     * 获取机器人安装详情
     */
    @Cacheable(value = "installation-details", key = "#installationId")
    public Optional<BotInstallation> getInstallationById(String installationId) {
        return installationRepository.findById(installationId);
    }
    
    /**
     * 检查机器人是否已安装
     */
    public boolean isBotInstalled(Long botId, String chatId, BotInstallation.InstallationType installationType) {
        return installationRepository.existsByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(
            botId, chatId, installationType);
    }
    
    /**
     * 根据类型获取安装
     */
    @Cacheable(value = "installations-by-type", key = "#installationType")
    public List<BotInstallation> getInstallationsByType(BotInstallation.InstallationType installationType) {
        return installationRepository.findByInstallationTypeAndIsActiveTrueOrderByInstalledAtDesc(installationType);
    }
    
    /**
     * 获取私聊安装
     */
    @Cacheable(value = "private-installations", unless = "#result.isEmpty()")
    public List<BotInstallation> getPrivateInstallations() {
        return getInstallationsByType(BotInstallation.InstallationType.PRIVATE_CHAT);
    }
    
    /**
     * 获取群组安装
     */
    @Cacheable(value = "group-installations", unless = "#result.isEmpty()")
    public List<BotInstallation> getGroupInstallations() {
        return getInstallationsByType(BotInstallation.InstallationType.GROUP_CHAT);
    }
    
    /**
     * 获取频道安装
     */
    @Cacheable(value = "channel-installations", unless = "#result.isEmpty()")
    public List<BotInstallation> getChannelInstallations() {
        return getInstallationsByType(BotInstallation.InstallationType.CHANNEL);
    }
    
    /**
     * 更新机器人权限
     */
    @Transactional
    @CacheEvict(value = {"installation-details", "chat-installations"}, allEntries = true)
    public BotInstallation updateBotPermissions(String installationId, Map<String, Object> newPermissions,
                                              String updatedBy) {
        BotInstallation installation = installationRepository.findById(installationId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!installation.getUserId().equals(updatedBy) && !isAdminUser(updatedBy, installation.getChatId())) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }
        
        installation.setPermissions(newPermissions);
        // updatedAt is automatically handled by @UpdateTimestamp
        
        BotInstallation updatedInstallation = installationRepository.save(installation);
        log.debug("Updated permissions for installation: {}", installationId);
        
        return updatedInstallation;
    }
    
    /**
     * 更新最后使用时间
     */
    @Transactional
    public void updateLastUsedTime(String installationId) {
        installationRepository.updateLastUsedTime(List.of(installationId), LocalDateTime.now());
        log.debug("Updated last used time for installation: {}", installationId);
    }
    
    /**
     * 获取安装统计信息
     */
    @Cacheable(value = "installation-stats", unless = "#result.isEmpty()")
    public Map<String, Object> getInstallationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 按类型统计
        List<Object[]> typeStats = installationRepository.countByInstallationType();
        
        // 按机器人统计
        List<Object[]> botStats = installationRepository.countByBot();
        
        // 活跃度统计（最近7天）
        Instant sevenDaysAgo = Instant.now().minus(7, java.time.temporal.ChronoUnit.DAYS);
        Object[] activityStats = installationRepository.getInstallationActivityStats(sevenDaysAgo);

        // 时间趋势（最近30天）
        Instant thirtyDaysAgo = Instant.now().minus(30, java.time.temporal.ChronoUnit.DAYS);
        List<Object[]> trends = installationRepository.getInstallationTrends(thirtyDaysAgo);
        
        stats.put("typeStats", typeStats);
        stats.put("botStats", botStats);
        stats.put("activityStats", activityStats);
        stats.put("trends", trends);
        stats.put("totalInstallations", installationRepository.countByIsActiveTrue());
        
        return stats;
    }
    
    /**
     * 获取热门机器人安装
     */
    @Cacheable(value = "popular-bot-installations", unless = "#result.isEmpty()")
    public List<Object[]> getPopularBotInstallations(int limit) {
        return installationRepository.findMostInstalledBots()
            .stream()
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取不活跃的安装
     */
    public List<BotInstallation> getInactiveInstallations() {
        // Use the repository method that expects Long (days) and boolean (isActive)
        return installationRepository.findInactiveInstallations(INACTIVE_INSTALLATION_DAYS, false);
    }
    
    /**
     * 获取用户安装历史
     */
    public Page<BotInstallation> getUserInstallationHistory(String userId, Pageable pageable) {
        return installationRepository.findByUserIdOrderByInstalledAtDesc(userId, pageable);
    }
    
    /**
     * 获取聊天安装历史
     */
    public Page<BotInstallation> getChatInstallationHistory(String chatId, Pageable pageable) {
        return installationRepository.findByChatIdOrderByInstalledAtDesc(chatId, pageable);
    }
    
    /**
     * 批量卸载机器人
     */
    @Transactional
    @CacheEvict(value = {"user-installations", "chat-installations", "bot-installations"}, allEntries = true)
    public void batchUninstallBots(List<String> installationIds, String uninstalledBy) {
        List<BotInstallation> installations = installationRepository.findAllById(installationIds);
        
        for (BotInstallation installation : installations) {
            if (installation.getIsActive()) {
                installation.setIsActive(false);
                installation.setUninstalledAt(Instant.now());
                installation.setUninstalledBy(uninstalledBy);
            }
        }
        
        installationRepository.saveAll(installations);
        log.info("Batch uninstalled {} bots by user: {}", installations.size(), uninstalledBy);
    }
    
    /**
     * 异步处理安装后操作
     */
    @Async
    @Transactional
    public CompletableFuture<Void> processInstallationAsync(BotInstallation installation) {
        try {
            log.debug("Processing installation: {}", installation.getId());
            
            // 发送欢迎消息
            sendWelcomeMessage(installation);
            
            // 设置默认权限
            setupDefaultPermissions(installation);
            
            // 记录安装事件
            recordInstallationEvent(installation);
            
            log.debug("Installation processing completed: {}", installation.getId());
        } catch (Exception e) {
            log.error("Error processing installation {}: {}", installation.getId(), e.getMessage(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 异步处理卸载后操作
     */
    @Async
    @Transactional
    public CompletableFuture<Void> processUninstallationAsync(BotInstallation installation) {
        try {
            log.debug("Processing uninstallation: {}", installation.getId());
            
            // 清理机器人数据
            cleanupBotData(installation);
            
            // 发送告别消息
            sendGoodbyeMessage(installation);
            
            // 记录卸载事件
            recordUninstallationEvent(installation);
            
            log.debug("Uninstallation processing completed: {}", installation.getId());
        } catch (Exception e) {
            log.error("Error processing uninstallation {}: {}", installation.getId(), e.getMessage(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 定时清理不活跃安装
     */
    @Scheduled(cron = "0 0 11 * * ?") // 每天上午11点执行
    @Transactional
    public void scheduledCleanupInactiveInstallations() {
        log.info("Starting scheduled cleanup of inactive installations");
        
        try {
            List<BotInstallation> inactiveInstallations = getInactiveInstallations();
            
            for (BotInstallation installation : inactiveInstallations) {
                // 标记为不活跃但不删除，保留历史记录
                installation.setIsActive(false);
                installation.setUninstalledAt(Instant.now());
                installation.setUninstalledBy(null); // 系统自动卸载
            }
            
            installationRepository.saveAll(inactiveInstallations);
            
            log.info("Cleaned up {} inactive installations", inactiveInstallations.size());
        } catch (Exception e) {
            log.error("Error in scheduled installation cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 验证安装数据
     */
    private void validateInstallation(Long botId, String userId, String chatId,
                                    BotInstallation.InstallationType installationType) {
        if (botId == null) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_ID);
        }
        if (userId == null) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_ID);
        }
        if (chatId == null) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_ID);
        }
        if (installationType == null) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_TYPE);
        }
    }
    
    /**
     * 验证安装数量限制
     */
    private void validateInstallationLimits(String userId, String chatId) {
        // 检查用户安装数量
        long userInstallations = installationRepository.countByUserIdAndIsActiveTrue(userId);
        if (userInstallations >= MAX_INSTALLATIONS_PER_USER) {
            throw new IllegalStateException("User has reached maximum installation limit: " + MAX_INSTALLATIONS_PER_USER);
        }
        
        // 检查聊天安装数量
        long chatInstallations = installationRepository.countByChatIdAndIsActiveTrue(chatId);
        if (chatInstallations >= MAX_INSTALLATIONS_PER_CHAT) {
            throw new IllegalStateException("Chat has reached maximum installation limit: " + MAX_INSTALLATIONS_PER_CHAT);
        }
    }
    
    /**
     * 检查是否为管理员用户
     */
    private boolean isAdminUser(String userId, String chatId) {
        // 这里可以调用群组服务检查管理员权限
        return false; // 简化实现
    }
    
    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(BotInstallation installation) {
        log.debug("Sending welcome message for installation: {}", installation.getId());
        // 实现欢迎消息发送逻辑
    }
    
    /**
     * 设置默认权限
     */
    private void setupDefaultPermissions(BotInstallation installation) {
        log.debug("Setting up default permissions for installation: {}", installation.getId());
        // 实现默认权限设置逻辑
    }
    
    /**
     * 记录安装事件
     */
    private void recordInstallationEvent(BotInstallation installation) {
        log.debug("Recording installation event: {}", installation.getId());
        // 实现安装事件记录逻辑
    }
    
    /**
     * 清理机器人数据
     */
    private void cleanupBotData(BotInstallation installation) {
        log.debug("Cleaning up bot data for installation: {}", installation.getId());
        // 实现机器人数据清理逻辑
    }
    
    /**
     * 发送告别消息
     */
    private void sendGoodbyeMessage(BotInstallation installation) {
        log.debug("Sending goodbye message for installation: {}", installation.getId());
        // 实现告别消息发送逻辑
    }
    
    /**
     * 记录卸载事件
     */
    private void recordUninstallationEvent(BotInstallation installation) {
        log.debug("Recording uninstallation event: {}", installation.getId());
        // 实现卸载事件记录逻辑
    }
}
