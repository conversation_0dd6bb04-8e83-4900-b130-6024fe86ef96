package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Poll;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 投票Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface PollRepository extends R2dbcRepository<Poll, Long> {

    /**
     * 根据消息ID查找投票
     */
    @Query("SELECT * FROM polls WHERE message_id = :messageId")
    Mono<Poll> findByMessageId(@Param("messageId") Long messageId);

    /**
     * 根据会话ID查找投票
     */
    @Query("SELECT * FROM polls WHERE conversation_id = :conversationId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Poll> findByConversationIdOrderByCreatedAtDesc(@Param("conversationId") Long conversationId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据创建者ID查找投票
     */
    @Query("SELECT * FROM polls WHERE creator_id = :creatorId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Poll> findByCreatorIdOrderByCreatedAtDesc(@Param("creatorId") Long creatorId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找开放的投票
     */
    @Query("SELECT * FROM polls WHERE is_closed = false AND (close_date IS NULL OR close_date > CURRENT_TIMESTAMP)")
    Flux<Poll> findOpenPolls();

    /**
     * 查找已关闭的投票
     */
    @Query("SELECT * FROM polls WHERE is_closed = true ORDER BY closed_at DESC LIMIT :limit OFFSET :offset")
    Flux<Poll> findByIsClosedTrueOrderByClosedAtDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找过期的投票
     */
    @Query("SELECT p FROM Poll p WHERE p.isClosed = false AND p.closeDate IS NOT NULL AND p.closeDate <= CURRENT_TIMESTAMP")
    List<Poll> findExpiredPolls();

    /**
     * 查找指定类型的投票
     */
    List<Poll> findByPollTypeOrderByCreatedAtDesc(Poll.PollType pollType, Pageable pageable);

    /**
     * 查找会话中的开放投票
     */
    @Query("SELECT p FROM Poll p WHERE p.conversationId = :conversationId AND p.isClosed = false " +
           "AND (p.closeDate IS NULL OR p.closeDate > CURRENT_TIMESTAMP)")
    List<Poll> findOpenPollsInConversation(@Param("conversationId") Long conversationId);

    /**
     * 统计用户创建的投票数量
     */
    @Query("SELECT COUNT(p) FROM Poll p WHERE p.creatorId = :creatorId")
    Long countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 统计会话中的投票数量
     */
    @Query("SELECT COUNT(p) FROM Poll p WHERE p.conversationId = :conversationId")
    Long countByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查找热门投票（参与人数多）
     */
    @Query("SELECT p FROM Poll p WHERE p.totalVoterCount >= :minVoters ORDER BY p.totalVoterCount DESC")
    List<Poll> findPopularPolls(@Param("minVoters") Integer minVoters, Pageable pageable);

    /**
     * 查找最近创建的投票
     */
    @Query("SELECT p FROM Poll p WHERE p.createdAt >= :since ORDER BY p.createdAt DESC")
    List<Poll> findRecentPolls(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查找即将过期的投票
     */
    @Query("SELECT p FROM Poll p WHERE p.isClosed = false AND p.closeDate IS NOT NULL " +
           "AND p.closeDate BETWEEN CURRENT_TIMESTAMP AND :deadline")
    List<Poll> findPollsExpiringBefore(@Param("deadline") LocalDateTime deadline);

    /**
     * 查找长时间运行的投票
     */
    @Query("SELECT p FROM Poll p WHERE p.isClosed = false AND p.createdAt < :cutoffTime")
    List<Poll> findLongRunningPolls(@Param("cutoffTime") LocalDateTime cutoffTime, Pageable pageable);

    /**
     * 自动关闭过期的投票
     */
    @Modifying
    @Query("UPDATE Poll p SET p.isClosed = true, p.closedAt = CURRENT_TIMESTAMP " +
           "WHERE p.isClosed = false AND p.closeDate IS NOT NULL AND p.closeDate <= CURRENT_TIMESTAMP")
    int closeExpiredPolls();

    /**
     * 查找测验投票
     */
    @Query("SELECT p FROM Poll p WHERE p.pollType = 'QUIZ' ORDER BY p.createdAt DESC")
    List<Poll> findQuizPolls(Pageable pageable);

    /**
     * 查找匿名投票
     */
    @Query("SELECT p FROM Poll p WHERE p.pollType = 'ANONYMOUS' OR p.isAnonymous = true ORDER BY p.createdAt DESC")
    List<Poll> findAnonymousPolls(Pageable pageable);

    /**
     * 查找多选投票
     */
    @Query("SELECT p FROM Poll p WHERE p.isMultipleChoice = true ORDER BY p.createdAt DESC")
    List<Poll> findMultipleChoicePolls(Pageable pageable);

    /**
     * 获取投票统计信息
     */
    @Query("SELECT " +
           "COUNT(p) as totalPolls, " +
           "COUNT(CASE WHEN p.isClosed = false THEN 1 END) as openPolls, " +
           "COUNT(CASE WHEN p.isClosed = true THEN 1 END) as closedPolls, " +
           "AVG(p.totalVoterCount) as avgParticipants " +
           "FROM Poll p WHERE p.createdAt >= :since")
    Object[] getPollStatistics(@Param("since") LocalDateTime since);

    /**
     * 按投票类型统计
     */
    @Query("SELECT p.pollType, COUNT(p) FROM Poll p WHERE p.createdAt >= :since GROUP BY p.pollType")
    List<Object[]> getPollStatsByType(@Param("since") LocalDateTime since);

    /**
     * 查找用户参与的投票
     */
    @Query("SELECT DISTINCT p FROM Poll p JOIN PollVote v ON p.id = v.pollId " +
           "WHERE v.voterId = :userId AND v.isRetracted = false ORDER BY v.voteTime DESC")
    Page<Poll> findPollsVotedByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找用户未参与的开放投票
     */
    @Query("SELECT p FROM Poll p WHERE p.conversationId = :conversationId AND p.isClosed = false " +
           "AND (p.closeDate IS NULL OR p.closeDate > CURRENT_TIMESTAMP) " +
           "AND p.id NOT IN (SELECT v.pollId FROM PollVote v WHERE v.voterId = :userId AND v.isRetracted = false)")
    List<Poll> findUnvotedOpenPollsForUser(@Param("conversationId") Long conversationId, 
                                          @Param("userId") Long userId);

    /**
     * 查找活跃的投票创建者
     */
    @Query("SELECT p.creatorId, COUNT(p) as pollCount FROM Poll p WHERE p.createdAt >= :since " +
           "GROUP BY p.creatorId ORDER BY pollCount DESC")
    List<Object[]> findActivePollCreators(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查找投票参与度高的会话
     */
    @Query("SELECT p.conversationId, AVG(p.totalVoterCount) as avgParticipants FROM Poll p " +
           "WHERE p.createdAt >= :since GROUP BY p.conversationId " +
           "HAVING AVG(p.totalVoterCount) >= :minAvgParticipants ORDER BY avgParticipants DESC")
    List<Object[]> findHighEngagementConversations(@Param("since") LocalDateTime since,
                                                   @Param("minAvgParticipants") Double minAvgParticipants,
                                                   Pageable pageable);

    /**
     * 删除旧的投票记录
     */
    @Modifying
    @Query("DELETE FROM Poll p WHERE p.createdAt < :cutoffTime AND p.isClosed = true")
    int deleteOldPolls(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找需要提醒的投票（即将过期）
     */
    @Query("SELECT p FROM Poll p WHERE p.isClosed = false AND p.closeDate IS NOT NULL " +
           "AND p.closeDate BETWEEN :startTime AND :endTime")
    List<Poll> findPollsNeedingReminder(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);
}
