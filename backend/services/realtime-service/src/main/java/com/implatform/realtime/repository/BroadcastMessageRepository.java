package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BroadcastMessage;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群发消息Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BroadcastMessageRepository extends R2dbcRepository<BroadcastMessage, Long> {

    /**
     * 根据发送者ID查找群发消息
     */
    @Query("SELECT * FROM broadcast_messages WHERE sender_id = :senderId AND is_active = true ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastMessage> findBySenderIdAndIsActiveTrueOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据发送者ID和状态查找群发消息
     */
    @Query("SELECT * FROM broadcast_messages WHERE sender_id = :senderId AND status = :status AND is_active = true ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastMessage> findBySenderIdAndStatusAndIsActiveTrueOrderByCreatedAtDesc(
            @Param("senderId") Long senderId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据发送者ID和群发类型查找群发消息
     */
    @Query("SELECT * FROM broadcast_messages WHERE sender_id = :senderId AND broadcast_type = :broadcastType AND is_active = true ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastMessage> findBySenderIdAndBroadcastTypeAndIsActiveTrueOrderByCreatedAtDesc(
            @Param("senderId") Long senderId, @Param("broadcastType") String broadcastType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找需要发送的群发消息
     */
    @Query("SELECT * FROM broadcast_messages WHERE status = 'PENDING' AND is_active = true AND " +
           "(is_scheduled = false OR (is_scheduled = true AND scheduled_time <= :currentTime)) " +
           "ORDER BY created_at ASC")
    Flux<BroadcastMessage> findMessagesToSend(@Param("currentTime") Instant currentTime);

    /**
     * 查找正在发送的群发消息
     */
    List<BroadcastMessage> findByStatusAndIsActiveTrueOrderByStartedAtAsc(BroadcastMessage.BroadcastStatus status);

    /**
     * 根据状态查找群发消息（分页）
     */
    Page<BroadcastMessage> findByStatus(BroadcastMessage.BroadcastStatus status, Pageable pageable);

    /**
     * 查找需要重试的群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.status = 'FAILED' AND bm.isActive = true AND " +
           "bm.retryCount < bm.maxRetryCount ORDER BY bm.updatedAt ASC")
    List<BroadcastMessage> findMessagesToRetry();

    /**
     * 查找超时的群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.status = 'SENDING' AND bm.isActive = true AND " +
           "bm.startedAt < :timeoutTime")
    List<BroadcastMessage> findTimeoutMessages(@Param("timeoutTime") Instant timeoutTime);

    /**
     * 统计用户的群发消息数量
     */
    long countBySenderIdAndIsActiveTrue(Long senderId);

    /**
     * 统计用户指定状态的群发消息数量
     */
    long countBySenderIdAndStatusAndIsActiveTrue(Long senderId, BroadcastMessage.BroadcastStatus status);

    /**
     * 统计用户指定类型的群发消息数量
     */
    long countBySenderIdAndBroadcastTypeAndIsActiveTrue(Long senderId, BroadcastMessage.BroadcastType broadcastType);

    /**
     * 搜索群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "(bm.title LIKE %:keyword% OR bm.content LIKE %:keyword%) " +
           "ORDER BY bm.createdAt DESC")
    Page<BroadcastMessage> searchBroadcastMessages(@Param("senderId") Long senderId, 
                                                   @Param("keyword") String keyword, 
                                                   Pageable pageable);

    /**
     * 根据时间范围查找群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "bm.createdAt BETWEEN :startTime AND :endTime ORDER BY bm.createdAt DESC")
    List<BroadcastMessage> findByTimeRange(@Param("senderId") Long senderId,
                                          @Param("startTime") Instant startTime,
                                          @Param("endTime") Instant endTime);

    /**
     * 查找最近的群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "bm.createdAt >= :since ORDER BY bm.createdAt DESC")
    List<BroadcastMessage> findRecentBroadcasts(@Param("senderId") Long senderId,
                                               @Param("since") Instant since);

    /**
     * 查找定时群发消息
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "bm.isScheduled = true ORDER BY bm.scheduledTime ASC")
    List<BroadcastMessage> findScheduledBroadcasts(@Param("senderId") Long senderId);

    /**
     * 查找即将发送的定时群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.status = 'PENDING' AND bm.isActive = true AND " +
           "bm.isScheduled = true AND bm.scheduledTime BETWEEN :currentTime AND :futureTime " +
           "ORDER BY bm.scheduledTime ASC")
    List<BroadcastMessage> findUpcomingScheduledBroadcasts(@Param("currentTime") Instant currentTime,
                                                           @Param("futureTime") Instant futureTime);

    /**
     * 更新群发状态
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.status = :status WHERE bm.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") BroadcastMessage.BroadcastStatus status);

    /**
     * 更新发送统计
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.actualRecipients = :actualRecipients, " +
           "bm.successCount = :successCount, bm.failureCount = :failureCount WHERE bm.id = :id")
    void updateSendingStats(@Param("id") Long id, 
                           @Param("actualRecipients") Integer actualRecipients,
                           @Param("successCount") Integer successCount, 
                           @Param("failureCount") Integer failureCount);

    /**
     * 增加成功计数
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.successCount = bm.successCount + 1 WHERE bm.id = :id")
    void incrementSuccessCount(@Param("id") Long id);

    /**
     * 增加失败计数
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.failureCount = bm.failureCount + 1 WHERE bm.id = :id")
    void incrementFailureCount(@Param("id") Long id);

    /**
     * 批量取消群发消息
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.status = 'CANCELLED' WHERE bm.senderId = :senderId AND bm.id IN :ids")
    void batchCancelBroadcasts(@Param("senderId") Long senderId, @Param("ids") List<Long> ids);

    /**
     * 软删除用户的群发消息
     */
    @Modifying
    @Query("UPDATE BroadcastMessage bm SET bm.isActive = false WHERE bm.senderId = :senderId")
    void softDeleteUserBroadcasts(@Param("senderId") Long senderId);

    /**
     * 获取用户群发统计
     */
    @Query("SELECT bm.status, COUNT(bm), SUM(bm.actualRecipients), SUM(bm.successCount), SUM(bm.failureCount) " +
           "FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true GROUP BY bm.status")
    List<Object[]> getUserBroadcastStatistics(@Param("senderId") Long senderId);

    /**
     * 获取每日群发统计
     */
    @Query("SELECT DATE(bm.createdAt), COUNT(bm), SUM(bm.actualRecipients) " +
           "FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.createdAt >= :startDate " +
           "GROUP BY DATE(bm.createdAt) ORDER BY DATE(bm.createdAt)")
    List<Object[]> getDailyBroadcastStatistics(@Param("senderId") Long senderId, @Param("startDate") Instant startDate);

    /**
     * 获取群发类型统计
     */
    @Query("SELECT bm.broadcastType, COUNT(bm), SUM(bm.actualRecipients) " +
           "FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true " +
           "GROUP BY bm.broadcastType ORDER BY COUNT(bm) DESC")
    List<Object[]> getBroadcastTypeStatistics(@Param("senderId") Long senderId);

    /**
     * 获取目标类型统计
     */
    @Query("SELECT bm.targetType, COUNT(bm), SUM(bm.actualRecipients) " +
           "FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true " +
           "GROUP BY bm.targetType ORDER BY COUNT(bm) DESC")
    List<Object[]> getTargetTypeStatistics(@Param("senderId") Long senderId);

    /**
     * 获取发送成功率统计
     */
    @Query("SELECT AVG(CASE WHEN bm.actualRecipients > 0 THEN bm.successCount * 100.0 / bm.actualRecipients ELSE 0 END) " +
           "FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.status IN ('COMPLETED', 'FAILED')")
    Double getAverageSuccessRate(@Param("senderId") Long senderId);

    /**
     * 获取平均发送时长（分钟）
     */
    @Query(value = "SELECT AVG((EXTRACT(EPOCH FROM completed_at) - EXTRACT(EPOCH FROM started_at)) / 60) " +
           "FROM broadcast_messages WHERE sender_id = :senderId AND started_at IS NOT NULL AND completed_at IS NOT NULL", nativeQuery = true)
    Double getAverageSendingDurationMinutes(@Param("senderId") Long senderId);

    /**
     * 查找发送效率最高的群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.status = 'COMPLETED' AND " +
           "bm.actualRecipients > 0 ORDER BY (bm.successCount * 100.0 / bm.actualRecipients) DESC")
    List<BroadcastMessage> findMostSuccessfulBroadcasts(@Param("senderId") Long senderId, Pageable pageable);

    /**
     * 查找发送量最大的群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true " +
           "ORDER BY bm.actualRecipients DESC")
    List<BroadcastMessage> findLargestBroadcasts(@Param("senderId") Long senderId, Pageable pageable);

    /**
     * 查找长时间运行的群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.status = 'SENDING' AND bm.isActive = true AND " +
           "bm.startedAt < :threshold ORDER BY bm.startedAt ASC")
    List<BroadcastMessage> findLongRunningBroadcasts(@Param("threshold") Instant threshold);

    /**
     * 获取系统群发概览
     */
    @Query("SELECT bm.status, COUNT(bm), SUM(bm.actualRecipients) FROM BroadcastMessage bm " +
           "WHERE bm.isActive = true GROUP BY bm.status")
    List<Object[]> getSystemBroadcastOverview();

    /**
     * 查找需要清理的旧群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.status IN ('COMPLETED', 'CANCELLED', 'FAILED') AND " +
           "bm.updatedAt < :cutoffTime")
    List<BroadcastMessage> findOldBroadcastsForCleanup(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 检查用户是否达到群发限制
     */
    @Query("SELECT COUNT(bm) >= :maxBroadcasts FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND " +
           "bm.status IN ('DRAFT', 'PENDING', 'SENDING') AND bm.isActive = true")
    boolean hasReachedBroadcastLimit(@Param("senderId") Long senderId, @Param("maxBroadcasts") Integer maxBroadcasts);

    /**
     * 获取用户今日群发数量
     */
    @Query("SELECT COUNT(bm) FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "DATE(bm.createdAt) = DATE(:today)")
    long getTodayBroadcastCount(@Param("senderId") Long senderId, @Param("today") Instant today);

    /**
     * 查找相似的群发内容
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.isActive = true AND " +
           "bm.id != :excludeId AND (bm.title = :title OR bm.content = :content) " +
           "ORDER BY bm.createdAt DESC")
    List<BroadcastMessage> findSimilarBroadcasts(@Param("senderId") Long senderId, 
                                                 @Param("excludeId") Long excludeId,
                                                 @Param("title") String title, 
                                                 @Param("content") String content);

    /**
     * 获取群发发送趋势
     */
    @Query("SELECT HOUR(bm.startedAt), COUNT(bm) FROM BroadcastMessage bm " +
           "WHERE bm.senderId = :senderId AND bm.startedAt >= :startDate " +
           "GROUP BY HOUR(bm.startedAt) ORDER BY HOUR(bm.startedAt)")
    List<Object[]> getBroadcastSendingTrend(@Param("senderId") Long senderId, @Param("startDate") Instant startDate);

    /**
     * 查找草稿状态的群发
     */
    @Query("SELECT bm FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND bm.status = 'DRAFT' AND " +
           "bm.isActive = true ORDER BY bm.updatedAt DESC")
    List<BroadcastMessage> findDraftBroadcasts(@Param("senderId") Long senderId);

    /**
     * 清理旧的草稿
     */
    @Modifying
    @Query("DELETE FROM BroadcastMessage bm WHERE bm.status = 'DRAFT' AND bm.updatedAt < :cutoffTime")
    int cleanupOldDrafts(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 获取群发消息的平均接收人数
     */
    @Query("SELECT AVG(bm.actualRecipients) FROM BroadcastMessage bm WHERE bm.senderId = :senderId AND " +
           "bm.status != 'DRAFT' AND bm.isActive = true")
    Double getAverageRecipientCount(@Param("senderId") Long senderId);
}
