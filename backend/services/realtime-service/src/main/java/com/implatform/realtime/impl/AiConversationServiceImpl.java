package com.implatform.realtime.impl;

import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.core.enums.SocialErrorCode;
import com.implatform.proto.social.v1.*;
import com.implatform.proto.common.v1.*;
import com.implatform.realtime.entity.AiConversation;
import com.implatform.realtime.entity.AiAssistant;
import com.implatform.realtime.repository.AiConversationRepository;
import com.implatform.realtime.repository.AiAssistantRepository;
import com.implatform.realtime.service.AiConversationService;
import com.google.protobuf.Timestamp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI对话服务实现类
 *
 * <p><strong>功能概述</strong>：
 * 实现AI对话的完整业务逻辑，提供对话管理、统计分析、数据导出等核心功能。
 * 专门针对AI对话的特殊需求进行优化，支持高并发、大数据量的场景。
 *
 * <p><strong>技术特性</strong>：
 * <ul>
 *   <li><strong>缓存优化</strong>：关键数据使用Redis缓存，提升查询性能</li>
 *   <li><strong>事务管理</strong>：确保数据一致性和完整性</li>
 *   <li><strong>异常处理</strong>：完善的错误处理和用户友好的错误信息</li>
 *   <li><strong>性能监控</strong>：详细的日志记录和性能指标</li>
 *   <li><strong>数据验证</strong>：严格的输入验证和业务规则检查</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AiConversationServiceImpl implements AiConversationService {

    private final AiConversationRepository aiConversationRepository;
    private final AiAssistantRepository aiAssistantRepository;

    // ==================== 对话管理 (Protobuf版本) ====================

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.userId")
    public CreateAiConversationResponse createConversation(CreateAiConversationRequest request) {
        try {
            log.info("创建AI对话 - 用户ID: {}, 助手ID: {}", request.getUserId(), request.getAssistantId());

            // 验证助手是否存在且可用
            AiAssistant assistant = aiAssistantRepository.findById(request.getAssistantId())
                .orElseThrow(() -> new BusinessException(SocialErrorCode.AI_ASSISTANT_NOT_FOUND));

            if (!assistant.getIsEnabled()) {
                throw new BusinessException(SocialErrorCode.AI_ASSISTANT_DISABLED);
            }

            // 生成对话标题（如果未提供）
            String title = StringUtils.hasText(request.getTitle()) ?
                request.getTitle() : generateConversationTitle(assistant.getName());

            // 创建对话实体
            AiConversation conversation = AiConversation.builder()
                .userId(request.getUserId())
                .assistantId(request.getAssistantId())
                .title(title)
                .description(request.getDescription())
                .status(AiConversation.ConversationStatus.ACTIVE)
                .priority(AiConversation.ConversationPriority.NORMAL)
                .enableStreaming(true)
                .enableMemory(true)
                .contextWindowSize(10)
                .build();

            // 设置AI参数（如果提供）
            if (request.hasAiParameters()) {
                setAiParametersFromProto(conversation, request.getAiParameters());
            }

            // 设置标签
            if (request.getTagsCount() > 0) {
                conversation.setTags(String.join(",", request.getTagsList()));
            }

            // 保存对话
            conversation = aiConversationRepository.save(conversation);

            log.info("AI对话创建成功 - 对话ID: {}, 用户ID: {}, 助手ID: {}",
                conversation.getId(), request.getUserId(), request.getAssistantId());

            // 构建响应
            AiConversationInfo conversationInfo = convertToProtoConversationInfo(conversation, assistant);

            return CreateAiConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话创建成功")
                .setConversationInfo(conversationInfo)
                .build();

        } catch (BusinessException e) {
            log.error("创建AI对话失败 - 业务异常: {}", e.getMessage());
            return CreateAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("创建AI对话失败 - 系统异常", e);
            return CreateAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Cacheable(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public GetAiConversationResponse getConversationDetail(GetAiConversationRequest request) {
        try {
            log.debug("获取AI对话详情 - 对话ID: {}, 用户ID: {}", request.getConversationId(), request.getUserId());

            Optional<AiConversation> conversationOpt = aiConversationRepository.findById(request.getConversationId())
                .filter(conversation -> conversation.getUserId().equals(request.getUserId()) &&
                                      conversation.getStatus() != AiConversation.ConversationStatus.DELETED);

            if (conversationOpt.isEmpty()) {
                return GetAiConversationResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("对话不存在或已被删除")
                    .build();
            }

            AiConversation conversation = conversationOpt.get();
            AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                .orElse(null);

            AiConversationInfo conversationInfo = convertToProtoConversationInfo(conversation, assistant);

            return GetAiConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取对话详情成功")
                .setConversationInfo(conversationInfo)
                .build();

        } catch (Exception e) {
            log.error("获取AI对话详情失败", e);
            return GetAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取对话详情失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public UpdateAiConversationResponse updateConversation(UpdateAiConversationRequest request) {
        try {
            log.info("更新AI对话 - 对话ID: {}, 用户ID: {}", request.getConversationId(), request.getUserId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            // 更新基本信息
            if (StringUtils.hasText(request.getTitle())) {
                conversation.setTitle(request.getTitle());
            }
            if (StringUtils.hasText(request.getDescription())) {
                conversation.setDescription(request.getDescription());
            }
            if (request.getPriority() != ConversationPriority.CONVERSATION_PRIORITY_UNSPECIFIED) {
                conversation.setPriority(convertFromProtoPriority(request.getPriority()));
            }

            // 更新AI参数
            if (request.hasAiParameters()) {
                setAiParametersFromProto(conversation, request.getAiParameters());
            }

            // 更新标签
            if (request.getTagsCount() > 0) {
                conversation.setTags(String.join(",", request.getTagsList()));
            }

            // 保存更新
            conversation = aiConversationRepository.save(conversation);

            // 获取助手信息
            AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                .orElse(null);

            AiConversationInfo conversationInfo = convertToProtoConversationInfo(conversation, assistant);

            log.info("AI对话更新成功 - 对话ID: {}", conversation.getId());

            return UpdateAiConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话更新成功")
                .setConversationInfo(conversationInfo)
                .build();

        } catch (BusinessException e) {
            log.error("更新AI对话失败 - 业务异常: {}", e.getMessage());
            return UpdateAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("更新AI对话失败 - 系统异常", e);
            return UpdateAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public DeleteAiConversationResponse deleteConversation(DeleteAiConversationRequest request) {
        try {
            log.info("删除AI对话 - 对话ID: {}, 用户ID: {}", request.getConversationId(), request.getUserId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            conversation.setStatus(AiConversation.ConversationStatus.DELETED);
            conversation.setDeletedAt(Instant.now());
            aiConversationRepository.save(conversation);

            log.info("AI对话删除成功 - 对话ID: {}", conversation.getId());

            return DeleteAiConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话删除成功")
                .build();

        } catch (BusinessException e) {
            log.error("删除AI对话失败 - 业务异常: {}", e.getMessage());
            return DeleteAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("删除AI对话失败 - 系统异常", e);
            return DeleteAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public RestoreAiConversationResponse restoreConversation(RestoreAiConversationRequest request) {
        try {
            log.info("恢复AI对话 - 对话ID: {}, 用户ID: {}", request.getConversationId(), request.getUserId());

            Optional<AiConversation> conversationOpt = aiConversationRepository.findById(request.getConversationId())
                .filter(conversation -> conversation.getUserId().equals(request.getUserId()) &&
                                      conversation.getStatus() == AiConversation.ConversationStatus.DELETED);

            if (conversationOpt.isEmpty()) {
                return RestoreAiConversationResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("对话不存在或未被删除")
                    .build();
            }

            AiConversation conversation = conversationOpt.get();
            conversation.setStatus(AiConversation.ConversationStatus.ACTIVE);
            conversation.setDeletedAt(null);
            aiConversationRepository.save(conversation);

            log.info("AI对话恢复成功 - 对话ID: {}", conversation.getId());

            return RestoreAiConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话恢复成功")
                .build();

        } catch (Exception e) {
            log.error("恢复AI对话失败", e);
            return RestoreAiConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("恢复对话失败: " + e.getMessage())
                .build();
        }
    }

    // ==================== 对话查询 (Protobuf版本) ====================

    @Override
    @Cacheable(value = "ai_conversations", key = "#request.userId + '_list_' + #request.pagination.page + '_' + #request.pagination.size")
    public GetUserAiConversationsResponse getUserConversations(GetUserAiConversationsRequest request) {
        try {
            log.debug("获取用户AI对话列表 - 用户ID: {}", request.getUserId());

            // 转换分页参数
            Sort.Direction direction = "asc".equalsIgnoreCase(request.getSortDirection()) ?
                Sort.Direction.ASC : Sort.Direction.DESC;
            String sortBy = StringUtils.hasText(request.getSortBy()) ? request.getSortBy() : "lastMessageAt";

            Pageable pageable = PageRequest.of(
                request.getPagination().getPage(),
                request.getPagination().getSize(),
                Sort.by(direction, sortBy)
            );

            // 查询对话
            Page<AiConversation> conversationPage = aiConversationRepository
                .findByUserIdAndStatusNot(request.getUserId(), AiConversation.ConversationStatus.DELETED, pageable);

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = new ArrayList<>();
            for (AiConversation conversation : conversationPage.getContent()) {
                AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                    .orElse(null);
                conversationInfos.add(convertToProtoConversationInfo(conversation, assistant));
            }

            // 构建分页信息
            return GetUserAiConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取对话列表成功")
                .addAllConversations(conversationInfos)
                .setPagination(OffsetPaginationInfo.newBuilder()
                    .setCurrentPage(conversationPage.getNumber())
                    .setPageSize(conversationPage.getSize())
                    .setTotalPages(conversationPage.getTotalPages())
                    .setCurrentPageSize(conversationPage.getNumberOfElements())
                    .setHasNextPage(conversationPage.hasNext())
                    .setHasPreviousPage(conversationPage.hasPrevious())
                    .setIsFirstPage(conversationPage.isFirst())
                    .setIsLastPage(conversationPage.isLast())
                    .build())
                .build();

        } catch (Exception e) {
            log.error("获取用户AI对话列表失败", e);
            return GetUserAiConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取对话列表失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public QueryAiConversationsResponse queryConversations(QueryAiConversationsRequest request) {
        try {
            log.debug("条件查询AI对话 - 用户ID: {}", request.getUserId());

            // 这里需要根据查询条件构建复杂的查询
            // 由于涉及到复杂的条件查询，我们使用Specification或自定义查询

            // 构建基本查询条件
            List<AiConversation> conversations = aiConversationRepository
                .findByUserIdAndStatusNot(request.getUserId(), AiConversation.ConversationStatus.DELETED);

            // 应用过滤条件
            conversations = conversations.stream()
                .filter(conversation -> {
                    // 助手ID过滤
                    if (request.getAssistantId() > 0 && !conversation.getAssistantId().equals(request.getAssistantId())) {
                        return false;
                    }

                    // 状态过滤
                    if (request.getStatus() != ConversationStatus.CONVERSATION_STATUS_UNSPECIFIED) {
                        AiConversation.ConversationStatus targetStatus = convertFromProtoStatus(request.getStatus());
                        if (!conversation.getStatus().equals(targetStatus)) {
                            return false;
                        }
                    }

                    // 置顶过滤
                    if (request.getIsPinned() && !conversation.getIsPinned()) {
                        return false;
                    }

                    // 收藏过滤
                    if (request.getIsFavorite() && !conversation.getIsFavorite()) {
                        return false;
                    }

                    // 关键词搜索
                    if (StringUtils.hasText(request.getKeyword())) {
                        String keyword = request.getKeyword().toLowerCase();
                        boolean matches = (conversation.getTitle() != null && conversation.getTitle().toLowerCase().contains(keyword)) ||
                                        (conversation.getDescription() != null && conversation.getDescription().toLowerCase().contains(keyword)) ||
                                        (conversation.getLastMessageContent() != null && conversation.getLastMessageContent().toLowerCase().contains(keyword));
                        if (!matches) {
                            return false;
                        }
                    }

                    // 时间范围过滤
                    if (request.hasStartTime()) {
                        Instant startTime = Instant.ofEpochSecond(request.getStartTime().getSeconds(), request.getStartTime().getNanos());
                        if (conversation.getCreatedAt().isBefore(startTime)) {
                            return false;
                        }
                    }

                    if (request.hasEndTime()) {
                        Instant endTime = Instant.ofEpochSecond(request.getEndTime().getSeconds(), request.getEndTime().getNanos());
                        if (conversation.getCreatedAt().isAfter(endTime)) {
                            return false;
                        }
                    }

                    // 最小消息数过滤
                    if (request.getMinMessageCount() > 0 && conversation.getMessageCount() < request.getMinMessageCount()) {
                        return false;
                    }

                    // 最小费用过滤
                    if (request.getMinCost() > 0 && conversation.getTotalCost() < request.getMinCost()) {
                        return false;
                    }

                    return true;
                })
                .collect(Collectors.toList());

            // 应用排序和分页
            // 这里简化处理，实际应该使用数据库级别的排序和分页
            List<AiConversationInfo> conversationInfos = conversations.stream()
                .map(conversation -> {
                    AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                        .orElse(null);
                    return convertToProtoConversationInfo(conversation, assistant);
                })
                .collect(Collectors.toList());

            // 构建分页信息（简化版本）
            PaginationResponse paginationResponse = PaginationResponse.newBuilder()
                .setOffsetInfo(OffsetPaginationInfo.newBuilder()
                    .setCurrentPage(0)
                    .setPageSize(conversationInfos.size())
                    .setTotalPages(1)
                    .setCurrentPageSize(conversationInfos.size())
                    .setHasNextPage(false)
                    .setHasPreviousPage(false)
                    .setIsFirstPage(true)
                    .setIsLastPage(true)
                    .build())
                .build();

            return QueryAiConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("查询对话成功")
                .addAllConversations(conversationInfos)
                .setPagination(paginationResponse)
                .build();

        } catch (Exception e) {
            log.error("条件查询AI对话失败", e);
            return QueryAiConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("查询对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public GetConversationsByAssistantResponse getConversationsByAssistant(GetConversationsByAssistantRequest request) {
        try {
            log.debug("获取用户与助手的对话 - 用户ID: {}, 助手ID: {}", request.getUserId(), request.getAssistantId());

            Pageable pageable = PageRequest.of(
                request.getPagination().getPage(),
                request.getPagination().getSize(),
                Sort.by(Sort.Direction.DESC, "lastMessageAt")
            );

            Page<AiConversation> conversationPage = aiConversationRepository
                .findByUserIdAndAssistantIdAndStatusNot(
                    request.getUserId(),
                    request.getAssistantId(),
                    AiConversation.ConversationStatus.DELETED,
                    pageable
                );

            // 获取助手信息
            AiAssistant assistant = aiAssistantRepository.findById(request.getAssistantId())
                .orElse(null);

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = conversationPage.getContent().stream()
                .map(conversation -> convertToProtoConversationInfo(conversation, assistant))
                .collect(Collectors.toList());

            // 构建分页信息
            OffsetPaginationInfo paginationInfo = OffsetPaginationInfo.newBuilder()
                .setCurrentPage(conversationPage.getNumber())
                .setPageSize(conversationPage.getSize())
                .setTotalPages(conversationPage.getTotalPages())
                .setCurrentPageSize(conversationPage.getNumberOfElements())
                .setHasNextPage(conversationPage.hasNext())
                .setHasPreviousPage(conversationPage.hasPrevious())
                .setIsFirstPage(conversationPage.isFirst())
                .setIsLastPage(conversationPage.isLast())
                .build();

            return GetConversationsByAssistantResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取与助手的对话成功")
                .addAllConversations(conversationInfos)
                .setPagination(paginationInfo)
                .build();

        } catch (Exception e) {
            log.error("获取用户与助手的对话失败", e);
            return GetConversationsByAssistantResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取与助手的对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Cacheable(value = "ai_conversations", key = "#request.userId + '_pinned'")
    public GetPinnedConversationsResponse getPinnedConversations(GetPinnedConversationsRequest request) {
        try {
            log.debug("获取用户置顶对话 - 用户ID: {}", request.getUserId());

            List<AiConversation> conversations = aiConversationRepository
                .findByUserIdAndIsPinnedTrueAndStatusNotOrderByPinnedAtDesc(
                    request.getUserId(),
                    AiConversation.ConversationStatus.DELETED
                );

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = conversations.stream()
                .map(conversation -> {
                    AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                        .orElse(null);
                    return convertToProtoConversationInfo(conversation, assistant);
                })
                .collect(Collectors.toList());

            return GetPinnedConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取置顶对话成功")
                .addAllConversations(conversationInfos)
                .build();

        } catch (Exception e) {
            log.error("获取用户置顶对话失败", e);
            return GetPinnedConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取置顶对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public GetFavoriteConversationsResponse getFavoriteConversations(GetFavoriteConversationsRequest request) {
        try {
            log.debug("获取用户收藏对话 - 用户ID: {}", request.getUserId());

            Pageable pageable = PageRequest.of(
                request.getPagination().getPage(),
                request.getPagination().getSize(),
                Sort.by(Sort.Direction.DESC, "favoritedAt")
            );

            Page<AiConversation> conversationPage = aiConversationRepository
                .findByUserIdAndIsFavoriteTrueAndStatusNot(
                    request.getUserId(),
                    AiConversation.ConversationStatus.DELETED,
                    pageable
                );

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = conversationPage.getContent().stream()
                .map(conversation -> {
                    AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                        .orElse(null);
                    return convertToProtoConversationInfo(conversation, assistant);
                })
                .collect(Collectors.toList());

            // 构建分页信息
            OffsetPaginationInfo paginationInfo = OffsetPaginationInfo.newBuilder()
                .setCurrentPage(conversationPage.getNumber())
                .setPageSize(conversationPage.getSize())
                .setTotalPages(conversationPage.getTotalPages())
                .setCurrentPageSize(conversationPage.getNumberOfElements())
                .setHasNextPage(conversationPage.hasNext())
                .setHasPreviousPage(conversationPage.hasPrevious())
                .setIsFirstPage(conversationPage.isFirst())
                .setIsLastPage(conversationPage.isLast())
                .build();

            return GetFavoriteConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取收藏对话成功")
                .addAllConversations(conversationInfos)
                .setPagination(paginationInfo)
                .build();

        } catch (Exception e) {
            log.error("获取用户收藏对话失败", e);
            return GetFavoriteConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取收藏对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public SearchConversationsResponse searchConversations(SearchConversationsRequest request) {
        try {
            log.debug("搜索AI对话 - 用户ID: {}, 关键词: {}", request.getUserId(), request.getKeyword());

            Pageable pageable = PageRequest.of(
                request.getPagination().getPage(),
                request.getPagination().getSize(),
                Sort.by(Sort.Direction.DESC, "lastMessageAt")
            );

            // 使用关键词搜索
            Page<AiConversation> conversationPage = aiConversationRepository
                .searchConversations(request.getUserId(), request.getKeyword(), pageable);

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = conversationPage.getContent().stream()
                .map(conversation -> {
                    AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                        .orElse(null);
                    return convertToProtoConversationInfo(conversation, assistant);
                })
                .collect(Collectors.toList());

            // 构建分页信息
            OffsetPaginationInfo paginationInfo = OffsetPaginationInfo.newBuilder()
                .setCurrentPage(conversationPage.getNumber())
                .setPageSize(conversationPage.getSize())
                .setTotalPages(conversationPage.getTotalPages())
                .setCurrentPageSize(conversationPage.getNumberOfElements())
                .setHasNextPage(conversationPage.hasNext())
                .setHasPreviousPage(conversationPage.hasPrevious())
                .setIsFirstPage(conversationPage.isFirst())
                .setIsLastPage(conversationPage.isLast())
                .build();

            return SearchConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("搜索对话成功")
                .addAllConversations(conversationInfos)
                .setPagination(paginationInfo)
                .build();

        } catch (Exception e) {
            log.error("搜索AI对话失败", e);
            return SearchConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("搜索对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public GetRecentActiveConversationsResponse getRecentActiveConversations(GetRecentActiveConversationsRequest request) {
        try {
            log.debug("获取最近活跃对话 - 用户ID: {}, 限制: {}", request.getUserId(), request.getLimit());

            Pageable pageable = PageRequest.of(0, request.getLimit(), Sort.by(Sort.Direction.DESC, "lastMessageAt"));

            Page<AiConversation> conversationPage = aiConversationRepository
                .findByUserIdAndStatusAndLastMessageAtIsNotNull(
                    request.getUserId(),
                    AiConversation.ConversationStatus.ACTIVE,
                    pageable
                );

            // 转换为protobuf格式
            List<AiConversationInfo> conversationInfos = conversationPage.getContent().stream()
                .map(conversation -> {
                    AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                        .orElse(null);
                    return convertToProtoConversationInfo(conversation, assistant);
                })
                .collect(Collectors.toList());

            return GetRecentActiveConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取最近活跃对话成功")
                .addAllConversations(conversationInfos)
                .build();

        } catch (Exception e) {
            log.error("获取最近活跃对话失败", e);
            return GetRecentActiveConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取最近活跃对话失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public GetLatestConversationWithAssistantResponse getLatestConversationWithAssistant(GetLatestConversationWithAssistantRequest request) {
        try {
            log.debug("获取与助手的最新对话 - 用户ID: {}, 助手ID: {}", request.getUserId(), request.getAssistantId());

            Optional<AiConversation> conversationOpt = aiConversationRepository
                .findFirstByUserIdAndAssistantIdAndStatusNotOrderByLastMessageAtDesc(
                    request.getUserId(),
                    request.getAssistantId(),
                    AiConversation.ConversationStatus.DELETED
                );

            if (conversationOpt.isEmpty()) {
                return GetLatestConversationWithAssistantResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("未找到与该助手的对话")
                    .build();
            }

            AiConversation conversation = conversationOpt.get();
            AiAssistant assistant = aiAssistantRepository.findById(conversation.getAssistantId())
                .orElse(null);

            AiConversationInfo conversationInfo = convertToProtoConversationInfo(conversation, assistant);

            return GetLatestConversationWithAssistantResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取最新对话成功")
                .setConversationInfo(conversationInfo)
                .build();

        } catch (Exception e) {
            log.error("获取与助手的最新对话失败", e);
            return GetLatestConversationWithAssistantResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取最新对话失败: " + e.getMessage())
                .build();
        }
    }

    // ==================== 对话状态管理 (Protobuf版本) ====================

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public PinConversationResponse pinConversation(PinConversationRequest request) {
        try {
            log.info("置顶AI对话 - 用户ID: {}, 对话ID: {}", request.getUserId(), request.getConversationId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            conversation.setIsPinned(true);
            conversation.setPinnedAt(Instant.now());
            aiConversationRepository.save(conversation);

            log.info("AI对话置顶成功 - 对话ID: {}", conversation.getId());

            return PinConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话置顶成功")
                .build();

        } catch (BusinessException e) {
            log.error("置顶AI对话失败 - 业务异常: {}", e.getMessage());
            return PinConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("置顶AI对话失败 - 系统异常", e);
            return PinConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public UnpinConversationResponse unpinConversation(UnpinConversationRequest request) {
        try {
            log.info("取消置顶AI对话 - 用户ID: {}, 对话ID: {}", request.getUserId(), request.getConversationId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            conversation.setIsPinned(false);
            conversation.setPinnedAt(null);
            aiConversationRepository.save(conversation);

            log.info("AI对话取消置顶成功 - 对话ID: {}", conversation.getId());

            return UnpinConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("取消置顶成功")
                .build();

        } catch (BusinessException e) {
            log.error("取消置顶AI对话失败 - 业务异常: {}", e.getMessage());
            return UnpinConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("取消置顶AI对话失败 - 系统异常", e);
            return UnpinConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public FavoriteConversationResponse favoriteConversation(FavoriteConversationRequest request) {
        try {
            log.info("收藏AI对话 - 用户ID: {}, 对话ID: {}", request.getUserId(), request.getConversationId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            conversation.setIsFavorite(true);
            conversation.setFavoritedAt(Instant.now());
            aiConversationRepository.save(conversation);

            log.info("AI对话收藏成功 - 对话ID: {}", conversation.getId());

            return FavoriteConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话收藏成功")
                .build();

        } catch (BusinessException e) {
            log.error("收藏AI对话失败 - 业务异常: {}", e.getMessage());
            return FavoriteConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("收藏AI对话失败 - 系统异常", e);
            return FavoriteConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public UnfavoriteConversationResponse unfavoriteConversation(UnfavoriteConversationRequest request) {
        try {
            log.info("取消收藏AI对话 - 用户ID: {}, 对话ID: {}", request.getUserId(), request.getConversationId());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            conversation.setIsFavorite(false);
            conversation.setFavoritedAt(null);
            aiConversationRepository.save(conversation);

            log.info("AI对话取消收藏成功 - 对话ID: {}", conversation.getId());

            return UnfavoriteConversationResponse.newBuilder()
                .setSuccess(true)
                .setMessage("取消收藏成功")
                .build();

        } catch (BusinessException e) {
            log.error("取消收藏AI对话失败 - 业务异常: {}", e.getMessage());
            return UnfavoriteConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("取消收藏AI对话失败 - 系统异常", e);
            return UnfavoriteConversationResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#request.conversationId + '_' + #request.userId")
    public UpdateConversationStatusResponse updateConversationStatus(UpdateConversationStatusRequest request) {
        try {
            log.info("更新AI对话状态 - 用户ID: {}, 对话ID: {}, 状态: {}",
                request.getUserId(), request.getConversationId(), request.getStatus());

            AiConversation conversation = getConversationByIdAndUserId(request.getConversationId(), request.getUserId());

            AiConversation.ConversationStatus newStatus = convertFromProtoStatus(request.getStatus());
            conversation.setStatus(newStatus);

            // 如果是删除状态，设置删除时间
            if (newStatus == AiConversation.ConversationStatus.DELETED) {
                conversation.setDeletedAt(Instant.now());
            } else if (conversation.getDeletedAt() != null) {
                // 如果从删除状态恢复，清除删除时间
                conversation.setDeletedAt(null);
            }

            aiConversationRepository.save(conversation);

            log.info("AI对话状态更新成功 - 对话ID: {}, 新状态: {}", conversation.getId(), newStatus);

            return UpdateConversationStatusResponse.newBuilder()
                .setSuccess(true)
                .setMessage("对话状态更新成功")
                .build();

        } catch (BusinessException e) {
            log.error("更新AI对话状态失败 - 业务异常: {}", e.getMessage());
            return UpdateConversationStatusResponse.newBuilder()
                .setSuccess(false)
                .setMessage(e.getMessage())
                .build();
        } catch (Exception e) {
            log.error("更新AI对话状态失败 - 系统异常", e);
            return UpdateConversationStatusResponse.newBuilder()
                .setSuccess(false)
                .setMessage("系统异常，请稍后重试")
                .build();
        }
    }

    // ==================== 批量操作 (Protobuf版本) ====================

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchOperateConversationsResponse batchOperateConversations(BatchOperateConversationsRequest request) {
        try {
            log.debug("批量操作对话 - 用户ID: {}, 操作类型: {}, 对话ID数量: {}", 
                request.getUserId(), request.getOperationType(), request.getConversationIdsCount());

            // 验证请求
            if (request.getUserId() <= 0) {
                log.warn("无效的用户ID: {}", request.getUserId());
                return BatchOperateConversationsResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("无效的用户ID")
                    .build();
            }

            if (request.getConversationIdsCount() == 0) {
                log.warn("对话ID列表为空");
                return BatchOperateConversationsResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("对话ID列表不能为空")
                    .build();
            }

            // 根据操作类型执行相应的批量操作
            switch (request.getOperationType()) {
                case BATCH_OPERATION_TYPE_PIN:
                    return processBatchPin(request);
                case BATCH_OPERATION_TYPE_UNPIN:
                    return processBatchUnpin(request);
                case BATCH_OPERATION_TYPE_FAVORITE:
                    return processBatchFavorite(request);
                case BATCH_OPERATION_TYPE_UNFAVORITE:
                    return processBatchUnfavorite(request);
                case BATCH_OPERATION_TYPE_DELETE:
                    return processBatchDelete(request);
                case BATCH_OPERATION_TYPE_ARCHIVE:
                    return processBatchArchive(request);
                case BATCH_OPERATION_TYPE_RESTORE:
                    return processBatchRestore(request);
                default:
                    log.warn("不支持的操作类型: {}", request.getOperationType());
                    return BatchOperateConversationsResponse.newBuilder()
                        .setSuccess(false)
                        .setMessage("不支持的操作类型")
                        .build();
            }

        } catch (Exception e) {
            log.error("批量操作对话失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("操作失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchPinConversationsResponse batchPinConversations(BatchPinConversationsRequest request) {
        try {
            log.info("批量置顶AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            Instant now = Instant.now();
            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getStatus() != AiConversation.ConversationStatus.DELETED) {
                    conversation.setIsPinned(true);
                    conversation.setPinnedAt(now);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量置顶AI对话完成 - 成功: {}", processedCount);

            return BatchPinConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量置顶完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量置顶AI对话失败", e);
            return BatchPinConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量置顶失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchUnpinConversationsResponse batchUnpinConversations(BatchUnpinConversationsRequest request) {
        try {
            log.info("批量取消置顶AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getIsPinned()) {
                    conversation.setIsPinned(false);
                    conversation.setPinnedAt(null);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量取消置顶AI对话完成 - 成功: {}", processedCount);

            return BatchUnpinConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量取消置顶完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量取消置顶AI对话失败", e);
            return BatchUnpinConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量取消置顶失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchFavoriteConversationsResponse batchFavoriteConversations(BatchFavoriteConversationsRequest request) {
        try {
            log.info("批量收藏AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            Instant now = Instant.now();
            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getStatus() != AiConversation.ConversationStatus.DELETED) {
                    conversation.setIsFavorite(true);
                    conversation.setFavoritedAt(now);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量收藏AI对话完成 - 成功: {}", processedCount);

            return BatchFavoriteConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量收藏完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量收藏AI对话失败", e);
            return BatchFavoriteConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量收藏失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchUnfavoriteConversationsResponse batchUnfavoriteConversations(BatchUnfavoriteConversationsRequest request) {
        try {
            log.info("批量取消收藏AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getIsFavorite()) {
                    conversation.setIsFavorite(false);
                    conversation.setFavoritedAt(null);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量取消收藏AI对话完成 - 成功: {}", processedCount);

            return BatchUnfavoriteConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量取消收藏完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量取消收藏AI对话失败", e);
            return BatchUnfavoriteConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量取消收藏失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchOperateConversationsResponse batchArchiveConversations(BatchOperateConversationsRequest request) {
        try {
            log.info("批量归档AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getStatus() == AiConversation.ConversationStatus.ACTIVE) {
                    conversation.setStatus(AiConversation.ConversationStatus.ARCHIVED);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量归档AI对话完成 - 成功: {}", processedCount);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量归档完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量归档AI对话失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量归档失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "ai_conversations", allEntries = true)
    public BatchDeleteConversationsResponse batchDeleteConversations(BatchDeleteConversationsRequest request) {
        try {
            log.info("批量删除AI对话 - 用户ID: {}, 对话数量: {}", request.getUserId(), request.getConversationIdsCount());

            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            Instant now = Instant.now();
            int processedCount = 0;

            for (AiConversation conversation : conversations) {
                if (conversation.getStatus() != AiConversation.ConversationStatus.DELETED) {
                    conversation.setStatus(AiConversation.ConversationStatus.DELETED);
                    conversation.setDeletedAt(now);
                    processedCount++;
                }
            }

            aiConversationRepository.saveAll(conversations);

            log.info("批量删除AI对话完成 - 成功: {}", processedCount);

            return BatchDeleteConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage(String.format("批量删除完成，成功处理 %d 个对话", processedCount))
                .build();

        } catch (Exception e) {
            log.error("批量删除AI对话失败", e);
            return BatchDeleteConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量删除失败: " + e.getMessage())
                .build();
        }
    }

    // ==================== 统计分析 (Protobuf版本) ====================

    @Override
    @Cacheable(value = "ai_conversation_statistics", key = "#request.userId + '_' + #request.timeRangeType")
    public GetConversationStatisticsResponse getUserStatistics(GetConversationStatisticsRequest request) {
        try {
            log.debug("获取用户对话统计 - 用户ID: {}, 时间范围: {}", request.getUserId(), request.getTimeRangeType());

            // 验证请求
            Instant startTime = calculateStartTime(request.getTimeRangeType());
            Instant endTime = Instant.now();

            // 获取对话统计数据
            Long userId = request.getUserId();

            // 总对话数
            long totalConversations = aiConversationRepository.countByUserIdAndCreatedAtBetween(
                userId, startTime, endTime);

            // 活跃对话数
            long activeConversations = aiConversationRepository.countByUserIdAndStatus(
                userId, AiConversation.ConversationStatus.ACTIVE);

            // 收藏对话数
            long favoriteConversations = aiConversationRepository.countByUserIdAndIsFavorite(
                userId, true);

            // 已删除对话数
            long deletedConversations = aiConversationRepository.countByUserIdAndDeletedAtIsNotNull(userId);

            // 构建响应
            ConversationStatistics statistics = ConversationStatistics.newBuilder()
                .setTotalConversations(totalConversations)
                .setActiveConversations(activeConversations)
                .setArchivedConversations(deletedConversations)
                .build();

            return GetConversationStatisticsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取统计数据成功")
                .setStatistics(statistics)
                .build();

        } catch (Exception e) {
            log.error("获取用户对话统计失败", e);
            return GetConversationStatisticsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取统计数据失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public GetConversationTrendResponse getConversationTrend(GetConversationTrendRequest request) {
        try {
            log.debug("获取对话趋势 - 用户ID: {}, 时间范围: {}", request.getUserId(), request.getTimeRangeType());

            // 验证请求
            if (request.getUserId() <= 0) {
                return GetConversationTrendResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("无效的用户ID")
                    .build();
            }

            // 根据时间范围类型计算天数
            int days = calculateDaysFromTimeRangeType(request.getTimeRangeType());
            Instant startTime = Instant.now().minusSeconds(days * 24 * 60 * 60);
            Instant endTime = Instant.now();

            // 按天统计对话数量
            List<AiConversation> conversations = aiConversationRepository.findByUserIdAndCreatedAtBetween(
                request.getUserId(), startTime, endTime);

            Map<String, List<AiConversation>> dailyConversations = conversations.stream()
                .collect(Collectors.groupingBy(conv -> 
                    conv.getCreatedAt().atZone(ZoneOffset.UTC).toLocalDate().toString()));

            List<TrendData> trendDataList = new ArrayList<>();
            for (int i = days - 1; i >= 0; i--) {
                LocalDate date = LocalDate.now().minusDays(i);
                String dateStr = date.toString();
                List<AiConversation> dayConversations = dailyConversations.getOrDefault(dateStr, new ArrayList<>());
                
                TrendData trendData = TrendData.newBuilder()
                    .setDate(dateStr)
                    .setValue(dayConversations.size())
                    .setDescription(date.format(DateTimeFormatter.ofPattern("MM-dd")))
                    .build();
                
                trendDataList.add(trendData);
            }

            return GetConversationTrendResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取趋势数据成功")
                .addAllTrendData(trendDataList)
                .build();

        } catch (Exception e) {
            log.error("获取对话趋势失败", e);
            return GetConversationTrendResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取趋势数据失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    @Cacheable(value = "ai_conversation_top_assistants", key = "#request.userId + '_' + #request.limit")
    public GetTopUsedAssistantsResponse getTopUsedAssistants(GetTopUsedAssistantsRequest request) {
        try {
            log.debug("获取最常用助手 - 用户ID: {}, 限制: {}", request.getUserId(), request.getLimit());

            // 查询用户的所有对话，按助手分组统计
            List<AiConversation> conversations = aiConversationRepository
                .findByUserIdAndStatusNot(request.getUserId(), AiConversation.ConversationStatus.DELETED);

            Map<Long, List<AiConversation>> conversationsByAssistant = conversations.stream()
                .collect(Collectors.groupingBy(AiConversation::getAssistantId));

            List<AssistantUsageInfo> usageInfoList = new ArrayList<>();

            for (Map.Entry<Long, List<AiConversation>> entry : conversationsByAssistant.entrySet()) {
                Long assistantId = entry.getKey();
                List<AiConversation> assistantConversations = entry.getValue();

                // 获取助手信息
                AiAssistant assistant = aiAssistantRepository.findById(assistantId).orElse(null);
                if (assistant == null) continue;

                // 计算统计信息
                int conversationCount = assistantConversations.size();
                long totalMessages = assistantConversations.stream()
                    .mapToLong(AiConversation::getMessageCount)
                    .sum();
                long totalTokens = assistantConversations.stream()
                    .mapToLong(AiConversation::getTotalTokens)
                    .sum();
                long totalCost = assistantConversations.stream()
                    .mapToLong(AiConversation::getTotalCost)
                    .sum();

                // 最后使用时间
                Instant lastUsedAt = assistantConversations.stream()
                    .map(AiConversation::getLastMessageAt)
                    .filter(Objects::nonNull)
                    .max(Instant::compareTo)
                    .orElse(null);

                AssistantUsageInfo usageInfo = AssistantUsageInfo.newBuilder()
                    .setAssistantId(assistantId)
                    .setAssistantName(assistant.getName())
                    .setAssistantAvatar(assistant.getAvatar() != null ? assistant.getAvatar() : "")
                    .setConversationCount(conversationCount)
                    .setMessageCount(totalMessages)
                    .setTotalCost(totalCost)
                    .setTotalCostDescription(String.format("%.2f元", totalCost / 100.0))
                    .setLastUsedAt(lastUsedAt != null ? convertToProtoTimestamp(lastUsedAt) : Timestamp.getDefaultInstance())
                    .setLastUsedAtDescription(lastUsedAt != null ? formatTimestamp(lastUsedAt) : "从未使用")
                    .build();

                usageInfoList.add(usageInfo);
            }

            // 按对话数量排序，取前N个
            usageInfoList.sort((a, b) -> Long.compare(b.getConversationCount(), a.getConversationCount()));
            if (usageInfoList.size() > request.getLimit()) {
                usageInfoList = usageInfoList.subList(0, request.getLimit());
            }

            return GetTopUsedAssistantsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("获取最常用助手成功")
                .addAllAssistants(usageInfoList)
                .build();

        } catch (Exception e) {
            log.error("获取最常用助手失败", e);
            return GetTopUsedAssistantsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("获取最常用助手失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public HasActiveConversationWithAssistantResponse hasActiveConversationWithAssistant(HasActiveConversationWithAssistantRequest request) {
        try {
            log.debug("检查活跃对话 - 用户ID: {}, 助手ID: {}", request.getUserId(), request.getAssistantId());

            boolean hasActive = aiConversationRepository
                .existsByUserIdAndAssistantIdAndStatus(
                    request.getUserId(),
                    request.getAssistantId(),
                    AiConversation.ConversationStatus.ACTIVE
                );

            return HasActiveConversationWithAssistantResponse.newBuilder()
                .setSuccess(true)
                .setMessage("检查完成")
                .setHasActive(hasActive)
                .build();

        } catch (Exception e) {
            log.error("检查活跃对话失败", e);
            return HasActiveConversationWithAssistantResponse.newBuilder()
                .setSuccess(false)
                .setMessage("检查失败: " + e.getMessage())
                .setHasActive(false)
                .build();
        }
    }

    // ==================== 数据导出 (Protobuf版本) ====================

    @Override
    public ExportConversationsResponse exportConversations(ExportConversationsRequest request) {
        try {
            log.info("导出AI对话数据 - 用户ID: {}, 格式: {}, 对话数量: {}",
                request.getUserId(), request.getFormat(), request.getConversationIdsCount());

            // 查询要导出的对话
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(request.getConversationIdsList(), request.getUserId());

            if (conversations.isEmpty()) {
                return ExportConversationsResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("未找到要导出的对话")
                    .build();
            }

            // 根据格式导出数据
            String exportData;
            String contentType;
            String fileExtension;

            switch (request.getFormat()) {
                case EXPORT_FORMAT_JSON:
                    exportData = exportToJson(conversations);
                    contentType = "application/json";
                    fileExtension = "json";
                    break;
                case EXPORT_FORMAT_CSV:
                    exportData = exportToCsv(conversations);
                    contentType = "text/csv";
                    fileExtension = "csv";
                    break;
                case EXPORT_FORMAT_MARKDOWN:
                    exportData = exportToMarkdown(conversations);
                    contentType = "text/markdown";
                    fileExtension = "md";
                    break;
                default:
                    return ExportConversationsResponse.newBuilder()
                        .setSuccess(false)
                        .setMessage("不支持的导出格式")
                        .build();
            }

            // 生成文件名
            String filename = String.format("%s_%s_%s.%s",
                StringUtils.hasText(request.getFileNamePrefix()) ? request.getFileNamePrefix() : "conversations",
                request.getUserId(),
                System.currentTimeMillis(),
                fileExtension);

            return ExportConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("导出成功")
                .setExportData(exportData)
                .setFilename(filename)
                .build();

        } catch (Exception e) {
            log.error("导出AI对话数据失败", e);
            return ExportConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("导出失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public ExportAllUserConversationsResponse exportAllUserConversations(ExportAllUserConversationsRequest request) {
        try {
            log.info("导出用户所有AI对话 - 用户ID: {}, 格式: {}", request.getUserId(), request.getFormat());

            // 查询用户的所有对话
            List<AiConversation> conversations = aiConversationRepository
                .findByUserIdAndStatusNot(request.getUserId(), AiConversation.ConversationStatus.DELETED);

            if (conversations.isEmpty()) {
                return ExportAllUserConversationsResponse.newBuilder()
                    .setSuccess(false)
                    .setMessage("用户没有可导出的对话")
                    .build();
            }

            // 根据格式导出数据
            String exportData;
            String contentType;
            String fileExtension;

            switch (request.getFormat()) {
                case EXPORT_FORMAT_JSON:
                    exportData = exportToJson(conversations);
                    contentType = "application/json";
                    fileExtension = "json";
                    break;
                case EXPORT_FORMAT_CSV:
                    exportData = exportToCsv(conversations);
                    contentType = "text/csv";
                    fileExtension = "csv";
                    break;
                case EXPORT_FORMAT_MARKDOWN:
                    exportData = exportToMarkdown(conversations);
                    contentType = "text/markdown";
                    fileExtension = "md";
                    break;
                default:
                    return ExportAllUserConversationsResponse.newBuilder()
                        .setSuccess(false)
                        .setMessage("不支持的导出格式")
                        .build();
            }

            // 生成文件名
            String filename = String.format("all_conversations_%s_%s.%s",
                request.getUserId(),
                System.currentTimeMillis(),
                fileExtension);

            return ExportAllUserConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("导出成功")
                .setExportData(exportData)
                .setFilename(filename)
                .build();

        } catch (Exception e) {
            log.error("导出用户所有AI对话失败", e);
            return ExportAllUserConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("导出失败: " + e.getMessage())
                .build();
        }
    }

    // ==================== Protobuf转换辅助方法 ====================

    /**
     * 根据ID和用户ID获取对话
     */
    private AiConversation getConversationByIdAndUserId(Long conversationId, Long userId) {
        return aiConversationRepository.findById(conversationId)
            .filter(conversation -> conversation.getUserId().equals(userId) &&
                                  conversation.getStatus() != AiConversation.ConversationStatus.DELETED)
            .orElseThrow(() -> new BusinessException(SocialErrorCode.AI_CONVERSATION_NOT_FOUND));
    }

    /**
     * 生成对话标题
     */
    private String generateConversationTitle(String assistantName) {
        return String.format("与%s的对话", assistantName);
    }

    /**
     * 将实体转换为Protobuf对话信息
     */
    private AiConversationInfo convertToProtoConversationInfo(AiConversation conversation, AiAssistant assistant) {
        AiConversationInfo.Builder builder = AiConversationInfo.newBuilder()
            .setId(conversation.getId())
            .setUserId(conversation.getUserId())
            .setAssistantId(conversation.getAssistantId())
            .setTitle(conversation.getTitle())
            .setDescription(conversation.getDescription() != null ? conversation.getDescription() : "")
            .setMessageCount(conversation.getMessageCount())
            .setTotalInputTokens(conversation.getTotalInputTokens())
            .setTotalOutputTokens(conversation.getTotalOutputTokens())
            .setTotalTokens(conversation.getTotalTokens())
            .setTotalCost(conversation.getTotalCost())
            .setAvgResponseTime(conversation.getAvgResponseTime())
            .setLastMessageContent(conversation.getLastMessageContent() != null ? conversation.getLastMessageContent() : "")
            .setLastMessageRole(conversation.getLastMessageRole() != null ? conversation.getLastMessageRole() : "")
            .setIsPinned(conversation.getIsPinned())
            .setIsFavorite(conversation.getIsFavorite())
            .setStatus(convertToProtoStatus(conversation.getStatus()))
            .setPriority(convertToProtoPriority(conversation.getPriority()))
            .setCreatedAt(convertToProtoTimestamp(conversation.getCreatedAt()))
            .setUpdatedAt(convertToProtoTimestamp(conversation.getUpdatedAt()));

        // 设置可选字段
        if (conversation.getSummary() != null) {
            builder.setSummary(conversation.getSummary());
        }
        if (conversation.getLastMessageId() != null) {
            builder.setLastMessageId(conversation.getLastMessageId());
        }
        if (conversation.getLastMessageAt() != null) {
            builder.setLastMessageAt(convertToProtoTimestamp(conversation.getLastMessageAt()));
        }
        if (conversation.getPinnedAt() != null) {
            builder.setPinnedAt(convertToProtoTimestamp(conversation.getPinnedAt()));
        }
        if (conversation.getFavoritedAt() != null) {
            builder.setFavoritedAt(convertToProtoTimestamp(conversation.getFavoritedAt()));
        }

        // 设置助手信息
        if (assistant != null) {
            builder.setAssistantName(assistant.getName())
                   .setAssistantAvatar(assistant.getAvatarUrl() != null ? assistant.getAvatarUrl() : "")
                   .setAssistantType(assistant.getAssistantType().name());
        }

        // 设置AI参数
        builder.setAiParameters(buildProtoAiParameters(conversation));

        // 设置标签
        if (StringUtils.hasText(conversation.getTags())) {
            builder.addAllTags(Arrays.asList(conversation.getTags().split(",")));
        }

        return builder.build();
    }

    /**
     * 构建Protobuf AI参数
     */
    private AiParameters buildProtoAiParameters(AiConversation conversation) {
        AiParameters.Builder builder = AiParameters.newBuilder()
            .setTemperature(conversation.getTemperature() != null ? conversation.getTemperature() : 0.7)
            .setMaxTokens(conversation.getMaxTokens() != null ? conversation.getMaxTokens() : 2048)
            .setTopP(conversation.getTopP() != null ? conversation.getTopP() : 1.0)
            .setFrequencyPenalty(conversation.getFrequencyPenalty() != null ? conversation.getFrequencyPenalty() : 0.0)
            .setPresencePenalty(conversation.getPresencePenalty() != null ? conversation.getPresencePenalty() : 0.0)
            .setEnableStreaming(conversation.getEnableStreaming() != null ? conversation.getEnableStreaming() : true)
            .setEnableMemory(conversation.getEnableMemory() != null ? conversation.getEnableMemory() : true)
            .setContextWindowSize(conversation.getContextWindowSize() != null ? conversation.getContextWindowSize() : 10);

        if (conversation.getSystemPrompt() != null) {
            builder.setSystemPrompt(conversation.getSystemPrompt());
        }

        return builder.build();
    }

    /**
     * 计算效率评分
     */
    private Integer calculateEfficiencyScore(AiConversation conversation) {
        if (conversation.getMessageCount() == 0) return 0;

        // 基于平均响应时间和令牌效率计算
        long avgResponseTime = conversation.getAvgResponseTime();
        double tokenEfficiency = conversation.getTotalTokens() > 0 ?
            (double) conversation.getMessageCount() / conversation.getTotalTokens() * 1000 : 0;

        int timeScore = avgResponseTime < 1000 ? 50 : Math.max(0, 50 - (int)(avgResponseTime - 1000) / 100);
        int efficiencyScore = Math.min(50, (int)(tokenEfficiency * 10));

        return Math.min(100, timeScore + efficiencyScore);
    }

    /**
     * 计算活跃度评分
     */
    private Integer calculateActivityScore(AiConversation conversation) {
        if (conversation.getLastMessageAt() == null) return 0;

        long daysSinceLastMessage = (Instant.now().getEpochSecond() - conversation.getLastMessageAt().getEpochSecond()) / (24 * 3600);
        int messageScore = Math.min(50, conversation.getMessageCount() * 2);
        int recentScore = daysSinceLastMessage < 1 ? 50 : Math.max(0, 50 - (int)daysSinceLastMessage * 5);

        return Math.min(100, messageScore + recentScore);
    }

    /**
     * 格式化响应时间
     */
    private String formatResponseTime(Long responseTime) {
        if (responseTime == null || responseTime == 0) return "未知";

        if (responseTime < 1000) {
            return responseTime + "ms";
        } else if (responseTime < 60000) {
            return String.format("%.1fs", responseTime / 1000.0);
        } else {
            return String.format("%.1fm", responseTime / 60000.0);
        }
    }

    /**
     * 格式化消息角色
     */
    private String formatMessageRole(String role) {
        if (role == null) return "未知";

        switch (role.toLowerCase()) {
            case "user": return "用户";
            case "assistant": return "助手";
            case "system": return "系统";
            default: return role;
        }
    }

    /**
     * 格式化时间
     */
    private String formatTime(Instant instant) {
        if (instant == null) return "未知";

        LocalDate date = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();

        if (date.equals(today)) {
            return "今天";
        } else if (date.equals(today.minusDays(1))) {
            return "昨天";
        } else if (date.isAfter(today.minusDays(7))) {
            return date.format(DateTimeFormatter.ofPattern("MM-dd"));
        } else {
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
    }

    // ==================== 对话状态管理 ====================

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId + '_' + #userId")
    public void pinConversation(Long conversationId, Long userId) {
        log.info("置顶AI对话 - 对话ID: {}, 用户ID: {}", conversationId, userId);

        AiConversation conversation = getConversationByIdAndUserId(conversationId, userId);
        conversation.pin();
        aiConversationRepository.save(conversation);

        log.info("AI对话置顶成功 - 对话ID: {}, 用户ID: {}", conversationId, userId);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId + '_' + #userId")
    public void unpinConversation(Long conversationId, Long userId) {
        log.info("取消置顶AI对话 - 对话ID: {}, 用户ID: {}", conversationId, userId);

        AiConversation conversation = getConversationByIdAndUserId(conversationId, userId);
        conversation.unpin();
        aiConversationRepository.save(conversation);

        log.info("AI对话取消置顶成功 - 对话ID: {}, 用户ID: {}", conversationId, userId);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId + '_' + #userId")
    public void favoriteConversation(Long conversationId, Long userId) {
        log.info("收藏AI对话 - 对话ID: {}, 用户ID: {}", conversationId, userId);

        AiConversation conversation = getConversationByIdAndUserId(conversationId, userId);
        conversation.favorite();
        aiConversationRepository.save(conversation);

        log.info("AI对话收藏成功 - 对话ID: {}, 用户ID: {}", conversationId, userId);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId + '_' + #userId")
    public void unfavoriteConversation(Long conversationId, Long userId) {
        log.info("取消收藏AI对话 - 对话ID: {}, 用户ID: {}", conversationId, userId);

        AiConversation conversation = getConversationByIdAndUserId(conversationId, userId);
        conversation.unfavorite();
        aiConversationRepository.save(conversation);

        log.info("AI对话取消收藏成功 - 对话ID: {}, 用户ID: {}", conversationId, userId);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId + '_' + #userId")
    public void updateConversationStatus(Long conversationId, Long userId, AiConversation.ConversationStatus status) {
        log.info("更新AI对话状态 - 对话ID: {}, 用户ID: {}, 状态: {}", conversationId, userId, status);

        AiConversation conversation = getConversationByIdAndUserId(conversationId, userId);
        conversation.setStatus(status);
        aiConversationRepository.save(conversation);

        log.info("AI对话状态更新成功 - 对话ID: {}, 用户ID: {}, 状态: {}", conversationId, userId, status);
    }

    // ==================== 批量操作 ====================

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchOperateConversations(Long userId, com.implatform.realtime.dto.AiConversationDTO.BatchOperationRequest request) {
        log.info("批量操作AI对话 - 用户ID: {}, 操作类型: {}, 对话数量: {}",
            userId, request.getOperationType(), request.getConversationIds().size());

        String operationType = request.getOperationType().toString();
        switch (operationType) {
            case "BATCH_OPERATION_TYPE_PIN":
                batchPinConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_UNPIN":
                batchUnpinConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_FAVORITE":
                batchFavoriteConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_UNFAVORITE":
                batchUnfavoriteConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_ARCHIVE":
                batchArchiveConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_DELETE":
                batchDeleteConversations(userId, request.getConversationIds());
                break;
            case "BATCH_OPERATION_TYPE_UNSPECIFIED":
                if (request.getTargetStatus() != null) {
                    aiConversationRepository.updateConversationStatus(
                        request.getConversationIds(), userId, request.getTargetStatus());
                }
                break;
            default:
                throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }

        log.info("批量操作AI对话完成 - 用户ID: {}, 操作类型: {}", userId, request.getOperationType());
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchPinConversations(Long userId, List<Long> conversationIds) {
        log.info("批量置顶AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        Instant now = Instant.now();
        int updated = aiConversationRepository.pinConversations(conversationIds, userId, now);

        log.info("批量置顶AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchUnpinConversations(Long userId, List<Long> conversationIds) {
        log.info("批量取消置顶AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        int updated = aiConversationRepository.unpinConversations(conversationIds, userId);

        log.info("批量取消置顶AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchFavoriteConversations(Long userId, List<Long> conversationIds) {
        log.info("批量收藏AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        Instant now = Instant.now();
        int updated = aiConversationRepository.favoriteConversations(conversationIds, userId, now);

        log.info("批量收藏AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchUnfavoriteConversations(Long userId, List<Long> conversationIds) {
        log.info("批量取消收藏AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        int updated = aiConversationRepository.unfavoriteConversations(conversationIds, userId);

        log.info("批量取消收藏AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchArchiveConversations(Long userId, List<Long> conversationIds) {
        log.info("批量归档AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        int updated = aiConversationRepository.updateConversationStatus(
            conversationIds, userId, AiConversation.ConversationStatus.ARCHIVED);

        log.info("批量归档AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#userId")
    public void batchDeleteConversations(Long userId, List<Long> conversationIds) {
        log.info("批量删除AI对话 - 用户ID: {}, 对话数量: {}", userId, conversationIds.size());

        int updated = aiConversationRepository.updateConversationStatus(
            conversationIds, userId, AiConversation.ConversationStatus.DELETED);

        log.info("批量删除AI对话完成 - 用户ID: {}, 更新数量: {}", userId, updated);
    }

    // ==================== 消息相关 ====================

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId")
    public void updateLastMessage(Long conversationId, Long messageId, String content, String role) {
        log.debug("更新对话最后消息 - 对话ID: {}, 消息ID: {}", conversationId, messageId);

        Instant now = Instant.now();
        String truncatedContent = content != null && content.length() > 500 ?
            content.substring(0, 500) + "..." : content;

        aiConversationRepository.updateLastMessage(conversationId, messageId, truncatedContent, role, now);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId")
    public void updateTokenUsage(Long conversationId, int inputTokens, int outputTokens) {
        log.debug("更新对话令牌使用量 - 对话ID: {}, 输入: {}, 输出: {}",
            conversationId, inputTokens, outputTokens);

        long totalTokens = inputTokens + outputTokens;
        aiConversationRepository.updateTokenUsage(conversationId, (long)inputTokens, (long)outputTokens, totalTokens);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId")
    public void updateCost(Long conversationId, long cost) {
        log.debug("更新对话费用 - 对话ID: {}, 费用: {}分", conversationId, cost);

        aiConversationRepository.updateCost(conversationId, cost);
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId")
    public void updateResponseTime(Long conversationId, long responseTime) {
        log.debug("更新对话响应时间 - 对话ID: {}, 响应时间: {}ms", conversationId, responseTime);

        // 获取对话并更新平均响应时间
        aiConversationRepository.findById(conversationId).ifPresent(conversation -> {
            conversation.updateAvgResponseTime(responseTime);
            aiConversationRepository.save(conversation);
        });
    }

    @Transactional
    @CacheEvict(value = "ai_conversations", key = "#conversationId")
    public void incrementMessageCount(Long conversationId) {
        log.debug("增加对话消息数量 - 对话ID: {}", conversationId);

        aiConversationRepository.findById(conversationId).ifPresent(conversation -> {
            conversation.incrementMessageCount();
            aiConversationRepository.save(conversation);
        });
    }

    // ==================== 辅助方法 ====================

    /**
     * 转换状态为Protobuf枚举
     */
    private ConversationStatus convertToProtoStatus(AiConversation.ConversationStatus status) {
        switch (status) {
            case ACTIVE:
                return ConversationStatus.CONVERSATION_STATUS_ACTIVE;
            case ARCHIVED:
                return ConversationStatus.CONVERSATION_STATUS_ARCHIVED;
            case DELETED:
                return ConversationStatus.CONVERSATION_STATUS_DELETED;
            default:
                return ConversationStatus.CONVERSATION_STATUS_UNSPECIFIED;
        }
    }

    /**
     * 转换优先级为Protobuf枚举
     */
    private ConversationPriority convertToProtoPriority(AiConversation.ConversationPriority priority) {
        switch (priority) {
            case LOW:
                return ConversationPriority.CONVERSATION_PRIORITY_LOW;
            case NORMAL:
                return ConversationPriority.CONVERSATION_PRIORITY_NORMAL;
            case HIGH:
                return ConversationPriority.CONVERSATION_PRIORITY_HIGH;
            case URGENT:
                return ConversationPriority.CONVERSATION_PRIORITY_URGENT;
            default:
                return ConversationPriority.CONVERSATION_PRIORITY_UNSPECIFIED;
        }
    }

    /**
     * 转换时间为Protobuf Timestamp
     */
    private com.google.protobuf.Timestamp convertToProtoTimestamp(Instant instant) {
        if (instant == null) {
            return com.google.protobuf.Timestamp.getDefaultInstance();
        }
        return com.google.protobuf.Timestamp.newBuilder()
            .setSeconds(instant.getEpochSecond())
            .setNanos(instant.getNano())
            .build();
    }

    /**
     * 根据时间范围类型获取天数
     */
    private int getTimeRangeDays(TimeRangeType timeRangeType) {
        switch (timeRangeType) {
            case TIME_RANGE_TYPE_LAST_7_DAYS:
                return 7;
            case TIME_RANGE_TYPE_LAST_30_DAYS:
                return 30;
            case TIME_RANGE_TYPE_LAST_90_DAYS:
                return 90;
            case TIME_RANGE_TYPE_LAST_YEAR:
                return 365;
            default:
                return 30; // 默认30天
        }
    }

    // ==================== Protobuf转换辅助方法 ====================



    /**
     * 从Protobuf设置AI参数到实体
     */
    private void setAiParametersFromProto(AiConversation conversation, AiParameters aiParameters) {
        if (aiParameters.getTemperature() > 0) {
            conversation.setTemperature(aiParameters.getTemperature());
        }
        if (aiParameters.getMaxTokens() > 0) {
            conversation.setMaxTokens(aiParameters.getMaxTokens());
        }
        if (aiParameters.getTopP() > 0) {
            conversation.setTopP(aiParameters.getTopP());
        }
        if (aiParameters.getFrequencyPenalty() != 0) {
            conversation.setFrequencyPenalty(aiParameters.getFrequencyPenalty());
        }
        if (aiParameters.getPresencePenalty() != 0) {
            conversation.setPresencePenalty(aiParameters.getPresencePenalty());
        }
        if (StringUtils.hasText(aiParameters.getSystemPrompt())) {
            conversation.setSystemPrompt(aiParameters.getSystemPrompt());
        }
        conversation.setEnableStreaming(aiParameters.getEnableStreaming());
        conversation.setEnableMemory(aiParameters.getEnableMemory());
        if (aiParameters.getContextWindowSize() > 0) {
            conversation.setContextWindowSize(aiParameters.getContextWindowSize());
        }
        // 注意：这些字段可能在实体类中不存在，需要根据实际情况调整
        // conversation.setHasCustomParameters(aiParameters.getHasCustomParameters());
        // conversation.setHasCustomSystemPrompt(aiParameters.getHasCustomSystemPrompt());
    }



    /**
     * 从Protobuf转换优先级枚举
     */
    private AiConversation.ConversationPriority convertFromProtoPriority(ConversationPriority priority) {
        switch (priority) {
            case CONVERSATION_PRIORITY_LOW:
                return AiConversation.ConversationPriority.LOW;
            case CONVERSATION_PRIORITY_NORMAL:
                return AiConversation.ConversationPriority.NORMAL;
            case CONVERSATION_PRIORITY_HIGH:
                return AiConversation.ConversationPriority.HIGH;
            case CONVERSATION_PRIORITY_URGENT:
                return AiConversation.ConversationPriority.URGENT;
            default:
                return AiConversation.ConversationPriority.NORMAL;
        }
    }

    /**
     * 从Protobuf转换状态枚举
     */
    private AiConversation.ConversationStatus convertFromProtoStatus(ConversationStatus status) {
        switch (status) {
            case CONVERSATION_STATUS_ACTIVE:
                return AiConversation.ConversationStatus.ACTIVE;
            case CONVERSATION_STATUS_PAUSED:
                return AiConversation.ConversationStatus.PAUSED;
            case CONVERSATION_STATUS_ARCHIVED:
                return AiConversation.ConversationStatus.ARCHIVED;
            case CONVERSATION_STATUS_DELETED:
                return AiConversation.ConversationStatus.DELETED;
            default:
                return AiConversation.ConversationStatus.ACTIVE;
        }
    }





    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Instant instant) {
        if (instant == null) {
            return "";
        }
        return instant.atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 格式化响应时间
     */
    private String formatResponseTime(long responseTime) {
        if (responseTime < 1000) {
            return responseTime + "ms";
        } else {
            return String.format("%.1fs", responseTime / 1000.0);
        }
    }

    /**
     * 获取消息角色描述
     */
    private String getMessageRoleDescription(String role) {
        if (role == null) {
            return "";
        }
        switch (role.toLowerCase()) {
            case "user":
                return "用户";
            case "assistant":
                return "助手";
            case "system":
                return "系统";
            default:
                return role;
        }
    }



    /**
     * 导出为JSON格式
     */
    private String exportToJson(List<AiConversation> conversations) {
        // 这里应该使用JSON库进行序列化，简化处理
        StringBuilder json = new StringBuilder();
        json.append("[\n");

        for (int i = 0; i < conversations.size(); i++) {
            AiConversation conversation = conversations.get(i);
            json.append("  {\n");
            json.append("    \"id\": ").append(conversation.getId()).append(",\n");
            json.append("    \"title\": \"").append(escapeJson(conversation.getTitle())).append("\",\n");
            json.append("    \"description\": \"").append(escapeJson(conversation.getDescription())).append("\",\n");
            json.append("    \"assistantId\": ").append(conversation.getAssistantId()).append(",\n");
            json.append("    \"messageCount\": ").append(conversation.getMessageCount()).append(",\n");
            json.append("    \"totalTokens\": ").append(conversation.getTotalTokens()).append(",\n");
            json.append("    \"totalCost\": ").append(conversation.getTotalCost()).append(",\n");
            json.append("    \"status\": \"").append(conversation.getStatus()).append("\",\n");
            json.append("    \"createdAt\": \"").append(conversation.getCreatedAt()).append("\",\n");
            json.append("    \"updatedAt\": \"").append(conversation.getUpdatedAt()).append("\"\n");
            json.append("  }");
            if (i < conversations.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }

        json.append("]");
        return json.toString();
    }

    /**
     * 导出为CSV格式
     */
    private String exportToCsv(List<AiConversation> conversations) {
        StringBuilder csv = new StringBuilder();

        // CSV头部
        csv.append("ID,标题,描述,助手ID,消息数,总令牌数,总费用,状态,创建时间,更新时间\n");

        // CSV数据
        for (AiConversation conversation : conversations) {
            csv.append(conversation.getId()).append(",");
            csv.append("\"").append(escapeCsv(conversation.getTitle())).append("\",");
            csv.append("\"").append(escapeCsv(conversation.getDescription())).append("\",");
            csv.append(conversation.getAssistantId()).append(",");
            csv.append(conversation.getMessageCount()).append(",");
            csv.append(conversation.getTotalTokens()).append(",");
            csv.append(conversation.getTotalCost()).append(",");
            csv.append("\"").append(conversation.getStatus()).append("\",");
            csv.append("\"").append(conversation.getCreatedAt()).append("\",");
            csv.append("\"").append(conversation.getUpdatedAt()).append("\"");
            csv.append("\n");
        }

        return csv.toString();
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 转义CSV字符串
     */
    private String escapeCsv(String str) {
        if (str == null) return "";
        return str.replace("\"", "\"\"");
    }

    /**
     * 导出为Markdown格式
     */
    private String exportToMarkdown(List<AiConversation> conversations) {
        StringBuilder markdown = new StringBuilder();
        
        // Markdown标题
        markdown.append("# AI对话导出\n\n");
        markdown.append("导出时间: ").append(formatTimestamp(Instant.now())).append("\n");
        markdown.append("总对话数: ").append(conversations.size()).append("\n\n");
        
        // 对话列表
        for (int i = 0; i < conversations.size(); i++) {
            AiConversation conversation = conversations.get(i);
            
            markdown.append("## ").append(i + 1).append(". ").append(conversation.getTitle()).append("\n\n");
            
            markdown.append("**基本信息:**\n");
            markdown.append("- ID: ").append(conversation.getId()).append("\n");
            markdown.append("- 助手ID: ").append(conversation.getAssistantId()).append("\n");
            markdown.append("- 状态: ").append(conversation.getStatus()).append("\n");
            markdown.append("- 创建时间: ").append(formatTimestamp(conversation.getCreatedAt())).append("\n");
            markdown.append("- 更新时间: ").append(formatTimestamp(conversation.getUpdatedAt())).append("\n\n");
            
            if (conversation.getDescription() != null && !conversation.getDescription().trim().isEmpty()) {
                markdown.append("**描述:** ").append(conversation.getDescription()).append("\n\n");
            }
            
            markdown.append("**统计信息:**\n");
            markdown.append("- 消息数量: ").append(conversation.getMessageCount()).append("\n");
            markdown.append("- 总令牌数: ").append(conversation.getTotalTokens()).append("\n");
            markdown.append("- 输入令牌: ").append(conversation.getTotalInputTokens()).append("\n");
            markdown.append("- 输出令牌: ").append(conversation.getTotalOutputTokens()).append("\n");
            markdown.append("- 总费用: ").append(conversation.getTotalCost()).append("\n");
            if (conversation.getAvgResponseTime() != null) {
                markdown.append("- 平均响应时间: ").append(formatResponseTime(conversation.getAvgResponseTime())).append("\n");
            }
            
            if (conversation.getLastMessageContent() != null && !conversation.getLastMessageContent().trim().isEmpty()) {
                markdown.append("\n**最后消息:**\n");
                markdown.append("- 角色: ").append(getMessageRoleDescription(conversation.getLastMessageRole())).append("\n");
                markdown.append("- 内容: ").append(conversation.getLastMessageContent()).append("\n");
            }
            
            markdown.append("\n---\n\n");
        }
        
        return markdown.toString();
    }

    /**
     * 处理批量置顶操作
     */
    private BatchOperateConversationsResponse processBatchPin(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setIsPinned(true);
                conversation.setPinnedAt(Instant.now());
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量置顶成功")
                .build();
        } catch (Exception e) {
            log.error("批量置顶失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量置顶失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量取消置顶操作
     */
    private BatchOperateConversationsResponse processBatchUnpin(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setIsPinned(false);
                conversation.setPinnedAt(null);
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量取消置顶成功")
                .build();
        } catch (Exception e) {
            log.error("批量取消置顶失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量取消置顶失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量收藏操作
     */
    private BatchOperateConversationsResponse processBatchFavorite(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setIsFavorite(true);
                conversation.setFavoritedAt(Instant.now());
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量收藏成功")
                .build();
        } catch (Exception e) {
            log.error("批量收藏失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量收藏失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量取消收藏操作
     */
    private BatchOperateConversationsResponse processBatchUnfavorite(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setIsFavorite(false);
                conversation.setFavoritedAt(null);
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量取消收藏成功")
                .build();
        } catch (Exception e) {
            log.error("批量取消收藏失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量取消收藏失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量删除操作
     */
    private BatchOperateConversationsResponse processBatchDelete(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setStatus(AiConversation.ConversationStatus.DELETED);
                conversation.setDeletedAt(Instant.now());
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量删除成功")
                .build();
        } catch (Exception e) {
            log.error("批量删除失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量删除失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量归档操作
     */
    private BatchOperateConversationsResponse processBatchArchive(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setStatus(AiConversation.ConversationStatus.ARCHIVED);
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量归档成功")
                .build();
        } catch (Exception e) {
            log.error("批量归档失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量归档失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 处理批量恢复操作
     */
    private BatchOperateConversationsResponse processBatchRestore(BatchOperateConversationsRequest request) {
        try {
            List<Long> conversationIds = request.getConversationIdsList();
            List<AiConversation> conversations = aiConversationRepository
                .findByIdInAndUserId(conversationIds, request.getUserId());

            for (AiConversation conversation : conversations) {
                conversation.setStatus(AiConversation.ConversationStatus.ACTIVE);
                conversation.setDeletedAt(null);
            }

            aiConversationRepository.saveAll(conversations);

            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(true)
                .setMessage("批量恢复成功")
                .build();
        } catch (Exception e) {
            log.error("批量恢复失败", e);
            return BatchOperateConversationsResponse.newBuilder()
                .setSuccess(false)
                .setMessage("批量恢复失败: " + e.getMessage())
                .build();
        }
    }

    /**
     * 根据时间范围类型计算开始时间
     */
    private Instant calculateStartTime(TimeRangeType timeRangeType) {
        Instant now = Instant.now();
        switch (timeRangeType) {
            case TIME_RANGE_TYPE_LAST_7_DAYS:
                return now.minusSeconds(7 * 24 * 60 * 60);
            case TIME_RANGE_TYPE_LAST_30_DAYS:
                return now.minusSeconds(30 * 24 * 60 * 60);
            case TIME_RANGE_TYPE_LAST_90_DAYS:
                return now.minusSeconds(90 * 24 * 60 * 60);
            case TIME_RANGE_TYPE_LAST_YEAR:
                return now.minusSeconds(365 * 24 * 60 * 60);
            case TIME_RANGE_TYPE_ALL_TIME:
                return Instant.EPOCH;
            default:
                return now.minusSeconds(7 * 24 * 60 * 60);
        }
    }

    /**
     * 根据时间范围类型计算天数
     */
    private int calculateDaysFromTimeRangeType(TimeRangeType timeRangeType) {
        switch (timeRangeType) {
            case TIME_RANGE_TYPE_LAST_7_DAYS:
                return 7;
            case TIME_RANGE_TYPE_LAST_30_DAYS:
                return 30;
            case TIME_RANGE_TYPE_LAST_90_DAYS:
                return 90;
            case TIME_RANGE_TYPE_LAST_YEAR:
                return 365;
            default:
                return 7;
        }
    }
}
