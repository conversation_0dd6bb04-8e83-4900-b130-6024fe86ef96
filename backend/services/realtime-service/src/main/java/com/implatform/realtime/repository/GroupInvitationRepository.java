package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupInvitation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群组邀请数据访问层 - R2DBC响应式版本
 * 提供群组邀请相关的数据库操作方法
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupInvitationRepository extends R2dbcRepository<GroupInvitation, Long> {

    /**
     * 根据邀请码查询邀请
     *
     * @param invitationCode 邀请码
     * @return 邀请信息
     */
    @Query("SELECT * FROM group_invitations WHERE invitation_code = :invitationCode")
    Mono<GroupInvitation> findByInvitationCode(@Param("invitationCode") String invitationCode);

    /**
     * 根据群组ID查询邀请列表
     *
     * @param groupId 群组ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 邀请分页结果
     */
    @Query("SELECT * FROM group_invitations WHERE group_id = :groupId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<GroupInvitation> findByGroupIdOrderByCreatedAtDesc(@Param("groupId") Long groupId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据群组ID和状态查询邀请
     *
     * @param groupId 群组ID
     * @param status 邀请状态
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 邀请分页结果
     */
    Page<GroupInvitation> findByGroupIdAndStatusOrderByCreatedAtDesc(
            Long groupId, GroupInvitation.InvitationStatus status, Pageable pageable);

    /**
     * 根据邀请人ID查询邀请
     * 
     * @param inviterId 邀请人ID
     * @param pageable 分页参数
     * @return 邀请分页结果
     */
    Page<GroupInvitation> findByInviterIdOrderByCreatedAtDesc(Long inviterId, Pageable pageable);

    /**
     * 根据被邀请人ID查询邀请
     * 
     * @param inviteeId 被邀请人ID
     * @param pageable 分页参数
     * @return 邀请分页结果
     */
    Page<GroupInvitation> findByInviteeIdOrderByCreatedAtDesc(Long inviteeId, Pageable pageable);

    /**
     * 查询待处理的邀请
     * 
     * @param pageable 分页参数
     * @return 待处理邀请分页结果
     */
    Page<GroupInvitation> findByStatusOrderByCreatedAtDesc(
            GroupInvitation.InvitationStatus status, Pageable pageable);

    /**
     * 查询即将过期的邀请
     * 
     * @param expiryThreshold 过期时间阈值
     * @param pageable 分页参数
     * @return 即将过期的邀请分页结果
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.status = 'PENDING' " +
           "AND gi.expiresAt IS NOT NULL AND gi.expiresAt <= :expiryThreshold " +
           "ORDER BY gi.expiresAt ASC")
    Page<GroupInvitation> findExpiringInvitations(@Param("expiryThreshold") Instant expiryThreshold, 
                                                  Pageable pageable);

    /**
     * 查询已过期但状态未更新的邀请
     * 
     * @param currentTime 当前时间
     * @param pageable 分页参数
     * @return 已过期的邀请分页结果
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.status = 'PENDING' " +
           "AND gi.expiresAt IS NOT NULL AND gi.expiresAt < :currentTime " +
           "ORDER BY gi.expiresAt ASC")
    Page<GroupInvitation> findExpiredInvitations(@Param("currentTime") Instant currentTime, 
                                                Pageable pageable);

    /**
     * 批量更新过期邀请状态
     * 
     * @param currentTime 当前时间
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupInvitation gi SET gi.status = 'EXPIRED' " +
           "WHERE gi.status = 'PENDING' AND gi.expiresAt IS NOT NULL AND gi.expiresAt < :currentTime")
    int markExpiredInvitations(@Param("currentTime") Instant currentTime);

    /**
     * 查询群组邀请统计
     * 
     * @param groupId 群组ID
     * @return 邀请统计 [总数, 待处理数, 已接受数, 已拒绝数, 已过期数]
     */
    @Query("SELECT COUNT(gi), " +
           "COUNT(CASE WHEN gi.status = 'PENDING' THEN 1 END), " +
           "COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END), " +
           "COUNT(CASE WHEN gi.status = 'REJECTED' THEN 1 END), " +
           "COUNT(CASE WHEN gi.status = 'EXPIRED' THEN 1 END) " +
           "FROM GroupInvitation gi WHERE gi.groupId = :groupId")
    Object[] getInvitationStatistics(@Param("groupId") Long groupId);

    /**
     * 查询邀请类型统计
     * 
     * @param groupId 群组ID
     * @return 类型统计结果
     */
    @Query("SELECT gi.invitationType, COUNT(gi), " +
           "COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END) " +
           "FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "GROUP BY gi.invitationType")
    List<Object[]> getInvitationTypeStatistics(@Param("groupId") Long groupId);

    /**
     * 查询用户的邀请统计
     * 
     * @param inviterId 邀请人ID
     * @return 用户邀请统计
     */
    @Query("SELECT COUNT(gi), " +
           "COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END), " +
           "COUNT(CASE WHEN gi.status = 'REJECTED' THEN 1 END) " +
           "FROM GroupInvitation gi WHERE gi.inviterId = :inviterId")
    Object[] getUserInvitationStatistics(@Param("inviterId") Long inviterId);

    /**
     * 查询需要审批的邀请
     * 
     * @param groupId 群组ID
     * @param pageable 分页参数
     * @return 需要审批的邀请分页结果
     */
    Page<GroupInvitation> findByGroupIdAndRequiresApprovalTrueAndStatusOrderByCreatedAtDesc(
            Long groupId, GroupInvitation.InvitationStatus status, Pageable pageable);

    /**
     * 查询多次使用的邀请码
     * 
     * @param groupId 群组ID
     * @return 多次使用的邀请列表
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.usageLimit > 1 AND gi.status IN ('PENDING', 'USED') " +
           "ORDER BY gi.createdAt DESC")
    List<GroupInvitation> findMultiUseInvitations(@Param("groupId") Long groupId);

    /**
     * 查询一次性邀请码
     * 
     * @param groupId 群组ID
     * @return 一次性邀请列表
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.usageLimit = 1 ORDER BY gi.createdAt DESC")
    List<GroupInvitation> findOneTimeInvitations(@Param("groupId") Long groupId);

    /**
     * 查询已达使用上限的邀请
     * 
     * @param groupId 群组ID
     * @return 已达上限的邀请列表
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.usedCount >= gi.usageLimit ORDER BY gi.createdAt DESC")
    List<GroupInvitation> findFullyUsedInvitations(@Param("groupId") Long groupId);

    /**
     * 查询活跃的邀请码（未过期且未达使用上限）
     * 
     * @param groupId 群组ID
     * @return 活跃邀请列表
     */
    @Query("SELECT gi FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.status = 'PENDING' AND gi.usedCount < gi.usageLimit " +
           "AND (gi.expiresAt IS NULL OR gi.expiresAt > CURRENT_TIMESTAMP) " +
           "ORDER BY gi.createdAt DESC")
    List<GroupInvitation> findActiveInvitations(@Param("groupId") Long groupId);

    /**
     * 检查邀请码是否存在
     * 
     * @param invitationCode 邀请码
     * @return 是否存在
     */
    boolean existsByInvitationCode(String invitationCode);

    /**
     * 检查用户是否已被邀请到群组
     * 
     * @param groupId 群组ID
     * @param inviteeId 被邀请人ID
     * @param status 邀请状态
     * @return 是否已被邀请
     */
    boolean existsByGroupIdAndInviteeIdAndStatus(Long groupId, Long inviteeId, 
                                                GroupInvitation.InvitationStatus status);

    /**
     * 统计用户今日邀请次数
     * 
     * @param inviterId 邀请人ID
     * @param startOfDay 今日开始时间
     * @return 今日邀请次数
     */
    @Query("SELECT COUNT(gi) FROM GroupInvitation gi WHERE gi.inviterId = :inviterId " +
           "AND gi.createdAt >= :startOfDay")
    long countTodayInvitations(@Param("inviterId") Long inviterId, 
                              @Param("startOfDay") Instant startOfDay);

    /**
     * 查询邀请成功率排行榜
     * 
     * @param pageable 分页参数
     * @return 成功率排行榜
     */
    @Query("SELECT gi.inviterId, COUNT(gi) as totalInvitations, " +
           "COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END) as acceptedInvitations, " +
           "(COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END) * 100.0 / COUNT(gi)) as successRate " +
           "FROM GroupInvitation gi GROUP BY gi.inviterId " +
           "HAVING COUNT(gi) >= 5 ORDER BY successRate DESC")
    Page<Object[]> findInvitationSuccessRateLeaderboard(Pageable pageable);

    /**
     * 查询邀请响应时间统计
     * 
     * @param groupId 群组ID
     * @return 响应时间统计
     */
    @Query("SELECT AVG(EXTRACT(EPOCH FROM gi.respondedAt) - EXTRACT(EPOCH FROM gi.createdAt)) as avgResponseTime, " +
           "MIN(EXTRACT(EPOCH FROM gi.respondedAt) - EXTRACT(EPOCH FROM gi.createdAt)) as minResponseTime, " +
           "MAX(EXTRACT(EPOCH FROM gi.respondedAt) - EXTRACT(EPOCH FROM gi.createdAt)) as maxResponseTime " +
           "FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.respondedAt IS NOT NULL")
    Object[] getInvitationResponseTimeStats(@Param("groupId") Long groupId);

    /**
     * 查询邀请趋势（按天统计）
     * 
     * @param groupId 群组ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日邀请统计
     */
    @Query(value = "SELECT DATE(created_at) as invitation_date, " +
           "COUNT(*) as daily_invitations, " +
           "COUNT(CASE WHEN status = 'ACCEPTED' THEN 1 END) as daily_accepted " +
           "FROM group_invitation WHERE group_id = :groupId " +
           "AND created_at BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(created_at) ORDER BY invitation_date", nativeQuery = true)
    List<Object[]> findInvitationTrend(@Param("groupId") Long groupId, 
                                      @Param("startTime") Instant startTime, 
                                      @Param("endTime") Instant endTime);

    /**
     * 查询最受欢迎的邀请来源
     * 
     * @param groupId 群组ID
     * @return 邀请来源统计
     */
    @Query("SELECT gi.invitationSource, COUNT(gi) as sourceCount, " +
           "COUNT(CASE WHEN gi.status = 'ACCEPTED' THEN 1 END) as acceptedCount " +
           "FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "AND gi.invitationSource IS NOT NULL " +
           "GROUP BY gi.invitationSource ORDER BY sourceCount DESC")
    List<Object[]> findPopularInvitationSources(@Param("groupId") Long groupId);

    /**
     * 取消用户的所有待处理邀请
     * 
     * @param inviterId 邀请人ID
     * @return 取消的邀请数量
     */
    @Modifying
    @Query("UPDATE GroupInvitation gi SET gi.status = 'CANCELLED' " +
           "WHERE gi.inviterId = :inviterId AND gi.status = 'PENDING'")
    int cancelPendingInvitationsByInviter(@Param("inviterId") Long inviterId);

    /**
     * 取消群组的所有待处理邀请
     * 
     * @param groupId 群组ID
     * @return 取消的邀请数量
     */
    @Modifying
    @Query("UPDATE GroupInvitation gi SET gi.status = 'CANCELLED' " +
           "WHERE gi.groupId = :groupId AND gi.status = 'PENDING'")
    int cancelPendingInvitationsByGroup(@Param("groupId") Long groupId);

    /**
     * 删除指定时间之前的已处理邀请记录
     * 
     * @param beforeTime 时间阈值
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM GroupInvitation gi WHERE gi.createdAt < :beforeTime " +
           "AND gi.status IN ('ACCEPTED', 'REJECTED', 'EXPIRED', 'CANCELLED', 'USED')")
    int deleteOldProcessedInvitations(@Param("beforeTime") Instant beforeTime);

    /**
     * 查询邀请码使用效率
     * 
     * @param groupId 群组ID
     * @return 使用效率统计
     */
    @Query("SELECT gi.invitationType, " +
           "AVG(gi.usedCount * 100.0 / gi.usageLimit) as avgUsageRate, " +
           "COUNT(gi) as totalInvitations " +
           "FROM GroupInvitation gi WHERE gi.groupId = :groupId " +
           "GROUP BY gi.invitationType ORDER BY avgUsageRate DESC")
    List<Object[]> getInvitationUsageEfficiency(@Param("groupId") Long groupId);

    /**
     * 查询需要人工审核的邀请
     * 
     * @param pageable 分页参数
     * @return 需要审核的邀请分页结果
     */
    Page<GroupInvitation> findByStatusAndRequiresApprovalTrueOrderByCreatedAtAsc(
            GroupInvitation.InvitationStatus status, Pageable pageable);

    /**
     * 查询指定IP地址的邀请记录
     * 
     * @param clientIp 客户端IP
     * @param pageable 分页参数
     * @return IP邀请记录分页结果
     */
    Page<GroupInvitation> findByClientIpOrderByCreatedAtDesc(String clientIp, Pageable pageable);

    /**
     * 统计IP地址的邀请频率
     * 
     * @param clientIp 客户端IP
     * @param timeWindow 时间窗口（小时）
     * @return 指定时间窗口内的邀请次数
     */
    @Query("SELECT COUNT(gi) FROM GroupInvitation gi WHERE gi.clientIp = :clientIp " +
           "AND gi.createdAt >= :timeThreshold")
    long countInvitationsByIpInTimeWindow(@Param("clientIp") String clientIp, 
                                         @Param("timeThreshold") Instant timeThreshold);
}
