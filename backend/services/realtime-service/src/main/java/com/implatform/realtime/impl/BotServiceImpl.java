package com.implatform.realtime.impl;

import com.implatform.realtime.entity.Bot;
import com.implatform.realtime.entity.BotCommand;
import com.implatform.realtime.entity.BotInstallation;
import com.implatform.realtime.repository.BotRepository;
import com.implatform.realtime.dto.BotDTO;
import com.implatform.service.BotService;
import org.springframework.stereotype.Service;
import com.implatform.common.core.enums.BotErrorCode;
import com.implatform.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.implatform.realtime.entity.BotCommand.CommandScope;
import com.implatform.realtime.entity.BotInstallation.InstallationType;

/**
 * 机器人服务实现类
 * 提供智能机器人管理和交互功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class BotServiceImpl implements BotService {
    
    private final BotRepository botRepository;
    
    // 机器人配置
    private static final int MAX_BOTS_PER_USER = 10;
    private static final double HIGH_RATING_THRESHOLD = 4.0;
    private static final long POPULAR_INSTALL_THRESHOLD = 1000L;
    private static final int INACTIVE_DAYS_THRESHOLD = 30;
    
    @Override
    @Transactional
    @CacheEvict(value = {"bot-list", "popular-bots"}, allEntries = true)
    public Bot createBot(Bot bot) {
        log.debug("Creating bot: {} by user: {}", bot.getUsername(), bot.getCreatedBy());
        
        // 验证机器人数据
        validateBot(bot);
        
        // 检查用户机器人数量限制
        long userBotCount = botRepository.countByCreatedByAndStatus(bot.getCreatedBy(), Bot.BotStatus.ACTIVE);
        if (userBotCount >= MAX_BOTS_PER_USER) {
            throw new IllegalStateException("User has reached maximum bot limit: " + MAX_BOTS_PER_USER);
        }
        
        // 检查用户名是否已存在
        if (botRepository.existsByUsernameAndStatus(bot.getUsername(), Bot.BotStatus.ACTIVE)) {
            throw new BusinessException(BotErrorCode.BOT_ALREADY_EXISTS);
        }
        
        // 生成机器人Token
        bot.setBotToken(generateBotToken());

        // 设置默认值
        if (bot.getStatus() == null) {
            bot.setStatus(Bot.BotStatus.ACTIVE);
        }
        if (bot.getBotType() == null) {
            bot.setBotType(Bot.BotType.CUSTOM);
        }
        if (bot.getInstallCount() == null) {
            bot.setInstallCount(0L);
        }
        if (bot.getRating() == null) {
            bot.setRating(BigDecimal.ZERO);
        }
        
        Bot savedBot = botRepository.save(bot);
        
        // 异步初始化机器人
        initializeBotAsync(savedBot);
        
        log.debug("Created bot with ID: {} and token: {}", savedBot.getId(), savedBot.getBotToken());
        return savedBot;
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-list", "popular-bots"}, allEntries = true)
    public Bot createBot(String userId, String username, String displayName, String description) {
        Bot bot = new Bot();
        bot.setUsername(username);
        bot.setDisplayName(displayName);
        bot.setDescription(description);
        bot.setOwnerId(userId);
        bot.setCreatedBy(userId);
        bot.setBotToken(generateBotToken());
        bot.setStatus(Bot.BotStatus.ACTIVE);
        bot.setIsPublic(false);
        bot.setIsVerified(false);

        return createBot(bot);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-list"}, allEntries = true)
    public Bot updateBot(Long botId, Bot botUpdate, String updatedBy) {
        Bot existingBot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!existingBot.getCreatedBy().equals(updatedBy) && !isAdminUser(updatedBy)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }
        
        // 更新允许的字段
        if (StringUtils.hasText(botUpdate.getDisplayName())) {
            existingBot.setDisplayName(botUpdate.getDisplayName());
        }
        if (StringUtils.hasText(botUpdate.getDescription())) {
            existingBot.setDescription(botUpdate.getDescription());
        }
        if (StringUtils.hasText(botUpdate.getAvatarUrl())) {
            existingBot.setAvatarUrl(botUpdate.getAvatarUrl());
        }
        if (StringUtils.hasText(botUpdate.getWebhookUrl())) {
            existingBot.setWebhookUrl(botUpdate.getWebhookUrl());
        }
        if (botUpdate.getCanJoinGroups() != null) {
            existingBot.setCanJoinGroups(botUpdate.getCanJoinGroups());
        }
        if (botUpdate.getCanReadAllGroupMessages() != null) {
            existingBot.setCanReadAllGroupMessages(botUpdate.getCanReadAllGroupMessages());
        }
        if (botUpdate.getSupportsInlineQueries() != null) {
            existingBot.setSupportsInlineQueries(botUpdate.getSupportsInlineQueries());
        }
        if (botUpdate.getTags() != null) {
            existingBot.setTags(botUpdate.getTags());
        }
        
        Bot updatedBot = botRepository.save(existingBot);
        log.debug("Updated bot: {}", botId);
        
        return updatedBot;
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-list"}, allEntries = true)
    public Bot updateBot(Long botId, String userId, String displayName, String description, String avatarUrl, String webhookUrl, List<String> tags) {
        Bot existingBot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!existingBot.getCreatedBy().equals(userId) && !isAdminUser(userId)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        // 更新字段
        if (StringUtils.hasText(displayName)) {
            existingBot.setDisplayName(displayName);
        }
        if (StringUtils.hasText(description)) {
            existingBot.setDescription(description);
        }
        if (StringUtils.hasText(avatarUrl)) {
            existingBot.setAvatarUrl(avatarUrl);
        }
        if (StringUtils.hasText(webhookUrl)) {
            existingBot.setWebhookUrl(webhookUrl);
        }
        if (tags != null) {
            existingBot.setTags(tags);
        }

        Bot updatedBot = botRepository.save(existingBot);
        log.debug("Updated bot: {}", botId);

        return updatedBot;
    }

    @Override
    @Cacheable(value = "bot-details", key = "#botId")
    public Optional<Bot> getBotById(Long botId) {
        return botRepository.findById(botId);
    }
    
    @Override
    @Cacheable(value = "bot-by-username", key = "#username")
    public Optional<Bot> getBotByUsername(String username) {
        return botRepository.findByUsernameAndStatus(username, Bot.BotStatus.ACTIVE);
    }
    
    @Override
    @Cacheable(value = "bot-by-token", key = "#botToken")
    public Optional<Bot> getBotByToken(String botToken) {
        return botRepository.findByBotTokenAndStatus(botToken, Bot.BotStatus.ACTIVE);
    }


    
    @Override
    @Cacheable(value = "user-bots", key = "#userId")
    public List<Bot> getUserBots(String userId) {
        return botRepository.findByCreatedByAndStatusOrderByCreatedAtDesc(userId, Bot.BotStatus.ACTIVE);
    }

    @Override
    @Cacheable(value = "user-bots-page", key = "#userId + '_' + #page + '_' + #size")
    public Page<Bot> getUserBots(String userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return botRepository.findByCreatedByAndStatusOrderByCreatedAtDesc(userId, Bot.BotStatus.ACTIVE)
                .stream()
                .collect(Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> new PageImpl<>(
                        list.stream().skip((long) page * size).limit(size).collect(Collectors.toList()),
                        pageable,
                        list.size()
                    )
                ));
    }
    
    @Override
    @Cacheable(value = "public-bots", unless = "#result.isEmpty()")
    public Page<Bot> getPublicBots(Pageable pageable) {
        return botRepository.findByIsPublicTrueAndStatusOrderByInstallCountDesc(Bot.BotStatus.ACTIVE, pageable);
    }

    @Override
    @Cacheable(value = "public-bots-page", key = "#page + '_' + #size")
    public Page<Bot> getPublicBots(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return getPublicBots(pageable);
    }
    
    @Override
    @Cacheable(value = "popular-bots", unless = "#result.isEmpty()")
    public List<Bot> getPopularBots(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return botRepository.findPopularBots(Bot.BotStatus.ACTIVE, POPULAR_INSTALL_THRESHOLD, pageable)
            .getContent();
    }
    
    @Override
    @Cacheable(value = "high-rated-bots", unless = "#result.isEmpty()")
    public List<Bot> getHighRatedBots(int limit) {
        return botRepository.findHighRatedBots(BigDecimal.valueOf(HIGH_RATING_THRESHOLD))
            .stream()
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    @Override
    @Cacheable(value = "bots-by-type", key = "#botType")
    public List<Bot> getBotsByType(Bot.BotType botType) {
        return botRepository.findByBotTypeAndStatusOrderByInstallCountDesc(botType, Bot.BotStatus.ACTIVE);
    }
    
    @Override
    @Cacheable(value = "bot-search", key = "#keyword + '_' + #pageable.pageNumber")
    public Page<BotDTO.BotInfoDTO> searchBots(String keyword, Pageable pageable) {
        log.debug("搜索机器人: keyword={}", keyword);

        Page<Bot> bots;
        if (!StringUtils.hasText(keyword)) {
            bots = botRepository.findByStatusAndIsPublic(Bot.BotStatus.ACTIVE, true, pageable);
        } else {
            bots = botRepository.findByDisplayNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
                    keyword, keyword, pageable);
        }

        return bots.map(bot -> BotDTO.BotInfoDTO.fromEntity(bot));
    }

    @Override
    @Cacheable(value = "bot-search-page", key = "#keyword + '_' + #page + '_' + #size")
    public Page<BotDTO.BotInfoDTO> searchBots(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return searchBots(keyword, pageable);
    }

    @Override
    @Cacheable(value = "bots-by-tags", key = "#tags.hashCode()")
    public List<Bot> getBotsByTags(List<String> tags) {
        return botRepository.findBotsByTags(tags, Bot.BotStatus.ACTIVE);
    }

    /**
     * 验证机器人数据
     */
    private void validateBot(Bot bot) {
        if (!StringUtils.hasText(bot.getUsername())) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_USERNAME);
        }
        if (!StringUtils.hasText(bot.getDisplayName())) {
            throw new BusinessException(BotErrorCode.INVALID_BOT_DISPLAY_NAME);
        }
    }

    /**
     * 生成机器人Token
     */
    private String generateBotToken() {
        return "BOT_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "");
    }



    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-list"}, allEntries = true)
    public void activateBot(Long botId, String activatedBy) {
        log.debug("Activating bot: {} by user: {}", botId, activatedBy);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(activatedBy) && !isAdminUser(activatedBy)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        bot.setStatus(Bot.BotStatus.ACTIVE);
        bot.setLastActiveAt(Instant.now());
        botRepository.save(bot);

        log.info("Activated bot: {}", botId);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-list"}, allEntries = true)
    public void suspendBot(Long botId, String suspendedBy, String reason) {
        log.debug("Suspending bot: {} by user: {} with reason: {}", botId, suspendedBy, reason);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(suspendedBy) && !isAdminUser(suspendedBy)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        bot.setStatus(Bot.BotStatus.SUSPENDED);
        botRepository.save(bot);

        log.info("Suspended bot: {} with reason: {}", botId, reason);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-list"}, allEntries = true)
    public void deleteBot(Long botId, String deletedBy) {
        log.debug("Deleting bot: {} by user: {}", botId, deletedBy);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(deletedBy) && !isAdminUser(deletedBy)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        bot.setStatus(Bot.BotStatus.DELETED);
        botRepository.save(bot);

        log.info("Deleted bot: {}", botId);
    }

    @Override
    @Transactional
    public void incrementInstallCount(Long botId) {
        log.debug("Incrementing install count for bot: {}", botId);
        botRepository.incrementInstallCount(botId);
    }

    @Override
    @Transactional
    public void decrementInstallCount(Long botId) {
        log.debug("Decrementing install count for bot: {}", botId);
        botRepository.decrementInstallCount(botId);
    }

    @Override
    public boolean validateBotToken(String botToken) {
        if (!StringUtils.hasText(botToken)) {
            return false;
        }

        Optional<Bot> bot = botRepository.findByBotTokenAndStatus(botToken, Bot.BotStatus.ACTIVE);
        return bot.isPresent();
    }

    @Override
    @Transactional
    @CacheEvict(value = {"bot-details", "bot-by-token"}, allEntries = true)
    public String regenerateBotToken(Long botId, String requestedBy) {
        log.debug("Regenerating bot token for bot: {} by user: {}", botId, requestedBy);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(requestedBy) && !isAdminUser(requestedBy)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        String newToken = generateBotToken();
        bot.setBotToken(newToken);
        botRepository.save(bot);

        log.info("Regenerated bot token for bot: {}", botId);
        return newToken;
    }

    @Override
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Transactional
    public void scheduledCleanupInactiveBots() {
        log.info("Starting scheduled cleanup of inactive bots");

        try {
            Instant threshold = Instant.now().minus(INACTIVE_DAYS_THRESHOLD, ChronoUnit.DAYS);
            List<Bot> inactiveBots = botRepository.findInactiveBots(threshold);

            for (Bot bot : inactiveBots) {
                if (bot.getInstallCount() == null || bot.getInstallCount() == 0) {
                    // 如果机器人没有安装，可以考虑暂停
                    bot.setStatus(Bot.BotStatus.SUSPENDED);
                    log.debug("Suspended inactive bot: {}", bot.getId());
                }
            }

            if (!inactiveBots.isEmpty()) {
                botRepository.saveAll(inactiveBots);
                log.info("Cleaned up {} inactive bots", inactiveBots.size());
            }
        } catch (Exception e) {
            log.error("Error in scheduled bot cleanup: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步初始化机器人
     */
    @Async
    private void initializeBotAsync(Bot bot) {
        try {
            log.debug("Initializing bot asynchronously: {}", bot.getId());
            // 这里可以添加机器人初始化逻辑
            // 例如：设置默认命令、初始化配置等
        } catch (Exception e) {
            log.error("Error initializing bot {}: {}", bot.getId(), e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void setWebhook(Long botId, String userId, String webhookUrl, String secretToken, List<String> allowedUpdates) {
        log.debug("Setting webhook for bot: {} by user: {}", botId, userId);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(userId) && !isAdminUser(userId)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        // TODO: 实现Webhook设置逻辑
        log.info("Set webhook for bot: {} to URL: {}", botId, webhookUrl);
    }

    @Override
    public List<BotCommand> getBotCommands(Long botId) {
        log.debug("Getting commands for bot: {}", botId);
        // TODO: 实现获取机器人命令逻辑
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public BotCommand addCommand(Long botId, String userId, String command, String description, CommandScope scope, String languageCode) {
        log.debug("Adding command {} for bot: {} by user: {}", command, botId, userId);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // 验证权限
        if (!bot.getCreatedBy().equals(userId) && !isAdminUser(userId)) {
            throw new BusinessException(BotErrorCode.BOT_ACCESS_DENIED);
        }

        // TODO: 实现添加命令逻辑
        log.info("Added command {} for bot: {}", command, botId);
        return null;
    }

    @Override
    @Transactional
    public void deleteCommand(String commandId, String userId) {
        log.debug("Deleting command: {} by user: {}", commandId, userId);
        // TODO: 实现删除命令逻辑
        log.info("Deleted command: {}", commandId);
    }

    @Override
    @Transactional
    public BotInstallation installBot(Long botId, String userId, InstallationType installationType, String targetId) {
        log.debug("Installing bot: {} by user: {} with type: {}", botId, userId, installationType);

        Bot bot = botRepository.findById(botId)
            .orElseThrow(() -> new BusinessException(BotErrorCode.BOT_NOT_FOUND));

        // TODO: 实现安装机器人逻辑
        log.info("Installed bot: {} for user: {}", botId, userId);
        return null;
    }

    @Override
    @Transactional
    public void uninstallBot(Long botId, String userId, InstallationType installationType, String targetId) {
        log.debug("Uninstalling bot: {} by user: {} with type: {}", botId, userId, installationType);
        // TODO: 实现卸载机器人逻辑
        log.info("Uninstalled bot: {} for user: {}", botId, userId);
    }

    @Override
    public List<BotInstallation> getUserInstalledBots(String userId) {
        log.debug("Getting installed bots for user: {}", userId);
        // TODO: 实现获取用户安装的机器人逻辑
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getBotStatistics() {
        log.debug("Getting bot statistics");

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalBots", 0L);
        statistics.put("activeBots", 0L);
        statistics.put("publicBots", 0L);
        statistics.put("verifiedBots", 0L);
        statistics.put("averageRating", 0.0);
        statistics.put("totalInstalls", 0L);

        // TODO: 实现真实的统计逻辑
        return statistics;
    }

    @Override
    public Page<Bot> getActiveBots(Pageable pageable) {
        log.debug("Getting active bots with pageable: {}", pageable);
        return botRepository.findByStatusAndIsPublic(Bot.BotStatus.ACTIVE, true, pageable);
    }

    @Override
    @Transactional
    public void reviewBot(Long id, String action, String reason) {
        log.debug("Reviewing bot: {} with action: {} and reason: {}", id, action, reason);
        // TODO: 实现机器人审核逻辑
        log.info("Reviewed bot: {} with action: {}", id, action);
    }

    @Override
    public Object testBot(Long id, String message) {
        log.debug("Testing bot: {} with message: {}", id, message);
        // TODO: 实现机器人测试逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("response", "Test response");
        return result;
    }

    @Override
    public String exportBots(Map<String, Object> params) {
        log.debug("Exporting bots with params: {}", params);
        // TODO: 实现机器人导出逻辑
        return "export-url";
    }

    @Override
    public List<Map<String, Object>> getBotUsageTrends(String startDate, String endDate) {
        log.debug("Getting bot usage trends from {} to {}", startDate, endDate);
        // TODO: 实现使用趋势统计逻辑
        return new ArrayList<>();
    }

    /**
     * 检查是否为管理员用户
     */
    private boolean isAdminUser(String userId) {
        // 简单实现，实际应该检查用户角色
        return false;
    }
}
