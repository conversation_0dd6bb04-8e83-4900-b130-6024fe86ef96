package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.OfficialAccountErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.dto.OfficialAccountDTO;
import com.implatform.realtime.entity.AccountFollower;
import com.implatform.realtime.entity.OfficialAccount;
import com.implatform.realtime.repository.AccountFollowerRepository;
import com.implatform.realtime.repository.OfficialAccountRepository;
import com.implatform.service.OfficialAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公众号服务实现
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class OfficialAccountServiceImpl implements OfficialAccountService {

    private final OfficialAccountRepository officialAccountRepository;
    private final AccountFollowerRepository accountFollowerRepository;

    @Override
    @Transactional
    public OfficialAccountDTO createAccount(OfficialAccountDTO.CreateAccountRequest request, String ownerId) {
        log.info("创建公众号: accountName={}, accountCode={}, ownerId={}", 
                request.getAccountName(), request.getAccountCode(), ownerId);

        try {
            // 检查账号代码是否已存在
            if (officialAccountRepository.existsByAccountCode(request.getAccountCode())) {
                throw new BusinessException(OfficialAccountErrorCode.ACCOUNT_ALREADY_EXISTS);
            }

            // 创建公众号实体
            OfficialAccount account = new OfficialAccount();
            account.setAccountName(request.getAccountName());
            account.setAccountCode(request.getAccountCode());
            account.setAccountType(OfficialAccount.AccountType.SUBSCRIPTION); // 默认为订阅号
            account.setDescription(request.getDescription());
            account.setAvatarUrl(request.getAvatarUrl());
            account.setOwnerId(ownerId);
            account.setCreatedBy(ownerId);
            account.setUpdatedBy(ownerId);

            // 保存到数据库
            account = officialAccountRepository.save(account);

            log.info("公众号创建成功: accountId={}, accountCode={}", account.getId(), account.getAccountCode());
            return convertToDTO(account);

        } catch (Exception e) {
            log.error("创建公众号失败: accountName={}, ownerId={}", request.getAccountName(), ownerId, e);
            throw e;
        }
    }

    @Override
    @Transactional
    public OfficialAccountDTO updateAccount(String accountId, OfficialAccountDTO.UpdateAccountRequest request, String operatorId) {
        log.info("更新公众号: accountId={}, operatorId={}", accountId, operatorId);

        try {
            OfficialAccount account = officialAccountRepository.findById(accountId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

            // 检查权限
            if (!account.getOwnerId().equals(operatorId)) {
                throw new BusinessException(OfficialAccountErrorCode.ACCOUNT_ACCESS_DENIED);
            }

            // 更新字段
            if (request.getAccountName() != null) {
                account.setAccountName(request.getAccountName().trim());
            }
            if (request.getDescription() != null) {
                account.setDescription(request.getDescription().trim());
            }
            if (request.getAvatarUrl() != null) {
                account.setAvatarUrl(request.getAvatarUrl());
            }
            // Note: welcomeMessage and autoReplyEnabled are not available in UpdateAccountRequest

            account.setUpdatedBy(operatorId);
            account = officialAccountRepository.save(account);

            log.info("公众号更新成功: accountId={}", accountId);
            return convertToDTO(account);

        } catch (Exception e) {
            log.error("更新公众号失败: accountId={}, operatorId={}", accountId, operatorId, e);
            throw e;
        }
    }

    @Override
    public OfficialAccountDTO getAccountInfo(String accountId) {
        log.debug("获取公众号详情: accountId={}", accountId);

        OfficialAccount account = officialAccountRepository.findById(accountId)
                .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

        return convertToDTO(account);
    }

    @Override
    public OfficialAccountDTO getAccountByCode(String accountCode) {
        log.debug("根据代码获取公众号: accountCode={}", accountCode);

        OfficialAccount account = officialAccountRepository.findByAccountCode(accountCode)
                .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

        return convertToDTO(account);
    }

    @Override
    public List<OfficialAccountDTO> getUserAccounts(String ownerId) {
        log.debug("获取用户公众号列表: ownerId={}", ownerId);

        List<OfficialAccount> accounts = officialAccountRepository.findByOwnerIdAndStatusOrderByCreatedAtDesc(
                ownerId, OfficialAccount.AccountStatus.ACTIVE);

        return accounts.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<OfficialAccountDTO> searchAccounts(String keyword, Pageable pageable) {
        log.debug("搜索公众号: keyword={}", keyword);

        Page<OfficialAccount> accounts = officialAccountRepository.searchAccounts(
                keyword, OfficialAccount.AccountStatus.ACTIVE, pageable);

        return accounts.map(this::convertToDTO);
    }

    @Override
    public Page<OfficialAccountDTO> getPopularAccounts(Pageable pageable) {
        log.debug("获取热门公众号");

        Page<OfficialAccount> accounts = officialAccountRepository.findPopularAccounts(
                OfficialAccount.AccountStatus.ACTIVE, 1000L, pageable);

        return accounts.map(this::convertToDTO);
    }

    @Override
    public Page<OfficialAccountDTO> getLatestAccounts(Pageable pageable) {
        log.debug("获取最新公众号");

        Page<OfficialAccount> accounts = officialAccountRepository.findByStatusOrderByCreatedAtDesc(
                OfficialAccount.AccountStatus.ACTIVE, pageable);

        return accounts.map(this::convertToDTO);
    }

    @Override
    @Transactional
    public OfficialAccountDTO.FollowResultDTO followAccount(String accountId, String userId, String followSource) {
        log.info("关注公众号: accountId={}, userId={}, source={}", accountId, userId, followSource);

        try {
            // 检查公众号是否存在
            OfficialAccount account = officialAccountRepository.findById(accountId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

            if (!account.isActive()) {
                throw new BusinessException(OfficialAccountErrorCode.ACCOUNT_DISABLED);
            }

            // 检查是否已关注
            Optional<AccountFollower> existingFollower = accountFollowerRepository.findByAccountIdAndUserId(accountId, userId);

            AccountFollower follower;
            boolean isNewFollow = false;

            if (existingFollower.isPresent()) {
                follower = existingFollower.get();
                if (follower.isFollowing()) {
                    throw new BusinessException(OfficialAccountErrorCode.ALREADY_FOLLOWING);
                }
                // 重新关注
                follower.follow();
                follower.setFollowSource(followSource);
            } else {
                // 新关注
                follower = new AccountFollower();
                follower.setAccountId(accountId);
                follower.setUserId(userId);
                follower.setFollowSource(followSource);
                follower.follow();
                isNewFollow = true;
            }

            follower = accountFollowerRepository.save(follower);

            // 更新公众号关注者数量
            if (isNewFollow || !existingFollower.get().isFollowing()) {
                account.incrementFollowerCount();
                officialAccountRepository.save(account);
            }

            log.info("关注成功: accountId={}, userId={}", accountId, userId);

            OfficialAccountDTO.FollowResultDTO result = new OfficialAccountDTO.FollowResultDTO();
            result.setSuccess(true);
            result.setMessage("关注成功");
            result.setFollowerCount(account.getFollowerCount());
            return result;

        } catch (Exception e) {
            log.error("关注公众号失败: accountId={}, userId={}", accountId, userId, e);

            OfficialAccountDTO.FollowResultDTO result = new OfficialAccountDTO.FollowResultDTO();
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            return result;
        }
    }

    @Override
    @Transactional
    public OfficialAccountDTO.UnfollowResultDTO unfollowAccount(String accountId, String userId) {
        log.info("取消关注公众号: accountId={}, userId={}", accountId, userId);

        try {
            AccountFollower follower = accountFollowerRepository.findByAccountIdAndUserId(accountId, userId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.FOLLOWER_NOT_FOUND));

            if (!follower.isFollowing()) {
                throw new BusinessException(OfficialAccountErrorCode.NOT_FOLLOWING);
            }

            // 取消关注
            follower.unfollow();
            accountFollowerRepository.save(follower);

            // 更新公众号关注者数量
            OfficialAccount account = officialAccountRepository.findById(accountId).orElse(null);
            if (account != null) {
                account.decrementFollowerCount();
                officialAccountRepository.save(account);
            }

            log.info("取消关注成功: accountId={}, userId={}", accountId, userId);

            OfficialAccountDTO.UnfollowResultDTO result = new OfficialAccountDTO.UnfollowResultDTO();
            result.setSuccess(true);
            result.setMessage("取消关注成功");
            result.setFollowerCount(account != null ? account.getFollowerCount() : 0L);
            return result;

        } catch (Exception e) {
            log.error("取消关注公众号失败: accountId={}, userId={}", accountId, userId, e);

            OfficialAccountDTO.UnfollowResultDTO result = new OfficialAccountDTO.UnfollowResultDTO();
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            return result;
        }
    }

    @Override
    public boolean isFollowing(String accountId, String userId) {
        return accountFollowerRepository.existsByAccountIdAndUserIdAndStatus(
                accountId, userId, AccountFollower.FollowStatus.FOLLOWING);
    }

    @Override
    public Page<OfficialAccountDTO.FollowerDTO> getFollowers(String accountId, Pageable pageable) {
        log.debug("获取公众号关注者列表: accountId={}", accountId);

        Page<AccountFollower> followers = accountFollowerRepository.findByAccountIdAndStatusOrderByFollowedAtDesc(
                accountId, AccountFollower.FollowStatus.FOLLOWING, pageable);

        return followers.map(this::convertToFollowerDTO);
    }

    @Override
    public Page<OfficialAccountDTO> getFollowedAccounts(String userId, Pageable pageable) {
        log.debug("获取用户关注的公众号列表: userId={}", userId);

        Page<AccountFollower> followers = accountFollowerRepository.findByUserIdAndStatusOrderByFollowedAtDesc(
                userId, AccountFollower.FollowStatus.FOLLOWING, pageable);

        return followers.map(follower -> {
            OfficialAccountDTO dto = convertToDTO(follower.getOfficialAccount());
            dto.setIsFollowing(true);
            dto.setFollowedAt(follower.getFollowedAt());
            return dto;
        });
    }

    @Override
    @Transactional
    public boolean applyVerification(String accountId, String verificationInfo, String operatorId) {
        log.info("申请公众号认证: accountId={}, operatorId={}", accountId, operatorId);

        try {
            OfficialAccount account = officialAccountRepository.findById(accountId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

            // 检查权限
            if (!account.getOwnerId().equals(operatorId)) {
                throw new BusinessException(OfficialAccountErrorCode.ACCOUNT_ACCESS_DENIED);
            }

            // 检查当前状态
            if (account.getVerificationStatus() == OfficialAccount.VerificationStatus.VERIFIED) {
                throw new BusinessException(OfficialAccountErrorCode.OPERATION_NOT_ALLOWED);
            }

            if (account.getVerificationStatus() == OfficialAccount.VerificationStatus.PENDING) {
                throw new BusinessException(OfficialAccountErrorCode.OPERATION_NOT_ALLOWED);
            }

            // 更新认证状态
            officialAccountRepository.updateVerificationStatus(
                    accountId,
                    OfficialAccount.VerificationStatus.PENDING,
                    verificationInfo,
                    Instant.now());

            log.info("认证申请提交成功: accountId={}", accountId);
            return true;

        } catch (Exception e) {
            log.error("申请认证失败: accountId={}, operatorId={}", accountId, operatorId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean reviewVerification(String accountId, boolean approved, String reason, String operatorId) {
        log.info("审核认证申请: accountId={}, approved={}, operatorId={}", accountId, approved, operatorId);

        try {
            OfficialAccount account = officialAccountRepository.findById(accountId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

            if (account.getVerificationStatus() != OfficialAccount.VerificationStatus.PENDING) {
                throw new BusinessException(OfficialAccountErrorCode.OPERATION_NOT_ALLOWED);
            }

            // 更新认证状态
            OfficialAccount.VerificationStatus newStatus = approved ?
                    OfficialAccount.VerificationStatus.VERIFIED :
                    OfficialAccount.VerificationStatus.REJECTED;

            officialAccountRepository.updateVerificationStatus(
                    accountId, newStatus, reason, Instant.now());

            log.info("认证审核完成: accountId={}, approved={}", accountId, approved);
            return true;

        } catch (Exception e) {
            log.error("审核认证失败: accountId={}, operatorId={}", accountId, operatorId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean setMenu(String accountId, String menuConfig, String operatorId) {
        log.info("设置公众号菜单: accountId={}, operatorId={}", accountId, operatorId);

        try {
            OfficialAccount account = officialAccountRepository.findById(accountId)
                    .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

            // 检查权限
            if (!account.getOwnerId().equals(operatorId)) {
                throw new BusinessException(OfficialAccountErrorCode.ACCOUNT_ACCESS_DENIED);
            }

            // 更新菜单配置
            officialAccountRepository.updateMenuConfig(accountId, menuConfig, Instant.now());

            log.info("菜单设置成功: accountId={}", accountId);
            return true;

        } catch (Exception e) {
            log.error("设置菜单失败: accountId={}, operatorId={}", accountId, operatorId, e);
            return false;
        }
    }

    @Override
    public String getMenuConfig(String accountId) {
        log.debug("获取公众号菜单配置: accountId={}", accountId);

        OfficialAccount account = officialAccountRepository.findById(accountId)
                .orElseThrow(() -> new BusinessException(OfficialAccountErrorCode.ACCOUNT_NOT_FOUND));

        return account.getMenuConfig();
    }

    @Override
    public boolean isAccountCodeAvailable(String accountCode) {
        return !officialAccountRepository.existsByAccountCode(accountCode);
    }

    @Override
    public String generateQRCode(String accountId) {
        log.info("生成公众号二维码: accountId={}", accountId);

        // 简化实现，实际应该调用二维码生成服务
        return "https://qr.example.com/account/" + accountId;
    }

    // 添加缺失的方法实现
    @Override
    @Transactional
    public void suspendAccount(String accountId, String reason, String operatorId) {
        log.info("暂停公众号: accountId={}, reason={}, operatorId={}", accountId, reason, operatorId);
        // 实现暂停逻辑
    }

    @Override
    @Transactional
    public void resumeAccount(String accountId, String operatorId) {
        log.info("恢复公众号: accountId={}, operatorId={}", accountId, operatorId);
        // 实现恢复逻辑
    }

    @Override
    @Transactional
    public void deleteAccount(String accountId, String operatorId) {
        log.info("删除公众号: accountId={}, operatorId={}", accountId, operatorId);
        // 实现删除逻辑
    }

    @Override
    public Map<String, Object> getAccountStatistics(String accountId) {
        log.debug("获取公众号统计信息: accountId={}", accountId);
        // 实现统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("followerCount", 0L);
        stats.put("articleCount", 0L);
        stats.put("readCount", 0L);
        return stats;
    }

    @Override
    public Map<String, Object> getPlatformStatistics() {
        log.debug("获取平台统计信息");
        // 实现平台统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalAccounts", 0L);
        stats.put("activeAccounts", 0L);
        stats.put("totalFollowers", 0L);
        return stats;
    }

    @Override
    public Map<String, Object> getFollowerStatistics(String accountId) {
        log.debug("获取关注者统计信息: accountId={}", accountId);
        // 实现关注者统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalFollowers", 0L);
        stats.put("activeFollowers", 0L);
        stats.put("newFollowers", 0L);
        return stats;
    }

    @Override
    public List<Map<String, Object>> getFollowTrend(String accountId, int days) {
        log.debug("获取关注趋势数据: accountId={}, days={}", accountId, days);
        // 实现趋势数据逻辑
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public boolean tagFollowers(String accountId, List<String> userIds, String tags, String operatorId) {
        log.info("批量标记关注者: accountId={}, userCount={}, operatorId={}", accountId, userIds.size(), operatorId);
        // 实现标记逻辑
        return true;
    }

    @Override
    public Page<OfficialAccountDTO.FollowerDTO> getActiveFollowers(String accountId, int days, Pageable pageable) {
        log.debug("获取活跃关注者: accountId={}, days={}", accountId, days);

        Instant sinceTime = Instant.now().minus(days, ChronoUnit.DAYS);
        Page<AccountFollower> followers = accountFollowerRepository.findActiveFollowers(
                accountId, AccountFollower.FollowStatus.FOLLOWING, sinceTime, pageable);

        return followers.map(this::convertToFollowerDTO);
    }

    @Override
    public Page<OfficialAccountDTO.FollowerDTO> getNewFollowers(String accountId, int days, Pageable pageable) {
        log.debug("获取新关注者: accountId={}, days={}", accountId, days);

        Instant sinceTime = Instant.now().minus(days, ChronoUnit.DAYS);
        Page<AccountFollower> followers = accountFollowerRepository.findNewFollowers(
                accountId, AccountFollower.FollowStatus.FOLLOWING, sinceTime, pageable);

        return followers.map(this::convertToFollowerDTO);
    }

    /**
     * 转换为DTO
     */
    private OfficialAccountDTO convertToDTO(OfficialAccount account) {
        OfficialAccountDTO dto = new OfficialAccountDTO();
        dto.setId(account.getId());
        dto.setAccountName(account.getAccountName());
        dto.setAccountCode(account.getAccountCode());
        dto.setAccountType(account.getAccountType().name());
        dto.setDescription(account.getDescription());
        dto.setAvatarUrl(account.getAvatarUrl());
        dto.setQrCodeUrl(account.getQrCodeUrl());
        dto.setOwnerId(account.getOwnerId());
        dto.setVerificationStatus(account.getVerificationStatus().name());
        dto.setVerificationInfo(account.getVerificationInfo());
        dto.setStatus(account.getStatus().name());
        dto.setFollowerCount(account.getFollowerCount());
        dto.setArticleCount(account.getArticleCount());
        dto.setTotalReadCount(account.getTotalReadCount());
        dto.setWelcomeMessage(account.getWelcomeMessage());
        dto.setAutoReplyEnabled(account.getAutoReplyEnabled());
        dto.setMenuConfig(account.getMenuConfig());
        dto.setCreatedAt(account.getCreatedAt());
        dto.setUpdatedAt(account.getUpdatedAt());
        dto.setCreatedBy(account.getCreatedBy());
        dto.setUpdatedBy(account.getUpdatedBy());
        return dto;
    }

    /**
     * 转换为关注者DTO
     */
    private OfficialAccountDTO.FollowerDTO convertToFollowerDTO(AccountFollower follower) {
        OfficialAccountDTO.FollowerDTO dto = new OfficialAccountDTO.FollowerDTO();
        dto.setId(follower.getId());
        dto.setUserId(follower.getUserId());
        dto.setStatus(follower.getStatus().name());
        dto.setFollowedAt(follower.getFollowedAt());
        dto.setFollowSource(follower.getFollowSource());
        // Convert String tags to List<String>
        String tags = follower.getUserTags();
        if (tags != null && !tags.isEmpty()) {
            dto.setUserTags(Arrays.asList(tags.split(",")));
        } else {
            dto.setUserTags(new ArrayList<>());
        }
        dto.setRemark(follower.getRemark());
        dto.setLastInteractionAt(follower.getLastInteractionAt());
        dto.setInteractionCount(follower.getInteractionCount());
        dto.setReceivePush(follower.getReceivePush());
        return dto;
    }
}
