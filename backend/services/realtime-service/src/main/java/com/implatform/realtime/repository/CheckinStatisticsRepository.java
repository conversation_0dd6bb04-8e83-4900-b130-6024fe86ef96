package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CheckinStatistics;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * 签到统计Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CheckinStatisticsRepository extends R2dbcRepository<CheckinStatistics, Long> {

    /**
     * 根据用户ID查找签到统计
     */
    @Query("SELECT * FROM checkin_statistics WHERE user_id = :userId")
    Mono<CheckinStatistics> findByUserId(@Param("userId") Long userId);

    /**
     * 获取排行榜数据（按总签到天数）
     */
    @Query("SELECT * FROM checkin_statistics ORDER BY total_checkin_days DESC, total_reward_coins DESC")
    Flux<CheckinStatistics> findTopByTotalCheckinDays();

    /**
     * 获取排行榜数据（按连续签到天数）
     */
    @Query("SELECT * FROM checkin_statistics ORDER BY current_continuous_days DESC, total_checkin_days DESC")
    Flux<CheckinStatistics> findTopByContinuousDays();

    /**
     * 获取排行榜数据（按最大连续签到天数）
     */
    @Query("SELECT * FROM checkin_statistics ORDER BY max_continuous_days DESC, total_checkin_days DESC")
    Flux<CheckinStatistics> findTopByMaxContinuousDays();

    /**
     * 统计总用户数
     */
    @Query("SELECT COUNT(*) FROM checkin_statistics WHERE total_checkin_days > 0")
    Mono<Long> countActiveUsers();

    /**
     * 统计今日签到用户数
     */
    @Query("SELECT COUNT(s) FROM CheckinStatistics s WHERE s.lastCheckinDate = :date")
    long countTodayCheckinUsers(@Param("date") LocalDate date);

    /**
     * 获取用户排名（按总签到天数）
     */
    @Query("SELECT COUNT(s) + 1 FROM CheckinStatistics s WHERE s.totalCheckinDays > " +
           "(SELECT s2.totalCheckinDays FROM CheckinStatistics s2 WHERE s2.userId = :userId)")
    Integer getUserRankByTotalDays(@Param("userId") Long userId);

    /**
     * 获取用户排名（按连续签到天数）
     */
    @Query("SELECT COUNT(s) + 1 FROM CheckinStatistics s WHERE s.currentContinuousDays > " +
           "(SELECT s2.currentContinuousDays FROM CheckinStatistics s2 WHERE s2.userId = :userId)")
    Integer getUserRankByContinuousDays(@Param("userId") Long userId);

    /**
     * 获取连续签到天数大于指定值的用户数
     */
    @Query("SELECT COUNT(s) FROM CheckinStatistics s WHERE s.currentContinuousDays >= :days")
    long countUsersByContinuousDays(@Param("days") int days);

    /**
     * 获取总签到天数大于指定值的用户数
     */
    @Query("SELECT COUNT(s) FROM CheckinStatistics s WHERE s.totalCheckinDays >= :days")
    long countUsersByTotalDays(@Param("days") int days);

    /**
     * 获取平均签到天数
     */
    @Query("SELECT AVG(s.totalCheckinDays) FROM CheckinStatistics s WHERE s.totalCheckinDays > 0")
    Double getAverageTotalDays();

    /**
     * 获取平均连续签到天数
     */
    @Query("SELECT AVG(s.currentContinuousDays) FROM CheckinStatistics s WHERE s.currentContinuousDays > 0")
    Double getAverageContinuousDays();

    /**
     * 获取总金币数统计
     */
    @Query("SELECT SUM(s.totalRewardCoins) FROM CheckinStatistics s")
    Long getTotalRewardCoins();

    /**
     * 获取需要重置连续天数的用户（昨天未签到）
     */
    @Query("SELECT s FROM CheckinStatistics s WHERE s.currentContinuousDays > 0 " +
           "AND s.lastCheckinDate < :yesterday")
    List<CheckinStatistics> findUsersNeedResetContinuous(@Param("yesterday") LocalDate yesterday);

    /**
     * 批量重置连续签到天数
     */
    @Query("UPDATE CheckinStatistics s SET s.currentContinuousDays = 0 " +
           "WHERE s.currentContinuousDays > 0 AND s.lastCheckinDate < :yesterday")
    int resetContinuousDaysForInactiveUsers(@Param("yesterday") LocalDate yesterday);
}
