package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Group;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群组数据访问层 - IM平台群组数据库操作接口（R2DBC响应式版本）
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupRepository extends R2dbcRepository<Group, Long> {

    /**
     * 根据创建者ID查找群组
     */
    @Query("SELECT * FROM groups WHERE creator_user_id = :creatorUserId AND group_status = :groupStatus")
    Flux<Group> findByCreatorUserIdAndGroupStatus(@Param("creatorUserId") Long creatorUserId, @Param("groupStatus") String groupStatus);

    /**
     * 根据群组类型查找群组
     */
    @Query("SELECT * FROM groups WHERE group_type = :groupType AND group_status = :groupStatus")
    Flux<Group> findByGroupTypeAndGroupStatus(@Param("groupType") String groupType, @Param("groupStatus") String groupStatus);

    /**
     * 根据关键词模糊查询公开群组
     */
    @Query("SELECT * FROM groups WHERE is_public = true AND group_status = :status " +
           "AND (LOWER(group_name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "LIMIT :limit OFFSET :offset")
    Flux<Group> findPublicGroupsByKeyword(@Param("keyword") String keyword,
                                         @Param("status") String status,
                                         @Param("limit") int limit,
                                         @Param("offset") long offset);

    /**
     * 根据邀请链接查找群组
     */
    @Query("SELECT * FROM groups WHERE invite_link = :inviteLink AND group_status = :groupStatus")
    Mono<Group> findByInviteLinkAndGroupStatus(@Param("inviteLink") String inviteLink, @Param("groupStatus") String groupStatus);

    /**
     * 根据用户名查找公开群组（超级群组特有）
     */
    @Query("SELECT * FROM groups WHERE username = :username AND group_status = :groupStatus")
    Mono<Group> findByUsernameAndGroupStatus(@Param("username") String username, @Param("groupStatus") String groupStatus);

    /**
     * 查找用户创建的群组
     */
    @Query("SELECT * FROM groups WHERE creator_user_id = :userId AND group_status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findGroupsByOwner(@Param("userId") Long userId,
                                 @Param("status") String status,
                                 @Param("limit") int limit,
                                 @Param("offset") long offset);

    /**
     * 统计用户创建的群组数量
     */
    @Query("SELECT COUNT(*) FROM groups WHERE creator_user_id = :creatorUserId AND group_status = :groupStatus")
    Mono<Long> countByCreatorUserIdAndGroupStatus(@Param("creatorUserId") Long creatorUserId, @Param("groupStatus") String groupStatus);

    /**
     * 查找用户加入的群组（通过群组成员表）
     */
    @Query("SELECT g.* FROM groups g JOIN group_members gm ON g.id = gm.group_id " +
           "WHERE gm.user_id = :userId AND gm.status = 'ACTIVE' AND g.group_status = :status " +
           "ORDER BY gm.joined_at DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findGroupsByMember(@Param("userId") Long userId,
                                  @Param("status") String status,
                                  @Param("limit") int limit,
                                  @Param("offset") long offset);

    /**
     * 查找活跃的公开群组
     */
    @Query("SELECT * FROM groups WHERE is_public = true AND group_status = :groupStatus ORDER BY member_count DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findByIsPublicTrueAndGroupStatusOrderByMemberCountDesc(@Param("groupStatus") String groupStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找最近创建的群组
     */
    @Query("SELECT * FROM groups WHERE group_status = :groupStatus ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findByGroupStatusOrderByCreatedAtDesc(@Param("groupStatus") String groupStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找超级群组（按成员数排序）
     */
    @Query("SELECT * FROM groups WHERE group_type = 'SUPER_GROUP' AND group_status = :status " +
           "ORDER BY member_count DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findSuperGroupsByMemberCount(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找已验证的超级群组
     */
    @Query("SELECT * FROM groups WHERE group_type = 'SUPER_GROUP' AND is_verified = true " +
           "AND group_status = :status ORDER BY member_count DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findVerifiedSuperGroups(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找成员数量在指定范围内的群组
     */
    @Query("SELECT * FROM groups WHERE group_status = :status AND member_count BETWEEN :minMembers AND :maxMembers LIMIT :limit OFFSET :offset")
    Flux<Group> findGroupsByMemberCountRange(@Param("status") String status,
                                           @Param("minMembers") Integer minMembers,
                                           @Param("maxMembers") Integer maxMembers,
                                           @Param("limit") int limit,
                                           @Param("offset") long offset);

    /**
     * 更新群组成员数量
     */
    @Modifying
    @Query("UPDATE groups SET member_count = :memberCount, updated_at = :updateTime WHERE id = :groupId")
    Mono<Integer> updateMemberCount(@Param("groupId") Long groupId,
                         @Param("memberCount") Integer memberCount,
                         @Param("updateTime") Instant updateTime);

    /**
     * 增加群组成员数量
     */
    @Modifying
    @Query("UPDATE groups SET member_count = member_count + 1, updated_at = :updateTime WHERE id = :groupId")
    Mono<Integer> incrementMemberCount(@Param("groupId") Long groupId, @Param("updateTime") Instant updateTime);

    /**
     * 减少群组成员数量
     */
    @Modifying
    @Query("UPDATE groups SET member_count = CASE WHEN member_count > 0 THEN member_count - 1 ELSE 0 END, " +
           "updated_at = :updateTime WHERE id = :groupId")
    Mono<Integer> decrementMemberCount(@Param("groupId") Long groupId, @Param("updateTime") Instant updateTime);

    /**
     * 更新群组状态
     */
    @Modifying
    @Query("UPDATE groups SET group_status = :status, updated_at = :updateTime WHERE id = :groupId")
    Mono<Integer> updateGroupStatus(@Param("groupId") Long groupId,
                         @Param("status") String status,
                         @Param("updateTime") Instant updateTime);

    /**
     * 查找需要清理的非活跃群组
     */
    @Query("SELECT * FROM groups WHERE group_status = :status AND updated_at < :beforeTime")
    Flux<Group> findInactiveGroups(@Param("status") String status,
                                  @Param("beforeTime") Instant beforeTime);

    /**
     * 检查群组是否存在且活跃
     */
    @Query("SELECT COUNT(*) > 0 FROM groups WHERE id = :groupId AND group_status = :groupStatus")
    Mono<Boolean> existsByIdAndGroupStatus(@Param("groupId") Long groupId, @Param("groupStatus") String groupStatus);

    /**
     * 更新超级群组统计信息
     */
    @Modifying
    @Query("UPDATE groups SET total_message_count = total_message_count + 1, " +
           "today_message_count = today_message_count + 1, " +
           "last_activity_at = :activityTime, updated_at = :updateTime " +
           "WHERE id = :groupId AND group_type = 'SUPER_GROUP' AND statistics_enabled = true")
    Mono<Integer> updateSuperGroupStatistics(@Param("groupId") Long groupId,
                                  @Param("activityTime") Instant activityTime,
                                  @Param("updateTime") Instant updateTime);

    /**
     * 重置今日消息计数
     */
    @Modifying
    @Query("UPDATE groups SET today_message_count = 0, updated_at = :updateTime " +
           "WHERE group_type = 'SUPER_GROUP' AND statistics_enabled = true")
    Mono<Integer> resetTodayMessageCount(@Param("updateTime") Instant updateTime);

    /**
     * 根据群组状态统计群组数量
     */
    @Query("SELECT COUNT(*) FROM groups WHERE group_status = :status")
    Mono<Long> countByGroupStatus(@Param("status") String status);

    /**
     * 根据状态查找群组并按最后活跃时间排序
     */
    @Query("SELECT * FROM groups WHERE group_status = :status ORDER BY last_activity_at DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findByGroupStatusOrderByLastActivityAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据成员数量查找群组并按成员数量排序
     */
    @Query("SELECT * FROM groups WHERE member_count >= :minMembers ORDER BY member_count DESC LIMIT :limit OFFSET :offset")
    Flux<Group> findByMemberCountGreaterThanEqualOrderByMemberCountDesc(@Param("minMembers") Integer minMembers, @Param("limit") int limit, @Param("offset") long offset);
}
