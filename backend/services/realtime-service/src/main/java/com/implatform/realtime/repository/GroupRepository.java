package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Group;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群组数据访问层 - IM平台群组数据库操作接口（R2DBC响应式版本）
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupRepository extends R2dbcRepository<Group, Long> {

    /**
     * 根据创建者ID查找群组
     */
    List<Group> findByCreatorUserIdAndGroupStatus(Long creatorUserId, Group.GroupStatus groupStatus);

    /**
     * 根据群组类型查找群组
     */
    List<Group> findByGroupTypeAndGroupStatus(Group.GroupType groupType, Group.GroupStatus groupStatus);

    /**
     * 根据关键词模糊查询公开群组
     */
    @Query("SELECT g FROM Group g WHERE g.isPublic = true AND g.groupStatus = :status " +
           "AND (LOWER(g.groupName) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(g.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Group> findPublicGroupsByKeyword(@Param("keyword") String keyword,
                                         @Param("status") Group.GroupStatus status,
                                         Pageable pageable);

    /**
     * 根据邀请链接查找群组
     */
    Optional<Group> findByInviteLinkAndGroupStatus(String inviteLink, Group.GroupStatus groupStatus);

    /**
     * 根据用户名查找公开群组（超级群组特有）
     */
    Optional<Group> findByUsernameAndGroupStatus(String username, Group.GroupStatus groupStatus);

    /**
     * 查找用户创建的群组
     */
    @Query("SELECT g FROM Group g WHERE g.creatorUserId = :userId AND g.groupStatus = :status ORDER BY g.createdAt DESC")
    Page<Group> findGroupsByOwner(@Param("userId") Long userId,
                                 @Param("status") Group.GroupStatus status,
                                 Pageable pageable);

    /**
     * 统计用户创建的群组数量
     */
    long countByCreatorUserIdAndGroupStatus(Long creatorUserId, Group.GroupStatus groupStatus);

    /**
     * 查找用户加入的群组（通过群组成员表）
     */
    @Query("SELECT g FROM Group g JOIN GroupMember gm ON g.id = gm.groupId " +
           "WHERE gm.userId = :userId AND gm.status = com.implatform.realtime.entity.GroupMember$MemberStatus.ACTIVE AND g.groupStatus = :status " +
           "ORDER BY gm.joinedAt DESC")
    Page<Group> findGroupsByMember(@Param("userId") Long userId,
                                  @Param("status") Group.GroupStatus status,
                                  Pageable pageable);

    /**
     * 查找活跃的公开群组
     */
    Page<Group> findByIsPublicTrueAndGroupStatusOrderByMemberCountDesc(Group.GroupStatus groupStatus, Pageable pageable);

    /**
     * 查找最近创建的群组
     */
    Page<Group> findByGroupStatusOrderByCreatedAtDesc(Group.GroupStatus groupStatus, Pageable pageable);

    /**
     * 查找超级群组（按成员数排序）
     */
    @Query("SELECT g FROM Group g WHERE g.groupType = 'SUPER_GROUP' AND g.groupStatus = :status " +
           "ORDER BY g.memberCount DESC")
    Page<Group> findSuperGroupsByMemberCount(@Param("status") Group.GroupStatus status, Pageable pageable);

    /**
     * 查找已验证的超级群组
     */
    @Query("SELECT g FROM Group g WHERE g.groupType = 'SUPER_GROUP' AND g.isVerified = true " +
           "AND g.groupStatus = :status ORDER BY g.memberCount DESC")
    Page<Group> findVerifiedSuperGroups(@Param("status") Group.GroupStatus status, Pageable pageable);

    /**
     * 查找成员数量在指定范围内的群组
     */
    @Query("SELECT g FROM Group g WHERE g.groupStatus = :status AND g.memberCount BETWEEN :minMembers AND :maxMembers")
    Page<Group> findGroupsByMemberCountRange(@Param("status") Group.GroupStatus status,
                                           @Param("minMembers") Integer minMembers,
                                           @Param("maxMembers") Integer maxMembers,
                                           Pageable pageable);

    /**
     * 更新群组成员数量
     */
    @Modifying
    @Query("UPDATE Group g SET g.memberCount = :memberCount, g.updatedAt = :updateTime WHERE g.id = :groupId")
    int updateMemberCount(@Param("groupId") Long groupId,
                         @Param("memberCount") Integer memberCount,
                         @Param("updateTime") Instant updateTime);

    /**
     * 增加群组成员数量
     */
    @Modifying
    @Query("UPDATE Group g SET g.memberCount = g.memberCount + 1, g.updatedAt = :updateTime WHERE g.id = :groupId")
    int incrementMemberCount(@Param("groupId") Long groupId, @Param("updateTime") Instant updateTime);

    /**
     * 减少群组成员数量
     */
    @Modifying
    @Query("UPDATE Group g SET g.memberCount = CASE WHEN g.memberCount > 0 THEN g.memberCount - 1 ELSE 0 END, " +
           "g.updatedAt = :updateTime WHERE g.id = :groupId")
    int decrementMemberCount(@Param("groupId") Long groupId, @Param("updateTime") Instant updateTime);

    /**
     * 更新群组状态
     */
    @Modifying
    @Query("UPDATE Group g SET g.groupStatus = :status, g.updatedAt = :updateTime WHERE g.id = :groupId")
    int updateGroupStatus(@Param("groupId") Long groupId,
                         @Param("status") Group.GroupStatus status,
                         @Param("updateTime") Instant updateTime);

    /**
     * 查找需要清理的非活跃群组
     */
    @Query("SELECT g FROM Group g WHERE g.groupStatus = :status AND g.updatedAt < :beforeTime")
    List<Group> findInactiveGroups(@Param("status") Group.GroupStatus status,
                                  @Param("beforeTime") Instant beforeTime);

    /**
     * 检查群组是否存在且活跃
     */
    boolean existsByIdAndGroupStatus(Long groupId, Group.GroupStatus groupStatus);

    /**
     * 更新超级群组统计信息
     */
    @Modifying
    @Query("UPDATE Group g SET g.totalMessageCount = g.totalMessageCount + 1, " +
           "g.todayMessageCount = g.todayMessageCount + 1, " +
           "g.lastActivityAt = :activityTime, g.updatedAt = :updateTime " +
           "WHERE g.id = :groupId AND g.groupType = 'SUPER_GROUP' AND g.statisticsEnabled = true")
    int updateSuperGroupStatistics(@Param("groupId") Long groupId,
                                  @Param("activityTime") Instant activityTime,
                                  @Param("updateTime") Instant updateTime);

    /**
     * 重置今日消息计数
     */
    @Modifying
    @Query("UPDATE Group g SET g.todayMessageCount = 0, g.updatedAt = :updateTime " +
           "WHERE g.groupType = 'SUPER_GROUP' AND g.statisticsEnabled = true")
    int resetTodayMessageCount(@Param("updateTime") Instant updateTime);

    /**
     * 根据群组状态统计群组数量
     */
    long countByGroupStatus(Group.GroupStatus status);

    /**
     * 根据状态查找群组并按最后活跃时间排序
     */
    Page<Group> findByGroupStatusOrderByLastActivityAtDesc(Group.GroupStatus status, Pageable pageable);

    /**
     * 根据成员数量查找群组并按成员数量排序
     */
    Page<Group> findByMemberCountGreaterThanEqualOrderByMemberCountDesc(Integer minMembers, Pageable pageable);
}
