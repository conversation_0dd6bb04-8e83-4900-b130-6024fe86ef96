package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CoinAccount;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 金币账户Repository接口
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface CoinAccountRepository extends JpaRepository<CoinAccount, Long> {

    /**
     * 根据用户ID查找金币账户
     */
    Optional<CoinAccount> findByUserId(Long userId);

    /**
     * 根据用户ID查找金币账户（加锁）
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT c FROM CoinAccount c WHERE c.userId = :userId")
    Optional<CoinAccount> findByUserIdWithLock(@Param("userId") Long userId);

    /**
     * 检查用户是否有金币账户
     */
    boolean existsByUserId(Long userId);

    /**
     * 获取金币余额大于指定值的用户数
     */
    @Query("SELECT COUNT(c) FROM CoinAccount c WHERE c.availableCoins >= :amount")
    long countUsersByMinBalance(@Param("amount") int amount);

    /**
     * 获取总金币统计
     */
    @Query("SELECT SUM(c.totalCoins) FROM CoinAccount c")
    Long getTotalCoinsInSystem();

    /**
     * 获取可用金币统计
     */
    @Query("SELECT SUM(c.availableCoins) FROM CoinAccount c")
    Long getTotalAvailableCoins();

    /**
     * 获取冻结金币统计
     */
    @Query("SELECT SUM(c.frozenCoins) FROM CoinAccount c")
    Long getTotalFrozenCoins();

    /**
     * 获取金币排行榜（按总金币）
     */
    @Query("SELECT c FROM CoinAccount c ORDER BY c.totalCoins DESC")
    List<CoinAccount> findTopByTotalCoins();

    /**
     * 获取金币排行榜（按可用金币）
     */
    @Query("SELECT c FROM CoinAccount c ORDER BY c.availableCoins DESC")
    List<CoinAccount> findTopByAvailableCoins();

    /**
     * 获取用户金币排名
     */
    @Query("SELECT COUNT(c) + 1 FROM CoinAccount c WHERE c.totalCoins > " +
           "(SELECT c2.totalCoins FROM CoinAccount c2 WHERE c2.userId = :userId)")
    Integer getUserRankByTotalCoins(@Param("userId") Long userId);

    /**
     * 获取平均金币数
     */
    @Query("SELECT AVG(c.totalCoins) FROM CoinAccount c WHERE c.totalCoins > 0")
    Double getAverageTotalCoins();

    /**
     * 获取平均可用金币数
     */
    @Query("SELECT AVG(c.availableCoins) FROM CoinAccount c WHERE c.availableCoins > 0")
    Double getAverageAvailableCoins();

    /**
     * 获取总获得金币统计
     */
    @Query("SELECT SUM(c.totalEarned) FROM CoinAccount c")
    Long getTotalEarnedCoins();

    /**
     * 获取总消费金币统计
     */
    @Query("SELECT SUM(c.totalSpent) FROM CoinAccount c")
    Long getTotalSpentCoins();

    /**
     * 获取总兑换金币统计
     */
    @Query("SELECT SUM(c.totalExchanged) FROM CoinAccount c")
    Long getTotalExchangedCoins();

    /**
     * 查找数据异常的账户（总金币不等于可用+冻结）
     */
    @Query("SELECT c FROM CoinAccount c WHERE c.totalCoins != (c.availableCoins + c.frozenCoins)")
    List<CoinAccount> findAnomalousAccounts();

    /**
     * 查找负余额账户
     */
    @Query("SELECT c FROM CoinAccount c WHERE c.availableCoins < 0 OR c.frozenCoins < 0 OR c.totalCoins < 0")
    List<CoinAccount> findNegativeBalanceAccounts();

    /**
     * 获取活跃用户账户（有交易记录的）
     */
    @Query("SELECT c FROM CoinAccount c WHERE c.totalEarned > 0 OR c.totalSpent > 0 OR c.totalExchanged > 0")
    List<CoinAccount> findActiveAccounts();

    /**
     * 获取富豪用户（金币数量前N名）
     */
    @Query("SELECT c FROM CoinAccount c ORDER BY c.totalCoins DESC")
    List<CoinAccount> findTopRichUsers();
}
