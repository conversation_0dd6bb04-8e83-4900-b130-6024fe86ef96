package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Bot;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 机器人Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BotRepository extends R2dbcRepository<Bot, Long> {

    /**
     * 根据用户名查找机器人
     */
    @Query("SELECT * FROM bots WHERE username = :username")
    Mono<Bot> findByUsername(@Param("username") String username);

    /**
     * 根据Token查找机器人
     */
    @Query("SELECT * FROM bots WHERE bot_token = :botToken")
    Mono<Bot> findByBotToken(@Param("botToken") String botToken);

    /**
     * 检查用户名是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM bots WHERE username = :username")
    Mono<Boolean> existsByUsername(@Param("username") String username);

    /**
     * 根据所有者ID查找机器人
     */
    @Query("SELECT * FROM bots WHERE owner_id = :ownerId ORDER BY created_at DESC")
    Flux<Bot> findByOwnerIdOrderByCreatedAtDesc(@Param("ownerId") String ownerId);

    /**
     * 分页查找所有者的机器人
     */
    @Query("SELECT * FROM bots WHERE owner_id = :ownerId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findByOwnerIdOrderByCreatedAtDesc(@Param("ownerId") String ownerId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态查找机器人
     */
    @Query("SELECT * FROM bots WHERE status = :status ORDER BY created_at DESC")
    Flux<Bot> findByStatusOrderByCreatedAtDesc(@Param("status") String status);

    /**
     * 查找公开的机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status ORDER BY install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findByIsPublicTrueAndStatusOrderByInstallCountDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据分类查找公开机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status AND category = :category ORDER BY install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findByIsPublicTrueAndStatusAndCategoryOrderByInstallCountDesc(
            @Param("status") String status, @Param("category") String category, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找已验证的机器人
     */
    @Query("SELECT * FROM bots WHERE is_verified = true AND status = :status ORDER BY install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findByIsVerifiedTrueAndStatusOrderByInstallCountDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据机器人类型查找
     */
    @Query("SELECT * FROM bots WHERE bot_type = :botType AND status = :status ORDER BY created_at DESC")
    Flux<Bot> findByBotTypeAndStatusOrderByCreatedAtDesc(@Param("botType") String botType, @Param("status") String status);

    /**
     * 搜索机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status AND " +
           "(LOWER(username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(display_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY install_count DESC, rating DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> searchPublicBots(@Param("keyword") String keyword, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据标签查找机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status AND " +
           "tags @> CAST(:tag AS jsonb) ORDER BY install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findByTag(@Param("tag") String tag, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找热门机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status AND " +
           "install_count >= :minInstalls ORDER BY install_count DESC, rating DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findPopularBots(@Param("status") String status, @Param("minInstalls") Long minInstalls, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找最新机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findLatestBots(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找评分最高的机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = :status AND " +
           "rating >= :minRating ORDER BY rating DESC, install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findTopRatedBots(@Param("status") String status, @Param("minRating") Double minRating, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找需要更新活跃时间的机器人
     */
    @Query("SELECT * FROM bots WHERE status = :status AND " +
           "(last_active_at IS NULL OR last_active_at < :threshold)")
    Flux<Bot> findBotsNeedingActivityUpdate(@Param("status") String status, @Param("threshold") Instant threshold);

    /**
     * 统计所有者的机器人数量
     */
    @Query("SELECT COUNT(*) FROM bots WHERE owner_id = :ownerId AND status != 'DELETED'")
    Mono<Long> countByOwnerId(@Param("ownerId") String ownerId);

    /**
     * 统计各状态的机器人数量
     */
    @Query("SELECT status, COUNT(*) FROM bots GROUP BY status")
    Flux<Object[]> countByStatus();

    /**
     * 统计各类型的机器人数量
     */
    @Query("SELECT bot_type, COUNT(*) FROM bots WHERE status != 'DELETED' GROUP BY bot_type")
    Flux<Object[]> countByType();

    /**
     * 统计各分类的机器人数量
     */
    @Query("SELECT category, COUNT(*) FROM bots WHERE is_public = true AND status = 'ACTIVE' GROUP BY category ORDER BY COUNT(*) DESC")
    Flux<Object[]> countByCategory();

    /**
     * 获取机器人统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as total_bots, " +
           "COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_bots, " +
           "COUNT(CASE WHEN is_public = true THEN 1 END) as public_bots, " +
           "COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_bots, " +
           "AVG(rating) as average_rating, " +
           "SUM(install_count) as total_installs " +
           "FROM bots WHERE status != 'DELETED'")
    Mono<Object[]> getBotStatistics();

    /**
     * 批量更新最后活跃时间
     */
    @Modifying
    @Query("UPDATE bots SET last_active_at = :activeTime WHERE id = ANY(:botIds)")
    Mono<Integer> updateLastActiveTime(@Param("botIds") String[] botIds, @Param("activeTime") Instant activeTime);

    /**
     * 批量更新安装数量
     */
    @Modifying
    @Query("UPDATE bots SET install_count = install_count + :increment WHERE id = :botId")
    Mono<Integer> updateInstallCount(@Param("botId") Long botId, @Param("increment") Long increment);

    /**
     * 检查用户名是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM bots WHERE LOWER(username) = LOWER(:username) AND id != :excludeId")
    Mono<Boolean> existsByUsernameIgnoreCaseAndIdNot(@Param("username") String username, @Param("excludeId") String excludeId);

    /**
     * 检查用户名是否存在（新建时）
     */
    @Query("SELECT COUNT(*) > 0 FROM bots WHERE LOWER(username) = LOWER(:username)")
    Mono<Boolean> existsByUsernameIgnoreCase(@Param("username") String username);

    /**
     * 查找相似的机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = 'ACTIVE' AND " +
           "category = :category AND id != :excludeId " +
           "ORDER BY rating DESC, install_count DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findSimilarBots(@Param("category") String category, @Param("excludeId") Long excludeId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找推荐机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = 'ACTIVE' AND " +
           "rating >= 4.0 AND install_count >= 100 " +
           "ORDER BY (rating * 0.7 + (install_count / 1000.0) * 0.3) DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> findRecommendedBots(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户安装的机器人
     */
    @Query("SELECT DISTINCT b.* FROM bots b JOIN bot_installations bi ON b.id = bi.bot_id " +
           "WHERE bi.installed_by = :userId AND bi.status = 'ACTIVE' " +
           "ORDER BY bi.last_used_at DESC NULLS LAST")
    Flux<Bot> findInstalledBotsByUser(@Param("userId") String userId);

    /**
     * 查找活跃机器人（最近30天有活动）
     */
    @Query("SELECT * FROM bots WHERE status = 'ACTIVE' AND " +
           "last_active_at >= :since ORDER BY last_active_at DESC")
    Flux<Bot> findActiveBotsSince(@Param("since") Instant since);

    /**
     * 查找非活跃机器人
     */
    @Query("SELECT * FROM bots WHERE status = 'ACTIVE' AND " +
           "(last_active_at IS NULL OR last_active_at < :threshold)")
    Flux<Bot> findInactiveBots(@Param("threshold") Instant threshold);

    /**
     * 获取机器人创建统计
     */
    @Query("SELECT DATE(created_at) as date, COUNT(*) as count FROM bots " +
           "WHERE created_at >= :since GROUP BY DATE(created_at) ORDER BY date")
    Flux<Object[]> getBotCreationStatistics(@Param("since") Instant since);

    /**
     * 查找需要清理的机器人
     */
    @Query("SELECT * FROM bots WHERE status = 'DELETED' AND updated_at < :cutoffTime")
    Flux<Bot> findBotsForCleanup(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 删除旧的已删除机器人
     */
    @Modifying
    @Query("DELETE FROM bots WHERE status = 'DELETED' AND updated_at < :cutoffTime")
    Mono<Integer> deleteOldDeletedBots(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 查找所有分类
     */
    @Query("SELECT DISTINCT category FROM bots WHERE category IS NOT NULL AND is_public = true AND status = 'ACTIVE'")
    Flux<String> findAllCategories();

    /**
     * 查找所有标签
     */
    @Query("SELECT DISTINCT jsonb_array_elements_text(tags) as tag FROM bots WHERE is_public = true AND status = 'ACTIVE'")
    Flux<String> findAllTags();

    /**
     * 查找热门标签
     */
    @Query("SELECT tag, COUNT(*) as count FROM (" +
           "SELECT jsonb_array_elements_text(tags) as tag FROM bots " +
           "WHERE is_public = true AND status = 'ACTIVE'" +
           ") t GROUP BY tag ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findPopularTags(@Param("limit") int limit, @Param("offset") long offset);

    // ============================================================================
    // Missing Methods Required by BotService
    // ============================================================================

    /**
     * 根据用户名和状态查找机器人
     */
    @Query("SELECT * FROM bots WHERE username = :username AND status = :status")
    Mono<Bot> findByUsernameAndStatus(@Param("username") String username, @Param("status") String status);

    /**
     * 检查用户名和状态是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM bots WHERE username = :username AND status = :status")
    Mono<Boolean> existsByUsernameAndStatus(@Param("username") String username, @Param("status") String status);

    /**
     * 根据Token和状态查找机器人
     */
    @Query("SELECT * FROM bots WHERE bot_token = :botToken AND status = :status")
    Mono<Bot> findByBotTokenAndStatus(@Param("botToken") String botToken, @Param("status") String status);

    /**
     * 检查Token和状态是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM bots WHERE bot_token = :botToken AND status = :status")
    Mono<Boolean> existsByBotTokenAndStatus(@Param("botToken") String botToken, @Param("status") String status);

    /**
     * 根据创建者和状态查找机器人
     */
    @Query("SELECT * FROM bots WHERE created_by = :createdBy AND status = :status ORDER BY created_at DESC")
    Flux<Bot> findByCreatedByAndStatusOrderByCreatedAtDesc(@Param("createdBy") String createdBy, @Param("status") String status);

    /**
     * 统计创建者的机器人数量
     */
    @Query("SELECT COUNT(*) FROM bots WHERE created_by = :createdBy AND status = :status")
    Mono<Long> countByCreatedByAndStatus(@Param("createdBy") String createdBy, @Param("status") String status);

    /**
     * 根据机器人类型和状态查找机器人（按安装数排序）
     */
    @Query("SELECT * FROM bots WHERE bot_type = :botType AND status = :status ORDER BY install_count DESC")
    Flux<Bot> findByBotTypeAndStatusOrderByInstallCountDesc(@Param("botType") String botType, @Param("status") String status);

    /**
     * 搜索机器人（支持状态过滤）
     */
    @Query("SELECT * FROM bots WHERE status = :status AND " +
           "(LOWER(username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(display_name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY install_count DESC, rating DESC LIMIT :limit OFFSET :offset")
    Flux<Bot> searchBots(@Param("keyword") String keyword, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据标签查找机器人
     */
    @Query("SELECT DISTINCT * FROM bots WHERE status = :status AND tags ?| :tags " +
           "ORDER BY install_count DESC")
    Flux<Bot> findBotsByTags(@Param("tags") String[] tags, @Param("status") String status);

    /**
     * 查找高评分机器人
     */
    @Query("SELECT * FROM bots WHERE is_public = true AND status = 'ACTIVE' AND " +
           "rating >= :minRating ORDER BY rating DESC, install_count DESC")
    Flux<Bot> findHighRatedBots(@Param("minRating") java.math.BigDecimal minRating);

    /**
     * 增加安装数量
     */
    @Modifying
    @Query("UPDATE bots SET install_count = install_count + 1 WHERE id = :botId")
    Mono<Void> incrementInstallCount(@Param("botId") Long botId);

    /**
     * 减少安装数量
     */
    @Modifying
    @Query("UPDATE bots SET install_count = CASE WHEN install_count > 0 THEN install_count - 1 ELSE 0 END WHERE id = :botId")
    Mono<Void> decrementInstallCount(@Param("botId") Long botId);

    /**
     * 统计各机器人类型数量
     */
    @Query("SELECT bot_type, COUNT(*) FROM bots WHERE status != 'DELETED' GROUP BY bot_type")
    Flux<Object[]> countByBotType();

    /**
     * 获取安装统计信息
     */
    @Query("SELECT " +
           "SUM(install_count) as total_installs, " +
           "AVG(install_count) as average_installs, " +
           "MAX(install_count) as max_installs, " +
           "COUNT(CASE WHEN install_count > 0 THEN 1 END) as installed_bots " +
           "FROM bots WHERE status = 'ACTIVE'")
    Mono<Object[]> getInstallStatistics();

    /**
     * 获取评分统计信息
     */
    @Query("SELECT " +
           "AVG(rating) as average_rating, " +
           "MAX(rating) as max_rating, " +
           "MIN(rating) as min_rating, " +
           "COUNT(CASE WHEN rating >= 4.0 THEN 1 END) as high_rated_bots, " +
           "COUNT(CASE WHEN rating IS NOT NULL THEN 1 END) as rated_bots " +
           "FROM bots WHERE status = 'ACTIVE'")
    Mono<Object[]> getRatingStatistics();

    /**
     * 根据显示名称或描述搜索机器人（忽略大小写）
     */
    @Query("SELECT * FROM bots WHERE " +
           "LOWER(display_name) LIKE LOWER(CONCAT('%', :displayName, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :description, '%')) " +
           "LIMIT :limit OFFSET :offset")
    Flux<Bot> findByDisplayNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(
            @Param("displayName") String displayName, @Param("description") String description, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态和最后活跃时间查找机器人
     */
    @Query("SELECT * FROM bots WHERE status = :status AND last_active_at > :lastActiveAt LIMIT :limit OFFSET :offset")
    Flux<Bot> findByStatusAndLastActiveAtAfter(@Param("status") String status, @Param("lastActiveAt") Instant lastActiveAt, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态和是否公开查找机器人
     */
    Page<Bot> findByStatusAndIsPublic(Bot.BotStatus status, Boolean isPublic, Pageable pageable);
}
