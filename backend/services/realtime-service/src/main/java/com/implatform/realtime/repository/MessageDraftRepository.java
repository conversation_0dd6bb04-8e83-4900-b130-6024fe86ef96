package com.implatform.realtime.repository;

import com.implatform.realtime.entity.MessageDraft;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 消息草稿数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface MessageDraftRepository extends R2dbcRepository<MessageDraft, Long> {

    /**
     * 根据用户ID和会话ID查找草稿
     */
    @Query("SELECT * FROM message_drafts WHERE user_id = :userId AND conversation_id = :conversationId")
    Mono<MessageDraft> findByUserIdAndConversationId(@Param("userId") Long userId, @Param("conversationId") Long conversationId);

    /**
     * 根据用户ID查找所有草稿
     */
    @Query("SELECT * FROM message_drafts WHERE user_id = :userId ORDER BY updated_at DESC")
    Flux<MessageDraft> findByUserIdOrderByUpdatedAtDesc(@Param("userId") Long userId);

    /**
     * 分页查询用户草稿
     */
    @Query("SELECT * FROM message_drafts WHERE user_id = :userId ORDER BY updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<MessageDraft> findByUserIdOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查询用户草稿数量
     */
    @Query("SELECT COUNT(*) FROM message_drafts WHERE user_id = :userId")
    Mono<Long> countByUserId(@Param("userId") Long userId);

    /**
     * 查询用户有媒体的草稿数量
     */
    @Query("SELECT COUNT(*) FROM message_drafts WHERE user_id = :userId AND has_media = :hasMedia")
    Mono<Long> countByUserIdAndHasMedia(@Param("userId") Long userId, @Param("hasMedia") Boolean hasMedia);

    /**
     * 根据草稿类型查询
     */
    List<MessageDraft> findByUserIdAndDraftType(Long userId, MessageDraft.DraftType draftType);

    /**
     * 查询指定时间之前的草稿
     */
    List<MessageDraft> findByUpdatedAtBefore(Instant cutoffTime);

    /**
     * 查询空草稿
     */
    @Query("SELECT d FROM MessageDraft d WHERE d.userId = :userId AND " +
           "((d.content IS NULL OR TRIM(d.content) = '') AND " +
           "(d.mediaUrls IS EMPTY OR d.mediaUrls IS NULL))")
    List<MessageDraft> findEmptyDraftsByUserId(@Param("userId") Long userId);

    /**
     * 批量删除用户的空草稿
     */
    @Modifying
    @Query("DELETE FROM MessageDraft d WHERE d.userId = :userId AND " +
           "((d.content IS NULL OR TRIM(d.content) = '') AND " +
           "(d.mediaUrls IS EMPTY OR d.mediaUrls IS NULL))")
    int deleteEmptyDraftsByUserId(@Param("userId") Long userId);

    /**
     * 删除指定时间之前的草稿
     */
    @Modifying
    @Query("DELETE FROM MessageDraft d WHERE d.updatedAt < :cutoffTime")
    int deleteOldDrafts(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 根据设备ID查询草稿
     */
    List<MessageDraft> findByUserIdAndLastEditDevice(Long userId, String deviceId);

    /**
     * 查询用户最近编辑的草稿
     */
    @Query("SELECT d FROM MessageDraft d WHERE d.userId = :userId " +
           "ORDER BY d.updatedAt DESC")
    List<MessageDraft> findRecentDraftsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计用户草稿总大小
     */
    @Query("SELECT COALESCE(SUM(d.draftSize), 0) FROM MessageDraft d WHERE d.userId = :userId")
    Long getTotalDraftSizeByUserId(@Param("userId") Long userId);

    /**
     * 查询会话是否有草稿
     */
    boolean existsByUserIdAndConversationId(Long userId, Long conversationId);

    /**
     * 根据会话ID列表查询草稿
     */
    @Query("SELECT d FROM MessageDraft d WHERE d.userId = :userId AND d.conversationId IN :conversationIds")
    List<MessageDraft> findByUserIdAndConversationIds(@Param("userId") Long userId, 
                                                      @Param("conversationIds") List<Long> conversationIds);
}
