package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupFolder;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 群文件夹Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupFolderRepository extends R2dbcRepository<GroupFolder, Long> {

    /**
     * 根据群组ID查找根文件夹
     */
    @Query("SELECT * FROM group_folders WHERE group_id = :groupId AND parent_id IS NULL AND status = :status ORDER BY sort_order ASC, created_at ASC")
    Flux<GroupFolder> findByGroupIdAndParentIdIsNullAndStatusOrderBySortOrderAscCreatedAtAsc(
            @Param("groupId") Long groupId, @Param("status") String status);

    /**
     * 根据父文件夹ID查找子文件夹
     */
    @Query("SELECT * FROM group_folders WHERE group_id = :groupId AND parent_id = :parentId AND status = :status ORDER BY sort_order ASC, created_at ASC")
    Flux<GroupFolder> findByGroupIdAndParentIdAndStatusOrderBySortOrderAscCreatedAtAsc(
            @Param("groupId") Long groupId, @Param("parentId") Long parentId, @Param("status") String status);

    /**
     * 分页查找群组文件夹
     */
    @Query("SELECT * FROM group_folders WHERE group_id = :groupId AND status = :status ORDER BY sort_order ASC, created_at ASC LIMIT :limit OFFSET :offset")
    Flux<GroupFolder> findByGroupIdAndStatusOrderBySortOrderAscCreatedAtAsc(
            @Param("groupId") Long groupId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据创建者查找文件夹
     */
    @Query("SELECT * FROM group_folders WHERE group_id = :groupId AND creator_id = :creatorId AND status = :status ORDER BY created_at DESC")
    Flux<GroupFolder> findByGroupIdAndCreatorIdAndStatusOrderByCreatedAtDesc(
            @Param("groupId") Long groupId, @Param("creatorId") Long creatorId, @Param("status") String status);

    /**
     * 搜索群文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "(gfo.folderName LIKE %:keyword% OR gfo.description LIKE %:keyword%) " +
           "ORDER BY gfo.sortOrder ASC, gfo.createdAt ASC")
    Page<GroupFolder> searchFolders(@Param("groupId") Long groupId, 
                                   @Param("status") GroupFolder.FolderStatus status,
                                   @Param("keyword") String keyword, 
                                   Pageable pageable);

    /**
     * 检查文件夹名称是否存在
     */
    boolean existsByGroupIdAndParentIdAndFolderNameAndStatus(
            Long groupId, Long parentId, String folderName, GroupFolder.FolderStatus status);

    /**
     * 查找指定路径的文件夹
     */
    Optional<GroupFolder> findByGroupIdAndFolderPathAndStatus(
            Long groupId, String folderPath, GroupFolder.FolderStatus status);

    /**
     * 查找指定深度的文件夹
     */
    List<GroupFolder> findByGroupIdAndDepthLevelAndStatusOrderBySortOrderAsc(
            Long groupId, Integer depthLevel, GroupFolder.FolderStatus status);

    /**
     * 查找深层文件夹（层级超过指定值）
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.depthLevel > :maxDepth ORDER BY gfo.depthLevel DESC")
    List<GroupFolder> findDeepFolders(@Param("groupId") Long groupId, 
                                     @Param("status") GroupFolder.FolderStatus status,
                                     @Param("maxDepth") Integer maxDepth);

    /**
     * 查找空文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.fileCount = 0 AND gfo.subfolderCount = 0 ORDER BY gfo.createdAt ASC")
    List<GroupFolder> findEmptyFolders(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 查找大文件夹（文件数量多）
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.fileCount > :minFileCount ORDER BY gfo.fileCount DESC")
    List<GroupFolder> findLargeFolders(@Param("groupId") Long groupId, 
                                      @Param("status") GroupFolder.FolderStatus status,
                                      @Param("minFileCount") Integer minFileCount);

    /**
     * 查找最近创建的文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.createdAt >= :since ORDER BY gfo.createdAt DESC")
    List<GroupFolder> findRecentFolders(@Param("groupId") Long groupId, 
                                       @Param("status") GroupFolder.FolderStatus status,
                                       @Param("since") LocalDateTime since);

    /**
     * 统计群组文件夹数量
     */
    long countByGroupIdAndStatus(Long groupId, GroupFolder.FolderStatus status);

    /**
     * 统计指定父文件夹下的子文件夹数量
     */
    long countByGroupIdAndParentIdAndStatus(Long groupId, Long parentId, GroupFolder.FolderStatus status);

    /**
     * 统计指定深度的文件夹数量
     */
    long countByGroupIdAndDepthLevelAndStatus(Long groupId, Integer depthLevel, GroupFolder.FolderStatus status);

    /**
     * 计算群组文件夹总大小
     */
    @Query("SELECT COALESCE(SUM(gfo.totalSize), 0) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status")
    Long calculateTotalFolderSize(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 更新文件夹文件计数
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.fileCount = gfo.fileCount + :delta WHERE gfo.id = :folderId")
    void updateFileCount(@Param("folderId") Long folderId, @Param("delta") Integer delta);

    /**
     * 更新文件夹子文件夹计数
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.subfolderCount = gfo.subfolderCount + :delta WHERE gfo.id = :folderId")
    void updateSubfolderCount(@Param("folderId") Long folderId, @Param("delta") Integer delta);

    /**
     * 更新文件夹总大小
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.totalSize = gfo.totalSize + :delta WHERE gfo.id = :folderId")
    void updateTotalSize(@Param("folderId") Long folderId, @Param("delta") Long delta);

    /**
     * 批量更新文件夹状态
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.status = :status WHERE gfo.groupId = :groupId AND gfo.id IN :folderIds")
    void batchUpdateStatus(@Param("groupId") Long groupId, 
                          @Param("folderIds") List<Long> folderIds, 
                          @Param("status") GroupFolder.FolderStatus status);

    /**
     * 批量更新排序顺序
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.sortOrder = :sortOrder WHERE gfo.id = :folderId")
    void updateSortOrder(@Param("folderId") Long folderId, @Param("sortOrder") Integer sortOrder);

    /**
     * 移动文件夹到新的父文件夹
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.parentId = :newParentId, gfo.folderPath = :newPath, gfo.depthLevel = :newDepth " +
           "WHERE gfo.id = :folderId")
    void moveFolder(@Param("folderId") Long folderId, 
                   @Param("newParentId") Long newParentId, 
                   @Param("newPath") String newPath, 
                   @Param("newDepth") Integer newDepth);

    /**
     * 获取文件夹层级统计
     */
    @Query("SELECT gfo.depthLevel, COUNT(gfo) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status " +
           "GROUP BY gfo.depthLevel ORDER BY gfo.depthLevel")
    List<Object[]> getFolderDepthStatistics(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取文件夹大小统计
     */
    @Query("SELECT gfo.id, gfo.folderName, gfo.totalSize FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status " +
           "ORDER BY gfo.totalSize DESC")
    List<Object[]> getFolderSizeStatistics(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取文件夹创建趋势
     */
    @Query("SELECT DATE(gfo.createdAt), COUNT(gfo) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND " +
           "gfo.createdAt >= :startDate GROUP BY DATE(gfo.createdAt) ORDER BY DATE(gfo.createdAt)")
    List<Object[]> getFolderCreationTrend(@Param("groupId") Long groupId, @Param("startDate") LocalDateTime startDate);

    /**
     * 查找所有子文件夹（递归）
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.folderPath LIKE CONCAT(:parentPath, '/%') ORDER BY gfo.depthLevel, gfo.sortOrder")
    List<GroupFolder> findAllSubfolders(@Param("groupId") Long groupId, 
                                       @Param("status") GroupFolder.FolderStatus status,
                                       @Param("parentPath") String parentPath);

    /**
     * 查找路径冲突的文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.folderPath = :folderPath AND gfo.id != :excludeId")
    List<GroupFolder> findPathConflicts(@Param("groupId") Long groupId, 
                                       @Param("status") GroupFolder.FolderStatus status,
                                       @Param("folderPath") String folderPath, 
                                       @Param("excludeId") Long excludeId);

    /**
     * 删除群组的所有文件夹
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.status = 'DELETED' WHERE gfo.groupId = :groupId")
    void softDeleteGroupFolders(@Param("groupId") Long groupId);

    /**
     * 删除指定文件夹及其所有子文件夹
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET gfo.status = 'DELETED' WHERE gfo.groupId = :groupId AND " +
           "(gfo.id = :folderId OR gfo.folderPath LIKE CONCAT(:folderPath, '/%'))")
    void softDeleteFolderAndSubfolders(@Param("groupId") Long groupId, 
                                      @Param("folderId") Long folderId, 
                                      @Param("folderPath") String folderPath);

    /**
     * 物理删除已删除状态的文件夹
     */
    @Modifying
    @Query("DELETE FROM GroupFolder gfo WHERE gfo.status = 'DELETED' AND gfo.updatedAt < :cutoffTime")
    int physicalDeleteOldFolders(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 重新计算文件夹统计信息
     */
    @Modifying
    @Query("UPDATE GroupFolder gfo SET " +
           "gfo.fileCount = (SELECT COUNT(gf) FROM GroupFile gf WHERE gf.groupId = gfo.groupId AND gf.folderId = gfo.id AND gf.status = 'ACTIVE'), " +
           "gfo.subfolderCount = (SELECT COUNT(gfo2) FROM GroupFolder gfo2 WHERE gfo2.groupId = gfo.groupId AND gfo2.parentId = gfo.id AND gfo2.status = 'ACTIVE'), " +
           "gfo.totalSize = (SELECT COALESCE(SUM(gf.fileSize), 0) FROM GroupFile gf WHERE gf.groupId = gfo.groupId AND gf.folderId = gfo.id AND gf.status = 'ACTIVE') " +
           "WHERE gfo.groupId = :groupId AND gfo.status = 'ACTIVE'")
    void recalculateFolderStatistics(@Param("groupId") Long groupId);

    /**
     * 获取群组文件夹概览
     */
    @Query("SELECT COUNT(gfo), COALESCE(SUM(gfo.fileCount), 0), COALESCE(SUM(gfo.totalSize), 0), " +
           "MAX(gfo.depthLevel), COUNT(CASE WHEN gfo.fileCount = 0 AND gfo.subfolderCount = 0 THEN 1 END) " +
           "FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status")
    List<Object[]> getGroupFolderOverview(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取平均文件夹大小
     */
    @Query("SELECT AVG(gfo.totalSize) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status")
    Double getAverageFolderSize(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取平均文件夹文件数
     */
    @Query("SELECT AVG(gfo.fileCount) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status")
    Double getAverageFolderFileCount(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 查找最大的文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status " +
           "ORDER BY gfo.totalSize DESC")
    List<GroupFolder> findLargestFolders(@Param("groupId") Long groupId, 
                                        @Param("status") GroupFolder.FolderStatus status, 
                                        Pageable pageable);

    /**
     * 查找文件最多的文件夹
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status " +
           "ORDER BY gfo.fileCount DESC")
    List<GroupFolder> findMostPopulatedFolders(@Param("groupId") Long groupId, 
                                              @Param("status") GroupFolder.FolderStatus status, 
                                              Pageable pageable);

    /**
     * 查找孤立的文件夹（父文件夹不存在）
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status AND " +
           "gfo.parentId IS NOT NULL AND gfo.parentId NOT IN " +
           "(SELECT gfo2.id FROM GroupFolder gfo2 WHERE gfo2.groupId = :groupId AND gfo2.status = 'ACTIVE')")
    List<GroupFolder> findOrphanedFolders(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取文件夹树结构
     */
    @Query("SELECT gfo FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND gfo.status = :status " +
           "ORDER BY gfo.depthLevel, gfo.parentId, gfo.sortOrder, gfo.folderName")
    List<GroupFolder> getFolderTree(@Param("groupId") Long groupId, @Param("status") GroupFolder.FolderStatus status);

    /**
     * 获取最大排序顺序
     */
    @Query("SELECT COALESCE(MAX(gfo.sortOrder), 0) FROM GroupFolder gfo WHERE gfo.groupId = :groupId AND " +
           "gfo.parentId = :parentId AND gfo.status = :status")
    Integer getMaxSortOrder(@Param("groupId") Long groupId, 
                           @Param("parentId") Long parentId, 
                           @Param("status") GroupFolder.FolderStatus status);
}
