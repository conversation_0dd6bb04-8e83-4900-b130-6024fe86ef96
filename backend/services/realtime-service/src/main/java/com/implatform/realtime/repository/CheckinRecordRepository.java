package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CheckinRecord;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * 签到记录Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CheckinRecordRepository extends R2dbcRepository<CheckinRecord, Long> {

    /**
     * 根据用户ID和签到日期查找签到记录
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId AND checkin_date = :checkinDate")
    Mono<CheckinRecord> findByUserIdAndCheckinDate(@Param("userId") Long userId, @Param("checkinDate") LocalDate checkinDate);

    /**
     * 检查用户在指定日期是否已签到
     */
    @Query("SELECT COUNT(*) > 0 FROM checkin_records WHERE user_id = :userId AND checkin_date = :checkinDate")
    Mono<Boolean> existsByUserIdAndCheckinDate(@Param("userId") Long userId, @Param("checkinDate") LocalDate checkinDate);

    /**
     * 获取用户指定日期范围内的签到记录
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId AND checkin_date BETWEEN :startDate AND :endDate ORDER BY checkin_date DESC")
    Flux<CheckinRecord> findByUserIdAndCheckinDateBetweenOrderByCheckinDateDesc(
            @Param("userId") Long userId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取用户最近的签到记录
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId ORDER BY checkin_date DESC LIMIT 1")
    Mono<CheckinRecord> findTopByUserIdOrderByCheckinDateDesc(@Param("userId") Long userId);

    /**
     * 获取用户指定数量的最近签到记录
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId ORDER BY checkin_date DESC LIMIT :limit OFFSET :offset")
    Flux<CheckinRecord> findRecentCheckinRecords(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 分页获取用户签到记录
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId ORDER BY checkin_date DESC LIMIT :limit OFFSET :offset")
    Flux<CheckinRecord> findByUserIdOrderByCheckinDateDesc(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计用户总签到天数
     */
    @Query("SELECT COUNT(*) FROM checkin_records WHERE user_id = :userId")
    Mono<Long> countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定日期范围内的签到天数
     */
    @Query("SELECT COUNT(*) FROM checkin_records WHERE user_id = :userId " +
           "AND checkin_date BETWEEN :startDate AND :endDate")
    Mono<Long> countByUserIdAndDateRange(@Param("userId") Long userId,
                                   @Param("startDate") LocalDate startDate,
                                   @Param("endDate") LocalDate endDate);

    /**
     * 统计用户总获得金币数
     */
    @Query("SELECT COALESCE(SUM(reward_coins), 0) FROM checkin_records WHERE user_id = :userId")
    Mono<Integer> sumRewardCoinsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户总补签次数
     */
    @Query("SELECT COUNT(*) FROM checkin_records WHERE user_id = :userId AND is_makeup = true")
    Mono<Long> countMakeupByUserId(@Param("userId") Long userId);

    /**
     * 统计用户总补签消耗
     */
    @Query("SELECT COALESCE(SUM(makeup_cost), 0) FROM checkin_records WHERE user_id = :userId AND is_makeup = true")
    Mono<Integer> sumMakeupCostByUserId(@Param("userId") Long userId);

    /**
     * 获取用户连续签到记录（从最近日期开始）
     */
    @Query("SELECT * FROM checkin_records WHERE user_id = :userId " +
           "AND checkin_date >= :startDate ORDER BY checkin_date DESC")
    Flux<CheckinRecord> findContinuousCheckinRecords(@Param("userId") Long userId,
                                                      @Param("startDate") LocalDate startDate);

    /**
     * 获取指定日期的所有签到记录（用于统计）
     */
    List<CheckinRecord> findByCheckinDate(LocalDate checkinDate);

    /**
     * 获取指定日期范围内的所有签到记录
     */
    List<CheckinRecord> findByCheckinDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 统计指定日期的签到用户数
     */
    @Query("SELECT COUNT(DISTINCT c.userId) FROM CheckinRecord c WHERE c.checkinDate = :date")
    long countDistinctUsersByDate(@Param("date") LocalDate date);

    /**
     * 统计指定日期范围内的签到用户数
     */
    @Query("SELECT COUNT(DISTINCT c.userId) FROM CheckinRecord c " +
           "WHERE c.checkinDate BETWEEN :startDate AND :endDate")
    long countDistinctUsersByDateRange(@Param("startDate") LocalDate startDate, 
                                       @Param("endDate") LocalDate endDate);

    /**
     * 获取用户指定月份的签到记录
     */
    @Query("SELECT c FROM CheckinRecord c WHERE c.userId = :userId " +
           "AND YEAR(c.checkinDate) = :year AND MONTH(c.checkinDate) = :month " +
           "ORDER BY c.checkinDate")
    List<CheckinRecord> findByUserIdAndYearMonth(@Param("userId") Long userId, 
                                                 @Param("year") int year, 
                                                 @Param("month") int month);

    /**
     * 统计用户指定月份的签到天数
     */
    @Query("SELECT COUNT(c) FROM CheckinRecord c WHERE c.userId = :userId " +
           "AND YEAR(c.checkinDate) = :year AND MONTH(c.checkinDate) = :month")
    long countByUserIdAndYearMonth(@Param("userId") Long userId, 
                                   @Param("year") int year, 
                                   @Param("month") int month);

    /**
     * 获取排行榜数据（按总签到天数）
     */
    @Query("SELECT c.userId, COUNT(c) as totalDays, MAX(c.continuousDays) as maxContinuous, " +
           "SUM(c.rewardCoins) as totalCoins FROM CheckinRecord c " +
           "WHERE c.checkinDate BETWEEN :startDate AND :endDate " +
           "GROUP BY c.userId ORDER BY totalDays DESC, totalCoins DESC")
    List<Object[]> getLeaderboardByTotalDays(@Param("startDate") LocalDate startDate, 
                                             @Param("endDate") LocalDate endDate, 
                                             Pageable pageable);

    /**
     * 获取连续签到排行榜数据
     */
    @Query("SELECT c.userId, MAX(c.continuousDays) as maxContinuous, COUNT(c) as totalDays, " +
           "SUM(c.rewardCoins) as totalCoins FROM CheckinRecord c " +
           "GROUP BY c.userId ORDER BY maxContinuous DESC, totalDays DESC")
    List<Object[]> getLeaderboardByContinuousDays(Pageable pageable);

    /**
     * 删除指定日期之前的记录（数据清理）
     */
    @Modifying
    @Transactional
    void deleteByCheckinDateBefore(LocalDate date);

    /**
     * 获取用户最大连续签到天数
     */
    @Query("SELECT MAX(c.continuousDays) FROM CheckinRecord c WHERE c.userId = :userId")
    Integer getMaxContinuousDaysByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否可以补签指定日期
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN false ELSE true END FROM CheckinRecord c " +
           "WHERE c.userId = :userId AND c.checkinDate = :date")
    boolean canMakeupForDate(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 统计用户本月补签次数
     */
    @Query("SELECT COUNT(c) FROM CheckinRecord c WHERE c.userId = :userId " +
           "AND c.isMakeup = true AND YEAR(c.checkinDate) = :year AND MONTH(c.checkinDate) = :month")
    long countMakeupByUserIdAndYearMonth(@Param("userId") Long userId,
                                         @Param("year") int year,
                                         @Param("month") int month);

    /**
     * 获取用户当前连续签到天数
     */
    @Query("SELECT c.continuousDays FROM CheckinRecord c WHERE c.userId = :userId " +
           "ORDER BY c.checkinDate DESC LIMIT 1")
    Integer getCurrentContinuousDays(@Param("userId") Long userId);

    /**
     * 获取用户昨天的签到记录
     */
    @Query("SELECT c FROM CheckinRecord c WHERE c.userId = :userId " +
           "AND c.checkinDate = :yesterday")
    Optional<CheckinRecord> findYesterdayRecord(@Param("userId") Long userId,
                                               @Param("yesterday") LocalDate yesterday);

    /**
     * 批量删除过期记录
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM CheckinRecord c WHERE c.checkinDate < :cutoffDate")
    int deleteExpiredRecords(@Param("cutoffDate") LocalDate cutoffDate);

    /**
     * 统计指定时间段内的总签到次数
     */
    @Query("SELECT COUNT(c) FROM CheckinRecord c WHERE c.checkinDate BETWEEN :startDate AND :endDate")
    long countTotalCheckinsByDateRange(@Param("startDate") LocalDate startDate,
                                       @Param("endDate") LocalDate endDate);

    /**
     * 获取活跃用户列表（指定天数内有签到记录）
     */
    @Query("SELECT DISTINCT c.userId FROM CheckinRecord c WHERE c.checkinDate >= :startDate")
    List<Long> findActiveUserIds(@Param("startDate") LocalDate startDate);

    /**
     * 统计用户在指定时间段的补签消耗
     */
    @Query("SELECT COALESCE(SUM(c.makeupCost), 0) FROM CheckinRecord c " +
           "WHERE c.userId = :userId AND c.isMakeup = true " +
           "AND c.checkinDate BETWEEN :startDate AND :endDate")
    int sumMakeupCostByUserIdAndDateRange(@Param("userId") Long userId,
                                          @Param("startDate") LocalDate startDate,
                                          @Param("endDate") LocalDate endDate);
}
