package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupPermission;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 群组权限数据访问层 - R2DBC响应式版本
 * 提供群组权限相关的数据库操作方法
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupPermissionRepository extends R2dbcRepository<GroupPermission, Long> {

    /**
     * 根据角色ID查询所有权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Query("SELECT * FROM group_permissions WHERE role_id = :roleId ORDER BY permission_category ASC, permission_action ASC")
    Flux<GroupPermission> findByRoleIdOrderByPermissionCategoryAscPermissionActionAsc(@Param("roleId") Long roleId);

    /**
     * 根据角色ID和权限动作查询权限
     *
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @return 权限信息
     */
    @Query("SELECT * FROM group_permissions WHERE role_id = :roleId AND permission_action = :permissionAction")
    Mono<GroupPermission> findByRoleIdAndPermissionAction(
            @Param("roleId") Long roleId, @Param("permissionAction") String permissionAction);

    /**
     * 查询角色的已授予权限
     *
     * @param roleId 角色ID
     * @return 已授予权限列表
     */
    @Query("SELECT * FROM group_permissions WHERE role_id = :roleId AND is_granted = true ORDER BY permission_category ASC")
    Flux<GroupPermission> findByRoleIdAndIsGrantedTrueOrderByPermissionCategoryAsc(@Param("roleId") Long roleId);

    /**
     * 查询角色的继承权限
     * 
     * @param roleId 角色ID
     * @return 继承权限列表
     */
    List<GroupPermission> findByRoleIdAndIsInheritedTrueOrderByPermissionCategoryAsc(Long roleId);

    /**
     * 根据权限类别查询权限
     * 
     * @param roleId 角色ID
     * @param category 权限类别
     * @return 权限列表
     */
    List<GroupPermission> findByRoleIdAndPermissionCategoryOrderByPermissionActionAsc(
            Long roleId, GroupPermission.PermissionCategory category);

    /**
     * 查询指定权限类别的已授予权限
     * 
     * @param roleId 角色ID
     * @param category 权限类别
     * @return 已授予权限列表
     */
    List<GroupPermission> findByRoleIdAndPermissionCategoryAndIsGrantedTrueOrderByPermissionActionAsc(
            Long roleId, GroupPermission.PermissionCategory category);

    /**
     * 检查角色是否拥有指定权限
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @return 是否拥有权限
     */
    @Query("SELECT CASE WHEN COUNT(gp) > 0 THEN true ELSE false END " +
           "FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "AND gp.permissionAction = :permissionAction AND gp.isGranted = true " +
           "AND (gp.expiresAt IS NULL OR gp.expiresAt > CURRENT_TIMESTAMP)")
    boolean hasPermission(@Param("roleId") Long roleId, 
                         @Param("permissionAction") GroupPermission.PermissionAction permissionAction);

    /**
     * 查询即将过期的权限
     * 
     * @param expiryThreshold 过期时间阈值
     * @param pageable 分页参数
     * @return 即将过期的权限分页结果
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.isGranted = true " +
           "AND gp.expiresAt IS NOT NULL AND gp.expiresAt <= :expiryThreshold " +
           "ORDER BY gp.expiresAt ASC")
    Page<GroupPermission> findExpiringPermissions(@Param("expiryThreshold") Instant expiryThreshold, 
                                                  Pageable pageable);

    /**
     * 查询已过期但状态未更新的权限
     * 
     * @param currentTime 当前时间
     * @param pageable 分页参数
     * @return 已过期的权限分页结果
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.isGranted = true " +
           "AND gp.expiresAt IS NOT NULL AND gp.expiresAt < :currentTime " +
           "ORDER BY gp.expiresAt ASC")
    Page<GroupPermission> findExpiredPermissions(@Param("currentTime") Instant currentTime, 
                                                Pageable pageable);

    /**
     * 批量更新过期权限状态
     * 
     * @param currentTime 当前时间
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupPermission gp SET gp.isGranted = false, " +
           "gp.modificationNotes = 'Permission expired automatically' " +
           "WHERE gp.isGranted = true AND gp.expiresAt IS NOT NULL AND gp.expiresAt < :currentTime")
    int markExpiredPermissions(@Param("currentTime") Instant currentTime);

    /**
     * 查询角色权限统计
     * 
     * @param roleId 角色ID
     * @return 权限统计 [总数, 已授予数, 继承数, 过期数]
     */
    @Query("SELECT COUNT(gp), " +
           "COUNT(CASE WHEN gp.isGranted = true THEN 1 END), " +
           "COUNT(CASE WHEN gp.isInherited = true THEN 1 END), " +
           "COUNT(CASE WHEN gp.expiresAt IS NOT NULL AND gp.expiresAt < CURRENT_TIMESTAMP THEN 1 END) " +
           "FROM GroupPermission gp WHERE gp.roleId = :roleId")
    Object[] getPermissionStatistics(@Param("roleId") Long roleId);

    /**
     * 查询权限类别统计
     * 
     * @param roleId 角色ID
     * @return 类别统计结果
     */
    @Query("SELECT gp.permissionCategory, COUNT(gp), " +
           "COUNT(CASE WHEN gp.isGranted = true THEN 1 END) " +
           "FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "GROUP BY gp.permissionCategory")
    List<Object[]> getPermissionCategoryStatistics(@Param("roleId") Long roleId);

    /**
     * 批量授予权限
     * 
     * @param roleId 角色ID
     * @param permissionActions 权限动作列表
     * @param grantedBy 授予人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupPermission gp SET gp.isGranted = true, gp.grantedBy = :grantedBy, " +
           "gp.lastModifiedBy = :grantedBy, gp.modificationNotes = 'Batch granted' " +
           "WHERE gp.roleId = :roleId AND gp.permissionAction IN :permissionActions")
    int batchGrantPermissions(@Param("roleId") Long roleId, 
                             @Param("permissionActions") List<GroupPermission.PermissionAction> permissionActions,
                             @Param("grantedBy") Long grantedBy);

    /**
     * 批量撤销权限
     * 
     * @param roleId 角色ID
     * @param permissionActions 权限动作列表
     * @param revokedBy 撤销人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupPermission gp SET gp.isGranted = false, " +
           "gp.lastModifiedBy = :revokedBy, gp.modificationNotes = 'Batch revoked' " +
           "WHERE gp.roleId = :roleId AND gp.permissionAction IN :permissionActions")
    int batchRevokePermissions(@Param("roleId") Long roleId, 
                              @Param("permissionActions") List<GroupPermission.PermissionAction> permissionActions,
                              @Param("revokedBy") Long revokedBy);

    /**
     * 复制权限到另一个角色
     * 
     * @param sourceRoleId 源角色ID
     * @param targetRoleId 目标角色ID
     * @param copiedBy 复制人ID
     * @return 复制的权限数量
     */
    @Modifying
    @Query(value = "INSERT INTO group_permission " +
           "(role_id, permission_action, permission_category, is_granted, description, granted_by, created_at, updated_at) " +
           "SELECT :targetRoleId, permission_action, permission_category, is_granted, description, :copiedBy, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP " +
           "FROM group_permission WHERE role_id = :sourceRoleId " +
           "ON CONFLICT (role_id, permission_action) DO NOTHING", nativeQuery = true)
    int copyPermissionsToRole(@Param("sourceRoleId") Long sourceRoleId, 
                             @Param("targetRoleId") Long targetRoleId, 
                             @Param("copiedBy") Long copiedBy);

    /**
     * 查询从指定角色继承的权限
     * 
     * @param inheritedFromRoleId 继承来源角色ID
     * @return 继承权限列表
     */
    List<GroupPermission> findByInheritedFromRoleIdOrderByRoleIdAscPermissionActionAsc(Long inheritedFromRoleId);

    /**
     * 更新继承权限
     * 
     * @param parentRoleId 父角色ID
     * @param childRoleId 子角色ID
     * @return 更新的记录数
     */
    @Modifying
    @Query(value = "INSERT INTO group_permission " +
           "(role_id, permission_action, permission_category, is_granted, is_inherited, inherited_from_role_id, description, created_at, updated_at) " +
           "SELECT :childRoleId, permission_action, permission_category, is_granted, true, :parentRoleId, description, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP " +
           "FROM group_permission WHERE role_id = :parentRoleId AND is_granted = true " +
           "ON CONFLICT (role_id, permission_action) DO UPDATE SET " +
           "is_granted = EXCLUDED.is_granted, is_inherited = true, inherited_from_role_id = :parentRoleId, updated_at = CURRENT_TIMESTAMP", 
           nativeQuery = true)
    int updateInheritedPermissions(@Param("parentRoleId") Long parentRoleId, 
                                  @Param("childRoleId") Long childRoleId);

    /**
     * 删除继承权限
     * 
     * @param childRoleId 子角色ID
     * @param parentRoleId 父角色ID
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM GroupPermission gp WHERE gp.roleId = :childRoleId " +
           "AND gp.isInherited = true AND gp.inheritedFromRoleId = :parentRoleId")
    int removeInheritedPermissions(@Param("childRoleId") Long childRoleId, 
                                  @Param("parentRoleId") Long parentRoleId);

    /**
     * 查询权限冲突（同一角色的相同权限有多条记录）
     * 
     * @param roleId 角色ID
     * @return 冲突的权限动作列表
     */
    @Query("SELECT gp.permissionAction FROM GroupPermission gp " +
           "WHERE gp.roleId = :roleId GROUP BY gp.permissionAction HAVING COUNT(gp) > 1")
    List<GroupPermission.PermissionAction> findConflictingPermissions(@Param("roleId") Long roleId);

    /**
     * 查询有条件限制的权限
     * 
     * @param roleId 角色ID
     * @return 有条件的权限列表
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "AND (gp.conditions IS NOT NULL OR gp.restrictions IS NOT NULL) " +
           "ORDER BY gp.permissionCategory ASC, gp.permissionAction ASC")
    List<GroupPermission> findConditionalPermissions(@Param("roleId") Long roleId);

    /**
     * 查询临时权限（有过期时间的权限）
     * 
     * @param roleId 角色ID
     * @return 临时权限列表
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "AND gp.expiresAt IS NOT NULL ORDER BY gp.expiresAt ASC")
    List<GroupPermission> findTemporaryPermissions(@Param("roleId") Long roleId);

    /**
     * 查询权限授予历史
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @param pageable 分页参数
     * @return 权限历史分页结果
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "AND gp.permissionAction = :permissionAction " +
           "ORDER BY gp.createdAt DESC")
    Page<GroupPermission> findPermissionHistory(@Param("roleId") Long roleId, 
                                               @Param("permissionAction") GroupPermission.PermissionAction permissionAction,
                                               Pageable pageable);

    /**
     * 查询最近授予的权限
     * 
     * @param roleId 角色ID
     * @param pageable 分页参数
     * @return 最近权限分页结果
     */
    @Query("SELECT gp FROM GroupPermission gp WHERE gp.roleId = :roleId " +
           "ORDER BY gp.grantedAt DESC")
    Page<GroupPermission> findRecentlyGrantedPermissions(@Param("roleId") Long roleId, Pageable pageable);

    /**
     * 查询权限使用频率统计
     * 
     * @return 权限使用统计
     */
    @Query("SELECT gp.permissionAction, COUNT(gp) as usageCount " +
           "FROM GroupPermission gp WHERE gp.isGranted = true " +
           "GROUP BY gp.permissionAction ORDER BY usageCount DESC")
    List<Object[]> getPermissionUsageStatistics();

    /**
     * 删除角色的所有权限
     * 
     * @param roleId 角色ID
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM GroupPermission gp WHERE gp.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询群组的所有权限（通过角色关联）
     * 
     * @param groupId 群组ID
     * @return 群组权限列表
     */
    @Query("SELECT gp FROM GroupPermission gp " +
           "JOIN GroupRole gr ON gp.roleId = gr.id " +
           "WHERE gr.groupId = :groupId AND gr.isActive = true " +
           "ORDER BY gr.hierarchyLevel ASC, gp.permissionCategory ASC, gp.permissionAction ASC")
    List<GroupPermission> findByGroupId(@Param("groupId") Long groupId);

    /**
     * 查询群组权限矩阵
     * 
     * @param groupId 群组ID
     * @return 权限矩阵 [角色ID, 角色名称, 权限动作, 是否授予]
     */
    @Query("SELECT gr.id, gr.roleName, gp.permissionAction, gp.isGranted " +
           "FROM GroupPermission gp " +
           "JOIN GroupRole gr ON gp.roleId = gr.id " +
           "WHERE gr.groupId = :groupId AND gr.isActive = true " +
           "ORDER BY gr.hierarchyLevel ASC, gp.permissionCategory ASC, gp.permissionAction ASC")
    List<Object[]> getGroupPermissionMatrix(@Param("groupId") Long groupId);
}
