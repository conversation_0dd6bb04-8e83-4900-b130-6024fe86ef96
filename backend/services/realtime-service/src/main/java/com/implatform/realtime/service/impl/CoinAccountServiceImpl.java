package com.implatform.realtime.service.impl;

import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.core.enums.SocialErrorCode;
import com.implatform.realtime.entity.CoinAccount;
import com.implatform.realtime.entity.CoinTransactionRecord;
import com.implatform.realtime.repository.CoinAccountRepository;
import com.implatform.realtime.repository.CoinTransactionRepository;
import com.implatform.realtime.service.CoinAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 金币账户服务实现类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class CoinAccountServiceImpl implements CoinAccountService {

    private final CoinAccountRepository coinAccountRepository;
    private final CoinTransactionRepository coinTransactionRepository;

    @Override
    public Optional<CoinAccount> getAccount(Long userId) {
        log.debug("获取用户金币账户: userId={}", userId);

        return coinAccountRepository.findByUserId(userId);
    }

    @Override
    @Transactional
    public CoinAccount getOrCreateAccount(Long userId) {
        log.debug("获取或创建用户金币账户: userId={}", userId);

        return coinAccountRepository.findByUserId(userId)
                .orElseGet(() -> createAccount(userId));
    }

    /**
     * 内部方法：获取账户，如果不存在则抛出异常
     */
    private CoinAccount getAccountInternal(Long userId) {
        return coinAccountRepository.findByUserId(userId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.COIN_ACCOUNT_NOT_FOUND));
    }

    @Override
    @Transactional
    public CoinAccount createAccount(Long userId) {
        log.info("创建用户金币账户: userId={}", userId);
        
        // 检查账户是否已存在
        if (coinAccountRepository.existsByUserId(userId)) {
            throw new BusinessException(SocialErrorCode.COIN_ACCOUNT_ALREADY_EXISTS);
        }
        
        CoinAccount account = CoinAccount.builder()
                .userId(userId)
                .totalCoins(0)
                .availableCoins(0)
                .frozenCoins(0)
                .totalEarned(0)
                .totalSpent(0)
                .totalExchanged(0)
                .build();
        
        return coinAccountRepository.save(account);
    }

    @Override
    @Transactional
    public void freezeAccount(Long userId, String reason, String operatorId) {
        log.info("冻结用户金币账户: userId={}, reason={}, operatorId={}", userId, reason, operatorId);
        
        CoinAccount account = getAccountInternal(userId);
        // account.freeze(); // 需要在实体类中实现这个方法
        coinAccountRepository.save(account);
        
        // 记录操作日志
        log.info("金币账户已冻结: userId={}, operatorId={}", userId, operatorId);
    }

    @Override
    @Transactional
    public void unfreezeAccount(Long userId, String operatorId) {
        log.info("解冻用户金币账户: userId={}, operatorId={}", userId, operatorId);
        
        CoinAccount account = getAccountInternal(userId);
        // account.unfreeze(); // 需要在实体类中实现这个方法
        coinAccountRepository.save(account);
        
        // 记录操作日志
        log.info("金币账户已解冻: userId={}, operatorId={}", userId, operatorId);
    }

    @Override
    @Transactional
    public void deleteAccount(Long userId, String operatorId) {
        log.info("删除用户金币账户: userId={}, operatorId={}", userId, operatorId);
        
        CoinAccount account = getAccountInternal(userId);
        
        // 检查账户是否有余额
        if (account.getTotalCoins() > 0) {
            throw new BusinessException(SocialErrorCode.COIN_ACCOUNT_HAS_BALANCE);
        }
        
        coinAccountRepository.delete(account);
        
        // 记录操作日志
        log.info("金币账户已删除: userId={}, operatorId={}", userId, operatorId);
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public CoinAccount addCoins(Long userId, BigDecimal amount, String source, String description) {
        log.info("增加用户金币: userId={}, amount={}, source={}", userId, amount, source);
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }
        
        // 获取账户（加锁）
        CoinAccount account = coinAccountRepository.findByUserIdWithLock(userId)
                .orElseGet(() -> createAccount(userId));
        
        int coinAmount = amount.intValue();
        int balanceBefore = account.getAvailableCoins();
        
        // 增加金币
        account.addCoins(coinAmount);
        account = coinAccountRepository.save(account);
        
        // 创建交易记录
        createTransactionRecord(userId, coinAmount, balanceBefore, account.getAvailableCoins(),
                CoinTransactionRecord.TransactionType.INCOME, source, description);
        
        log.info("金币增加成功: userId={}, amount={}, newBalance={}", userId, coinAmount, account.getAvailableCoins());
        return account;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public CoinAccount deductCoins(Long userId, BigDecimal amount, String reason, String description) {
        log.info("扣除用户金币: userId={}, amount={}, reason={}", userId, amount, reason);
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }
        
        // 获取账户（加锁）
        CoinAccount account = coinAccountRepository.findByUserIdWithLock(userId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.COIN_ACCOUNT_NOT_FOUND));
        
        int coinAmount = amount.intValue();
        
        // 检查余额是否充足
        if (!account.hasSufficientBalance(coinAmount)) {
            throw new BusinessException(SocialErrorCode.INSUFFICIENT_BALANCE);
        }
        
        int balanceBefore = account.getAvailableCoins();
        
        // 扣除金币
        account.spendCoins(coinAmount);
        account = coinAccountRepository.save(account);
        
        // 创建交易记录
        createTransactionRecord(userId, -coinAmount, balanceBefore, account.getAvailableCoins(),
                CoinTransactionRecord.TransactionType.EXPENSE, reason, description);
        
        log.info("金币扣除成功: userId={}, amount={}, newBalance={}", userId, coinAmount, account.getAvailableCoins());
        return account;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void transferCoins(Long fromUserId, Long toUserId, BigDecimal amount, String description) {
        log.info("转账金币: fromUserId={}, toUserId={}, amount={}", fromUserId, toUserId, amount);
        
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }
        
        if (fromUserId.equals(toUserId)) {
            throw new BusinessException(SocialErrorCode.INVALID_PARAMETER);
        }
        
        // 扣除发送方金币
        deductCoins(fromUserId, amount, "TRANSFER_OUT", "转账给用户" + toUserId + ": " + description);
        
        // 增加接收方金币
        addCoins(toUserId, amount, "TRANSFER_IN", "来自用户" + fromUserId + "的转账: " + description);
        
        log.info("金币转账成功: fromUserId={}, toUserId={}, amount={}", fromUserId, toUserId, amount);
    }

    @Override
    public boolean hasSufficientBalance(Long userId, BigDecimal amount) {
        Optional<CoinAccount> accountOpt = coinAccountRepository.findByUserId(userId);
        if (accountOpt.isEmpty()) {
            return false;
        }
        
        return accountOpt.get().hasSufficientBalance(amount.intValue());
    }

    @Override
    public BigDecimal getBalance(Long userId) {
        return coinAccountRepository.findByUserId(userId)
                .map(account -> BigDecimal.valueOf(account.getTotalCoins()))
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public BigDecimal getFrozenBalance(Long userId) {
        return coinAccountRepository.findByUserId(userId)
                .map(account -> BigDecimal.valueOf(account.getFrozenCoins()))
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public BigDecimal getAvailableBalance(Long userId) {
        return coinAccountRepository.findByUserId(userId)
                .map(account -> BigDecimal.valueOf(account.getAvailableCoins()))
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public Page<CoinAccount> getAccounts(CoinAccount.AccountStatus status, Pageable pageable) {
        log.debug("分页查询金币账户: status={}", status);
        
        // 由于CoinAccount实体中没有status字段，这里返回所有账户
        return coinAccountRepository.findAll(pageable);
    }

    @Override
    public Page<CoinAccount> searchAccounts(String keyword, Pageable pageable) {
        log.debug("搜索金币账户: keyword={}", keyword);
        
        // 简单实现，实际应该根据用户名等信息搜索
        return coinAccountRepository.findAll(pageable);
    }

    @Override
    public List<CoinAccount> getBalanceRanking(int limit) {
        log.debug("获取金币余额排行榜: limit={}", limit);
        
        List<CoinAccount> topAccounts = coinAccountRepository.findTopByTotalCoins();
        return topAccounts.stream().limit(limit).toList();
    }

    @Override
    public List<CoinAccount> getActiveAccounts(int days, int limit) {
        log.debug("获取活跃账户: days={}, limit={}", days, limit);
        
        List<CoinAccount> activeAccounts = coinAccountRepository.findActiveAccounts();
        return activeAccounts.stream().limit(limit).toList();
    }

    /**
     * 创建交易记录
     */
    private void createTransactionRecord(Long userId, int amount, int balanceBefore, int balanceAfter,
                                       CoinTransactionRecord.TransactionType type, String source, String description) {
        CoinTransactionRecord record = CoinTransactionRecord.builder()
                .transactionNo(generateTransactionNo())
                .userId(userId)
                .amount(amount)
                .balanceBefore(balanceBefore)
                .balanceAfter(balanceAfter)
                .transactionType(type)
                .description(description + " (来源: " + source + ")")
                .status(CoinTransactionRecord.TransactionStatus.SUCCESS)
                .build();
        
        coinTransactionRepository.save(record);
    }

    @Override
    public CoinAccountDTO.AccountStatistics getAccountStatistics() {
        log.debug("获取账户统计信息");

        long totalAccounts = coinAccountRepository.count();
        long activeAccounts = coinAccountRepository.findActiveAccounts().size();
        Long totalBalance = coinAccountRepository.getTotalCoinsInSystem();
        Long totalFrozenBalance = coinAccountRepository.getTotalFrozenCoins();
        Double averageBalance = coinAccountRepository.getAverageTotalCoins();

        return CoinAccountDTO.AccountStatistics.builder()
                .totalAccounts(totalAccounts)
                .activeAccounts(activeAccounts)
                .frozenAccounts(0L) // 暂时设为0，需要根据实际状态字段实现
                .suspiciousAccounts(0L) // 暂时设为0，需要根据实际状态字段实现
                .totalBalance(totalBalance != null ? BigDecimal.valueOf(totalBalance) : BigDecimal.ZERO)
                .totalFrozenBalance(totalFrozenBalance != null ? BigDecimal.valueOf(totalFrozenBalance) : BigDecimal.ZERO)
                .averageBalance(averageBalance != null ? BigDecimal.valueOf(averageBalance) : BigDecimal.ZERO)
                .todayNewAccounts(0L) // 需要根据创建时间统计
                .weekNewAccounts(0L) // 需要根据创建时间统计
                .build();
    }

    @Override
    public CoinAccountDTO.UserAccountStatistics getUserAccountStatistics(Long userId) {
        log.debug("获取用户账户统计: userId={}", userId);

        CoinAccount account = coinAccountRepository.findByUserId(userId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.COIN_ACCOUNT_NOT_FOUND));

        long transactionCount = coinTransactionRepository.countByUserId(userId);
        int totalIncome = coinTransactionRepository.sumIncomeByUserId(userId);
        int totalExpense = coinTransactionRepository.sumExpenseByUserId(userId);

        Optional<CoinTransactionRecord> lastTransaction = coinTransactionRepository.findTopByUserIdOrderByCreatedAtDesc(userId);

        return CoinAccountDTO.UserAccountStatistics.builder()
                .userId(userId)
                .totalTransactions(transactionCount)
                .incomeTransactions((long) totalIncome) // 简化实现
                .expenseTransactions((long) totalExpense) // 简化实现
                .totalIncome(BigDecimal.valueOf(totalIncome))
                .totalExpense(BigDecimal.valueOf(totalExpense))
                .netGain(BigDecimal.valueOf(totalIncome - totalExpense))
                .maxIncome(BigDecimal.valueOf(totalIncome)) // 简化实现
                .maxExpense(BigDecimal.valueOf(totalExpense)) // 简化实现
                .build();
    }

    @Override
    public List<CoinAccountDTO.BalanceDistribution> getBalanceDistribution() {
        log.debug("获取余额分布统计");

        List<CoinAccountDTO.BalanceDistribution> distributions = new ArrayList<>();

        // 简单的余额分布统计
        long total = coinAccountRepository.count();
        if (total == 0) {
            return distributions;
        }

        // 0金币
        long zeroBalance = coinAccountRepository.countUsersByMinBalance(0) - coinAccountRepository.countUsersByMinBalance(1);
        distributions.add(createBalanceDistribution("0", zeroBalance, total));

        // 1-100金币
        long range1to100 = coinAccountRepository.countUsersByMinBalance(1) - coinAccountRepository.countUsersByMinBalance(101);
        distributions.add(createBalanceDistribution("1-100", range1to100, total));

        // 101-1000金币
        long range101to1000 = coinAccountRepository.countUsersByMinBalance(101) - coinAccountRepository.countUsersByMinBalance(1001);
        distributions.add(createBalanceDistribution("101-1000", range101to1000, total));

        // 1000+金币
        long range1000Plus = coinAccountRepository.countUsersByMinBalance(1001);
        distributions.add(createBalanceDistribution("1000+", range1000Plus, total));

        return distributions;
    }

    @Override
    public List<CoinAccountDTO.GrowthTrend> getAccountGrowthTrend(int days) {
        log.debug("获取账户增长趋势: days={}", days);

        List<CoinAccountDTO.GrowthTrend> trends = new ArrayList<>();

        // 简单实现，实际应该根据创建时间统计每日数据
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            trends.add(CoinAccountDTO.GrowthTrend.builder()
                    .date(date)
                    .newAccounts(0L) // 需要根据实际数据统计
                    .totalAccounts(coinAccountRepository.count())
                    .activeAccounts(0L) // 需要根据实际数据统计
                    .totalBalance(BigDecimal.valueOf(coinAccountRepository.getTotalCoinsInSystem() != null ?
                        coinAccountRepository.getTotalCoinsInSystem() : 0))
                    .build());
        }

        return trends;
    }

    @Override
    public CoinAccountDTO.RiskAssessment assessAccountRisk(Long userId) {
        log.debug("评估账户风险: userId={}", userId);

        CoinAccount account = coinAccountRepository.findByUserId(userId)
                .orElseThrow(() -> new BusinessException(SocialErrorCode.COIN_ACCOUNT_NOT_FOUND));

        List<String> riskFactors = new ArrayList<>();
        List<String> recommendations = new ArrayList<>();
        int riskScore = 0;
        String riskLevel = "LOW";

        // 检查账户余额异常
        if (!account.isValid()) {
            riskFactors.add("账户数据不一致");
            recommendations.add("修复账户数据");
            riskScore += 30;
        }

        // 检查负余额
        if (account.getAvailableCoins() < 0 || account.getFrozenCoins() < 0) {
            riskFactors.add("存在负余额");
            recommendations.add("检查交易记录");
            riskScore += 50;
        }

        // 根据风险评分确定风险等级
        if (riskScore >= 70) {
            riskLevel = "HIGH";
        } else if (riskScore >= 30) {
            riskLevel = "MEDIUM";
        }

        return CoinAccountDTO.RiskAssessment.builder()
                .userId(userId)
                .riskLevel(riskLevel)
                .riskScore(riskScore)
                .riskFactors(riskFactors)
                .recommendations(recommendations)
                .assessmentTime(Instant.now())
                .build();
    }

    @Override
    public List<CoinAccount> getAbnormalAccounts() {
        log.debug("获取异常账户");

        List<CoinAccount> abnormalAccounts = new ArrayList<>();

        // 获取数据异常的账户
        abnormalAccounts.addAll(coinAccountRepository.findAnomalousAccounts());

        // 获取负余额账户
        abnormalAccounts.addAll(coinAccountRepository.findNegativeBalanceAccounts());

        return abnormalAccounts;
    }

    @Override
    @Transactional
    public void markSuspiciousAccount(Long userId, String reason, String operatorId) {
        log.info("标记可疑账户: userId={}, reason={}, operatorId={}", userId, reason, operatorId);

        CoinAccount account = getAccountInternal(userId);
        // 由于CoinAccount实体中没有可疑标记字段，这里只记录日志
        // 实际实现中应该添加相应字段或使用其他方式标记

        log.info("账户已标记为可疑: userId={}, reason={}, operatorId={}", userId, reason, operatorId);
    }

    @Override
    @Transactional
    public void unmarkSuspiciousAccount(Long userId, String operatorId) {
        log.info("解除可疑标记: userId={}, operatorId={}", userId, operatorId);

        CoinAccount account = getAccountInternal(userId);
        // 由于CoinAccount实体中没有可疑标记字段，这里只记录日志

        log.info("账户可疑标记已解除: userId={}, operatorId={}", userId, operatorId);
    }

    /**
     * 创建余额分布统计项
     */
    private CoinAccountDTO.BalanceDistribution createBalanceDistribution(String range, long count, long total) {
        double percentage = total > 0 ? (count * 100.0 / total) : 0.0;
        return CoinAccountDTO.BalanceDistribution.builder()
                .balanceRange(range)
                .accountCount(count)
                .percentage(percentage)
                .totalBalance(BigDecimal.ZERO) // 需要根据实际数据计算
                .build();
    }

    @Override
    @Transactional
    public List<CoinAccount> batchCreateAccounts(List<Long> userIds) {
        log.info("批量创建金币账户: userCount={}", userIds.size());

        List<CoinAccount> accounts = new ArrayList<>();

        for (Long userId : userIds) {
            try {
                if (!coinAccountRepository.existsByUserId(userId)) {
                    CoinAccount account = createAccount(userId);
                    accounts.add(account);
                }
            } catch (Exception e) {
                log.error("创建账户失败: userId={}", userId, e);
            }
        }

        log.info("批量创建账户完成: 成功创建{}个账户", accounts.size());
        return accounts;
    }

    @Override
    @Transactional
    public void batchAddCoins(List<Long> userIds, BigDecimal amount, String source, String description) {
        log.info("批量增加金币: userCount={}, amount={}, source={}", userIds.size(), amount, source);

        int successCount = 0;
        for (Long userId : userIds) {
            try {
                addCoins(userId, amount, source, description);
                successCount++;
            } catch (Exception e) {
                log.error("增加金币失败: userId={}, amount={}", userId, amount, e);
            }
        }

        log.info("批量增加金币完成: 成功处理{}个账户", successCount);
    }

    @Override
    @Transactional
    public void batchDeductCoins(List<Long> userIds, BigDecimal amount, String reason, String description) {
        log.info("批量扣除金币: userCount={}, amount={}, reason={}", userIds.size(), amount, reason);

        int successCount = 0;
        for (Long userId : userIds) {
            try {
                deductCoins(userId, amount, reason, description);
                successCount++;
            } catch (Exception e) {
                log.error("扣除金币失败: userId={}, amount={}", userId, amount, e);
            }
        }

        log.info("批量扣除金币完成: 成功处理{}个账户", successCount);
    }

    @Override
    @Transactional
    public int cleanupZeroBalanceAccounts(int days) {
        log.info("清理零余额账户: days={}", days);

        Instant cutoffTime = Instant.now().minusSeconds(days * 24 * 60 * 60L);

        // 查找零余额且长时间未活动的账户
        List<CoinAccount> zeroBalanceAccounts = coinAccountRepository.findAll().stream()
                .filter(account -> account.getTotalCoins() == 0 &&
                                 account.getUpdatedAt().isBefore(cutoffTime))
                .toList();

        int cleanedCount = 0;
        for (CoinAccount account : zeroBalanceAccounts) {
            try {
                // 检查是否有未完成的交易
                long pendingTransactions = coinTransactionRepository.countByUserIdAndTransactionType(
                    account.getUserId(), CoinTransactionRecord.TransactionType.INCOME);

                if (pendingTransactions == 0) {
                    coinAccountRepository.delete(account);
                    cleanedCount++;
                }
            } catch (Exception e) {
                log.error("清理账户失败: userId={}", account.getUserId(), e);
            }
        }

        log.info("零余额账户清理完成: 清理了{}个账户", cleanedCount);
        return cleanedCount;
    }

    @Override
    @Transactional
    public void repairAccountData(Long userId) {
        log.info("修复账户数据: userId={}", userId);

        CoinAccount account = getAccountInternal(userId);

        // 重新计算账户余额
        int totalEarned = coinTransactionRepository.sumIncomeByUserId(userId);
        int totalSpent = coinTransactionRepository.sumExpenseByUserId(userId);

        account.setTotalEarned(totalEarned);
        account.setTotalSpent(totalSpent);
        account.setTotalCoins(totalEarned - totalSpent - account.getTotalExchanged());
        account.setAvailableCoins(account.getTotalCoins() - account.getFrozenCoins());

        coinAccountRepository.save(account);

        log.info("账户数据修复完成: userId={}, newBalance={}", userId, account.getTotalCoins());
    }

    @Override
    @Transactional
    public void recalculateBalance(Long userId) {
        log.info("重新计算账户余额: userId={}", userId);

        repairAccountData(userId);
    }

    @Override
    @Transactional
    public void syncAccountStatus() {
        log.info("同步账户状态");

        List<CoinAccount> anomalousAccounts = coinAccountRepository.findAnomalousAccounts();

        int repairedCount = 0;
        for (CoinAccount account : anomalousAccounts) {
            try {
                repairAccountData(account.getUserId());
                repairedCount++;
            } catch (Exception e) {
                log.error("修复账户失败: userId={}", account.getUserId(), e);
            }
        }

        log.info("账户状态同步完成: 修复了{}个账户", repairedCount);
    }

    /**
     * 生成交易流水号
     */
    private String generateTransactionNo() {
        return "COIN_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
