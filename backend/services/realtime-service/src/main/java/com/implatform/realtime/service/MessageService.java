package com.implatform.realtime.service;

import com.implatform.common.webflux.response.PageResult;
import com.implatform.message.dto.MessageDTO;
import com.implatform.message.dto.SendMessageDTO;
import com.implatform.realtime.entity.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 消息服务接口 - IM平台核心消息处理服务
 *
 * <p><strong>业务用途</strong>：
 * 提供IM平台中所有消息相关的核心业务功能，包括消息发送、接收、存储、查询、撤回、删除等完整的消息生命周期管理。
 * 支持多种消息类型（文本、图片、视频、音频、文件等）和特殊功能（自毁消息、定时消息、消息反应等）。
 *
 * <p><strong>核心功能模块</strong>：
 * <ul>
 *   <li><strong>消息发送</strong>：支持实时消息发送，包括内容验证、权限检查、存储持久化</li>
 *   <li><strong>消息查询</strong>：提供分页查询、条件筛选、历史消息检索功能</li>
 *   <li><strong>消息管理</strong>：支持消息撤回、删除、编辑等管理操作</li>
 *   <li><strong>状态管理</strong>：处理消息已读状态、未读计数、送达状态等</li>
 *   <li><strong>特殊消息</strong>：支持自毁消息、定时消息、系统消息等特殊类型</li>
 * </ul>
 *
 * <p><strong>业务场景</strong>：
 * <ul>
 *   <li>用户在私聊或群聊中发送各种类型的消息</li>
 *   <li>查看历史聊天记录和消息详情</li>
 *   <li>管理消息状态（撤回不当消息、删除敏感内容）</li>
 *   <li>跟踪消息传递状态和用户阅读情况</li>
 *   <li>处理特殊消息需求（阅后即焚、定时发送）</li>
 * </ul>
 *
 * <p><strong>技术特性</strong>：
 * <ul>
 *   <li><strong>事务安全</strong>：所有写操作都在事务中执行，确保数据一致性</li>
 *   <li><strong>性能优化</strong>：支持分页查询、索引优化、缓存策略</li>
 *   <li><strong>并发处理</strong>：支持高并发消息发送和查询</li>
 *   <li><strong>扩展性</strong>：支持插件化的消息类型和处理器</li>
 *   <li><strong>监控审计</strong>：完整的操作日志和性能监控</li>
 * </ul>
 *
 * <p><strong>集成关系</strong>：
 * <ul>
 *   <li>与ConversationService协作管理会话和消息的关联关系</li>
 *   <li>与NotificationService集成实现消息推送和通知</li>
 *   <li>与MediaService协作处理媒体消息的存储和访问</li>
 *   <li>与UserService验证用户权限和获取用户信息</li>
 *   <li>与SecurityService处理消息加密和安全审计</li>
 * </ul>
 *
 * <p><strong>使用示例</strong>：
 * <pre>
 * // 发送文本消息
 * SendMessageDTO messageDTO = new SendMessageDTO();
 * messageDTO.setConversationId(conversationId);
 * messageDTO.setMessageType(MessageType.TEXT);
 * messageDTO.setContent("Hello, World!");
 * MessageDTO result = messageService.sendMessage(messageDTO, senderId);
 *
 * // 查询会话消息
 * PageResult&lt;MessageDTO&gt; messages = messageService.getConversationMessages(
 *     conversationId, 0, 20);
 *
 * // 撤回消息
 * messageService.recallMessage(messageId, userId);
 * </pre>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface MessageService {
    
    /**
     * 发送消息到指定会话
     *
     * <p><strong>业务功能</strong>：
     * 将用户创建的消息发送到指定的会话中，支持多种消息类型和特殊功能。
     * 执行完整的消息发送流程，包括权限验证、内容检查、存储持久化、状态更新等。
     *
     * <p><strong>处理流程</strong>：
     * <ol>
     *   <li>验证发送者权限和会话状态</li>
     *   <li>检查消息内容和格式</li>
     *   <li>处理特殊消息类型（自毁、定时等）</li>
     *   <li>保存消息到数据库</li>
     *   <li>更新会话最后消息信息</li>
     *   <li>触发实时推送和通知</li>
     * </ol>
     *
     * @param sendMessageDTO 消息发送请求对象，包含消息内容、类型、会话ID等信息
     * @param senderId 发送者用户ID，用于权限验证和消息归属
     * @return MessageDTO 发送成功的消息信息，包含消息ID、时间戳、状态等
     * @throws BusinessException 当会话不存在、用户无权限、消息内容无效时抛出
     *
     * @example
     * <pre>
     * SendMessageDTO dto = new SendMessageDTO();
     * dto.setConversationId(123L);
     * dto.setMessageType(MessageType.TEXT);
     * dto.setContent("Hello!");
     * MessageDTO result = messageService.sendMessage(dto, 456L);
     * </pre>
     */
    MessageDTO sendMessage(SendMessageDTO sendMessageDTO, Long senderId);

    /**
     * 分页获取会话中的消息列表
     *
     * <p><strong>业务功能</strong>：
     * 按时间倒序获取指定会话中的消息记录，支持分页查询以优化性能。
     * 自动过滤已删除的消息，确保用户只能看到有效的消息内容。
     *
     * <p><strong>查询特性</strong>：
     * <ul>
     *   <li>按创建时间倒序排列（最新消息在前）</li>
     *   <li>自动过滤删除状态的消息</li>
     *   <li>支持分页查询，避免大量数据加载</li>
     *   <li>包含消息的完整信息和状态</li>
     * </ul>
     *
     * @param conversationId 会话ID，指定要查询的会话
     * @param page 页码，从0开始计数
     * @param size 每页大小，建议10-50之间
     * @return PageResult&lt;MessageDTO&gt; 分页消息列表，包含总数和分页信息
     * @throws BusinessException 当会话不存在或用户无权限访问时抛出
     *
     * @example
     * <pre>
     * // 获取第一页，每页20条消息
     * PageResult&lt;MessageDTO&gt; messages = messageService.getConversationMessages(123L, 0, 20);
     * List&lt;MessageDTO&gt; messageList = messages.getData();
     * </pre>
     */
    PageResult<MessageDTO> getConversationMessages(Long conversationId, Integer page, Integer size);

    /**
     * 撤回指定的消息
     *
     * <p><strong>业务功能</strong>：
     * 允许消息发送者在限定时间内撤回已发送的消息。撤回后消息状态变为已撤回，
     * 其他用户将看到"消息已撤回"的提示，但消息记录仍保留用于审计。
     *
     * <p><strong>撤回规则</strong>：
     * <ul>
     *   <li>只有消息发送者可以撤回自己的消息</li>
     *   <li>消息发送后2分钟内可以撤回</li>
     *   <li>撤回后消息状态变为RECALLED</li>
     *   <li>撤回操作会通知会话中的其他用户</li>
     * </ul>
     *
     * @param messageId 要撤回的消息ID
     * @param userId 操作用户ID，必须是消息的发送者
     * @throws BusinessException 当消息不存在、用户无权限或超过撤回时限时抛出
     *
     * @example
     * <pre>
     * // 撤回消息
     * messageService.recallMessage(789L, 456L);
     * </pre>
     */
    void recallMessage(Long messageId, Long userId);

    /**
     * 删除指定的消息
     *
     * <p><strong>业务功能</strong>：
     * 允许消息发送者删除自己发送的消息。删除后消息状态变为已删除，
     * 在消息列表中不再显示，但数据库中保留记录用于审计和恢复。
     *
     * <p><strong>删除规则</strong>：
     * <ul>
     *   <li>只有消息发送者可以删除自己的消息</li>
     *   <li>删除操作不受时间限制</li>
     *   <li>删除后消息在列表中不可见</li>
     *   <li>数据库中保留删除记录用于审计</li>
     * </ul>
     *
     * @param messageId 要删除的消息ID
     * @param userId 操作用户ID，必须是消息的发送者
     * @throws BusinessException 当消息不存在或用户无权限时抛出
     *
     * @example
     * <pre>
     * // 删除消息
     * messageService.deleteMessage(789L, 456L);
     * </pre>
     */
    void deleteMessage(Long messageId, Long userId);

    /**
     * 标记会话中的消息为已读状态
     *
     * <p><strong>业务功能</strong>：
     * 将指定会话中用户的所有未读消息标记为已读状态，更新最后阅读时间，
     * 清零该会话的未读消息计数，并通知发送者消息已被阅读。
     *
     * <p><strong>处理逻辑</strong>：
     * <ul>
     *   <li>更新用户在该会话中的最后阅读时间</li>
     *   <li>清零该会话的未读消息计数</li>
     *   <li>发送已读回执给消息发送者</li>
     *   <li>更新消息的已读状态统计</li>
     * </ul>
     *
     * @param conversationId 会话ID，指定要标记已读的会话
     * @param userId 用户ID，指定执行已读操作的用户
     * @throws BusinessException 当会话不存在或用户不是会话成员时抛出
     *
     * @example
     * <pre>
     * // 标记会话已读
     * messageService.markAsRead(123L, 456L);
     * </pre>
     */
    void markAsRead(Long conversationId, Long userId);

    /**
     * 获取用户的未读消息总数
     *
     * <p><strong>业务功能</strong>：
     * 统计用户在所有会话中的未读消息总数，用于显示应用图标上的红点提醒。
     * 计算范围包括私聊、群聊等所有类型的会话。
     *
     * <p><strong>统计规则</strong>：
     * <ul>
     *   <li>统计所有会话中的未读消息</li>
     *   <li>排除已静音会话的消息（可选）</li>
     *   <li>排除系统消息和通知消息</li>
     *   <li>实时更新，支持缓存优化</li>
     * </ul>
     *
     * @param userId 用户ID，指定要统计未读消息的用户
     * @return Integer 未读消息总数，0表示没有未读消息
     *
     * @example
     * <pre>
     * // 获取未读消息数
     * Integer unreadCount = messageService.getUnreadCount(456L);
     * if (unreadCount > 0) {
     *     // 显示红点提醒
     * }
     * </pre>
     */
    Integer getUnreadCount(Long userId);

    /**
     * 获取用户自毁消息列表
     */
    Page<Message> getUserSelfDestructMessages(Long userId, Pageable pageable);

    /**
     * 获取自毁消息统计
     */
    SelfDestructController.SelfDestructStatistics getSelfDestructStatistics();

    /**
     * 获取用户自毁消息统计
     */
    SelfDestructController.UserSelfDestructStatistics getUserSelfDestructStatistics(Long userId);

    /**
     * 保存消息
     */
    Message saveMessage(Message message);

    // ==================== 管理接口方法 ====================

    /**
     * 获取消息列表（管理接口）
     */
    Page<MessageDTO> getMessages(Map<String, Object> params, Pageable pageable);

    /**
     * 根据ID获取消息（管理接口）
     */
    MessageDTO getMessageById(Long id);

    /**
     * 批量删除消息（管理接口）
     */
    void deleteMessages(List<Long> ids);

    /**
     * 获取用户消息列表（管理接口）
     */
    Page<MessageDTO> getUserMessages(Long userId, Pageable pageable);

    /**
     * 搜索消息（管理接口）
     */
    Page<MessageDTO> searchMessages(String keyword, String messageType, Pageable pageable);

    /**
     * 获取消息统计（管理接口）
     */
    Map<String, Object> getMessageStatistics();

    /**
     * 获取热门消息（管理接口）
     */
    Page<MessageDTO> getPopularMessages(Pageable pageable);

    /**
     * 获取敏感消息（管理接口）
     */
    Page<MessageDTO> getSensitiveMessages(Pageable pageable);

    /**
     * 审核消息（管理接口）
     */
    void reviewMessage(Long id, String action, String reason);

    /**
     * 标记消息（管理接口）
     */
    void markMessage(Long id, String markType, String reason);

    /**
     * 导出消息（管理接口）
     */
    String exportMessages(Map<String, Object> params);

    /**
     * 获取消息趋势（管理接口）
     */
    List<Map<String, Object>> getMessageTrends(String startDate, String endDate);
}