package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CloudNote;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 云笔记Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CloudNoteRepository extends R2dbcRepository<CloudNote, Long> {

    /**
     * 根据用户ID查找笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND note_status = :noteStatus ORDER BY is_pinned DESC, updated_at DESC")
    Flux<CloudNote> findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus);

    /**
     * 分页查找用户笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND note_status = :noteStatus ORDER BY is_pinned DESC, updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<CloudNote> findByUserIdAndNoteStatusOrderByIsPinnedDescUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据文件夹ID查找笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND folder_id = :folderId AND note_status = :noteStatus ORDER BY is_pinned DESC, sort_order ASC, updated_at DESC")
    Flux<CloudNote> findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(@Param("userId") Long userId, @Param("folderId") Long folderId, @Param("noteStatus") String noteStatus);

    /**
     * 分页查找文件夹内笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND folder_id = :folderId AND note_status = :noteStatus ORDER BY is_pinned DESC, sort_order ASC, updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<CloudNote> findByUserIdAndFolderIdAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(@Param("userId") Long userId, @Param("folderId") Long folderId, @Param("noteStatus") String noteStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找根目录笔记（无文件夹）
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND folder_id IS NULL AND note_status = :noteStatus ORDER BY is_pinned DESC, sort_order ASC, updated_at DESC")
    Flux<CloudNote> findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus);

    /**
     * 分页查找根目录笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND folder_id IS NULL AND note_status = :noteStatus ORDER BY is_pinned DESC, sort_order ASC, updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<CloudNote> findByUserIdAndFolderIdIsNullAndNoteStatusOrderByIsPinnedDescSortOrderAscUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据笔记类型查找笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND note_type = :noteType AND note_status = :noteStatus ORDER BY updated_at DESC")
    Flux<CloudNote> findByUserIdAndNoteTypeAndNoteStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("noteType") String noteType, @Param("noteStatus") String noteStatus);

    /**
     * 查找置顶笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND is_pinned = true AND note_status = :noteStatus ORDER BY updated_at DESC")
    Flux<CloudNote> findByUserIdAndIsPinnedTrueAndNoteStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus);

    /**
     * 查找收藏笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND is_favorite = true AND note_status = :noteStatus ORDER BY updated_at DESC")
    Flux<CloudNote> findByUserIdAndIsFavoriteTrueAndNoteStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus);

    /**
     * 查找公开笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND is_public = true AND note_status = :noteStatus ORDER BY updated_at DESC")
    Flux<CloudNote> findByUserIdAndIsPublicTrueAndNoteStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("noteStatus") String noteStatus);

    /**
     * 根据优先级查找笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND priority = :priority AND note_status = :noteStatus ORDER BY updated_at DESC")
    Flux<CloudNote> findByUserIdAndPriorityAndNoteStatusOrderByUpdatedAtDesc(@Param("userId") Long userId, @Param("priority") String priority, @Param("noteStatus") String noteStatus);

    /**
     * 搜索笔记（标题和内容）
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND note_status = :noteStatus AND " +
           "(title LIKE CONCAT('%', :keyword, '%') OR content LIKE CONCAT('%', :keyword, '%') OR tags LIKE CONCAT('%', :keyword, '%')) " +
           "ORDER BY is_pinned DESC, updated_at DESC")
    Flux<CloudNote> searchNotes(@Param("userId") Long userId, @Param("noteStatus") String noteStatus, @Param("keyword") String keyword);

    /**
     * 分页搜索笔记
     */
    @Query("SELECT * FROM cloud_notes WHERE user_id = :userId AND note_status = :noteStatus AND " +
           "(title LIKE CONCAT('%', :keyword, '%') OR content LIKE CONCAT('%', :keyword, '%') OR tags LIKE CONCAT('%', :keyword, '%')) " +
           "ORDER BY is_pinned DESC, updated_at DESC LIMIT :limit OFFSET :offset")
    Flux<CloudNote> searchNotes(@Param("userId") Long userId, @Param("noteStatus") String noteStatus, @Param("keyword") String keyword, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据标签搜索笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus AND cn.tags LIKE %:tag% " +
           "ORDER BY cn.isPinned DESC, cn.updatedAt DESC")
    List<CloudNote> findByTag(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, @Param("tag") String tag);

    /**
     * 查找需要提醒的笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = 'ACTIVE' AND " +
           "cn.reminderAt IS NOT NULL AND cn.reminderAt <= :now")
    List<CloudNote> findNotesNeedingReminder(@Param("userId") Long userId, @Param("now") Instant now);

    /**
     * 查找过期笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = 'ACTIVE' AND " +
           "cn.expiresAt IS NOT NULL AND cn.expiresAt <= :now")
    List<CloudNote> findExpiredNotes(@Param("userId") Long userId, @Param("now") Instant now);

    /**
     * 查找最近编辑的笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus AND " +
           "cn.lastEditAt >= :since ORDER BY cn.lastEditAt DESC")
    List<CloudNote> findRecentlyEditedNotes(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, @Param("since") Instant since);

    /**
     * 查找最近阅读的笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus AND " +
           "cn.lastReadAt >= :since ORDER BY cn.lastReadAt DESC")
    List<CloudNote> findRecentlyReadNotes(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, @Param("since") Instant since);

    /**
     * 查找待同步的笔记
     */
    List<CloudNote> findByUserIdAndSyncStatus(Long userId, CloudNote.SyncStatus syncStatus);

    /**
     * 统计用户笔记数量
     */
    long countByUserIdAndNoteStatus(Long userId, CloudNote.NoteStatus noteStatus);

    /**
     * 统计文件夹内笔记数量
     */
    long countByUserIdAndFolderIdAndNoteStatus(Long userId, Long folderId, CloudNote.NoteStatus noteStatus);

    /**
     * 统计根目录笔记数量
     */
    long countByUserIdAndFolderIdIsNullAndNoteStatus(Long userId, CloudNote.NoteStatus noteStatus);

    /**
     * 统计各类型笔记数量
     */
    @Query("SELECT cn.noteType, COUNT(cn) FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus GROUP BY cn.noteType")
    List<Object[]> countByNoteType(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus);

    /**
     * 统计各状态笔记数量
     */
    @Query("SELECT cn.noteStatus, COUNT(cn) FROM CloudNote cn WHERE cn.userId = :userId GROUP BY cn.noteStatus")
    List<Object[]> countByNoteStatus(@Param("userId") Long userId);

    /**
     * 统计各优先级笔记数量
     */
    @Query("SELECT cn.priority, COUNT(cn) FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus GROUP BY cn.priority")
    List<Object[]> countByPriority(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus);

    /**
     * 获取用户笔记统计
     */
    @Query("SELECT COUNT(cn), SUM(cn.wordCount), SUM(cn.charCount), AVG(cn.readCount), AVG(cn.editCount) " +
           "FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus")
    List<Object[]> getUserNoteStatistics(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus);

    /**
     * 获取热门标签
     */
    @Query("SELECT cn.tags FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus AND cn.tags IS NOT NULL")
    List<String> findAllTags(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus);

    /**
     * 批量更新笔记状态
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.noteStatus = :status, cn.updatedAt = :now WHERE cn.userId = :userId AND cn.id IN :noteIds")
    int batchUpdateNoteStatus(@Param("userId") Long userId, @Param("noteIds") List<Long> noteIds,
                             @Param("status") CloudNote.NoteStatus status, @Param("now") Instant now);

    /**
     * 批量移动笔记到文件夹
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.folderId = :folderId, cn.updatedAt = :now WHERE cn.userId = :userId AND cn.id IN :noteIds")
    int batchMoveNotesToFolder(@Param("userId") Long userId, @Param("noteIds") List<Long> noteIds,
                              @Param("folderId") Long folderId, @Param("now") Instant now);

    /**
     * 批量置顶/取消置顶笔记
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.isPinned = :pinned, cn.updatedAt = :now WHERE cn.userId = :userId AND cn.id IN :noteIds")
    int batchPinNotes(@Param("userId") Long userId, @Param("noteIds") List<Long> noteIds,
                     @Param("pinned") Boolean pinned, @Param("now") Instant now);

    /**
     * 批量收藏/取消收藏笔记
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.isFavorite = :favorite, cn.updatedAt = :now WHERE cn.userId = :userId AND cn.id IN :noteIds")
    int batchFavoriteNotes(@Param("userId") Long userId, @Param("noteIds") List<Long> noteIds,
                          @Param("favorite") Boolean favorite, @Param("now") Instant now);

    /**
     * 更新笔记排序
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.sortOrder = :sortOrder, cn.updatedAt = :now WHERE cn.id = :noteId AND cn.userId = :userId")
    int updateNoteSortOrder(@Param("userId") Long userId, @Param("noteId") Long noteId,
                           @Param("sortOrder") Integer sortOrder, @Param("now") Instant now);

    /**
     * 更新同步状态
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.syncStatus = :syncStatus, cn.lastSyncAt = :now WHERE cn.userId = :userId AND cn.id IN :noteIds")
    int updateSyncStatus(@Param("userId") Long userId, @Param("noteIds") List<Long> noteIds,
                        @Param("syncStatus") CloudNote.SyncStatus syncStatus, @Param("now") Instant now);

    /**
     * 清理过期笔记
     */
    @Modifying
    @Query("UPDATE CloudNote cn SET cn.noteStatus = 'DELETED', cn.updatedAt = :now WHERE cn.expiresAt IS NOT NULL AND cn.expiresAt < :now")
    int cleanupExpiredNotes(@Param("now") Instant now);

    /**
     * 物理删除已删除的笔记
     */
    @Modifying
    @Query("DELETE FROM CloudNote cn WHERE cn.noteStatus = 'DELETED' AND cn.updatedAt < :cutoffTime")
    int physicalDeleteOldNotes(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 获取笔记创建趋势
     */
    @Query("SELECT DATE(cn.createdAt), COUNT(cn) FROM CloudNote cn WHERE cn.userId = :userId AND " +
           "cn.createdAt >= :startDate GROUP BY DATE(cn.createdAt) ORDER BY DATE(cn.createdAt)")
    List<Object[]> getNoteCreationTrend(@Param("userId") Long userId, @Param("startDate") Instant startDate);

    /**
     * 获取笔记编辑趋势
     */
    @Query("SELECT DATE(cn.lastEditAt), COUNT(cn) FROM CloudNote cn WHERE cn.userId = :userId AND " +
           "cn.lastEditAt >= :startDate GROUP BY DATE(cn.lastEditAt) ORDER BY DATE(cn.lastEditAt)")
    List<Object[]> getNoteEditTrend(@Param("userId") Long userId, @Param("startDate") Instant startDate);

    /**
     * 获取最活跃的笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus " +
           "ORDER BY (cn.readCount + cn.editCount) DESC")
    List<CloudNote> getMostActiveNotes(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, Pageable pageable);

    /**
     * 获取最长的笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus " +
           "ORDER BY cn.wordCount DESC")
    List<CloudNote> getLongestNotes(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, Pageable pageable);

    /**
     * 查找相似笔记
     */
    @Query("SELECT cn FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus AND cn.id != :excludeId AND " +
           "(cn.title LIKE %:keyword% OR cn.content LIKE %:keyword% OR cn.tags LIKE %:keyword%) " +
           "ORDER BY cn.updatedAt DESC")
    List<CloudNote> findSimilarNotes(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus, 
                                    @Param("excludeId") Long excludeId, @Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取用户笔记概览
     */
    @Query("SELECT COUNT(cn), " +
           "COUNT(CASE WHEN cn.isPinned = true THEN 1 END), " +
           "COUNT(CASE WHEN cn.isFavorite = true THEN 1 END), " +
           "COUNT(CASE WHEN cn.isPublic = true THEN 1 END), " +
           "COUNT(CASE WHEN cn.isEncrypted = true THEN 1 END) " +
           "FROM CloudNote cn WHERE cn.userId = :userId AND cn.noteStatus = :noteStatus")
    List<Object[]> getUserNoteOverview(@Param("userId") Long userId, @Param("noteStatus") CloudNote.NoteStatus noteStatus);

    /**
     * 检查笔记是否存在
     */
    boolean existsByUserIdAndId(Long userId, Long noteId);

    /**
     * 检查标题是否重复
     */
    boolean existsByUserIdAndTitleAndNoteStatusAndIdNot(Long userId, String title, CloudNote.NoteStatus noteStatus, Long excludeId);

    /**
     * 获取下一个排序顺序
     */
    @Query("SELECT COALESCE(MAX(cn.sortOrder), 0) + 1 FROM CloudNote cn WHERE cn.userId = :userId AND cn.folderId = :folderId")
    Integer getNextSortOrder(@Param("userId") Long userId, @Param("folderId") Long folderId);

    /**
     * 获取下一个排序顺序（根目录）
     */
    @Query("SELECT COALESCE(MAX(cn.sortOrder), 0) + 1 FROM CloudNote cn WHERE cn.userId = :userId AND cn.folderId IS NULL")
    Integer getNextSortOrderForRoot(@Param("userId") Long userId);
}
