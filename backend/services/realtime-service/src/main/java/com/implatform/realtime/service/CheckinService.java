package com.implatform.realtime.service;

import com.implatform.realtime.dto.CheckinLeaderboardDTO;
import com.implatform.realtime.dto.checkin.CheckinDTO;
import com.implatform.realtime.dto.checkin.CoinAccountDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 签到服务接口 - IM平台签到系统核心服务
 *
 * <p><strong>业务用途</strong>：
 * 提供完整的签到系统功能，包括每日签到、补签、奖励计算、排行榜管理等。
 * 支持灵活的奖励规则配置、连续签到激励、金币兑换等完整的签到生态系统。
 *
 * <p><strong>核心功能模块</strong>：
 * <ul>
 *   <li><strong>签到管理</strong>：每日签到、补签、签到状态查询</li>
 *   <li><strong>奖励系统</strong>：基础奖励、连续奖励、特殊奖励计算</li>
 *   <li><strong>排行榜</strong>：多维度排行榜生成和管理</li>
 *   <li><strong>金币系统</strong>：金币账户管理、兑换功能</li>
 *   <li><strong>统计分析</strong>：签到数据统计和分析</li>
 * </ul>
 *
 * <p><strong>业务规则</strong>：
 * <ul>
 *   <li>每用户每日只能签到一次</li>
 *   <li>连续签到天数影响奖励金币数量</li>
 *   <li>补签需要消耗金币，成本递增</li>
 *   <li>金币可兑换为钱包余额</li>
 *   <li>排行榜支持点赞和社交互动</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface CheckinService {

    // ==================== 签到核心功能 ====================

    /**
     * 每日签到
     *
     * @param userId 用户ID
     * @param request 签到请求
     * @return 签到结果
     */
    CheckinDTO.CheckinResultDTO dailyCheckin(Long userId, CheckinDTO.DailyCheckinRequestDTO request);

    /**
     * 补签
     *
     * @param userId 用户ID
     * @param request 补签请求
     * @return 补签结果
     */
    CheckinDTO.MakeupCheckinResultDTO makeupCheckin(Long userId, CheckinDTO.MakeupCheckinRequestDTO request);

    /**
     * 获取用户签到状态
     *
     * @param userId 用户ID
     * @return 签到状态
     */
    CheckinDTO.CheckinStatusDTO getCheckinStatus(Long userId);

    /**
     * 获取用户签到历史
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 签到历史记录
     */
    List<CheckinDTO.CheckinRecordDTO> getCheckinHistory(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取签到日历
     *
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 签到日历
     */
    CheckinDTO.CheckinCalendarDTO getCheckinCalendar(Long userId, int year, int month);

    // ==================== 奖励和配置 ====================

    /**
     * 计算签到奖励
     *
     * @param userId 用户ID
     * @param checkinDate 签到日期
     * @param continuousDays 连续天数
     * @return 奖励金币数
     */
    int calculateCheckinReward(Long userId, LocalDate checkinDate, int continuousDays);

    /**
     * 计算补签成本
     *
     * @param userId 用户ID
     * @param targetDate 补签日期
     * @return 补签成本
     */
    int calculateMakeupCost(Long userId, LocalDate targetDate);

    /**
     * 获取奖励规则配置
     *
     * @return 奖励规则
     */
    CheckinLeaderboardDTO.CheckinRewardRulesDTO getRewardRules();

    /**
     * 获取补签规则配置
     *
     * @return 补签规则
     */
    CheckinLeaderboardDTO.MakeupRulesDTO getMakeupRules();

    // ==================== 排行榜功能 ====================

    /**
     * 获取签到排行榜
     *
     * @param type 排行榜类型
     * @param period 排行周期
     * @param pageable 分页参数
     * @return 排行榜数据
     */
    Page<CheckinLeaderboardDTO> getCheckinLeaderboard(String type, String period, Pageable pageable);

    /**
     * 获取用户在排行榜中的排名
     *
     * @param userId 用户ID
     * @param type 排行榜类型
     * @param period 排行周期
     * @return 用户排名信息
     */
    CheckinLeaderboardDTO.UserRankingDTO getUserRanking(Long userId, String type, String period);

    /**
     * 点赞排行榜记录
     *
     * @param userId 点赞用户ID
     * @param targetUserId 被点赞用户ID
     * @param type 排行榜类型
     * @param period 排行周期
     * @return 点赞结果
     */
    CheckinLeaderboardDTO.LikeResultDTO likeLeaderboardRecord(Long userId, Long targetUserId, String type, String period);

    /**
     * 取消点赞
     *
     * @param userId 用户ID
     * @param targetUserId 被点赞用户ID
     * @param type 排行榜类型
     * @param period 排行周期
     */
    void unlikeLeaderboardRecord(Long userId, Long targetUserId, String type, String period);

    // ==================== 金币系统 ====================

    /**
     * 获取用户金币账户信息
     *
     * @param userId 用户ID
     * @return 金币账户信息
     */
    CoinAccountDTO getCoinAccount(Long userId);

    /**
     * 金币兑换钱包余额
     *
     * @param userId 用户ID
     * @param request 兑换请求
     * @return 兑换结果
     */
    CheckinLeaderboardDTO.CoinExchangeResultDTO exchangeCoinsToWallet(Long userId, CheckinLeaderboardDTO.CoinExchangeRequestDTO request);

    /**
     * 获取金币交易记录
     *
     * @param userId 用户ID
     * @param type 交易类型
     * @param pageable 分页参数
     * @return 交易记录
     */
    Page<CheckinLeaderboardDTO.CoinTransactionDTO> getCoinTransactions(Long userId, String type, Pageable pageable);

    // ==================== 统计分析 ====================

    /**
     * 获取签到统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    CheckinLeaderboardDTO.CheckinStatisticsDTO getCheckinStatistics(Long userId);

    /**
     * 获取系统签到统计
     *
     * @return 系统统计信息
     */
    Map<String, Object> getSystemCheckinStatistics();

    /**
     * 获取用户签到趋势
     *
     * @param userId 用户ID
     * @param days 天数
     * @return 签到趋势数据
     */
    List<CheckinLeaderboardDTO.CheckinTrendDTO> getCheckinTrend(Long userId, int days);

    // ==================== 管理功能 ====================

    /**
     * 更新排行榜数据
     *
     * @param type 排行榜类型
     * @param period 排行周期
     */
    void updateLeaderboard(String type, String period);

    /**
     * 清理过期数据
     *
     * @param days 保留天数
     * @return 清理记录数
     */
    int cleanupExpiredData(int days);

    /**
     * 修复异常数据
     *
     * @return 修复记录数
     */
    int repairAnomalousData();
}
