package com.implatform.realtime.service;

import com.implatform.realtime.entity.Bot;
import com.implatform.realtime.entity.BotCommand;
import com.implatform.realtime.entity.BotCommand.CommandScope;
import com.implatform.realtime.entity.BotInstallation;
import com.implatform.realtime.entity.BotInstallation.InstallationType;
import com.implatform.realtime.dto.BotDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 机器人服务接口
 * 提供智能机器人管理和交互功能
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface BotService {

    /**
     * 创建机器人
     */
    @Transactional
    Bot createBot(Bot bot);

    /**
     * 创建机器人（控制器版本）
     */
    @Transactional
    Bot createBot(String userId, String username, String displayName, String description);

    /**
     * 更新机器人信息
     */
    @Transactional
    Bot updateBot(Long botId, Bot botUpdate, String updatedBy);

    /**
     * 更新机器人信息（控制器版本）
     */
    @Transactional
    Bot updateBot(Long botId, String userId, String displayName, String description, String avatarUrl, String webhookUrl, List<String> tags);

    /**
     * 获取机器人详情
     */
    Optional<Bot> getBotById(Long botId);

    /**
     * 根据用户名获取机器人
     */
    Optional<Bot> getBotByUsername(String username);

    /**
     * 根据Token获取机器人
     */
    Optional<Bot> getBotByToken(String botToken);



    /**
     * 获取用户创建的机器人
     */
    List<Bot> getUserBots(String userId);

    /**
     * 获取用户创建的机器人（分页）
     */
    Page<Bot> getUserBots(String userId, int page, int size);

    /**
     * 获取公开机器人列表
     */
    Page<Bot> getPublicBots(Pageable pageable);

    /**
     * 获取公开机器人列表（分页）
     */
    Page<Bot> getPublicBots(int page, int size);

    /**
     * 获取热门机器人
     */
    List<Bot> getPopularBots(int limit);

    /**
     * 获取高评分机器人
     */
    List<Bot> getHighRatedBots(int limit);

    /**
     * 根据类型获取机器人
     */
    List<Bot> getBotsByType(Bot.BotType botType);

    /**
     * 搜索机器人
     */
    Page<BotDTO.BotInfoDTO> searchBots(String keyword, Pageable pageable);

    /**
     * 搜索机器人（分页版本）
     */
    Page<BotDTO.BotInfoDTO> searchBots(String keyword, int page, int size);

    /**
     * 根据标签获取机器人
     */
    List<Bot> getBotsByTags(List<String> tags);

    /**
     * 激活机器人
     */
    @Transactional
    void activateBot(Long botId, String activatedBy);

    /**
     * 暂停机器人
     */
    @Transactional
    void suspendBot(Long botId, String suspendedBy, String reason);

    /**
     * 删除机器人
     */
    @Transactional
    void deleteBot(Long botId, String deletedBy);

    /**
     * 增加安装数量
     */
    @Transactional
    void incrementInstallCount(Long botId);

    /**
     * 减少安装数量
     */
    @Transactional
    void decrementInstallCount(Long botId);

    /**
     * 验证机器人Token
     */
    boolean validateBotToken(String botToken);

    /**
     * 重新生成机器人Token
     */
    @Transactional
    String regenerateBotToken(Long botId, String requestedBy);

    /**
     * 定时清理不活跃机器人
     */
    void scheduledCleanupInactiveBots();

    /**
     * 设置机器人Webhook
     */
    @Transactional
    void setWebhook(Long botId, String userId, String webhookUrl, String secretToken, List<String> allowedUpdates);

    /**
     * 获取机器人命令
     */
    List<BotCommand> getBotCommands(Long botId);

    /**
     * 添加机器人命令
     */
    @Transactional
    BotCommand addCommand(Long botId, String userId, String command, String description, CommandScope scope, String languageCode);

    /**
     * 删除机器人命令
     */
    @Transactional
    void deleteCommand(String commandId, String userId);

    /**
     * 安装机器人
     */
    @Transactional
    BotInstallation installBot(Long botId, String userId, InstallationType installationType, String targetId);

    /**
     * 卸载机器人
     */
    @Transactional
    void uninstallBot(Long botId, String userId, InstallationType installationType, String targetId);

    /**
     * 获取用户安装的机器人
     */
    List<BotInstallation> getUserInstalledBots(String userId);

    /**
     * 获取机器人统计信息
     */
    Map<String, Object> getBotStatistics();

    /**
     * 获取活跃机器人
     */
    Page<Bot> getActiveBots(Pageable pageable);

    /**
     * 审核机器人
     */
    @Transactional
    void reviewBot(Long id, String action, String reason);

    /**
     * 测试机器人
     */
    Object testBot(Long id, String message);

    /**
     * 导出机器人数据
     */
    String exportBots(Map<String, Object> params);

    /**
     * 获取机器人使用趋势
     */
    List<Map<String, Object>> getBotUsageTrends(String startDate, String endDate);
}
