package com.implatform.realtime.service.impl;

import com.implatform.realtime.entity.Conversation;
import com.implatform.realtime.entity.UserConversationSettings;
import com.implatform.realtime.repository.ConversationRepository;
import com.implatform.repository.UserConversationSettingsRepository;
import com.implatform.service.SystemConversationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 系统对话管理服务实现
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemConversationServiceImpl implements SystemConversationService {

    private final ConversationRepository conversationRepository;
    private final UserConversationSettingsRepository userConversationSettingsRepository;
    
    @Override
    @Transactional
    public Conversation getOrCreateSystemNotificationConversation(Long userId) {
        log.debug("获取或创建系统通知对话: userId={}", userId);
        
        // 先尝试获取现有的系统通知对话
        Conversation existingConversation = getSystemNotificationConversation(userId);
        if (existingConversation != null) {
            log.debug("找到现有系统通知对话: conversationId={}", existingConversation.getId());
            return existingConversation;
        }
        
        // 如果不存在，则创建新的系统通知对话
        log.info("系统通知对话不存在，开始创建: userId={}", userId);
        return createSystemNotificationConversation(userId);
    }
    
    @Override
    @Transactional
    public Conversation createSystemNotificationConversation(Long userId) {
        log.info("创建系统通知对话: userId={}", userId);
        
        // 检查是否已存在
        if (hasSystemNotificationConversation(userId)) {
            log.warn("用户已有系统通知对话: userId={}", userId);
            return getSystemNotificationConversation(userId);
        }
        
        // 获取或创建系统通知对话
        Long conversationId = getSystemNotificationConversationId();
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new RuntimeException("系统通知对话创建失败"));

        // 创建用户对话设置
        UserConversationSettings settings = UserConversationSettings.builder()
                .userId(userId)
                .conversationId(conversationId)
                .customName(SYSTEM_NOTIFICATION_TITLE)
                .isPinned(false)
                .isMuted(false)
                .isHidden(false)
                .unreadCount(0)
                .status(UserConversationSettings.UserConversationStatus.ACTIVE)
                .notificationLevel(UserConversationSettings.NotificationLevel.ALL)
                .accessCount(0L)
                .build();

        userConversationSettingsRepository.save(settings);
        log.info("用户系统通知对话设置创建成功: userId={}, conversationId={}", userId, conversationId);

        return conversation;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean hasSystemNotificationConversation(Long userId) {
        // 检查用户是否有系统通知对话设置
        Long conversationId = getSystemNotificationConversationId();
        return userConversationSettingsRepository.existsByUserIdAndConversationId(userId, conversationId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Conversation getSystemNotificationConversation(Long userId) {
        // 通过用户对话设置查找系统通知对话
        Optional<UserConversationSettings> settingsOpt = userConversationSettingsRepository
                .findByUserIdAndConversationId(userId, getSystemNotificationConversationId());

        if (settingsOpt.isPresent()) {
            Long conversationId = settingsOpt.get().getConversationId();
            return conversationRepository.findById(conversationId).orElse(null);
        }

        // 如果没有找到用户设置，尝试查找系统通知对话
        Optional<Conversation> conversationOpt = conversationRepository
                .findByCreatorIdAndConversationTypeAndIsDeletedFalse(
                        SYSTEM_NOTIFICATION_USER_ID,
                        Conversation.ConversationType.SYSTEM)
                .stream()
                .findFirst();

        return conversationOpt.orElse(null);
    }

    /**
     * 获取系统通知对话ID（如果不存在则创建）
     */
    private Long getSystemNotificationConversationId() {
        Optional<Conversation> conversationOpt = conversationRepository
                .findByCreatorIdAndConversationTypeAndIsDeletedFalse(
                        SYSTEM_NOTIFICATION_USER_ID,
                        Conversation.ConversationType.SYSTEM)
                .stream()
                .findFirst();

        if (conversationOpt.isPresent()) {
            return conversationOpt.get().getId();
        }

        // 如果不存在，创建系统通知对话
        Conversation conversation = Conversation.builder()
                .conversationType(Conversation.ConversationType.SYSTEM)
                .title(SYSTEM_NOTIFICATION_TITLE)
                .description("接收系统通知和重要消息")
                .creatorId(SYSTEM_NOTIFICATION_USER_ID)
                .isActive(true)
                .isDeleted(false)
                .messageCount(0L)
                .unreadCount(0L)
                .lastActivityAt(Instant.now())
                .build();

        Conversation savedConversation = conversationRepository.save(conversation);
        log.info("创建系统通知对话成功: conversationId={}", savedConversation.getId());

        return savedConversation.getId();
    }
    
    @Override
    @Transactional
    public void initializeUserSystemConversations(Long userId) {
        log.info("初始化用户系统对话: userId={}", userId);
        
        try {
            // 创建系统通知对话
            getOrCreateSystemNotificationConversation(userId);
            
            log.info("用户系统对话初始化完成: userId={}", userId);
        } catch (Exception e) {
            log.error("用户系统对话初始化失败: userId={}", userId, e);
            throw new RuntimeException("用户系统对话初始化失败", e);
        }
    }
    
    @Override
    @Transactional
    public void updateLastActivityTime(Long conversationId) {
        log.debug("更新对话最后活动时间: conversationId={}", conversationId);
        
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isPresent()) {
            Conversation conversation = conversationOpt.get();
            conversation.setLastActivityAt(Instant.now());
            conversationRepository.save(conversation);
            log.debug("对话最后活动时间更新成功: conversationId={}", conversationId);
        } else {
            log.warn("对话不存在，无法更新活动时间: conversationId={}", conversationId);
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isSystemNotificationConversation(Long conversationId) {
        Optional<Conversation> conversationOpt = conversationRepository.findById(conversationId);
        if (conversationOpt.isPresent()) {
            Conversation conversation = conversationOpt.get();
            return conversation.getConversationType() == Conversation.ConversationType.SYSTEM &&
                   SYSTEM_NOTIFICATION_USER_ID.equals(conversation.getCreatorId());
        }
        return false;
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getUnreadSystemMessageCount(Long userId) {
        // 这里需要调用message-service来获取未读消息数量
        // 暂时返回0，实际实现需要通过Feign客户端调用
        log.debug("获取系统消息未读数量: userId={}", userId);
        
        // TODO: 通过MessageService客户端获取实际的未读数量
        return 0L;
    }
}
