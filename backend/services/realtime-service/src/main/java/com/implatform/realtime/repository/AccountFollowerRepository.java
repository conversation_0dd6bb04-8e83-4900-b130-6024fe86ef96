package com.implatform.realtime.repository;

import com.implatform.realtime.entity.AccountFollower;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 公众号关注者数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface AccountFollowerRepository extends R2dbcRepository<AccountFollower, String> {

    /**
     * 查找用户对公众号的关注记录
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId AND user_id = :userId")
    Mono<AccountFollower> findByAccountIdAndUserId(@Param("accountId") String accountId, @Param("userId") String userId);

    /**
     * 获取公众号的关注者列表
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId AND status = :status ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findByAccountIdAndStatusOrderByFollowedAtDesc(
            @Param("accountId") String accountId,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 获取用户关注的公众号列表
     */
    @Query("SELECT * FROM account_followers WHERE user_id = :userId AND status = :status ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findByUserIdAndStatusOrderByFollowedAtDesc(
            @Param("userId") String userId,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 统计公众号关注者数量
     */
    @Query("SELECT COUNT(*) FROM account_followers WHERE account_id = :accountId AND status = :status")
    Mono<Long> countByAccountIdAndStatus(@Param("accountId") String accountId, @Param("status") String status);

    /**
     * 统计用户关注的公众号数量
     */
    long countByUserIdAndStatus(String userId, AccountFollower.FollowStatus status);

    /**
     * 获取活跃关注者
     */
    @Query("SELECT af FROM AccountFollower af WHERE af.accountId = :accountId " +
           "AND af.status = :status AND af.lastInteractionAt >= :sinceTime " +
           "ORDER BY af.lastInteractionAt DESC")
    Page<AccountFollower> findActiveFollowers(@Param("accountId") String accountId,
                                            @Param("status") AccountFollower.FollowStatus status,
                                            @Param("sinceTime") Instant sinceTime,
                                            Pageable pageable);

    /**
     * 获取新关注者
     */
    @Query("SELECT af FROM AccountFollower af WHERE af.accountId = :accountId " +
           "AND af.status = :status AND af.followedAt >= :sinceTime " +
           "ORDER BY af.followedAt DESC")
    Page<AccountFollower> findNewFollowers(@Param("accountId") String accountId,
                                         @Param("status") AccountFollower.FollowStatus status,
                                         @Param("sinceTime") Instant sinceTime,
                                         Pageable pageable);

    /**
     * 根据标签查找关注者
     */
    @Query("SELECT af FROM AccountFollower af WHERE af.accountId = :accountId " +
           "AND af.status = :status AND af.userTags LIKE %:tag% " +
           "ORDER BY af.followedAt DESC")
    Page<AccountFollower> findFollowersByTag(@Param("accountId") String accountId,
                                           @Param("status") AccountFollower.FollowStatus status,
                                           @Param("tag") String tag,
                                           Pageable pageable);

    /**
     * 更新关注状态
     */
    @Modifying
    @Query("UPDATE AccountFollower af SET af.status = :status, af.unfollowedAt = :unfollowedAt, af.updatedAt = :updatedAt " +
           "WHERE af.accountId = :accountId AND af.userId = :userId")
    int updateFollowStatus(@Param("accountId") String accountId,
                          @Param("userId") String userId,
                          @Param("status") AccountFollower.FollowStatus status,
                          @Param("unfollowedAt") Instant unfollowedAt,
                          @Param("updatedAt") Instant updatedAt);

    /**
     * 更新互动信息
     */
    @Modifying
    @Query("UPDATE AccountFollower af SET af.interactionCount = af.interactionCount + 1, " +
           "af.lastInteractionAt = :interactionTime, af.updatedAt = :updatedAt " +
           "WHERE af.accountId = :accountId AND af.userId = :userId")
    int updateInteraction(@Param("accountId") String accountId,
                         @Param("userId") String userId,
                         @Param("interactionTime") Instant interactionTime,
                         @Param("updatedAt") Instant updatedAt);

    /**
     * 批量更新用户标签
     */
    @Modifying
    @Query("UPDATE AccountFollower af SET af.userTags = :tags, af.updatedAt = :updatedAt " +
           "WHERE af.accountId = :accountId AND af.userId IN :userIds")
    int batchUpdateTags(@Param("accountId") String accountId,
                       @Param("userIds") List<String> userIds,
                       @Param("tags") String tags,
                       @Param("updatedAt") Instant updatedAt);

    /**
     * 获取关注者统计信息
     */
    @Query("SELECT " +
           "COUNT(af) as totalFollowers, " +
           "COUNT(CASE WHEN af.followedAt >= :todayStart THEN 1 END) as todayNew, " +
           "COUNT(CASE WHEN af.followedAt >= :weekStart THEN 1 END) as weekNew, " +
           "COUNT(CASE WHEN af.followedAt >= :monthStart THEN 1 END) as monthNew, " +
           "COUNT(CASE WHEN af.lastInteractionAt >= :activeThreshold THEN 1 END) as activeFollowers " +
           "FROM AccountFollower af WHERE af.accountId = :accountId AND af.status = :status")
    List<Object[]> getFollowerStatistics(@Param("accountId") String accountId,
                                        @Param("status") AccountFollower.FollowStatus status,
                                        @Param("todayStart") Instant todayStart,
                                        @Param("weekStart") Instant weekStart,
                                        @Param("monthStart") Instant monthStart,
                                        @Param("activeThreshold") Instant activeThreshold);

    /**
     * 获取关注趋势数据
     */
    @Query("SELECT DATE(af.followedAt) as followDate, COUNT(af) as followCount " +
           "FROM AccountFollower af WHERE af.accountId = :accountId " +
           "AND af.status = :status AND af.followedAt >= :startTime " +
           "GROUP BY DATE(af.followedAt) ORDER BY followDate")
    List<Object[]> getFollowTrend(@Param("accountId") String accountId,
                                 @Param("status") AccountFollower.FollowStatus status,
                                 @Param("startTime") Instant startTime);

    /**
     * 检查用户是否关注了公众号
     */
    boolean existsByAccountIdAndUserIdAndStatus(String accountId, String userId, AccountFollower.FollowStatus status);

    /**
     * 获取互动最多的关注者
     */
    @Query("SELECT af FROM AccountFollower af WHERE af.accountId = :accountId " +
           "AND af.status = :status ORDER BY af.interactionCount DESC")
    List<AccountFollower> findTopInteractiveFollowers(@Param("accountId") String accountId,
                                                     @Param("status") AccountFollower.FollowStatus status,
                                                     Pageable pageable);

    /**
     * 删除指定时间之前的取消关注记录
     */
    @Modifying
    @Query("DELETE FROM AccountFollower af WHERE af.status = :status AND af.unfollowedAt < :beforeTime")
    int deleteOldUnfollowedRecords(@Param("status") AccountFollower.FollowStatus status,
                                  @Param("beforeTime") Instant beforeTime);
}
