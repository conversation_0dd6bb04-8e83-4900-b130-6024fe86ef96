package com.implatform.realtime.service;

import com.implatform.realtime.entity.GroupPermission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 群组权限服务接口
 * 提供权限检查、继承解析和批量操作功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface GroupPermissionService {

    /**
     * 授予权限
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @param grantedBy 授予人ID
     * @return 权限实体
     * @throws IllegalArgumentException 当角色不存在时
     */
    GroupPermission grantPermission(Long roleId, GroupPermission.PermissionAction permissionAction, 
                                   Long grantedBy);

    /**
     * 授予权限（带过期时间）
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @param expiresAt 过期时间
     * @param grantedBy 授予人ID
     * @return 权限实体
     */
    GroupPermission grantPermissionWithExpiry(Long roleId, GroupPermission.PermissionAction permissionAction,
                                             Instant expiresAt, Long grantedBy);

    /**
     * 撤销权限
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @param revokedBy 撤销人ID
     * @return 是否成功撤销
     */
    boolean revokePermission(Long roleId, GroupPermission.PermissionAction permissionAction, 
                           Long revokedBy);

    /**
     * 批量授予权限
     * 
     * @param roleId 角色ID
     * @param permissionActions 权限动作列表
     * @param grantedBy 授予人ID
     * @return 成功授予的权限数量
     */
    int batchGrantPermissions(Long roleId, List<GroupPermission.PermissionAction> permissionActions,
                            Long grantedBy);

    /**
     * 批量撤销权限
     * 
     * @param roleId 角色ID
     * @param permissionActions 权限动作列表
     * @param revokedBy 撤销人ID
     * @return 成功撤销的权限数量
     */
    int batchRevokePermissions(Long roleId, List<GroupPermission.PermissionAction> permissionActions,
                             Long revokedBy);

    /**
     * 复制权限到另一个角色
     * 
     * @param sourceRoleId 源角色ID
     * @param targetRoleId 目标角色ID
     * @param copiedBy 复制人ID
     * @return 复制的权限数量
     */
    int copyPermissions(Long sourceRoleId, Long targetRoleId, Long copiedBy);

    /**
     * 检查角色是否拥有指定权限（包含继承）
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @return 是否拥有权限
     */
    boolean hasPermission(Long roleId, GroupPermission.PermissionAction permissionAction);

    /**
     * 检查用户是否拥有群组权限
     * 
     * @param userId 用户ID
     * @param groupId 群组ID
     * @param permissionAction 权限动作
     * @return 是否拥有权限
     */
    boolean hasGroupPermission(Long userId, Long groupId, GroupPermission.PermissionAction permissionAction);

    /**
     * 获取角色的所有有效权限（包含继承）
     * 
     * @param roleId 角色ID
     * @return 有效权限集合
     */
    Set<GroupPermission.PermissionAction> getEffectivePermissions(Long roleId);

    /**
     * 获取用户在群组中的所有有效权限
     * 
     * @param userId 用户ID
     * @param groupId 群组ID
     * @return 有效权限集合
     */
    Set<GroupPermission.PermissionAction> getUserGroupPermissions(Long userId, Long groupId);

    /**
     * 获取角色的直接权限（不包含继承）
     * 
     * @param roleId 角色ID
     * @return 直接权限列表
     */
    List<GroupPermission> getDirectPermissions(Long roleId);

    /**
     * 获取角色的继承权限
     * 
     * @param roleId 角色ID
     * @return 继承权限列表
     */
    List<GroupPermission> getInheritedPermissions(Long roleId);

    /**
     * 获取角色的权限按类别分组
     * 
     * @param roleId 角色ID
     * @return 按类别分组的权限
     */
    Map<GroupPermission.PermissionCategory, List<GroupPermission>> getPermissionsByCategory(Long roleId);

    /**
     * 获取角色的临时权限（有过期时间）
     * 
     * @param roleId 角色ID
     * @return 临时权限列表
     */
    List<GroupPermission> getTemporaryPermissions(Long roleId);

    /**
     * 更新权限继承关系
     * 
     * @param parentRoleId 父角色ID
     * @param childRoleId 子角色ID
     * @return 更新的继承权限数量
     */
    int updateInheritedPermissions(Long parentRoleId, Long childRoleId);

    /**
     * 移除权限继承关系
     * 
     * @param childRoleId 子角色ID
     * @param parentRoleId 父角色ID
     * @return 移除的继承权限数量
     */
    int removeInheritedPermissions(Long childRoleId, Long parentRoleId);

    /**
     * 刷新角色权限缓存
     * 
     * @param roleId 角色ID
     */
    void refreshPermissionCache(Long roleId);

    /**
     * 刷新群组权限缓存
     * 
     * @param groupId 群组ID
     */
    void refreshGroupPermissionCache(Long groupId);

    /**
     * 设置权限过期时间
     * 
     * @param roleId 角色ID
     * @param permissionAction 权限动作
     * @param expiresAt 过期时间
     * @param modifiedBy 修改人ID
     * @return 是否成功设置
     */
    boolean setPermissionExpiry(Long roleId, GroupPermission.PermissionAction permissionAction,
                              Instant expiresAt, Long modifiedBy);

    /**
     * 处理过期权限
     * 
     * @return 处理的过期权限数量
     */
    int processExpiredPermissions();

    /**
     * 获取即将过期的权限
     * 
     * @param expiryThreshold 过期时间阈值
     * @param pageable 分页参数
     * @return 即将过期的权限分页结果
     */
    Page<GroupPermission> getExpiringPermissions(Instant expiryThreshold, Pageable pageable);

    /**
     * 获取权限统计信息
     * 
     * @param roleId 角色ID
     * @return 权限统计信息
     */
    PermissionStatistics getPermissionStatistics(Long roleId);

    /**
     * 获取权限类别统计
     * 
     * @param roleId 角色ID
     * @return 类别统计结果
     */
    Map<GroupPermission.PermissionCategory, PermissionCategoryStats> getPermissionCategoryStatistics(Long roleId);

    /**
     * 获取权限使用频率统计
     * 
     * @return 权限使用统计
     */
    List<PermissionUsageStats> getPermissionUsageStatistics();

    /**
     * 验证权限配置的合理性
     * 
     * @param roleId 角色ID
     * @return 验证结果
     */
    PermissionValidationResult validatePermissionConfiguration(Long roleId);

    /**
     * 获取权限冲突检测结果
     * 
     * @param roleId 角色ID
     * @return 冲突的权限动作列表
     */
    List<GroupPermission.PermissionAction> detectPermissionConflicts(Long roleId);

    /**
     * 获取群组权限矩阵
     * 
     * @param groupId 群组ID
     * @return 权限矩阵
     */
    GroupPermissionMatrix getGroupPermissionMatrix(Long groupId);

    /**
     * 导出角色权限配置
     * 
     * @param roleId 角色ID
     * @return 权限配置JSON
     */
    String exportPermissionConfiguration(Long roleId);

    /**
     * 导入角色权限配置
     * 
     * @param roleId 角色ID
     * @param configurationJson 权限配置JSON
     * @param importedBy 导入人ID
     * @return 导入的权限数量
     */
    int importPermissionConfiguration(Long roleId, String configurationJson, Long importedBy);

    /**
     * 权限统计信息
     */
    class PermissionStatistics {
        private final int totalPermissions;
        private final int grantedPermissions;
        private final int inheritedPermissions;
        private final int expiredPermissions;
        private final int temporaryPermissions;

        public PermissionStatistics(int totalPermissions, int grantedPermissions, 
                                  int inheritedPermissions, int expiredPermissions, 
                                  int temporaryPermissions) {
            this.totalPermissions = totalPermissions;
            this.grantedPermissions = grantedPermissions;
            this.inheritedPermissions = inheritedPermissions;
            this.expiredPermissions = expiredPermissions;
            this.temporaryPermissions = temporaryPermissions;
        }

        // Getters
        public int getTotalPermissions() { return totalPermissions; }
        public int getGrantedPermissions() { return grantedPermissions; }
        public int getInheritedPermissions() { return inheritedPermissions; }
        public int getExpiredPermissions() { return expiredPermissions; }
        public int getTemporaryPermissions() { return temporaryPermissions; }
    }

    /**
     * 权限类别统计
     */
    class PermissionCategoryStats {
        private final GroupPermission.PermissionCategory category;
        private final int totalPermissions;
        private final int grantedPermissions;

        public PermissionCategoryStats(GroupPermission.PermissionCategory category, 
                                     int totalPermissions, int grantedPermissions) {
            this.category = category;
            this.totalPermissions = totalPermissions;
            this.grantedPermissions = grantedPermissions;
        }

        // Getters
        public GroupPermission.PermissionCategory getCategory() { return category; }
        public int getTotalPermissions() { return totalPermissions; }
        public int getGrantedPermissions() { return grantedPermissions; }
    }

    /**
     * 权限使用统计
     */
    class PermissionUsageStats {
        private final GroupPermission.PermissionAction permissionAction;
        private final long usageCount;

        public PermissionUsageStats(GroupPermission.PermissionAction permissionAction, long usageCount) {
            this.permissionAction = permissionAction;
            this.usageCount = usageCount;
        }

        // Getters
        public GroupPermission.PermissionAction getPermissionAction() { return permissionAction; }
        public long getUsageCount() { return usageCount; }
    }

    /**
     * 权限验证结果
     */
    class PermissionValidationResult {
        private final boolean isValid;
        private final List<String> issues;
        private final List<String> recommendations;

        public PermissionValidationResult(boolean isValid, List<String> issues, 
                                        List<String> recommendations) {
            this.isValid = isValid;
            this.issues = issues;
            this.recommendations = recommendations;
        }

        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getIssues() { return issues; }
        public List<String> getRecommendations() { return recommendations; }
    }

    /**
     * 群组权限矩阵
     */
    class GroupPermissionMatrix {
        private final Long groupId;
        private final Map<Long, String> roleNames;
        private final Map<Long, Map<GroupPermission.PermissionAction, Boolean>> permissionMatrix;

        public GroupPermissionMatrix(Long groupId, Map<Long, String> roleNames,
                                   Map<Long, Map<GroupPermission.PermissionAction, Boolean>> permissionMatrix) {
            this.groupId = groupId;
            this.roleNames = roleNames;
            this.permissionMatrix = permissionMatrix;
        }

        // Getters
        public Long getGroupId() { return groupId; }
        public Map<Long, String> getRoleNames() { return roleNames; }
        public Map<Long, Map<GroupPermission.PermissionAction, Boolean>> getPermissionMatrix() { return permissionMatrix; }
    }
}
