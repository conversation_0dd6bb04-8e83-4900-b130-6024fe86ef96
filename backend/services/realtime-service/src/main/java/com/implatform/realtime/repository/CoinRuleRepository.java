package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CoinRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 金币规则Repository接口
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface CoinRuleRepository extends JpaRepository<CoinRule, Long> {

    /**
     * 根据规则名称查找规则
     */
    Optional<CoinRule> findByRuleName(String ruleName);

    /**
     * 检查规则名称是否存在
     */
    boolean existsByRuleName(String ruleName);

    /**
     * 根据规则类型查找规则
     */
    List<CoinRule> findByRuleType(CoinRule.RuleType ruleType);

    /**
     * 根据动作查找规则
     */
    List<CoinRule> findByAction(String action);

    /**
     * 根据状态查找规则
     */
    List<CoinRule> findByRuleStatus(CoinRule.RuleStatus ruleStatus);

    /**
     * 根据状态分页查找规则
     */
    Page<CoinRule> findByRuleStatus(CoinRule.RuleStatus ruleStatus, Pageable pageable);

    /**
     * 获取活跃规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.ruleStatus = 'ACTIVE' ORDER BY r.priority DESC, r.createdAt ASC")
    List<CoinRule> findActiveRules();

    /**
     * 根据动作获取活跃规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.action = :action AND r.ruleStatus = 'ACTIVE' ORDER BY r.priority DESC")
    List<CoinRule> findActiveRulesByAction(@Param("action") String action);

    /**
     * 根据规则类型和动作查找规则
     */
    List<CoinRule> findByRuleTypeAndAction(CoinRule.RuleType ruleType, String action);

    /**
     * 搜索规则（按名称或描述）
     */
    @Query("SELECT r FROM CoinRule r WHERE r.ruleName LIKE %:keyword% OR r.description LIKE %:keyword%")
    Page<CoinRule> searchRules(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取过期规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.effectiveUntil IS NOT NULL AND r.effectiveUntil < :currentTime")
    List<CoinRule> findExpiredRules(@Param("currentTime") Instant currentTime);

    /**
     * 获取即将生效的规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.effectiveFrom IS NOT NULL AND r.effectiveFrom > :currentTime AND r.effectiveFrom <= :futureTime")
    List<CoinRule> findUpcomingRules(@Param("currentTime") Instant currentTime, @Param("futureTime") Instant futureTime);

    /**
     * 获取当前有效的规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.ruleStatus = 'ACTIVE' " +
           "AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= :currentTime) " +
           "AND (r.effectiveUntil IS NULL OR r.effectiveUntil > :currentTime)")
    List<CoinRule> findCurrentEffectiveRules(@Param("currentTime") Instant currentTime);

    /**
     * 根据优先级范围查找规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.priority BETWEEN :minPriority AND :maxPriority ORDER BY r.priority DESC")
    List<CoinRule> findByPriorityRange(@Param("minPriority") Integer minPriority, @Param("maxPriority") Integer maxPriority);

    /**
     * 统计规则总数
     */
    long countByRuleStatus(CoinRule.RuleStatus ruleStatus);

    /**
     * 统计指定动作的规则数
     */
    long countByAction(String action);

    /**
     * 统计指定类型的规则数
     */
    long countByRuleType(CoinRule.RuleType ruleType);

    /**
     * 获取规则执行统计
     */
    @Query("SELECT SUM(r.executionCount), SUM(r.totalReward) FROM CoinRule r WHERE r.ruleStatus = 'ACTIVE'")
    Object[] getExecutionStatistics();

    /**
     * 获取指定时间范围内创建的规则
     */
    List<CoinRule> findByCreatedAtBetween(Instant startTime, Instant endTime);

    /**
     * 获取指定时间范围内更新的规则
     */
    List<CoinRule> findByUpdatedAtBetween(Instant startTime, Instant endTime);

    /**
     * 获取最近创建的规则
     */
    @Query("SELECT r FROM CoinRule r ORDER BY r.createdAt DESC")
    List<CoinRule> findRecentRules(Pageable pageable);

    /**
     * 获取最常执行的规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.executionCount > 0 ORDER BY r.executionCount DESC")
    List<CoinRule> findMostExecutedRules(Pageable pageable);

    /**
     * 获取奖励最多的规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.totalReward > 0 ORDER BY r.totalReward DESC")
    List<CoinRule> findHighestRewardRules(Pageable pageable);

    /**
     * 批量更新规则状态
     */
    @Modifying
    @Transactional
    @Query("UPDATE CoinRule r SET r.ruleStatus = :status, r.updatedBy = :operatorId WHERE r.id IN :ruleIds")
    int batchUpdateStatus(@Param("ruleIds") List<Long> ruleIds, 
                         @Param("status") CoinRule.RuleStatus status, 
                         @Param("operatorId") String operatorId);

    /**
     * 批量删除过期规则
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM CoinRule r WHERE r.effectiveUntil IS NOT NULL AND r.effectiveUntil < :cutoffTime")
    int deleteExpiredRules(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 重置规则统计
     */
    @Modifying
    @Transactional
    @Query("UPDATE CoinRule r SET r.executionCount = 0, r.totalReward = 0 WHERE r.id = :ruleId")
    int resetRuleStatistics(@Param("ruleId") Long ruleId);

    /**
     * 获取规则配置冲突检查
     */
    @Query("SELECT r FROM CoinRule r WHERE r.action = :action AND r.ruleStatus = 'ACTIVE' " +
           "AND r.id != :excludeRuleId AND r.priority = :priority")
    List<CoinRule> findConflictingRules(@Param("action") String action, 
                                       @Param("priority") Integer priority, 
                                       @Param("excludeRuleId") Long excludeRuleId);

    /**
     * 获取需要同步状态的规则
     */
    @Query("SELECT r FROM CoinRule r WHERE r.ruleStatus = 'ACTIVE' " +
           "AND ((r.effectiveFrom IS NOT NULL AND r.effectiveFrom > :currentTime) " +
           "OR (r.effectiveUntil IS NOT NULL AND r.effectiveUntil <= :currentTime))")
    List<CoinRule> findRulesNeedingStatusSync(@Param("currentTime") Instant currentTime);

    /**
     * 获取规则使用趋势数据
     */
    @Query(value = "SELECT DATE(created_at) as date, COUNT(*) as count " +
           "FROM coin_rules WHERE created_at BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(created_at) ORDER BY date", nativeQuery = true)
    List<Object[]> getRuleCreationTrend(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 获取规则执行趋势数据
     */
    @Query(value = "SELECT DATE(updated_at) as date, SUM(execution_count) as total_executions " +
           "FROM coin_rules WHERE updated_at BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(updated_at) ORDER BY date", nativeQuery = true)
    List<Object[]> getRuleExecutionTrend(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 检查规则名称是否重复（排除指定规则）
     */
    boolean existsByRuleNameAndIdNot(String ruleName, Long id);

    /**
     * 获取指定创建者的规则
     */
    List<CoinRule> findByCreatedBy(String createdBy);

    /**
     * 获取指定创建者的规则（分页）
     */
    Page<CoinRule> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * 获取规则类型分布统计
     */
    @Query("SELECT r.ruleType, COUNT(r) FROM CoinRule r GROUP BY r.ruleType")
    List<Object[]> getRuleTypeDistribution();

    /**
     * 获取规则状态分布统计
     */
    @Query("SELECT r.ruleStatus, COUNT(r) FROM CoinRule r GROUP BY r.ruleStatus")
    List<Object[]> getRuleStatusDistribution();

    /**
     * 获取动作分布统计
     */
    @Query("SELECT r.action, COUNT(r) FROM CoinRule r GROUP BY r.action ORDER BY COUNT(r) DESC")
    List<Object[]> getActionDistribution();
}
