package com.implatform.realtime.service;

import com.implatform.realtime.dto.AiAssistantDTO;
import com.implatform.realtime.entity.AiAssistant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

import java.util.List;

/**
 * AI助手服务接口
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface AiAssistantService {

    // ==================== 助手管理 ====================

    /**
     * 创建AI助手
     */
    AiAssistantDTO.AssistantInfo createAssistant(Long userId, AiAssistantDTO.CreateAssistantRequest request);

    /**
     * 获取助手详情
     */
    AiAssistantDTO.AssistantInfo getAssistantDetail(Long assistantId, Long userId);

    /**
     * 根据ID获取助手
     */
    Optional<com.implatform.realtime.entity.AiAssistant> getAssistantById(Long assistantId);

    /**
     * 更新助手信息
     */
    AiAssistantDTO.AssistantInfo updateAssistant(Long assistantId, Long userId, AiAssistantDTO.UpdateAssistantRequest request);

    /**
     * 删除助手
     */
    void deleteAssistant(Long assistantId, Long userId);

    /**
     * 启用助手
     */
    AiAssistantDTO.AssistantInfo enableAssistant(Long assistantId, Long userId);

    /**
     * 禁用助手
     */
    AiAssistantDTO.AssistantInfo disableAssistant(Long assistantId, Long userId);

    /**
     * 复制助手
     */
    AiAssistantDTO.AssistantInfo cloneAssistant(Long assistantId, Long userId, String newName);

    // ==================== 助手查询 ====================

    /**
     * 获取公开助手列表
     */
    Page<AiAssistantDTO.AssistantInfo> getPublicAssistants(AiAssistant.AssistantType type, Pageable pageable);

    /**
     * 获取用户创建的助手
     */
    Page<AiAssistantDTO.AssistantInfo> getUserAssistants(Long userId, Pageable pageable);

    /**
     * 获取推荐助手
     */
    List<AiAssistantDTO.AssistantInfo> getRecommendedAssistants(Long userId, int limit);

    /**
     * 获取热门助手
     */
    List<AiAssistantDTO.AssistantInfo> getPopularAssistants(int limit);

    /**
     * 根据提供商获取助手
     */
    List<AiAssistantDTO.AssistantInfo> getAssistantsByProvider(AiAssistant.AiProvider provider);

    /**
     * 根据类型获取助手
     */
    List<AiAssistantDTO.AssistantInfo> getAssistantsByType(AiAssistant.AssistantType type);

    /**
     * 搜索助手
     */
    Page<AiAssistantDTO.AssistantInfo> searchAssistants(String keyword, Pageable pageable);

    // ==================== 对话管理 ====================

    /**
     * 创建对话会话
     */
    AiAssistantDTO.ConversationInfo createConversation(Long userId, Long assistantId, String title);

    /**
     * 获取对话详情
     */
    AiAssistantDTO.ConversationInfo getConversationDetail(Long conversationId, Long userId);

    /**
     * 更新对话信息
     */
    AiAssistantDTO.ConversationInfo updateConversation(Long conversationId, Long userId, AiAssistantDTO.UpdateConversationRequest request);

    /**
     * 删除对话
     */
    void deleteConversation(Long conversationId, Long userId);

    /**
     * 清空对话历史
     */
    void clearConversationHistory(Long conversationId, Long userId);

    /**
     * 归档对话
     */
    AiAssistantDTO.ConversationInfo archiveConversation(Long conversationId, Long userId);

    /**
     * 恢复对话
     */
    AiAssistantDTO.ConversationInfo restoreConversation(Long conversationId, Long userId);

    /**
     * 置顶对话
     */
    AiAssistantDTO.ConversationInfo togglePinConversation(Long conversationId, Long userId);

    /**
     * 收藏对话
     */
    AiAssistantDTO.ConversationInfo toggleFavoriteConversation(Long conversationId, Long userId);

    /**
     * 获取用户对话列表
     */
    Page<AiAssistantDTO.ConversationInfo> getUserConversations(Long userId, String status, Pageable pageable);

    /**
     * 获取助手的对话列表
     */
    Page<AiAssistantDTO.ConversationInfo> getAssistantConversations(Long assistantId, Long userId, Pageable pageable);

    // ==================== 消息处理 ====================

    /**
     * 发送消息
     */
    AiAssistantDTO.MessageInfo sendMessage(Long conversationId, Long userId, AiAssistantDTO.SendMessageRequest request);

    /**
     * 流式发送消息
     */
    void sendStreamMessage(Long conversationId, Long userId, AiAssistantDTO.SendMessageRequest request, 
                          AiAssistantDTO.StreamCallback callback);

    /**
     * 重新生成回复
     */
    AiAssistantDTO.MessageInfo regenerateResponse(Long messageId, Long userId);

    /**
     * 重新生成消息
     */
    AiAssistantDTO.MessageInfo regenerateMessage(Long messageId, Long userId);

    /**
     * 获取对话消息
     */
    Page<AiAssistantDTO.MessageInfo> getConversationMessages(Long conversationId, Long userId, Pageable pageable);

    /**
     * 删除消息
     */
    void deleteMessage(Long messageId, Long userId);

    /**
     * 编辑消息
     */
    AiAssistantDTO.MessageInfo editMessage(Long messageId, Long userId, String newContent);

    // ==================== 模板管理 ====================

    /**
     * 创建提示词模板
     */
    AiAssistantDTO.PromptTemplate createPromptTemplate(Long userId, AiAssistantDTO.CreatePromptTemplateRequest request);

    /**
     * 获取提示词模板
     */
    AiAssistantDTO.PromptTemplate getPromptTemplate(Long templateId, Long userId);

    /**
     * 更新提示词模板
     */
    AiAssistantDTO.PromptTemplate updatePromptTemplate(Long templateId, Long userId, AiAssistantDTO.UpdatePromptTemplateRequest request);

    /**
     * 删除提示词模板
     */
    void deletePromptTemplate(Long templateId, Long userId);

    /**
     * 获取用户模板列表
     */
    Page<AiAssistantDTO.PromptTemplate> getUserPromptTemplates(Long userId, Pageable pageable);

    /**
     * 获取公开模板列表
     */
    Page<AiAssistantDTO.PromptTemplate> getPublicPromptTemplates(Pageable pageable);

    /**
     * 搜索模板
     */
    Page<AiAssistantDTO.PromptTemplate> searchPromptTemplates(String keyword, Pageable pageable);

    // ==================== 配置管理 ====================

    /**
     * 测试助手配置
     */
    AiAssistantDTO.TestResult testAssistantConfig(AiAssistantDTO.TestConfigRequest request);

    /**
     * 获取模型列表
     */
    List<AiAssistantDTO.ModelInfo> getAvailableModels(AiAssistant.AiProvider provider);

    /**
     * 验证API密钥
     */
    AiAssistantDTO.ValidationResult validateApiKey(AiAssistant.AiProvider provider, String apiKey, String endpoint);

    /**
     * 获取模型定价
     */
    AiAssistantDTO.PricingInfo getModelPricing(AiAssistant.AiProvider provider, String modelName);

    // ==================== 统计分析 ====================

    /**
     * 获取助手使用统计
     */
    Object getAssistantStatistics(Long assistantId, Long userId);

    /**
     * 获取用户AI使用统计
     */
    Object getUserAiStatistics(Long userId);

    /**
     * 获取对话统计
     */
    Object getConversationStatistics(Long conversationId, Long userId);

    /**
     * 获取使用趋势
     */
    List<Object> getUsageTrend(Long userId, int days);

    /**
     * 获取费用统计
     */
    Object getCostStatistics(Long userId, String period);

    // ==================== 评分和反馈 ====================

    /**
     * 评分助手
     */
    void rateAssistant(Long assistantId, Long userId, double rating, String comment);

    /**
     * 获取助手评分
     */
    Object getAssistantRating(Long assistantId);

    /**
     * 反馈消息质量
     */
    void feedbackMessage(Long messageId, Long userId, boolean helpful, String feedback);

    /**
     * 举报助手
     */
    void reportAssistant(Long assistantId, Long userId, String reason, String description);

    // ==================== 导入导出 ====================

    /**
     * 导出对话记录
     */
    String exportConversation(Long conversationId, Long userId, String format);

    /**
     * 导出助手配置
     */
    String exportAssistantConfig(Long assistantId, Long userId);

    /**
     * 导入助手配置
     */
    AiAssistantDTO.AssistantInfo importAssistantConfig(Long userId, String configData);

    /**
     * 批量导出对话
     */
    String batchExportConversations(Long userId, List<Long> conversationIds, String format);

    // ==================== 智能功能 ====================

    /**
     * 智能推荐助手
     */
    List<AiAssistantDTO.AssistantInfo> getSmartRecommendations(Long userId, String context);

    /**
     * 自动生成提示词
     */
    String generatePrompt(String description, AiAssistant.AssistantType type);

    /**
     * 优化提示词
     */
    String optimizePrompt(String originalPrompt, String feedback);

    /**
     * 分析对话质量
     */
    Object analyzeConversationQuality(Long conversationId, Long userId);

    // ==================== 批量操作 ====================

    /**
     * 批量删除对话
     */
    void batchDeleteConversations(Long userId, List<Long> conversationIds);

    /**
     * 批量归档对话
     */
    void batchArchiveConversations(Long userId, List<Long> conversationIds);

    /**
     * 批量更新助手状态
     */
    void batchUpdateAssistantStatus(Long userId, List<Long> assistantIds, boolean enabled);

    // ==================== 系统管理 ====================

    /**
     * 清理过期对话
     */
    int cleanupExpiredConversations(int days);

    /**
     * 重新计算助手统计
     */
    void recalculateAssistantStatistics(Long assistantId);

    /**
     * 修复对话数据
     */
    void repairConversationData(Long conversationId);

    /**
     * 同步助手配置
     */
    void syncAssistantConfig(Long assistantId);

    // ==================== 管理员接口 ====================

    /**
     * 获取AI助手列表（管理员接口）
     */
    Page<Object> getAiAssistants(java.util.Map<String, Object> params, Pageable pageable);

    /**
     * 根据ID获取AI助手（管理员接口）
     */
    Object getAiAssistantById(Long id);

    /**
     * 创建AI助手（管理员接口）
     */
    Object createAiAssistant(Object request);

    /**
     * 更新AI助手（管理员接口）
     */
    Object updateAiAssistant(Long id, Object request);

    /**
     * 删除AI助手（管理员接口）
     */
    void deleteAiAssistant(Long id);
}
