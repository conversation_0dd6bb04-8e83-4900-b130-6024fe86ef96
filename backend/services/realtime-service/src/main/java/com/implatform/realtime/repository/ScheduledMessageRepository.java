package com.implatform.realtime.repository;

import com.implatform.realtime.entity.ScheduledMessage;
import com.implatform.realtime.entity.Message;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 定时消息Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ScheduledMessageRepository extends R2dbcRepository<ScheduledMessage, Long> {

    /**
     * 查找需要发送的定时消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'PENDING' AND sm.isActive = true AND sm.scheduledTime <= :currentTime ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findMessagesToSend(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找需要重试的定时消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'FAILED' AND sm.isActive = true AND sm.retryCount < sm.maxRetryCount AND sm.nextRetryTime <= :currentTime ORDER BY sm.nextRetryTime ASC")
    List<ScheduledMessage> findMessagesToRetry(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找过期的定时消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'PENDING' AND sm.isActive = true AND sm.scheduledTime < :cutoffTime")
    List<ScheduledMessage> findExpiredMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 根据发送者ID查找定时消息
     */
    Page<ScheduledMessage> findBySenderIdAndIsActiveTrueOrderByScheduledTimeDesc(Long senderId, Pageable pageable);

    /**
     * 根据发送者ID和状态查找定时消息
     */
    Page<ScheduledMessage> findBySenderIdAndStatusAndIsActiveTrueOrderByScheduledTimeDesc(
            Long senderId, ScheduledMessage.ScheduleStatus status, Pageable pageable);

    /**
     * 根据会话ID查找定时消息
     */
    Page<ScheduledMessage> findByConversationIdAndIsActiveTrueOrderByScheduledTimeDesc(Long conversationId, Pageable pageable);

    /**
     * 根据发送者ID和会话ID查找定时消息
     */
    Page<ScheduledMessage> findBySenderIdAndConversationIdAndIsActiveTrueOrderByScheduledTimeDesc(
            Long senderId, Long conversationId, Pageable pageable);

    /**
     * 统计用户的定时消息数量
     */
    long countBySenderIdAndIsActiveTrue(Long senderId);

    /**
     * 统计用户指定状态的定时消息数量
     */
    long countBySenderIdAndStatusAndIsActiveTrue(Long senderId, ScheduledMessage.ScheduleStatus status);

    /**
     * 统计会话的定时消息数量
     */
    long countByConversationIdAndIsActiveTrue(Long conversationId);

    /**
     * 根据时间范围查找定时消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true AND sm.scheduledTime BETWEEN :startTime AND :endTime ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findByTimeRange(@Param("senderId") Long senderId, 
                                          @Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查找即将发送的消息（指定时间内）
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'PENDING' AND sm.isActive = true AND sm.scheduledTime BETWEEN :currentTime AND :futureTime ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findUpcomingMessages(@Param("currentTime") LocalDateTime currentTime, 
                                               @Param("futureTime") LocalDateTime futureTime);

    /**
     * 查找重复消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.repeatType IS NOT NULL AND sm.repeatType != 'NONE' AND sm.isActive = true ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findRepeatingMessages();

    /**
     * 查找需要处理重复的消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'SENT' AND sm.repeatType IS NOT NULL AND sm.repeatType != 'NONE' AND sm.isActive = true AND " +
           "(sm.maxRepeatCount IS NULL OR sm.currentRepeatCount < sm.maxRepeatCount) AND " +
           "(sm.repeatEndTime IS NULL OR sm.repeatEndTime > :currentTime)")
    List<ScheduledMessage> findMessagesNeedingRepeat(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.status = :status WHERE sm.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") ScheduledMessage.ScheduleStatus status);

    /**
     * 批量取消定时消息
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.status = 'CANCELLED', sm.isActive = false WHERE sm.senderId = :senderId AND sm.id IN :ids")
    void batchCancelMessages(@Param("senderId") Long senderId, @Param("ids") List<Long> ids);

    /**
     * 取消用户的所有待发送消息
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.status = 'CANCELLED', sm.isActive = false WHERE sm.senderId = :senderId AND sm.status = 'PENDING'")
    void cancelAllPendingMessages(@Param("senderId") Long senderId);

    /**
     * 自动标记过期消息
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.status = 'EXPIRED', sm.isActive = false WHERE sm.status = 'PENDING' AND sm.scheduledTime < :cutoffTime")
    int autoMarkExpiredMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 更新重复次数
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.currentRepeatCount = sm.currentRepeatCount + 1 WHERE sm.id = :id")
    void incrementRepeatCount(@Param("id") Long id);

    /**
     * 更新定时时间
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.scheduledTime = :scheduledTime, sm.status = 'PENDING' WHERE sm.id = :id")
    void updateScheduledTime(@Param("id") Long id, @Param("scheduledTime") LocalDateTime scheduledTime);

    /**
     * 重置重试状态
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.retryCount = 0, sm.nextRetryTime = null, sm.failureReason = null, sm.status = 'PENDING' WHERE sm.id = :id")
    void resetRetryStatus(@Param("id") Long id);

    /**
     * 根据消息类型查找定时消息
     */
    Page<ScheduledMessage> findBySenderIdAndMessageTypeAndIsActiveTrueOrderByScheduledTimeDesc(
            Long senderId, Message.MessageType messageType, Pageable pageable);

    /**
     * 根据会话类型查找定时消息
     */
    Page<ScheduledMessage> findBySenderIdAndConversationTypeAndIsActiveTrueOrderByScheduledTimeDesc(
            Long senderId, ScheduledMessage.ConversationType conversationType, Pageable pageable);

    /**
     * 搜索定时消息内容
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true AND sm.content LIKE %:keyword% ORDER BY sm.scheduledTime DESC")
    Page<ScheduledMessage> searchByContent(@Param("senderId") Long senderId, 
                                          @Param("keyword") String keyword, 
                                          Pageable pageable);

    /**
     * 获取用户定时消息统计
     */
    @Query("SELECT sm.status, COUNT(sm) FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true GROUP BY sm.status")
    List<Object[]> getUserScheduleStatistics(@Param("senderId") Long senderId);

    /**
     * 获取每日定时消息统计
     */
    @Query("SELECT DATE(sm.scheduledTime), COUNT(sm) FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.scheduledTime >= :startDate GROUP BY DATE(sm.scheduledTime) ORDER BY DATE(sm.scheduledTime)")
    List<Object[]> getDailyScheduleStatistics(@Param("senderId") Long senderId, @Param("startDate") LocalDateTime startDate);

    /**
     * 获取消息类型统计
     */
    @Query("SELECT sm.messageType, COUNT(sm) FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true GROUP BY sm.messageType ORDER BY COUNT(sm) DESC")
    List<Object[]> getMessageTypeStatistics(@Param("senderId") Long senderId);

    /**
     * 获取会话类型统计
     */
    @Query("SELECT sm.conversationType, COUNT(sm) FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true GROUP BY sm.conversationType ORDER BY COUNT(sm) DESC")
    List<Object[]> getConversationTypeStatistics(@Param("senderId") Long senderId);

    /**
     * 获取重复类型统计
     */
    @Query("SELECT sm.repeatType, COUNT(sm) FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true GROUP BY sm.repeatType ORDER BY COUNT(sm) DESC")
    List<Object[]> getRepeatTypeStatistics(@Param("senderId") Long senderId);

    /**
     * 查找失败次数最多的消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.status = 'FAILED' ORDER BY sm.retryCount DESC")
    List<ScheduledMessage> findMostFailedMessages(@Param("senderId") Long senderId, Pageable pageable);

    /**
     * 查找最近发送的消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.status = 'SENT' ORDER BY sm.sentAt DESC")
    List<ScheduledMessage> findRecentlySentMessages(@Param("senderId") Long senderId, Pageable pageable);

    /**
     * 查找长时间未发送的消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'PENDING' AND sm.isActive = true AND sm.scheduledTime < :cutoffTime ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findLongPendingMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取平均发送延迟（秒）
     */
    @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM sent_at) - EXTRACT(EPOCH FROM scheduled_time)) FROM scheduled_messages WHERE sender_id = :senderId AND status = 'SENT' AND sent_at IS NOT NULL", nativeQuery = true)
    Double getAverageSendDelay(@Param("senderId") Long senderId);

    /**
     * 获取发送成功率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN sm.status = 'SENT' THEN 1 END) * 100.0 / COUNT(sm) " +
           "FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.status IN ('SENT', 'FAILED', 'EXPIRED')")
    Double getSendSuccessRate(@Param("senderId") Long senderId);

    /**
     * 查找重复发送异常的消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.repeatType IS NOT NULL AND sm.repeatType != 'NONE' AND sm.currentRepeatCount > COALESCE(sm.maxRepeatCount, 999)")
    List<ScheduledMessage> findAbnormalRepeatingMessages();

    /**
     * 清理旧的已完成消息
     */
    @Modifying
    @Query("DELETE FROM ScheduledMessage sm WHERE sm.status IN ('SENT', 'CANCELLED', 'EXPIRED') AND sm.updatedAt < :cutoffTime")
    int cleanupOldMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取用户在指定时间段的定时消息
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.isActive = true AND " +
           "((sm.scheduledTime BETWEEN :startTime AND :endTime) OR " +
           "(sm.repeatType IS NOT NULL AND sm.repeatType != 'NONE' AND sm.scheduledTime <= :endTime)) " +
           "ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findUserMessagesInTimeRange(@Param("senderId") Long senderId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 检查用户是否达到定时消息限制
     */
    @Query("SELECT COUNT(sm) >= :maxMessages FROM ScheduledMessage sm WHERE sm.senderId = :senderId AND sm.status = 'PENDING' AND sm.isActive = true")
    boolean hasReachedScheduleLimit(@Param("senderId") Long senderId, @Param("maxMessages") Integer maxMessages);

    /**
     * 获取系统定时消息概览
     */
    @Query("SELECT sm.status, COUNT(sm) FROM ScheduledMessage sm WHERE sm.isActive = true GROUP BY sm.status")
    List<Object[]> getSystemScheduleOverview();

    /**
     * 查找需要提醒的消息（即将发送）
     */
    @Query("SELECT sm FROM ScheduledMessage sm WHERE sm.status = 'PENDING' AND sm.isActive = true AND sm.scheduledTime BETWEEN :currentTime AND :reminderTime ORDER BY sm.scheduledTime ASC")
    List<ScheduledMessage> findMessagesNeedingReminder(@Param("currentTime") LocalDateTime currentTime,
                                                       @Param("reminderTime") LocalDateTime reminderTime);

    /**
     * 删除用户的所有定时消息
     */
    void deleteBySenderId(Long senderId);

    /**
     * 软删除用户的所有定时消息
     */
    @Modifying
    @Query("UPDATE ScheduledMessage sm SET sm.isActive = false WHERE sm.senderId = :senderId")
    void softDeleteUserMessages(@Param("senderId") Long senderId);
}
