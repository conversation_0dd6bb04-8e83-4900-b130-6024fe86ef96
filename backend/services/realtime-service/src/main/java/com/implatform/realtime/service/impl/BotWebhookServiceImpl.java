package com.implatform.realtime.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.implatform.realtime.entity.Bot;
import com.implatform.realtime.service.BotWebhookService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 机器人Webhook服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BotWebhookServiceImpl implements BotWebhookService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final ExecutorService webhookExecutor = Executors.newFixedThreadPool(10);

    private static final String SIGNATURE_HEADER = "X-Bot-Signature";
    private static final String TIMESTAMP_HEADER = "X-Bot-Timestamp";
    private static final String BOT_ID_HEADER = "X-Bot-Id";
    private static final int WEBHOOK_TIMEOUT = 10000; // 10秒超时

    /**
     * 发送Webhook事件
     */
    @Override
    public boolean sendWebhookEvent(Bot bot, BotWebhookService.WebhookEventData eventData) {
        if (!bot.hasWebhook()) {
            log.warn("机器人没有配置Webhook: botId={}", bot.getId());
            return false;
        }

        try {
            // 异步发送Webhook
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                return sendWebhookSync(bot, eventData);
            }, webhookExecutor);

            // 等待结果（最多10秒）
            return future.get(WEBHOOK_TIMEOUT, java.util.concurrent.TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("发送Webhook事件失败: botId={}, webhookUrl={}", 
                    bot.getId(), bot.getWebhookUrl(), e);
            return false;
        }
    }

    /**
     * 同步发送Webhook
     */
    private boolean sendWebhookSync(Bot bot, BotWebhookService.WebhookEventData eventData) {
        try {
            log.debug("发送Webhook事件: botId={}, eventType={}", bot.getId(), eventData.getEventType());

            // 序列化事件数据
            String jsonPayload = objectMapper.writeValueAsString(eventData);
            
            // 创建HTTP请求
            HttpHeaders headers = createWebhookHeaders(bot, jsonPayload);
            HttpEntity<String> request = new HttpEntity<>(jsonPayload, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    bot.getWebhookUrl(),
                    HttpMethod.POST,
                    request,
                    String.class
            );

            // 检查响应状态
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("Webhook发送成功: botId={}, status={}", bot.getId(), response.getStatusCode());
                return true;
            } else {
                log.warn("Webhook响应异常: botId={}, status={}, body={}", 
                        bot.getId(), response.getStatusCode(), response.getBody());
                return false;
            }

        } catch (Exception e) {
            log.error("Webhook发送失败: botId={}, error={}", bot.getId(), e.getMessage());
            return false;
        }
    }

    /**
     * 创建Webhook请求头
     */
    private HttpHeaders createWebhookHeaders(Bot bot, String payload) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 添加时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        headers.set(TIMESTAMP_HEADER, timestamp);
        
        // 添加机器人ID
        headers.set(BOT_ID_HEADER, bot.getId().toString());
        
        // 添加签名（如果配置了密钥）
        if (bot.getWebhookSecret() != null && !bot.getWebhookSecret().trim().isEmpty()) {
            String signature = generateSignature(payload, timestamp, bot.getWebhookSecret());
            headers.set(SIGNATURE_HEADER, signature);
        }
        
        // 添加用户代理
        headers.set("User-Agent", "IM-Platform-Bot/1.0");
        
        return headers;
    }

    /**
     * 生成Webhook签名
     */
    private String generateSignature(String payload, String timestamp, String secret) {
        try {
            String data = timestamp + "." + payload;
            
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return "sha256=" + Base64.getEncoder().encodeToString(hash);
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("生成Webhook签名失败", e);
            return "";
        }
    }

    /**
     * 验证Webhook签名
     */
    @Override
    public boolean verifyWebhookSignature(String payload, String timestamp, String signature, String secret) {
        if (signature == null || secret == null) {
            return false;
        }
        
        String expectedSignature = generateSignature(payload, timestamp, secret);
        return signature.equals(expectedSignature);
    }

    /**
     * 发送消息事件到Webhook
     */
    @Override
    public boolean sendMessageEvent(Bot bot, Long userId, Long chatId, String messageType, String content) {
        MessageEventData eventData = MessageEventData.builder()
                .eventType("message")
                .botId(bot.getId())
                .userId(userId)
                .chatId(chatId)
                .messageType(messageType)
                .content(content)
                .timestamp(System.currentTimeMillis())
                .build();

        return sendWebhookEvent(bot, convertToWebhookEventData(eventData));
    }

    /**
     * 发送回调查询事件到Webhook
     */
    @Override
    public boolean sendCallbackQueryEvent(Bot bot, Long userId, Long chatId, String callbackData) {
        CallbackQueryEventData eventData = CallbackQueryEventData.builder()
                .eventType("callback_query")
                .botId(bot.getId())
                .userId(userId)
                .chatId(chatId)
                .callbackData(callbackData)
                .timestamp(System.currentTimeMillis())
                .build();

        return sendWebhookEvent(bot, convertToWebhookEventData(eventData));
    }

    /**
     * 发送内联查询事件到Webhook
     */
    @Override
    public boolean sendInlineQueryEvent(Bot bot, Long userId, String query) {
        InlineQueryEventData eventData = InlineQueryEventData.builder()
                .eventType("inline_query")
                .botId(bot.getId())
                .userId(userId)
                .query(query)
                .timestamp(System.currentTimeMillis())
                .build();

        return sendWebhookEvent(bot, convertToWebhookEventData(eventData));
    }

    /**
     * 测试Webhook连接
     */
    @Override
    public BotWebhookService.WebhookTestResult testWebhook(Bot bot) {
        if (!bot.hasWebhook()) {
            return BotWebhookService.WebhookTestResult.noWebhook();
        }

        try {
            // 创建测试事件
            TestEventData testData = TestEventData.builder()
                    .eventType("test")
                    .botId(bot.getId())
                    .message("Webhook连接测试")
                    .timestamp(System.currentTimeMillis())
                    .build();

            // 发送测试请求
            long startTime = System.currentTimeMillis();
            boolean success = sendWebhookEvent(bot, convertToWebhookEventData(testData));
            long responseTime = System.currentTimeMillis() - startTime;

            if (success) {
                return BotWebhookService.WebhookTestResult.success(responseTime);
            } else {
                return BotWebhookService.WebhookTestResult.failed("请求失败");
            }

        } catch (Exception e) {
            log.error("Webhook测试失败: botId={}", bot.getId(), e);
            return BotWebhookService.WebhookTestResult.failed(e.getMessage());
        }
    }

    /**
     * 转换为通用Webhook事件数据
     */
    private BotWebhookService.WebhookEventData convertToWebhookEventData(Object eventData) {
        // 这里简化处理，实际应该根据具体事件类型进行转换
        try {
            String json = objectMapper.writeValueAsString(eventData);
            return objectMapper.readValue(json, BotWebhookService.WebhookEventData.class);
        } catch (Exception e) {
            log.error("转换Webhook事件数据失败", e);
            return new BotWebhookService.WebhookEventData();
        }
    }

    /**
     * Webhook事件数据基类
     */
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebhookEventData {
        private String eventType;
        private Long botId;
        private Long timestamp;
        private Object data;


    }

    /**
     * 消息事件数据
     */
    @Getter
    @Setter
    public static class MessageEventData {
        private String eventType;
        private Long botId;
        private Long userId;
        private Long chatId;
        private String messageType;
        private String content;
        private Long timestamp;

        public static MessageEventDataBuilder builder() {
            return new MessageEventDataBuilder();
        }

        public static class MessageEventDataBuilder {
            private MessageEventData data = new MessageEventData();

            public MessageEventDataBuilder eventType(String eventType) {
                data.setEventType(eventType);
                return this;
            }

            public MessageEventDataBuilder botId(Long botId) {
                data.setBotId(botId);
                return this;
            }

            public MessageEventDataBuilder userId(Long userId) {
                data.setUserId(userId);
                return this;
            }

            public MessageEventDataBuilder chatId(Long chatId) {
                data.setChatId(chatId);
                return this;
            }

            public MessageEventDataBuilder messageType(String messageType) {
                data.setMessageType(messageType);
                return this;
            }

            public MessageEventDataBuilder content(String content) {
                data.setContent(content);
                return this;
            }

            public MessageEventDataBuilder timestamp(Long timestamp) {
                data.setTimestamp(timestamp);
                return this;
            }

            public MessageEventData build() {
                return data;
            }
        }
    }

    /**
     * 回调查询事件数据
     */
    @Getter
    @Setter
    public static class CallbackQueryEventData {
        private String eventType;
        private Long botId;
        private Long userId;
        private Long chatId;
        private String callbackData;
        private Long timestamp;

        public static CallbackQueryEventDataBuilder builder() {
            return new CallbackQueryEventDataBuilder();
        }


        public static class CallbackQueryEventDataBuilder {
            private final CallbackQueryEventData data = new CallbackQueryEventData();

            public CallbackQueryEventDataBuilder eventType(String eventType) {
                data.setEventType(eventType);
                return this;
            }

            public CallbackQueryEventDataBuilder botId(Long botId) {
                data.setBotId(botId);
                return this;
            }

            public CallbackQueryEventDataBuilder userId(Long userId) {
                data.setUserId(userId);
                return this;
            }

            public CallbackQueryEventDataBuilder chatId(Long chatId) {
                data.setChatId(chatId);
                return this;
            }

            public CallbackQueryEventDataBuilder callbackData(String callbackData) {
                data.setCallbackData(callbackData);
                return this;
            }

            public CallbackQueryEventDataBuilder timestamp(Long timestamp) {
                data.setTimestamp(timestamp);
                return this;
            }

            public CallbackQueryEventData build() {
                return data;
            }
        }
    }

    /**
     * 内联查询事件数据
     */
    @Getter
    @Setter
    public static class InlineQueryEventData {
        private String eventType;
        private Long botId;
        private Long userId;
        private String query;
        private Long timestamp;

        public static InlineQueryEventDataBuilder builder() {
            return new InlineQueryEventDataBuilder();
        }

        public static class InlineQueryEventDataBuilder {
            private final InlineQueryEventData data = new InlineQueryEventData();

            public InlineQueryEventDataBuilder eventType(String eventType) {
                data.setEventType(eventType);
                return this;
            }

            public InlineQueryEventDataBuilder botId(Long botId) {
                data.setBotId(botId);
                return this;
            }

            public InlineQueryEventDataBuilder userId(Long userId) {
                data.setUserId(userId);
                return this;
            }

            public InlineQueryEventDataBuilder query(String query) {
                data.setQuery(query);
                return this;
            }

            public InlineQueryEventDataBuilder timestamp(Long timestamp) {
                data.setTimestamp(timestamp);
                return this;
            }

            public InlineQueryEventData build() {
                return data;
            }
        }
    }

    /**
     * 测试事件数据
     */
    @Getter
    @Setter
    public static class TestEventData {
        private String eventType;
        private Long botId;
        private String message;
        private Long timestamp;

        public static TestEventDataBuilder builder() {
            return new TestEventDataBuilder();
        }

        public static class TestEventDataBuilder {
            private final TestEventData data = new TestEventData();

            public TestEventDataBuilder eventType(String eventType) {
                data.setEventType(eventType);
                return this;
            }

            public TestEventDataBuilder botId(Long botId) {
                data.setBotId(botId);
                return this;
            }

            public TestEventDataBuilder message(String message) {
                data.setMessage(message);
                return this;
            }

            public TestEventDataBuilder timestamp(Long timestamp) {
                data.setTimestamp(timestamp);
                return this;
            }

            public TestEventData build() {
                return data;
            }
        }
    }

    /**
     * Webhook测试结果
     */
    @Getter
    @Setter
    public static class WebhookTestResult {
        private final boolean success;
        private final String message;
        private final Long responseTime;

        private WebhookTestResult(boolean success, String message, Long responseTime) {
            this.success = success;
            this.message = message;
            this.responseTime = responseTime;
        }

        public static WebhookTestResult success(Long responseTime) {
            return new WebhookTestResult(true, "连接成功", responseTime);
        }

        public static WebhookTestResult failed(String message) {
            return new WebhookTestResult(false, message, null);
        }

        public static WebhookTestResult noWebhook() {
            return new WebhookTestResult(false, "未配置Webhook", null);
        }
    }
}
