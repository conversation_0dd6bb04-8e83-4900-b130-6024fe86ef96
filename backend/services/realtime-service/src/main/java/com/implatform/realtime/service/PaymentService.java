package com.implatform.realtime.service;

import com.implatform.realtime.dto.PaymentDTO;
import com.implatform.realtime.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 支付服务接口
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface PaymentService {

    // ==================== 支付创建和处理 ====================

    /**
     * 创建支付订单
     */
    PaymentDTO.PaymentInfo createPayment(PaymentDTO.CreatePaymentRequest request);

    /**
     * 处理支付
     */
    PaymentDTO.PaymentResult processPayment(String paymentOrderNo, PaymentDTO.ProcessPaymentRequest request);

    /**
     * 确认支付
     */
    PaymentDTO.PaymentInfo confirmPayment(String paymentOrderNo, String externalTransactionId);

    /**
     * 取消支付
     */
    void cancelPayment(String paymentOrderNo, String reason);

    /**
     * 支付回调处理
     */
    void handlePaymentCallback(String provider, Map<String, Object> callbackData);

    // ==================== 支付查询 ====================

    /**
     * 根据订单号查询支付信息
     */
    PaymentDTO.PaymentInfo getPaymentByOrderNo(String paymentOrderNo);

    /**
     * 根据ID查询支付信息
     */
    PaymentDTO.PaymentInfo getPaymentById(Long paymentId);

    /**
     * 查询用户支付记录
     */
    Page<PaymentDTO.PaymentInfo> getUserPayments(Long userId, Pageable pageable);

    /**
     * 查询用户指定状态的支付记录
     */
    Page<PaymentDTO.PaymentInfo> getUserPaymentsByStatus(Long userId, Payment.PaymentStatus status, Pageable pageable);

    /**
     * 查询支付记录（管理员）
     */
    Page<PaymentDTO.PaymentInfo> getPayments(PaymentDTO.PaymentQueryRequest request, Pageable pageable);

    // ==================== 退款处理 ====================

    /**
     * 申请退款
     */
    PaymentDTO.RefundInfo requestRefund(String paymentOrderNo, PaymentDTO.RefundRequest request);

    /**
     * 处理退款
     */
    PaymentDTO.RefundInfo processRefund(Long refundId, PaymentDTO.ProcessRefundRequest request);

    /**
     * 查询退款信息
     */
    PaymentDTO.RefundInfo getRefundInfo(Long refundId);

    /**
     * 查询支付的退款记录
     */
    List<PaymentDTO.RefundInfo> getPaymentRefunds(String paymentOrderNo);

    // ==================== 支付统计 ====================

    /**
     * 获取支付统计信息
     */
    PaymentDTO.PaymentStatistics getPaymentStatistics(Instant startDate, Instant endDate);

    /**
     * 获取用户支付统计
     */
    PaymentDTO.UserPaymentStatistics getUserPaymentStatistics(Long userId);

    /**
     * 获取支付方式统计
     */
    List<PaymentDTO.PaymentMethodStatistics> getPaymentMethodStatistics();

    /**
     * 获取每日支付统计
     */
    List<PaymentDTO.DailyStatistics> getDailyPaymentStatistics(Instant startDate, int days);

    /**
     * 获取月度支付统计
     */
    List<PaymentDTO.MonthlyStatistics> getMonthlyPaymentStatistics(Instant startDate, int months);

    // ==================== 支付管理 ====================

    /**
     * 更新支付状态
     */
    void updatePaymentStatus(String paymentOrderNo, Payment.PaymentStatus status, String reason);

    /**
     * 处理过期支付
     */
    int processExpiredPayments();

    /**
     * 同步支付状态
     */
    void syncPaymentStatus(String paymentOrderNo);

    /**
     * 重试失败支付
     */
    PaymentDTO.PaymentResult retryPayment(String paymentOrderNo);

    // ==================== 风控和验证 ====================

    /**
     * 验证支付请求
     */
    PaymentDTO.ValidationResult validatePaymentRequest(PaymentDTO.CreatePaymentRequest request);

    /**
     * 检查支付风险
     */
    PaymentDTO.RiskAssessment assessPaymentRisk(Long userId, BigDecimal amount, Payment.PaymentMethod method);

    /**
     * 检查重复支付
     */
    boolean checkDuplicatePayment(Long userId, String businessType, Long businessOrderId);

    /**
     * 获取支付限额
     */
    PaymentDTO.PaymentLimits getPaymentLimits(Long userId, Payment.PaymentMethod method);

    // ==================== 支付配置 ====================

    /**
     * 获取支付配置
     */
    PaymentDTO.PaymentConfig getPaymentConfig(Payment.PaymentProvider provider);

    /**
     * 更新支付配置
     */
    void updatePaymentConfig(Payment.PaymentProvider provider, PaymentDTO.PaymentConfig config);

    /**
     * 测试支付配置
     */
    PaymentDTO.ConfigTestResult testPaymentConfig(Payment.PaymentProvider provider);

    // ==================== 财务报表 ====================

    /**
     * 生成财务报表
     */
    PaymentDTO.FinancialReport generateFinancialReport(Instant startDate, Instant endDate);

    /**
     * 导出支付记录
     */
    String exportPaymentRecords(PaymentDTO.ExportRequest request);

    /**
     * 获取收入趋势
     */
    List<PaymentDTO.RevenueTrend> getRevenueTrend(Instant startDate, Instant endDate, String period);

    // ==================== 批量操作 ====================

    /**
     * 批量处理支付
     */
    PaymentDTO.BatchProcessResult batchProcessPayments(List<String> paymentOrderNos, String action);

    /**
     * 批量退款
     */
    PaymentDTO.BatchRefundResult batchRefund(List<String> paymentOrderNos, String reason);

    /**
     * 批量更新状态
     */
    int batchUpdateStatus(List<String> paymentOrderNos, Payment.PaymentStatus status);

    // ==================== 系统维护 ====================

    /**
     * 清理过期数据
     */
    int cleanupExpiredData(int days);

    /**
     * 修复异常数据
     */
    int repairAnomalousData();

    /**
     * 重新计算统计数据
     */
    void recalculateStatistics();

    /**
     * 数据一致性检查
     */
    PaymentDTO.ConsistencyCheckResult checkDataConsistency();
}
