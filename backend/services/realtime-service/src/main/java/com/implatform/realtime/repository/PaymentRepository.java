package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Payment;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 支付Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface PaymentRepository extends R2dbcRepository<Payment, Long> {

    /**
     * 根据支付订单号查找支付记录
     */
    @Query("SELECT * FROM payments WHERE payment_order_no = :paymentOrderNo")
    Mono<Payment> findByPaymentOrderNo(@Param("paymentOrderNo") String paymentOrderNo);

    /**
     * 根据外部交易ID查找支付记录
     */
    @Query("SELECT * FROM payments WHERE external_transaction_id = :externalTransactionId")
    Mono<Payment> findByExternalTransactionId(@Param("externalTransactionId") String externalTransactionId);

    /**
     * 根据用户ID查找支付记录
     */
    @Query("SELECT * FROM payments WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据用户ID和状态查找支付记录
     */
    @Query("SELECT * FROM payments WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByUserIdAndStatusOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据支付方式查找支付记录
     */
    @Query("SELECT * FROM payments WHERE payment_method = :paymentMethod ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByPaymentMethodOrderByCreatedAtDesc(@Param("paymentMethod") String paymentMethod, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据支付提供商查找支付记录
     */
    @Query("SELECT * FROM payments WHERE payment_provider = :paymentProvider ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByPaymentProviderOrderByCreatedAtDesc(@Param("paymentProvider") String paymentProvider, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态查找支付记录
     */
    @Query("SELECT * FROM payments WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByStatusOrderByCreatedAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据支付类型查找支付记录
     */
    @Query("SELECT * FROM payments WHERE payment_type = :paymentType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findByPaymentTypeOrderByCreatedAtDesc(@Param("paymentType") String paymentType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找过期的待支付订单
     */
    @Query("SELECT * FROM payments WHERE status = 'PENDING' AND expires_at < :now")
    Flux<Payment> findExpiredPendingPayments(@Param("now") Instant now);

    /**
     * 统计用户支付总金额
     */
    @Query("SELECT SUM(amount) FROM payments WHERE user_id = :userId AND status = 'SUCCESS'")
    Mono<BigDecimal> sumSuccessfulPaymentsByUserId(@Param("userId") Long userId);

    /**
     * 统计指定时间范围内的支付金额
     */
    @Query("SELECT SUM(amount) FROM payments WHERE status = 'SUCCESS' AND paid_at BETWEEN :startDate AND :endDate")
    Mono<BigDecimal> sumSuccessfulPaymentsByDateRange(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * 统计指定时间范围内的支付数量
     */
    @Query("SELECT COUNT(*) FROM payments WHERE status = 'SUCCESS' AND paid_at BETWEEN :startDate AND :endDate")
    Mono<Long> countSuccessfulPaymentsByDateRange(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * 根据业务类型和业务订单ID查找支付记录
     */
    @Query("SELECT * FROM payments WHERE business_type = :businessType AND business_order_id = :businessOrderId")
    Flux<Payment> findByBusinessTypeAndBusinessOrderId(@Param("businessType") String businessType, @Param("businessOrderId") Long businessOrderId);

    /**
     * 查找可退款的支付记录
     */
    @Query("SELECT * FROM payments WHERE status = 'SUCCESS' AND refunded_amount < amount LIMIT :limit OFFSET :offset")
    Flux<Payment> findRefundablePayments(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计各支付方式的使用情况
     */
    @Query("SELECT payment_method, COUNT(*), SUM(amount) FROM payments WHERE status = 'SUCCESS' GROUP BY payment_method")
    Flux<Object[]> getPaymentMethodStatistics();

    /**
     * 统计各支付提供商的使用情况
     */
    @Query("SELECT payment_provider, COUNT(*), SUM(amount) FROM payments WHERE status = 'SUCCESS' GROUP BY payment_provider")
    Flux<Object[]> getPaymentProviderStatistics();

    /**
     * 获取每日支付统计
     */
    @Query("SELECT DATE(paid_at) as payment_date, COUNT(*) as payment_count, SUM(amount) as total_amount " +
           "FROM payments WHERE status = 'SUCCESS' AND paid_at >= :startDate " +
           "GROUP BY DATE(paid_at) ORDER BY payment_date DESC")
    Flux<Object[]> getDailyPaymentStatistics(@Param("startDate") Instant startDate);

    /**
     * 获取月度支付统计
     */
    @Query("SELECT EXTRACT(YEAR FROM paid_at) as year, EXTRACT(MONTH FROM paid_at) as month, COUNT(*) as payment_count, SUM(amount) as total_amount " +
           "FROM payments WHERE status = 'SUCCESS' AND paid_at >= :startDate " +
           "GROUP BY EXTRACT(YEAR FROM paid_at), EXTRACT(MONTH FROM paid_at) ORDER BY year DESC, month DESC")
    Flux<Object[]> getMonthlyPaymentStatistics(@Param("startDate") Instant startDate);

    /**
     * 查找需要处理的支付记录
     */
    @Query("SELECT * FROM payments WHERE status IN ('PENDING', 'PROCESSING') AND created_at < :threshold")
    Flux<Payment> findPaymentsNeedingProcessing(@Param("threshold") Instant threshold);

    /**
     * 根据金额范围查找支付记录
     */
    @Query("SELECT * FROM payments WHERE amount BETWEEN :minAmount AND :maxAmount AND status = 'SUCCESS' ORDER BY paid_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findPaymentsByAmountRange(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找高风险支付记录
     */
    @Query("SELECT * FROM payments WHERE amount > :threshold OR error_message IS NOT NULL ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Payment> findHighRiskPayments(@Param("threshold") BigDecimal threshold, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计用户支付失败次数
     */
    @Query("SELECT COUNT(*) FROM payments WHERE user_id = :userId AND status = 'FAILED' AND created_at >= :since")
    Mono<Long> countFailedPaymentsByUserSince(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 查找重复支付记录
     */
    @Query("SELECT * FROM payments WHERE user_id = :userId AND business_type = :businessType AND business_order_id = :businessOrderId AND status = 'SUCCESS'")
    Flux<Payment> findDuplicatePayments(@Param("userId") Long userId, @Param("businessType") String businessType, @Param("businessOrderId") Long businessOrderId);

    /**
     * 获取支付成功率统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count, " +
           "COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count, " +
           "COUNT(*) as total_count " +
           "FROM payments WHERE created_at >= :startDate")
    Mono<Object[]> getPaymentSuccessRateStatistics(@Param("startDate") Instant startDate);

    /**
     * 查找异常支付记录
     */
    @Query("SELECT * FROM payments WHERE " +
           "(status = 'SUCCESS' AND paid_at IS NULL) OR " +
           "(status = 'FAILED' AND error_message IS NULL) OR " +
           "(actual_amount IS NULL AND status = 'SUCCESS')")
    Flux<Payment> findAnomalousPayments();
}
