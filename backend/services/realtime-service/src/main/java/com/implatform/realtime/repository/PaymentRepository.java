package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 支付Repository
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {

    /**
     * 根据支付订单号查找支付记录
     */
    Optional<Payment> findByPaymentOrderNo(String paymentOrderNo);

    /**
     * 根据外部交易ID查找支付记录
     */
    Optional<Payment> findByExternalTransactionId(String externalTransactionId);

    /**
     * 根据用户ID查找支付记录
     */
    Page<Payment> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找支付记录
     */
    Page<Payment> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Payment.PaymentStatus status, Pageable pageable);

    /**
     * 根据支付方式查找支付记录
     */
    Page<Payment> findByPaymentMethodOrderByCreatedAtDesc(Payment.PaymentMethod paymentMethod, Pageable pageable);

    /**
     * 根据支付提供商查找支付记录
     */
    Page<Payment> findByPaymentProviderOrderByCreatedAtDesc(Payment.PaymentProvider paymentProvider, Pageable pageable);

    /**
     * 根据状态查找支付记录
     */
    Page<Payment> findByStatusOrderByCreatedAtDesc(Payment.PaymentStatus status, Pageable pageable);

    /**
     * 根据支付类型查找支付记录
     */
    Page<Payment> findByPaymentTypeOrderByCreatedAtDesc(Payment.PaymentType paymentType, Pageable pageable);

    /**
     * 查找过期的待支付订单
     */
    @Query("SELECT p FROM Payment p WHERE p.status = 'PENDING' AND p.expiresAt < :now")
    List<Payment> findExpiredPendingPayments(@Param("now") Instant now);

    /**
     * 统计用户支付总金额
     */
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.userId = :userId AND p.status = 'SUCCESS'")
    BigDecimal sumSuccessfulPaymentsByUserId(@Param("userId") Long userId);

    /**
     * 统计指定时间范围内的支付金额
     */
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.status = 'SUCCESS' AND p.paidAt BETWEEN :startDate AND :endDate")
    BigDecimal sumSuccessfulPaymentsByDateRange(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * 统计指定时间范围内的支付数量
     */
    @Query("SELECT COUNT(p) FROM Payment p WHERE p.status = 'SUCCESS' AND p.paidAt BETWEEN :startDate AND :endDate")
    Long countSuccessfulPaymentsByDateRange(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * 根据业务类型和业务订单ID查找支付记录
     */
    List<Payment> findByBusinessTypeAndBusinessOrderId(String businessType, Long businessOrderId);

    /**
     * 查找可退款的支付记录
     */
    @Query("SELECT p FROM Payment p WHERE p.status = 'SUCCESS' AND p.refundedAmount < p.amount")
    Page<Payment> findRefundablePayments(Pageable pageable);

    /**
     * 统计各支付方式的使用情况
     */
    @Query("SELECT p.paymentMethod, COUNT(p), SUM(p.amount) FROM Payment p WHERE p.status = 'SUCCESS' GROUP BY p.paymentMethod")
    List<Object[]> getPaymentMethodStatistics();

    /**
     * 统计各支付提供商的使用情况
     */
    @Query("SELECT p.paymentProvider, COUNT(p), SUM(p.amount) FROM Payment p WHERE p.status = 'SUCCESS' GROUP BY p.paymentProvider")
    List<Object[]> getPaymentProviderStatistics();

    /**
     * 获取每日支付统计
     */
    @Query("SELECT DATE(p.paidAt) as paymentDate, COUNT(p) as paymentCount, SUM(p.amount) as totalAmount " +
           "FROM Payment p WHERE p.status = 'SUCCESS' AND p.paidAt >= :startDate " +
           "GROUP BY DATE(p.paidAt) ORDER BY paymentDate DESC")
    List<Object[]> getDailyPaymentStatistics(@Param("startDate") Instant startDate);

    /**
     * 获取月度支付统计
     */
    @Query("SELECT YEAR(p.paidAt) as year, MONTH(p.paidAt) as month, COUNT(p) as paymentCount, SUM(p.amount) as totalAmount " +
           "FROM Payment p WHERE p.status = 'SUCCESS' AND p.paidAt >= :startDate " +
           "GROUP BY YEAR(p.paidAt), MONTH(p.paidAt) ORDER BY year DESC, month DESC")
    List<Object[]> getMonthlyPaymentStatistics(@Param("startDate") Instant startDate);

    /**
     * 查找需要处理的支付记录
     */
    @Query("SELECT p FROM Payment p WHERE p.status IN ('PENDING', 'PROCESSING') AND p.createdAt < :threshold")
    List<Payment> findPaymentsNeedingProcessing(@Param("threshold") Instant threshold);

    /**
     * 根据金额范围查找支付记录
     */
    @Query("SELECT p FROM Payment p WHERE p.amount BETWEEN :minAmount AND :maxAmount AND p.status = 'SUCCESS' ORDER BY p.paidAt DESC")
    Page<Payment> findPaymentsByAmountRange(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, Pageable pageable);

    /**
     * 查找高风险支付记录
     */
    @Query("SELECT p FROM Payment p WHERE p.amount > :threshold OR p.errorMessage IS NOT NULL ORDER BY p.createdAt DESC")
    Page<Payment> findHighRiskPayments(@Param("threshold") BigDecimal threshold, Pageable pageable);

    /**
     * 统计用户支付失败次数
     */
    @Query("SELECT COUNT(p) FROM Payment p WHERE p.userId = :userId AND p.status = 'FAILED' AND p.createdAt >= :since")
    Long countFailedPaymentsByUserSince(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 查找重复支付记录
     */
    @Query("SELECT p FROM Payment p WHERE p.userId = :userId AND p.businessType = :businessType AND p.businessOrderId = :businessOrderId AND p.status = 'SUCCESS'")
    List<Payment> findDuplicatePayments(@Param("userId") Long userId, @Param("businessType") String businessType, @Param("businessOrderId") Long businessOrderId);

    /**
     * 获取支付成功率统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN p.status = 'SUCCESS' THEN 1 END) as successCount, " +
           "COUNT(CASE WHEN p.status = 'FAILED' THEN 1 END) as failedCount, " +
           "COUNT(p) as totalCount " +
           "FROM Payment p WHERE p.createdAt >= :startDate")
    Object[] getPaymentSuccessRateStatistics(@Param("startDate") Instant startDate);

    /**
     * 查找异常支付记录
     */
    @Query("SELECT p FROM Payment p WHERE " +
           "(p.status = 'SUCCESS' AND p.paidAt IS NULL) OR " +
           "(p.status = 'FAILED' AND p.errorMessage IS NULL) OR " +
           "(p.actualAmount IS NULL AND p.status = 'SUCCESS')")
    List<Payment> findAnomalousPayments();
}
