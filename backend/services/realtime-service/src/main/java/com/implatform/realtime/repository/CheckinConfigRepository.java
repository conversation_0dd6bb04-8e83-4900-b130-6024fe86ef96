package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CheckinConfig;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 签到配置Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CheckinConfigRepository extends R2dbcRepository<CheckinConfig, Long> {

    /**
     * 根据配置类型和配置键查找配置
     */
    @Query("SELECT * FROM checkin_configs WHERE config_type = :configType AND config_key = :configKey")
    Mono<CheckinConfig> findByConfigTypeAndConfigKey(@Param("configType") String configType, @Param("configKey") String configKey);

    /**
     * 检查配置是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM checkin_configs WHERE config_type = :configType AND config_key = :configKey")
    Mono<Boolean> existsByConfigTypeAndConfigKey(@Param("configType") String configType, @Param("configKey") String configKey);

    /**
     * 根据配置类型查找所有配置
     */
    @Query("SELECT * FROM checkin_configs WHERE config_type = :configType ORDER BY config_key")
    Flux<CheckinConfig> findByConfigTypeOrderByConfigKey(@Param("configType") String configType);

    /**
     * 根据配置类型查找启用的配置
     */
    @Query("SELECT * FROM checkin_configs WHERE config_type = :configType AND is_active = true ORDER BY config_key")
    Flux<CheckinConfig> findByConfigTypeAndIsActiveTrueOrderByConfigKey(@Param("configType") String configType);

    /**
     * 查找所有启用的配置
     */
    List<CheckinConfig> findByIsActiveTrueOrderByConfigTypeAscConfigKeyAsc();

    /**
     * 查找所有禁用的配置
     */
    List<CheckinConfig> findByIsActiveFalseOrderByConfigTypeAscConfigKeyAsc();

    /**
     * 根据配置键模糊查找
     */
    List<CheckinConfig> findByConfigKeyContainingIgnoreCaseOrderByConfigTypeAscConfigKeyAsc(String keyword);

    /**
     * 根据描述模糊查找
     */
    List<CheckinConfig> findByDescriptionContainingIgnoreCaseOrderByConfigTypeAscConfigKeyAsc(String keyword);

    /**
     * 获取指定配置类型的配置数量
     */
    long countByConfigType(CheckinConfig.ConfigType configType);

    /**
     * 获取启用配置的数量
     */
    long countByIsActiveTrue();

    /**
     * 获取禁用配置的数量
     */
    long countByIsActiveFalse();

    /**
     * 批量启用指定类型的配置
     */
    @Modifying
    @Transactional
    @Query("UPDATE CheckinConfig c SET c.isActive = true WHERE c.configType = :configType")
    int enableConfigsByType(@Param("configType") CheckinConfig.ConfigType configType);

    /**
     * 批量禁用指定类型的配置
     */
    @Modifying
    @Transactional
    @Query("UPDATE CheckinConfig c SET c.isActive = false WHERE c.configType = :configType")
    int disableConfigsByType(@Param("configType") CheckinConfig.ConfigType configType);

    /**
     * 获取所有配置类型
     */
    @Query("SELECT DISTINCT c.configType FROM CheckinConfig c ORDER BY c.configType")
    List<CheckinConfig.ConfigType> findAllConfigTypes();

    /**
     * 获取指定类型的所有配置键
     */
    @Query("SELECT c.configKey FROM CheckinConfig c WHERE c.configType = :configType ORDER BY c.configKey")
    List<String> findConfigKeysByType(@Param("configType") CheckinConfig.ConfigType configType);

    /**
     * 查找JSON格式的配置
     */
    @Query("SELECT c FROM CheckinConfig c WHERE c.configValue LIKE '{%}' OR c.configValue LIKE '[%]'")
    List<CheckinConfig> findJsonConfigs();

    /**
     * 查找数值型配置
     */
    @Query(value = "SELECT * FROM user_checkin_config WHERE config_value ~ '^[0-9]+\\.?[0-9]*$'", nativeQuery = true)
    List<CheckinConfig> findNumericConfigs();

    /**
     * 查找布尔型配置
     */
    @Query("SELECT c FROM CheckinConfig c WHERE LOWER(c.configValue) IN ('true', 'false')")
    List<CheckinConfig> findBooleanConfigs();

    /**
     * 根据配置值查找配置
     */
    List<CheckinConfig> findByConfigValueOrderByConfigTypeAscConfigKeyAsc(String configValue);

    /**
     * 查找配置值为空的配置
     */
    @Query("SELECT c FROM CheckinConfig c WHERE c.configValue IS NULL OR c.configValue = ''")
    List<CheckinConfig> findEmptyValueConfigs();

    /**
     * 获取配置统计信息
     */
    @Query("SELECT c.configType, COUNT(c), " +
           "SUM(CASE WHEN c.isActive = true THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN c.isActive = false THEN 1 ELSE 0 END) " +
           "FROM CheckinConfig c GROUP BY c.configType ORDER BY c.configType")
    List<Object[]> getConfigStatistics();

    /**
     * 查找最近更新的配置
     */
    List<CheckinConfig> findTop10ByOrderByUpdatedAtDesc();

    /**
     * 查找最近创建的配置
     */
    List<CheckinConfig> findTop10ByOrderByCreatedAtDesc();

    /**
     * 删除指定类型的所有配置
     */
    @Modifying
    @Transactional
    void deleteByConfigType(CheckinConfig.ConfigType configType);

    /**
     * 删除指定类型和键的配置
     */
    @Modifying
    @Transactional
    void deleteByConfigTypeAndConfigKey(CheckinConfig.ConfigType configType, String configKey);
}
