package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Conversation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 会话数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ConversationRepository extends R2dbcRepository<Conversation, Long> {

    /**
     * 根据创建者ID查找会话
     */
    @Query("SELECT * FROM conversations WHERE creator_id = :creatorId AND is_deleted = false")
    Flux<Conversation> findByCreatorIdAndIsDeletedFalse(@Param("creatorId") Long creatorId);

    /**
     * 根据会话类型查找会话
     */
    @Query("SELECT * FROM conversations WHERE conversation_type = :conversationType AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<Conversation> findByConversationTypeAndIsDeletedFalse(
            @Param("conversationType") String conversationType,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 根据创建者ID和会话类型查找会话
     */
    @Query("SELECT * FROM conversations WHERE creator_id = :creatorId AND conversation_type = :conversationType AND is_deleted = false")
    Flux<Conversation> findByCreatorIdAndConversationTypeAndIsDeletedFalse(
            @Param("creatorId") Long creatorId,
            @Param("conversationType") String conversationType);

    /**
     * 查找活跃的会话
     */
    @Query("SELECT * FROM conversations WHERE is_active = :isActive AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<Conversation> findByIsActiveAndIsDeletedFalse(@Param("isActive") Boolean isActive, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据最后消息时间查找会话
     */
    @Query("SELECT * FROM conversations WHERE last_message_at >= :startTime AND is_deleted = false ORDER BY last_message_at DESC")
    Flux<Conversation> findRecentConversations(@Param("startTime") Instant startTime);

    /**
     * 查找置顶的会话
     */
    @Query("SELECT * FROM conversations WHERE is_pinned = true AND is_deleted = false ORDER BY last_message_at DESC")
    Flux<Conversation> findByIsPinnedTrueAndIsDeletedFalseOrderByLastMessageAtDesc();

    /**
     * 统计用户创建的会话数量
     */
    @Query("SELECT COUNT(*) FROM conversations WHERE creator_id = :creatorId AND is_deleted = false")
    Mono<Long> countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 统计指定类型的会话数量
     */
    @Query("SELECT COUNT(*) FROM conversations WHERE conversation_type = :conversationType AND is_deleted = false")
    Mono<Long> countByConversationTypeAndIsDeletedFalse(@Param("conversationType") String conversationType);

    /**
     * 更新会话的最后消息信息
     */
    @Modifying
    @Query("UPDATE conversations SET last_message_id = :messageId, last_message_content = :content, " +
           "last_message_sender_id = :senderId, last_message_at = :messageTime, message_count = message_count + 1 " +
           "WHERE id = :conversationId")
    Mono<Void> updateLastMessage(@Param("conversationId") Long conversationId,
                          @Param("messageId") Long messageId,
                          @Param("content") String content,
                          @Param("senderId") Long senderId,
                          @Param("messageTime") Instant messageTime);

    /**
     * 增加未读消息数量
     */
    @Modifying
    @Query("UPDATE conversations SET unread_count = unread_count + 1 WHERE id = :conversationId")
    Mono<Void> incrementUnreadCount(@Param("conversationId") Long conversationId);

    /**
     * 清零未读消息数量
     */
    @Modifying
    @Query("UPDATE conversations SET unread_count = 0 WHERE id = :conversationId")
    Mono<Void> clearUnreadCount(@Param("conversationId") Long conversationId);

    /**
     * 软删除会话
     */
    @Modifying
    @Query("UPDATE conversations SET is_deleted = true, updated_at = CURRENT_TIMESTAMP WHERE id = :conversationId")
    Mono<Void> softDeleteById(@Param("conversationId") Long conversationId);

    /**
     * 恢复已删除的会话
     */
    @Modifying
    @Query("UPDATE conversations SET is_deleted = false, updated_at = CURRENT_TIMESTAMP WHERE id = :conversationId")
    Mono<Void> restoreById(@Param("conversationId") Long conversationId);

    /**
     * 设置会话置顶状态
     */
    @Modifying
    @Query("UPDATE conversations SET is_pinned = :pinned, updated_at = CURRENT_TIMESTAMP WHERE id = :conversationId")
    Mono<Void> updatePinnedStatus(@Param("conversationId") Long conversationId, @Param("pinned") Boolean pinned);

    /**
     * 设置会话静音状态
     */
    @Modifying
    @Query("UPDATE conversations SET is_muted = :muted, updated_at = CURRENT_TIMESTAMP WHERE id = :conversationId")
    Mono<Void> updateMutedStatus(@Param("conversationId") Long conversationId, @Param("muted") Boolean muted);

    /**
     * 查找需要清理的旧会话
     */
    @Query("SELECT * FROM conversations WHERE last_message_at < :cutoffTime AND is_deleted = false")
    Flux<Conversation> findOldConversations(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 根据参与者数量范围查找会话
     */
    @Query("SELECT * FROM conversations WHERE participant_count BETWEEN :minCount AND :maxCount AND is_deleted = false")
    Flux<Conversation> findByParticipantCountBetween(@Param("minCount") Integer minCount, @Param("maxCount") Integer maxCount);

    /**
     * 查找最活跃的会话
     */
    @Query("SELECT * FROM conversations WHERE is_deleted = false ORDER BY message_count DESC, last_message_at DESC LIMIT :limit OFFSET :offset")
    Flux<Conversation> findMostActiveConversations(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户的系统通知对话
     * 注意：这里简化处理，实际应该通过会话参与者表来查找
     */
    @Query("SELECT * FROM conversations WHERE conversation_type = 'SYSTEM' AND creator_id = :systemUserId AND is_deleted = false")
    Flux<Conversation> findSystemNotificationConversations(@Param("systemUserId") Long systemUserId);

    /**
     * 检查用户是否有系统通知对话
     */
    @Query("SELECT COUNT(*) > 0 FROM conversations WHERE conversation_type = 'SYSTEM' AND creator_id = :systemUserId AND is_deleted = false")
    Mono<Boolean> existsSystemNotificationConversation(@Param("systemUserId") Long systemUserId);

    /**
     * 更新对话最后活动时间
     */
    @Modifying
    @Query("UPDATE conversations SET last_activity_at = :activityTime WHERE id = :conversationId")
    Mono<Void> updateLastActivityTime(@Param("conversationId") Long conversationId, @Param("activityTime") Instant activityTime);
}
