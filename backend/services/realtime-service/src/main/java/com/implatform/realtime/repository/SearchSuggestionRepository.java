package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchSuggestion;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 搜索建议Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchSuggestionRepository extends R2dbcRepository<SearchSuggestion, Long> {
    
    /**
     * 根据建议文本查找建议
     */
    Optional<SearchSuggestion> findBySuggestionTextAndIsActiveTrue(String suggestionText);
    
    /**
     * 根据建议类型查找激活的建议
     */
    List<SearchSuggestion> findBySuggestionTypeAndIsActiveTrueOrderByWeightScoreDesc(
        SearchSuggestion.SuggestionType suggestionType);
    
    /**
     * 根据语言代码查找建议
     */
    List<SearchSuggestion> findByLanguageCodeAndIsActiveTrueOrderByWeightScoreDesc(String languageCode);
    
    /**
     * 查找以指定文本开头的建议（自动补全）
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionText LIKE :prefix% AND s.isActive = true AND s.suggestionType = :suggestionType ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findAutoCompleteSuggestions(
        @Param("prefix") String prefix,
        @Param("suggestionType") SearchSuggestion.SuggestionType suggestionType);
    
    /**
     * 查找包含指定文本的建议（模糊匹配）
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionText LIKE %:text% AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findFuzzySuggestions(@Param("text") String text);
    
    /**
     * 查找热门建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND s.frequency >= :threshold ORDER BY s.frequency DESC, s.weightScore DESC")
    List<SearchSuggestion> findPopularSuggestions(@Param("threshold") Long threshold);
    
    /**
     * 查找高权重建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND s.weightScore >= :threshold ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findHighWeightSuggestions(@Param("threshold") Double threshold);
    
    /**
     * 查找高相关性建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND s.relevanceScore >= :threshold ORDER BY s.relevanceScore DESC")
    List<SearchSuggestion> findHighRelevanceSuggestions(@Param("threshold") Double threshold);
    
    /**
     * 根据建议来源查找建议
     */
    List<SearchSuggestion> findBySuggestionSourceAndIsActiveTrueOrderByWeightScoreDesc(
        SearchSuggestion.SuggestionSource suggestionSource);
    
    /**
     * 查找最近使用的建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND s.lastUsedAt IS NOT NULL ORDER BY s.lastUsedAt DESC")
    List<SearchSuggestion> findRecentlyUsedSuggestions();
    
    /**
     * 查找长时间未使用的建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND (s.lastUsedAt IS NULL OR s.lastUsedAt < :threshold)")
    List<SearchSuggestion> findUnusedSuggestions(@Param("threshold") Instant threshold);
    
    /**
     * 统计各建议类型的数量
     */
    @Query("SELECT s.suggestionType, COUNT(s) FROM SearchSuggestion s WHERE s.isActive = true GROUP BY s.suggestionType")
    List<Object[]> countBySuggestionType();
    
    /**
     * 统计各建议来源的数量
     */
    @Query("SELECT s.suggestionSource, COUNT(s) FROM SearchSuggestion s WHERE s.isActive = true GROUP BY s.suggestionSource")
    List<Object[]> countBySuggestionSource();
    
    /**
     * 统计各语言的建议数量
     */
    @Query("SELECT s.languageCode, COUNT(s) FROM SearchSuggestion s WHERE s.isActive = true GROUP BY s.languageCode")
    List<Object[]> countByLanguage();
    
    /**
     * 获取建议使用统计
     */
    @Query("SELECT s.suggestionText, s.frequency, s.weightScore, s.lastUsedAt FROM SearchSuggestion s WHERE s.isActive = true ORDER BY s.frequency DESC")
    List<Object[]> getSuggestionUsageStats();
    
    /**
     * 查找需要更新权重的建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND (s.lastUsedAt IS NULL OR s.lastUsedAt < :threshold)")
    List<SearchSuggestion> findSuggestionsNeedingWeightUpdate(@Param("threshold") Instant threshold);
    
    /**
     * 批量更新建议权重
     */
    @Modifying
    @Query("UPDATE SearchSuggestion s SET s.weightScore = :weightScore WHERE s.id IN :suggestionIds")
    int batchUpdateWeightScore(@Param("suggestionIds") List<Long> suggestionIds, @Param("weightScore") Double weightScore);
    
    /**
     * 批量激活建议
     */
    @Modifying
    @Query("UPDATE SearchSuggestion s SET s.isActive = true WHERE s.id IN :suggestionIds")
    int batchActivate(@Param("suggestionIds") List<Long> suggestionIds);
    
    /**
     * 批量停用建议
     */
    @Modifying
    @Query("UPDATE SearchSuggestion s SET s.isActive = false WHERE s.id IN :suggestionIds")
    int batchDeactivate(@Param("suggestionIds") List<Long> suggestionIds);
    
    /**
     * 增加建议使用频率
     */
    @Modifying
    @Query("UPDATE SearchSuggestion s SET s.frequency = s.frequency + 1, s.lastUsedAt = :usedAt WHERE s.id = :suggestionId")
    int incrementFrequency(@Param("suggestionId") Long suggestionId, @Param("usedAt") Instant usedAt);
    
    /**
     * 查找相似建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionText LIKE %:text% AND s.id != :excludeId AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findSimilarSuggestions(@Param("text") String text, @Param("excludeId") Long excludeId);
    
    /**
     * 查找拼写纠正建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :correctionType AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findSpellingCorrections(@Param("correctionType") SearchSuggestion.SuggestionType correctionType);
    
    /**
     * 查找同义词建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :synonymType AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findSynonymSuggestions(@Param("synonymType") SearchSuggestion.SuggestionType synonymType);
    
    /**
     * 查找趋势建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :trendingType AND s.isActive = true AND s.lastUsedAt >= :since ORDER BY s.frequency DESC")
    List<SearchSuggestion> findTrendingSuggestions(
        @Param("trendingType") SearchSuggestion.SuggestionType trendingType,
        @Param("since") Instant since);
    
    /**
     * 查找个性化建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :personalizedType AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findPersonalizedSuggestions(@Param("personalizedType") SearchSuggestion.SuggestionType personalizedType);
    
    /**
     * 查找分类建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :categoryType AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findCategorySuggestions(@Param("categoryType") SearchSuggestion.SuggestionType categoryType);
    
    /**
     * 查找实体建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.suggestionType = :entityType AND s.isActive = true ORDER BY s.weightScore DESC")
    List<SearchSuggestion> findEntitySuggestions(@Param("entityType") SearchSuggestion.SuggestionType entityType);
    
    /**
     * 获取建议性能统计
     */
    @Query("SELECT AVG(s.frequency), MAX(s.frequency), MIN(s.frequency), AVG(s.weightScore) FROM SearchSuggestion s WHERE s.isActive = true")
    Object[] getSuggestionPerformanceStats();
    
    /**
     * 查找低质量建议
     */
    @Query("SELECT s FROM SearchSuggestion s WHERE s.isActive = true AND (s.frequency < :frequencyThreshold OR s.weightScore < :weightThreshold)")
    List<SearchSuggestion> findLowQualitySuggestions(
        @Param("frequencyThreshold") Long frequencyThreshold,
        @Param("weightThreshold") Double weightThreshold);
    
    /**
     * 删除过期建议
     */
    @Modifying
    @Query("DELETE FROM SearchSuggestion s WHERE s.isActive = false AND s.lastUsedAt < :cutoffTime")
    int deleteExpiredSuggestions(@Param("cutoffTime") Instant cutoffTime);
    
    /**
     * 检查建议是否存在
     */
    boolean existsBySuggestionTextAndIsActiveTrue(String suggestionText);
    
    /**
     * 查找重复建议
     */
    @Query("SELECT s.suggestionText, COUNT(s) as duplicateCount FROM SearchSuggestion s GROUP BY s.suggestionText HAVING COUNT(s) > 1")
    List<Object[]> findDuplicateSuggestions();
    
    /**
     * 获取建议覆盖率统计
     */
    @Query("SELECT s.suggestionType, COUNT(s), AVG(s.frequency) FROM SearchSuggestion s WHERE s.isActive = true GROUP BY s.suggestionType ORDER BY COUNT(s) DESC")
    List<Object[]> getSuggestionCoverageStats();
    
    /**
     * 查找最新建议
     */
    List<SearchSuggestion> findTop20ByIsActiveTrueOrderByCreatedAtDesc();
    
    /**
     * 查找最受欢迎的建议
     */
    List<SearchSuggestion> findTop20ByIsActiveTrueOrderByFrequencyDesc();
    
    /**
     * 分页查找激活的建议
     */
    Page<SearchSuggestion> findByIsActiveTrueOrderByWeightScoreDesc(Pageable pageable);
}
