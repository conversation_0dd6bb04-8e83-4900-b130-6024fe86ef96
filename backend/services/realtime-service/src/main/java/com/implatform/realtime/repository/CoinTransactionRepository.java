package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CoinTransactionRecord;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 金币交易记录Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CoinTransactionRepository extends R2dbcRepository<CoinTransactionRecord, Long> {

    /**
     * 根据交易流水号查找交易记录
     */
    @Query("SELECT * FROM coin_transaction_records WHERE transaction_no = :transactionNo")
    Mono<CoinTransactionRecord> findByTransactionNo(@Param("transactionNo") String transactionNo);

    /**
     * 检查交易流水号是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM coin_transaction_records WHERE transaction_no = :transactionNo")
    Mono<Boolean> existsByTransactionNo(@Param("transactionNo") String transactionNo);

    /**
     * 获取用户的交易记录（分页）
     */
    @Query("SELECT * FROM coin_transaction_records WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<CoinTransactionRecord> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取用户指定类型的交易记录
     */
    @Query("SELECT * FROM coin_transaction_records WHERE user_id = :userId AND transaction_type = :transactionType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<CoinTransactionRecord> findByUserIdAndTransactionTypeOrderByCreatedAtDesc(
            @Param("userId") Long userId, @Param("transactionType") String transactionType, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取用户指定状态的交易记录
     */
    @Query("SELECT * FROM coin_transaction_records WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<CoinTransactionRecord> findByUserIdAndStatusOrderByCreatedAtDesc(
            Long userId, CoinTransactionRecord.TransactionStatus status, Pageable pageable);

    /**
     * 获取用户指定时间范围内的交易记录
     */
    List<CoinTransactionRecord> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
            Long userId, Instant startTime, Instant endTime);

    /**
     * 统计用户交易总数
     */
    long countByUserId(Long userId);

    /**
     * 统计用户指定类型的交易数
     */
    long countByUserIdAndTransactionType(Long userId, CoinTransactionRecord.TransactionType transactionType);

    /**
     * 统计用户收入交易总额
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM CoinTransactionRecord t " +
           "WHERE t.userId = :userId AND t.amount > 0 AND t.status = 'SUCCESS'")
    int sumIncomeByUserId(@Param("userId") Long userId);

    /**
     * 统计用户支出交易总额
     */
    @Query("SELECT COALESCE(SUM(ABS(t.amount)), 0) FROM CoinTransactionRecord t " +
           "WHERE t.userId = :userId AND t.amount < 0 AND t.status = 'SUCCESS'")
    int sumExpenseByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定类型的交易总额
     */
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM CoinTransactionRecord t " +
           "WHERE t.userId = :userId AND t.transactionType = :type AND t.status = 'SUCCESS'")
    int sumAmountByUserIdAndType(@Param("userId") Long userId, 
                                 @Param("type") CoinTransactionRecord.TransactionType type);

    /**
     * 获取用户最近的交易记录
     */
    Optional<CoinTransactionRecord> findTopByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 获取用户指定数量的最近交易记录
     */
    @Query("SELECT t FROM CoinTransactionRecord t WHERE t.userId = :userId ORDER BY t.createdAt DESC")
    List<CoinTransactionRecord> findRecentTransactionsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取系统总交易数
     */
    @Query("SELECT COUNT(t) FROM CoinTransactionRecord t WHERE t.status = 'SUCCESS'")
    long countSuccessfulTransactions();

    /**
     * 获取系统总交易金额
     */
    @Query("SELECT COALESCE(SUM(ABS(t.amount)), 0) FROM CoinTransactionRecord t WHERE t.status = 'SUCCESS'")
    long getTotalTransactionAmount();

    /**
     * 获取指定时间范围内的交易统计
     */
    @Query("SELECT t.transactionType, COUNT(t), SUM(ABS(t.amount)) FROM CoinTransactionRecord t " +
           "WHERE t.createdAt BETWEEN :startTime AND :endTime AND t.status = 'SUCCESS' " +
           "GROUP BY t.transactionType")
    List<Object[]> getTransactionStatistics(@Param("startTime") Instant startTime, 
                                            @Param("endTime") Instant endTime);

    /**
     * 获取每日交易统计
     */
    @Query("SELECT DATE(t.createdAt), COUNT(t), SUM(ABS(t.amount)) FROM CoinTransactionRecord t " +
           "WHERE t.createdAt BETWEEN :startTime AND :endTime AND t.status = 'SUCCESS' " +
           "GROUP BY DATE(t.createdAt) ORDER BY DATE(t.createdAt)")
    List<Object[]> getDailyTransactionStatistics(@Param("startTime") Instant startTime, 
                                                  @Param("endTime") Instant endTime);

    /**
     * 获取用户每日交易统计
     */
    @Query("SELECT DATE(t.createdAt), COUNT(t), SUM(t.amount) FROM CoinTransactionRecord t " +
           "WHERE t.userId = :userId AND t.createdAt BETWEEN :startTime AND :endTime AND t.status = 'SUCCESS' " +
           "GROUP BY DATE(t.createdAt) ORDER BY DATE(t.createdAt)")
    List<Object[]> getUserDailyTransactionStatistics(@Param("userId") Long userId,
                                                      @Param("startTime") Instant startTime, 
                                                      @Param("endTime") Instant endTime);

    /**
     * 查找失败的交易记录
     */
    List<CoinTransactionRecord> findByStatusOrderByCreatedAtDesc(CoinTransactionRecord.TransactionStatus status);

    /**
     * 查找失败的交易记录（分页）
     */
    Page<CoinTransactionRecord> findByStatusOrderByCreatedAtDesc(CoinTransactionRecord.TransactionStatus status, Pageable pageable);

    /**
     * 查找指定状态的交易记录
     */
    List<CoinTransactionRecord> findByStatus(CoinTransactionRecord.TransactionStatus status);

    /**
     * 获取用户指定类型和状态的交易记录
     */
    Page<CoinTransactionRecord> findByUserIdAndTransactionTypeAndStatusOrderByCreatedAtDesc(
            Long userId, CoinTransactionRecord.TransactionType type, CoinTransactionRecord.TransactionStatus status, Pageable pageable);

    /**
     * 查找指定状态和时间之前的交易记录
     */
    List<CoinTransactionRecord> findByStatusAndCreatedAtBefore(CoinTransactionRecord.TransactionStatus status, Instant cutoffTime);

    /**
     * 查找待处理的交易记录
     */
    List<CoinTransactionRecord> findByStatusAndCreatedAtBeforeOrderByCreatedAtAsc(
            CoinTransactionRecord.TransactionStatus status, Instant cutoffTime);

    /**
     * 删除指定时间之前的记录（数据清理）
     */
    @Modifying
    @Transactional
    void deleteByCreatedAtBefore(Instant cutoffTime);

    /**
     * 查找余额计算异常的交易记录
     */
    @Query("SELECT t FROM CoinTransactionRecord t WHERE t.balanceAfter != (t.balanceBefore + t.amount)")
    List<CoinTransactionRecord> findAnomalousTransactions();

    /**
     * 获取用户指定关联ID的交易记录
     */
    List<CoinTransactionRecord> findByUserIdAndRelatedIdOrderByCreatedAtDesc(Long userId, String relatedId);

    /**
     * 统计指定时间范围内的活跃用户数
     */
    @Query("SELECT COUNT(DISTINCT t.userId) FROM CoinTransactionRecord t " +
           "WHERE t.createdAt BETWEEN :startTime AND :endTime AND t.status = 'SUCCESS'")
    long countActiveUsersInPeriod(@Param("startTime") Instant startTime, 
                                  @Param("endTime") Instant endTime);

    /**
     * 获取交易量排行榜
     */
    @Query("SELECT t.userId, COUNT(t), SUM(ABS(t.amount)) FROM CoinTransactionRecord t " +
           "WHERE t.status = 'SUCCESS' AND t.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY t.userId ORDER BY SUM(ABS(t.amount)) DESC")
    List<Object[]> getTransactionVolumeRanking(@Param("startTime") Instant startTime, 
                                               @Param("endTime") Instant endTime, 
                                               Pageable pageable);
}
