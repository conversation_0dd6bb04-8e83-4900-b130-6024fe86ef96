package com.implatform.realtime.repository;

import com.implatform.realtime.entity.ChannelMessage;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

/**
 * 频道消息数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ChannelMessageRepository extends R2dbcRepository<ChannelMessage, Long> {

    /**
     * 根据频道ID查找消息
     */
    @Query("SELECT * FROM channel_messages WHERE channel_id = :channelId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<ChannelMessage> findByChannelIdOrderByCreatedAtDesc(@Param("channelId") Long channelId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据频道ID和时间范围查找消息
     */
    @Query("SELECT * FROM channel_messages WHERE channel_id = :channelId AND created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<ChannelMessage> findByChannelIdAndCreatedAtBetweenOrderByCreatedAtDesc(
            @Param("channelId") Long channelId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找频道最新消息
     */
    @Query("SELECT * FROM channel_messages WHERE channel_id = :channelId ORDER BY created_at DESC LIMIT 10")
    Flux<ChannelMessage> findTop10ByChannelIdOrderByCreatedAtDesc(@Param("channelId") Long channelId);

    /**
     * 根据发送者查找消息
     */
    @Query("SELECT * FROM channel_messages WHERE sender_id = :senderId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<ChannelMessage> findBySenderIdOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 搜索频道消息
     */
    @Query("SELECT * FROM channel_messages WHERE channel_id = :channelId AND " +
           "(content LIKE CONCAT('%', :keyword, '%') OR media_name LIKE CONCAT('%', :keyword, '%')) " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<ChannelMessage> searchChannelMessages(@Param("channelId") Long channelId,
                                              @Param("keyword") String keyword, 
                                              Pageable pageable);

    /**
     * 统计频道消息数量
     */
    long countByChannelId(Long channelId);

    /**
     * 统计今日频道消息数量
     */
    @Query("SELECT COUNT(cm) FROM ChannelMessage cm WHERE cm.channelId = :channelId AND " +
           "cm.createdAt >= :startOfDay")
    long countTodayMessages(@Param("channelId") Long channelId, @Param("startOfDay") LocalDateTime startOfDay);

    /**
     * 统计本周频道消息数量
     */
    @Query("SELECT COUNT(cm) FROM ChannelMessage cm WHERE cm.channelId = :channelId AND " +
           "cm.createdAt >= :startOfWeek")
    long countWeekMessages(@Param("channelId") Long channelId, @Param("startOfWeek") LocalDateTime startOfWeek);

    /**
     * 统计本月频道消息数量
     */
    @Query("SELECT COUNT(cm) FROM ChannelMessage cm WHERE cm.channelId = :channelId AND " +
           "cm.createdAt >= :startOfMonth")
    long countMonthMessages(@Param("channelId") Long channelId, @Param("startOfMonth") LocalDateTime startOfMonth);

    /**
     * 删除频道所有消息
     */
    void deleteByChannelId(Long channelId);

    /**
     * 根据消息类型查找
     */
    Page<ChannelMessage> findByChannelIdAndMessageTypeOrderByCreatedAtDesc(
            Long channelId, ChannelMessage.MessageType messageType, Pageable pageable);

    /**
     * 查找置顶消息
     */
    List<ChannelMessage> findByChannelIdAndIsPinnedTrueOrderByCreatedAtDesc(Long channelId);

    /**
     * 批量删除消息
     */
    void deleteByIdIn(List<Long> messageIds);
}
