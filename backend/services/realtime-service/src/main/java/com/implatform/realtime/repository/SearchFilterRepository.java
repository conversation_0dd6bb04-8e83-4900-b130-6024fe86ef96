package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchFilter;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 搜索过滤器Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchFilterRepository extends R2dbcRepository<SearchFilter, Long> {
    
    /**
     * 根据用户ID查找激活的过滤器
     */
    List<SearchFilter> findByUserIdAndIsActiveTrueOrderByPriorityDescUsageCountDesc(Long userId);
    
    /**
     * 查找全局过滤器
     */
    List<SearchFilter> findByUserIdIsNullAndIsActiveTrueOrderByPriorityDescUsageCountDesc();
    
    /**
     * 查找公开过滤器
     */
    List<SearchFilter> findByIsPublicTrueAndIsActiveTrueOrderByUsageCountDesc();
    
    /**
     * 查找系统过滤器
     */
    List<SearchFilter> findByIsSystemTrueAndIsActiveTrueOrderByPriorityDesc();
    
    /**
     * 根据过滤器名称查找
     */
    Optional<SearchFilter> findByFilterNameAndUserIdAndIsActiveTrue(String filterName, Long userId);
    
    /**
     * 根据过滤器类型查找
     */
    List<SearchFilter> findByFilterTypeAndIsActiveTrueOrderByUsageCountDesc(SearchFilter.FilterType filterType);
    
    /**
     * 根据过滤器范围查找
     */
    List<SearchFilter> findByFilterScopeAndIsActiveTrueOrderByUsageCountDesc(SearchFilter.FilterScope filterScope);
    
    /**
     * 查找用户的默认过滤器
     */
    Optional<SearchFilter> findByUserIdAndIsDefaultTrueAndIsActiveTrue(Long userId);
    
    /**
     * 查找全局默认过滤器
     */
    Optional<SearchFilter> findByUserIdIsNullAndIsDefaultTrueAndIsActiveTrue();
    
    /**
     * 查找热门过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.isActive = true AND f.usageCount >= :threshold ORDER BY f.usageCount DESC")
    List<SearchFilter> findPopularFilters(@Param("threshold") Long threshold);
    
    /**
     * 查找高优先级过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.isActive = true AND f.priority >= :priority ORDER BY f.priority DESC")
    List<SearchFilter> findHighPriorityFilters(@Param("priority") Integer priority);
    
    /**
     * 查找最近使用的过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.userId = :userId AND f.isActive = true AND f.lastUsedAt IS NOT NULL ORDER BY f.lastUsedAt DESC")
    List<SearchFilter> findRecentlyUsedFilters(@Param("userId") Long userId);
    
    /**
     * 查找长时间未使用的过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.isActive = true AND (f.lastUsedAt IS NULL OR f.lastUsedAt < :threshold)")
    List<SearchFilter> findUnusedFilters(@Param("threshold") Instant threshold);
    
    /**
     * 统计各过滤器类型的数量
     */
    @Query("SELECT f.filterType, COUNT(f) FROM SearchFilter f WHERE f.isActive = true GROUP BY f.filterType")
    List<Object[]> countByFilterType();
    
    /**
     * 统计各过滤器范围的数量
     */
    @Query("SELECT f.filterScope, COUNT(f) FROM SearchFilter f WHERE f.isActive = true GROUP BY f.filterScope")
    List<Object[]> countByFilterScope();
    
    /**
     * 统计用户过滤器数量
     */
    @Query("SELECT COUNT(f) FROM SearchFilter f WHERE f.userId = :userId AND f.isActive = true")
    long countUserFilters(@Param("userId") Long userId);
    
    /**
     * 获取过滤器使用统计
     */
    @Query("SELECT f.filterName, f.usageCount, f.lastUsedAt FROM SearchFilter f WHERE f.isActive = true ORDER BY f.usageCount DESC")
    List<Object[]> getFilterUsageStats();
    
    /**
     * 增加过滤器使用次数
     */
    @Modifying
    @Query("UPDATE SearchFilter f SET f.usageCount = f.usageCount + 1, f.lastUsedAt = :usedAt WHERE f.id = :filterId")
    int incrementUsageCount(@Param("filterId") Long filterId, @Param("usedAt") Instant usedAt);
    
    /**
     * 批量激活过滤器
     */
    @Modifying
    @Query("UPDATE SearchFilter f SET f.isActive = true WHERE f.id IN :filterIds")
    int batchActivate(@Param("filterIds") List<Long> filterIds);
    
    /**
     * 批量停用过滤器
     */
    @Modifying
    @Query("UPDATE SearchFilter f SET f.isActive = false WHERE f.id IN :filterIds")
    int batchDeactivate(@Param("filterIds") List<Long> filterIds);
    
    /**
     * 设置默认过滤器
     */
    @Modifying
    @Query("UPDATE SearchFilter f SET f.isDefault = CASE WHEN f.id = :filterId THEN true ELSE false END WHERE f.userId = :userId OR (f.userId IS NULL AND :userId IS NULL)")
    int setDefaultFilter(@Param("filterId") Long filterId, @Param("userId") Long userId);
    
    /**
     * 查找相似过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterName LIKE %:name% AND f.id != :excludeId AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findSimilarFilters(@Param("name") String name, @Param("excludeId") Long excludeId);
    
    /**
     * 查找用户可访问的过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE (f.userId = :userId OR f.userId IS NULL OR f.isPublic = true) AND f.isActive = true ORDER BY f.priority DESC, f.usageCount DESC")
    List<SearchFilter> findAccessibleFilters(@Param("userId") Long userId);
    
    /**
     * 查找基础过滤器
     */
    List<SearchFilter> findByFilterTypeAndIsActiveTrueOrderByPriorityDesc(SearchFilter.FilterType filterType);
    
    /**
     * 查找高级过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :advancedType AND f.isActive = true ORDER BY f.priority DESC")
    List<SearchFilter> findAdvancedFilters(@Param("advancedType") SearchFilter.FilterType advancedType);
    
    /**
     * 查找自定义过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :customType AND f.userId = :userId AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findCustomFilters(
        @Param("customType") SearchFilter.FilterType customType,
        @Param("userId") Long userId);
    
    /**
     * 查找预定义过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :predefinedType AND f.isActive = true ORDER BY f.priority DESC")
    List<SearchFilter> findPredefinedFilters(@Param("predefinedType") SearchFilter.FilterType predefinedType);
    
    /**
     * 查找动态过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :dynamicType AND f.isActive = true ORDER BY f.lastUsedAt DESC")
    List<SearchFilter> findDynamicFilters(@Param("dynamicType") SearchFilter.FilterType dynamicType);
    
    /**
     * 查找条件过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :conditionalType AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findConditionalFilters(@Param("conditionalType") SearchFilter.FilterType conditionalType);
    
    /**
     * 查找范围过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :rangeType AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findRangeFilters(@Param("rangeType") SearchFilter.FilterType rangeType);
    
    /**
     * 查找模糊过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :fuzzyType AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findFuzzyFilters(@Param("fuzzyType") SearchFilter.FilterType fuzzyType);
    
    /**
     * 查找精确过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :exactType AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findExactFilters(@Param("exactType") SearchFilter.FilterType exactType);
    
    /**
     * 查找正则表达式过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.filterType = :regexType AND f.isActive = true ORDER BY f.usageCount DESC")
    List<SearchFilter> findRegexFilters(@Param("regexType") SearchFilter.FilterType regexType);
    
    /**
     * 获取过滤器性能统计
     */
    @Query("SELECT f.filterType, AVG(f.usageCount), MAX(f.usageCount), COUNT(f) FROM SearchFilter f WHERE f.isActive = true GROUP BY f.filterType")
    List<Object[]> getFilterPerformanceStats();
    
    /**
     * 查找低使用率过滤器
     */
    @Query("SELECT f FROM SearchFilter f WHERE f.isActive = true AND f.usageCount < :threshold")
    List<SearchFilter> findLowUsageFilters(@Param("threshold") Long threshold);
    
    /**
     * 删除过期过滤器
     */
    @Modifying
    @Query("DELETE FROM SearchFilter f WHERE f.isActive = false AND f.lastUsedAt < :cutoffTime")
    int deleteExpiredFilters(@Param("cutoffTime") Instant cutoffTime);
    
    /**
     * 检查过滤器名称是否存在
     */
    boolean existsByFilterNameAndUserIdAndIsActiveTrue(String filterName, Long userId);
    
    /**
     * 查找重复过滤器
     */
    @Query("SELECT f.filterName, COUNT(f) as duplicateCount FROM SearchFilter f WHERE f.userId = :userId GROUP BY f.filterName HAVING COUNT(f) > 1")
    List<Object[]> findDuplicateFilters(@Param("userId") Long userId);
    
    /**
     * 获取过滤器复杂度分析
     */
    @Query("SELECT f.filterType, f.filterScope, AVG(f.usageCount) FROM SearchFilter f WHERE f.isActive = true GROUP BY f.filterType, f.filterScope ORDER BY AVG(f.usageCount) DESC")
    List<Object[]> getFilterComplexityAnalysis();
    
    /**
     * 查找最新过滤器
     */
    List<SearchFilter> findTop20ByIsActiveTrueOrderByCreatedAtDesc();
    
    /**
     * 查找最受欢迎的过滤器
     */
    List<SearchFilter> findTop20ByIsActiveTrueOrderByUsageCountDesc();
    
    /**
     * 分页查找用户过滤器
     */
    Page<SearchFilter> findByUserIdAndIsActiveTrueOrderByPriorityDescUsageCountDesc(Long userId, Pageable pageable);
    
    /**
     * 分页查找公开过滤器
     */
    Page<SearchFilter> findByIsPublicTrueAndIsActiveTrueOrderByUsageCountDesc(Pageable pageable);
}
