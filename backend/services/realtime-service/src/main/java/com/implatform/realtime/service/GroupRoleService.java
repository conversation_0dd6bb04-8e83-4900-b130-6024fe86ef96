package com.implatform.realtime.service;

import com.implatform.realtime.entity.GroupRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 群组角色服务接口
 * 提供群组角色管理、层级控制和权限继承功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface GroupRoleService {

    /**
     * 创建群组角色
     * 
     * @param groupId 群组ID
     * @param roleName 角色名称
     * @param displayName 显示名称
     * @param description 角色描述
     * @param hierarchyLevel 层级等级
     * @param parentRoleId 父角色ID（可为空）
     * @param createdBy 创建人ID
     * @return 创建的角色
     * @throws IllegalArgumentException 当参数无效时
     * @throws IllegalStateException 当角色名称已存在或层级冲突时
     */
    GroupRole createRole(Long groupId, String roleName, String displayName, String description,
                        Integer hierarchyLevel, Long parentRoleId, Long createdBy);

    /**
     * 创建基于模板的角色
     * 
     * @param groupId 群组ID
     * @param roleTemplate 角色模板
     * @param hierarchyLevel 层级等级
     * @param createdBy 创建人ID
     * @return 创建的角色
     * @throws IllegalArgumentException 当参数无效时
     * @throws IllegalStateException 当模板角色已存在时
     */
    GroupRole createRoleFromTemplate(Long groupId, GroupRole.RoleTemplate roleTemplate,
                                   Integer hierarchyLevel, Long createdBy);

    /**
     * 更新角色信息
     * 
     * @param roleId 角色ID
     * @param displayName 新的显示名称
     * @param description 新的描述
     * @param roleColor 角色颜色
     * @param modifierId 修改人ID
     * @return 更新后的角色
     * @throws IllegalArgumentException 当角色不存在或不可编辑时
     */
    GroupRole updateRole(Long roleId, String displayName, String description, 
                        String roleColor, Long modifierId);

    /**
     * 删除角色
     * 
     * @param roleId 角色ID
     * @param deletedBy 删除人ID
     * @throws IllegalArgumentException 当角色不存在或不可删除时
     * @throws IllegalStateException 当角色仍有成员时
     */
    void deleteRole(Long roleId, Long deletedBy);

    /**
     * 激活角色
     * 
     * @param roleId 角色ID
     * @param activatedBy 激活人ID
     * @return 激活后的角色
     */
    GroupRole activateRole(Long roleId, Long activatedBy);

    /**
     * 停用角色
     * 
     * @param roleId 角色ID
     * @param deactivatedBy 停用人ID
     * @return 停用后的角色
     */
    GroupRole deactivateRole(Long roleId, Long deactivatedBy);

    /**
     * 设置角色父级（建立继承关系）
     * 
     * @param roleId 角色ID
     * @param parentRoleId 父角色ID
     * @param modifierId 修改人ID
     * @return 更新后的角色
     * @throws IllegalArgumentException 当角色不存在时
     * @throws IllegalStateException 当会产生循环继承时
     */
    GroupRole setParentRole(Long roleId, Long parentRoleId, Long modifierId);

    /**
     * 移除角色父级关系
     * 
     * @param roleId 角色ID
     * @param modifierId 修改人ID
     * @return 更新后的角色
     */
    GroupRole removeParentRole(Long roleId, Long modifierId);

    /**
     * 调整角色层级等级
     * 
     * @param roleId 角色ID
     * @param newHierarchyLevel 新的层级等级
     * @param modifierId 修改人ID
     * @return 更新后的角色
     * @throws IllegalArgumentException 当层级等级无效时
     * @throws IllegalStateException 当层级冲突时
     */
    GroupRole adjustHierarchyLevel(Long roleId, Integer newHierarchyLevel, Long modifierId);

    /**
     * 设置角色成员数量限制
     * 
     * @param roleId 角色ID
     * @param memberLimit 成员数量限制
     * @param modifierId 修改人ID
     * @return 更新后的角色
     */
    GroupRole setMemberLimit(Long roleId, Integer memberLimit, Long modifierId);

    /**
     * 增加角色成员数量
     * 
     * @param roleId 角色ID
     * @return 是否成功增加
     */
    boolean incrementMemberCount(Long roleId);

    /**
     * 减少角色成员数量
     * 
     * @param roleId 角色ID
     * @return 是否成功减少
     */
    boolean decrementMemberCount(Long roleId);

    /**
     * 检查角色是否可以管理目标角色
     * 
     * @param managerRoleId 管理者角色ID
     * @param targetRoleId 目标角色ID
     * @return 是否可以管理
     */
    boolean canManageRole(Long managerRoleId, Long targetRoleId);

    /**
     * 检查角色成员数量是否已达上限
     * 
     * @param roleId 角色ID
     * @return 是否已达上限
     */
    boolean isMemberLimitReached(Long roleId);

    /**
     * 获取角色详情
     * 
     * @param roleId 角色ID
     * @return 角色信息
     */
    Optional<GroupRole> getRoleById(Long roleId);

    /**
     * 根据群组ID和角色名称获取角色
     * 
     * @param groupId 群组ID
     * @param roleName 角色名称
     * @return 角色信息
     */
    Optional<GroupRole> getRoleByName(Long groupId, String roleName);

    /**
     * 获取群组的所有角色
     * 
     * @param groupId 群组ID
     * @return 角色列表（按层级排序）
     */
    List<GroupRole> getGroupRoles(Long groupId);

    /**
     * 获取群组的系统角色
     * 
     * @param groupId 群组ID
     * @return 系统角色列表
     */
    List<GroupRole> getSystemRoles(Long groupId);

    /**
     * 获取群组的自定义角色
     * 
     * @param groupId 群组ID
     * @return 自定义角色列表
     */
    List<GroupRole> getCustomRoles(Long groupId);

    /**
     * 获取角色的子角色
     * 
     * @param parentRoleId 父角色ID
     * @return 子角色列表
     */
    List<GroupRole> getChildRoles(Long parentRoleId);

    /**
     * 获取角色的继承链
     * 
     * @param roleId 角色ID
     * @return 继承链（从当前角色到根角色）
     */
    List<GroupRole> getRoleInheritanceChain(Long roleId);

    /**
     * 获取可以管理指定角色的角色列表
     * 
     * @param groupId 群组ID
     * @param targetRoleId 目标角色ID
     * @return 可管理的角色列表
     */
    List<GroupRole> getRolesCanManage(Long groupId, Long targetRoleId);

    /**
     * 获取群主角色
     * 
     * @param groupId 群组ID
     * @return 群主角色
     */
    Optional<GroupRole> getOwnerRole(Long groupId);

    /**
     * 获取管理员角色列表
     * 
     * @param groupId 群组ID
     * @return 管理员角色列表
     */
    List<GroupRole> getAdminRoles(Long groupId);

    /**
     * 获取版主角色列表
     * 
     * @param groupId 群组ID
     * @return 版主角色列表
     */
    List<GroupRole> getModeratorRoles(Long groupId);

    /**
     * 获取普通成员角色
     * 
     * @param groupId 群组ID
     * @return 普通成员角色
     */
    Optional<GroupRole> getMemberRole(Long groupId);

    /**
     * 获取角色统计信息
     * 
     * @param groupId 群组ID
     * @return 角色统计信息
     */
    RoleStatistics getRoleStatistics(Long groupId);

    /**
     * 获取成员数量最多的角色
     * 
     * @param groupId 群组ID
     * @param pageable 分页参数
     * @return 角色分页结果
     */
    Page<GroupRole> getTopRolesByMemberCount(Long groupId, Pageable pageable);

    /**
     * 验证角色层级结构的完整性
     * 
     * @param groupId 群组ID
     * @return 验证结果
     */
    RoleHierarchyValidationResult validateRoleHierarchy(Long groupId);

    /**
     * 检测并修复角色层级问题
     * 
     * @param groupId 群组ID
     * @param fixedBy 修复人ID
     * @return 修复结果
     */
    RoleHierarchyFixResult fixRoleHierarchyIssues(Long groupId, Long fixedBy);

    /**
     * 批量更新角色状态
     * 
     * @param roleIds 角色ID列表
     * @param isActive 激活状态
     * @param modifierId 修改人ID
     * @return 更新的角色数量
     */
    int batchUpdateRoleStatus(List<Long> roleIds, Boolean isActive, Long modifierId);

    /**
     * 复制角色到其他群组
     * 
     * @param sourceRoleId 源角色ID
     * @param targetGroupId 目标群组ID
     * @param copiedBy 复制人ID
     * @return 复制的角色
     */
    GroupRole copyRoleToGroup(Long sourceRoleId, Long targetGroupId, Long copiedBy);

    /**
     * 角色统计信息
     */
    class RoleStatistics {
        private final int totalRoles;
        private final int systemRoles;
        private final int customRoles;
        private final int activeRoles;
        private final int rolesWithMembers;

        public RoleStatistics(int totalRoles, int systemRoles, int customRoles, 
                            int activeRoles, int rolesWithMembers) {
            this.totalRoles = totalRoles;
            this.systemRoles = systemRoles;
            this.customRoles = customRoles;
            this.activeRoles = activeRoles;
            this.rolesWithMembers = rolesWithMembers;
        }

        // Getters
        public int getTotalRoles() { return totalRoles; }
        public int getSystemRoles() { return systemRoles; }
        public int getCustomRoles() { return customRoles; }
        public int getActiveRoles() { return activeRoles; }
        public int getRolesWithMembers() { return rolesWithMembers; }
    }

    /**
     * 角色层级验证结果
     */
    class RoleHierarchyValidationResult {
        private final boolean isValid;
        private final List<String> issues;
        private final List<Long> orphanRoles;
        private final List<Long> circularRoles;

        public RoleHierarchyValidationResult(boolean isValid, List<String> issues, 
                                           List<Long> orphanRoles, List<Long> circularRoles) {
            this.isValid = isValid;
            this.issues = issues;
            this.orphanRoles = orphanRoles;
            this.circularRoles = circularRoles;
        }

        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getIssues() { return issues; }
        public List<Long> getOrphanRoles() { return orphanRoles; }
        public List<Long> getCircularRoles() { return circularRoles; }
    }

    /**
     * 角色层级修复结果
     */
    class RoleHierarchyFixResult {
        private final int fixedIssues;
        private final List<String> fixedProblems;
        private final List<String> remainingIssues;

        public RoleHierarchyFixResult(int fixedIssues, List<String> fixedProblems, 
                                    List<String> remainingIssues) {
            this.fixedIssues = fixedIssues;
            this.fixedProblems = fixedProblems;
            this.remainingIssues = remainingIssues;
        }

        // Getters
        public int getFixedIssues() { return fixedIssues; }
        public List<String> getFixedProblems() { return fixedProblems; }
        public List<String> getRemainingIssues() { return remainingIssues; }
    }
}
