package com.implatform.realtime.service.impl;

import com.implatform.realtime.entity.GroupMember;
import com.implatform.realtime.dto.GroupMemberDTO;
import com.implatform.realtime.repository.GroupMemberRepository;
import com.implatform.service.GroupMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 群组成员服务实现类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupMemberServiceImpl implements GroupMemberService {

    private final GroupMemberRepository groupMemberRepository;

    @Override
    @Transactional
    public GroupMember addMember(Long groupId, Long userId, GroupMember.MemberRole role, 
                                GroupMember.JoinMethod joinMethod, String inviterId) {
        log.debug("添加群组成员: groupId={}, userId={}, role={}", groupId, userId, role);
        
        // 检查是否已经是成员
        Optional<GroupMember> existing = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (existing.isPresent() && existing.get().getStatus() == GroupMember.MemberStatus.ACTIVE) {
            log.warn("用户已经是群组成员: groupId={}, userId={}", groupId, userId);
            return existing.get();
        }
        
        GroupMember member = GroupMember.builder()
                .groupId(groupId)
                .userId(userId)
                .memberRole(role)
                .status(GroupMember.MemberStatus.ACTIVE)
                .joinMethod(joinMethod)
                .invitedByUserId(Long.valueOf(inviterId))
                .joinedAt(Instant.now())
                .build();
        
        return groupMemberRepository.save(member);
    }

    @Override
    @Transactional
    public void removeMember(Long groupId, Long userId, String operatorId) {
        log.debug("移除群组成员: groupId={}, userId={}, operatorId={}", groupId, userId, operatorId);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setStatus(GroupMember.MemberStatus.LEFT);
            groupMember.setLeftAt(Instant.now());
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public GroupMember updateMemberRole(Long groupId, Long userId, GroupMember.MemberRole newRole, String operatorId) {
        log.debug("更新成员角色: groupId={}, userId={}, newRole={}, operatorId={}", groupId, userId, newRole, operatorId);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setMemberRole(newRole);
            groupMember.setUpdatedAt(Instant.now());
            return groupMemberRepository.save(groupMember);
        }
        
        throw new IllegalArgumentException("群组成员不存在");
    }

    @Override
    public Optional<GroupMember> getMember(Long groupId, Long userId) {
        return groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
    }

    @Override
    public Optional<GroupMember> getGroupMemberById(Long id) {
        return groupMemberRepository.findById(id);
    }

    @Override
    public boolean isMember(Long groupId, Long userId) {
        return groupMemberRepository.existsByGroupIdAndUserIdAndStatus(groupId, userId, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    public boolean isOwner(Long groupId, Long userId) {
        return groupMemberRepository.existsByGroupIdAndUserIdAndMemberRoleAndStatus(
                groupId, userId, GroupMember.MemberRole.OWNER, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    public boolean isAdmin(Long groupId, Long userId) {
        return groupMemberRepository.existsByGroupIdAndUserIdAndMemberRoleAndStatus(
                groupId, userId, GroupMember.MemberRole.ADMIN, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    public boolean hasAdminPermission(Long groupId, Long userId) {
        return isOwner(groupId, userId) || isAdmin(groupId, userId);
    }

    @Override
    public Page<GroupMember> getGroupMembers(Long groupId, GroupMember.MemberStatus status, Pageable pageable) {
        return groupMemberRepository.findByGroupIdAndStatus(groupId, status, pageable);
    }

    @Override
    public long getGroupMemberCount(Long groupId) {
        return groupMemberRepository.countByGroupIdAndStatus(groupId, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    public long getActiveMemberCount(Long groupId) {
        return getGroupMemberCount(groupId);
    }

    @Override
    public Page<GroupMember> getUserGroups(Long userId, GroupMember.MemberStatus status, Pageable pageable) {
        return groupMemberRepository.findByUserIdAndStatus(userId, status, pageable);
    }

    @Override
    public Page<GroupMember> searchMembers(Long groupId, String keyword, Pageable pageable) {
        // 这里需要实现搜索逻辑，可能需要关联用户表
        return groupMemberRepository.findByGroupIdAndStatus(groupId, GroupMember.MemberStatus.ACTIVE, pageable);
    }

    @Override
    public List<GroupMember> getGroupAdmins(Long groupId) {
        return groupMemberRepository.findGroupAdmins(groupId, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    public Optional<GroupMember> getGroupOwner(Long groupId) {
        return groupMemberRepository.findByGroupIdAndMemberRoleAndStatus(
                groupId, GroupMember.MemberRole.OWNER, GroupMember.MemberStatus.ACTIVE);
    }

    @Override
    @Transactional
    public void muteMember(Long groupId, Long userId, Instant mutedUntil, String operatorId, String reason) {
        log.debug("禁言成员: groupId={}, userId={}, mutedUntil={}, operatorId={}", groupId, userId, mutedUntil, operatorId);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setIsMuted(true);
            groupMember.setMutedUntil(mutedUntil);
            groupMember.setUpdatedAt(Instant.now());
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public void unmuteMember(Long groupId, Long userId, String operatorId) {
        log.debug("解除禁言: groupId={}, userId={}, operatorId={}", groupId, userId, operatorId);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setIsMuted(false);
            groupMember.setMutedUntil(null);
            groupMember.setUpdatedAt(Instant.now());
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public void kickMember(Long groupId, Long userId, String operatorId, String reason) {
        log.debug("踢出成员: groupId={}, userId={}, operatorId={}, reason={}", groupId, userId, operatorId, reason);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setStatus(GroupMember.MemberStatus.KICKED);
            groupMember.setLeftAt(Instant.now());
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public void banMember(Long groupId, Long userId, String operatorId, String reason) {
        log.debug("封禁成员: groupId={}, userId={}, operatorId={}, reason={}", groupId, userId, operatorId, reason);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setStatus(GroupMember.MemberStatus.BANNED);
            groupMember.setLeftAt(Instant.now());
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public void unbanMember(Long groupId, Long userId, String operatorId) {
        log.debug("解封成员: groupId={}, userId={}, operatorId={}", groupId, userId, operatorId);
        
        Optional<GroupMember> member = groupMemberRepository.findByGroupIdAndUserId(groupId, userId);
        if (member.isPresent()) {
            GroupMember groupMember = member.get();
            groupMember.setStatus(GroupMember.MemberStatus.ACTIVE);
            groupMember.setLeftAt(null);
            groupMemberRepository.save(groupMember);
        }
    }

    @Override
    @Transactional
    public void transferOwnership(Long groupId, Long newOwnerId, String currentOwnerId) {
        log.debug("转让群主: groupId={}, newOwnerId={}, currentOwnerId={}", groupId, newOwnerId, currentOwnerId);
        
        // 将当前群主改为管理员
        Optional<GroupMember> currentOwner = groupMemberRepository.findByGroupIdAndUserId(groupId, Long.valueOf(currentOwnerId));
        if (currentOwner.isPresent()) {
            GroupMember owner = currentOwner.get();
            owner.setMemberRole(GroupMember.MemberRole.ADMIN);
            groupMemberRepository.save(owner);
        }
        
        // 将新成员设为群主
        Optional<GroupMember> newOwner = groupMemberRepository.findByGroupIdAndUserId(groupId, newOwnerId);
        if (newOwner.isPresent()) {
            GroupMember member = newOwner.get();
            member.setMemberRole(GroupMember.MemberRole.OWNER);
            groupMemberRepository.save(member);
        }
    }

    @Override
    @Transactional
    public List<GroupMember> batchAddMembers(Long groupId, List<Long> userIds, 
                                           GroupMember.JoinMethod joinMethod, String operatorId) {
        log.debug("批量添加成员: groupId={}, userCount={}, operatorId={}", groupId, userIds.size(), operatorId);
        
        return userIds.stream()
                .map(userId -> addMember(groupId, userId, GroupMember.MemberRole.MEMBER, joinMethod, operatorId))
                .toList();
    }

    @Override
    @Transactional
    public void batchRemoveMembers(Long groupId, List<Long> userIds, String operatorId) {
        log.debug("批量移除成员: groupId={}, userCount={}, operatorId={}", groupId, userIds.size(), operatorId);
        
        userIds.forEach(userId -> removeMember(groupId, userId, operatorId));
    }

    @Override
    @Transactional
    public void batchUpdateMemberRoles(Long groupId, List<Long> userIds, 
                                     GroupMember.MemberRole newRole, String operatorId) {
        log.debug("批量更新成员角色: groupId={}, userCount={}, newRole={}, operatorId={}", 
                groupId, userIds.size(), newRole, operatorId);
        
        userIds.forEach(userId -> updateMemberRole(groupId, userId, newRole, operatorId));
    }

    @Override
    public GroupMemberDTO.MemberStatistics getMemberStatistics(Long groupId) {
        // 实现统计逻辑
        return GroupMemberDTO.MemberStatistics.builder()
                .groupId(groupId)
                .totalMembers(getGroupMemberCount(groupId))
                .activeMembers(getActiveMemberCount(groupId))
                .build();
    }

    @Override
    public List<GroupMemberDTO.ActivityStatistics> getMemberActivityStatistics(Long groupId, int days) {
        // 实现活跃度统计逻辑
        return List.of();
    }

    @Override
    public List<GroupMemberDTO.JoinTrend> getMemberJoinTrend(Long groupId, int days) {
        // 实现加入趋势统计逻辑
        return List.of();
    }

    @Override
    @Transactional
    public int cleanupExpiredMutes() {
        log.debug("清理过期禁言状态");
        
        List<GroupMember> expiredMutes = groupMemberRepository.findMembersToUnmute(
                Instant.now(), GroupMember.MemberStatus.ACTIVE);
        
        expiredMutes.forEach(member -> {
            member.setIsMuted(false);
            member.setMutedUntil(null);
        });
        
        groupMemberRepository.saveAll(expiredMutes);
        return expiredMutes.size();
    }

    @Override
    @Transactional
    public int cleanupLeftMembers(int days) {
        log.debug("清理已退出成员记录: days={}", days);
        
        Instant cutoffTime = Instant.now().minusSeconds(days * 24 * 60 * 60L);
        return groupMemberRepository.deleteOldLeftMembers(cutoffTime);
    }

    @Override
    @Transactional
    public void repairMemberData(Long groupId) {
        log.debug("修复成员数据: groupId={}", groupId);
        
        // 实现数据修复逻辑
        Page<GroupMember> membersPage = groupMemberRepository.findByGroupIdAndStatus(
                groupId, GroupMember.MemberStatus.ACTIVE, Pageable.unpaged());
        List<GroupMember> members = membersPage.getContent();
        
        // 检查并修复数据一致性
        members.forEach(member -> {
            if (member.getJoinedAt() == null) {
                member.setJoinedAt(member.getCreatedAt());
            }
            if (member.getUpdatedAt() == null) {
                member.setUpdatedAt(member.getCreatedAt());
            }
        });
        
        groupMemberRepository.saveAll(members);
    }

    // ==================== 管理员接口实现 ====================

    @Override
    @Transactional
    public void removeGroupMember(Long memberId) {
        log.info("管理员移除群组成员: memberId={}", memberId);

        GroupMember member = groupMemberRepository.findById(memberId)
                .orElseThrow(() -> new RuntimeException("群组成员不存在"));

        // 更新成员状态为已移除
        member.setStatus(GroupMember.MemberStatus.KICKED);
        member.setLeftAt(Instant.now());
        groupMemberRepository.save(member);

        log.info("群组成员移除成功: memberId={}, groupId={}", memberId, member.getGroupId());
    }
}
