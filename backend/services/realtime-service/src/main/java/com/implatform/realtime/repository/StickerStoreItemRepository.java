package com.implatform.realtime.repository;

import com.implatform.realtime.entity.StickerStoreItem;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 表情包商店商品Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface StickerStoreItemRepository extends R2dbcRepository<StickerStoreItem, Long> {

    /**
     * 根据表情包ID查找商店商品
     */
    @Query("SELECT * FROM sticker_store_items WHERE pack_id = :packId")
    Mono<StickerStoreItem> findByPackId(@Param("packId") Long packId);

    /**
     * 检查表情包是否存在且激活
     */
    @Query("SELECT COUNT(*) > 0 FROM sticker_store_items WHERE pack_id = :packId AND is_active = true")
    Mono<Boolean> existsByPackIdAndIsActiveTrue(@Param("packId") Long packId);

    /**
     * 根据表情包ID和激活状态查找
     */
    @Query("SELECT * FROM sticker_store_items WHERE pack_id = :packId AND is_active = true")
    Mono<StickerStoreItem> findByPackIdAndIsActiveTrue(@Param("packId") Long packId);

    /**
     * 根据状态查找商品
     */
    @Query("SELECT * FROM sticker_store_items WHERE status = :status ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<StickerStoreItem> findByStatusOrderBySortOrderAsc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据分类ID查找商品
     */
    @Query("SELECT * FROM sticker_store_items WHERE category_id = :categoryId AND status = :status ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<StickerStoreItem> findByCategoryIdAndStatusOrderBySortOrderAsc(@Param("categoryId") Long categoryId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找免费商品
     */
    Page<StickerStoreItem> findByIsFreeAndStatusOrderBySortOrderAsc(Boolean isFree, StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 查找精选商品
     */
    Page<StickerStoreItem> findByIsFeaturedAndStatusOrderBySortOrderAsc(Boolean isFeatured, StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 查找热门商品
     */
    Page<StickerStoreItem> findByIsHotAndStatusOrderByPurchaseCountDesc(Boolean isHot, StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 查找新品
     */
    Page<StickerStoreItem> findByIsNewAndStatusOrderByCreatedAtDesc(Boolean isNew, StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 根据价格范围查找商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.price BETWEEN :minPrice AND :maxPrice AND s.status = :status ORDER BY s.sortOrder ASC")
    Page<StickerStoreItem> findByPriceRange(@Param("minPrice") BigDecimal minPrice, 
                                           @Param("maxPrice") BigDecimal maxPrice, 
                                           @Param("status") StickerStoreItem.ItemStatus status, 
                                           Pageable pageable);

    /**
     * 搜索商品（按标题和描述）
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE (s.title LIKE %:keyword% OR s.description LIKE %:keyword%) AND s.status = :status ORDER BY s.sortOrder ASC")
    Page<StickerStoreItem> searchByKeyword(@Param("keyword") String keyword, 
                                          @Param("status") StickerStoreItem.ItemStatus status, 
                                          Pageable pageable);

    /**
     * 根据标签查找商品
     */
    @Query(value = "SELECT * FROM sticker_store_items s WHERE :tag = ANY(s.tags) AND s.status = CAST(:status AS text) ORDER BY s.sort_order ASC",
           nativeQuery = true)
    Page<StickerStoreItem> findByTag(@Param("tag") String tag,
                                    @Param("status") String status,
                                    Pageable pageable);

    /**
     * 获取最受欢迎的商品（按购买次数排序）
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.status = :status ORDER BY s.purchaseCount DESC, s.downloadCount DESC")
    Page<StickerStoreItem> findMostPopular(@Param("status") StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 获取评分最高的商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.status = :status AND s.ratingCount > 0 ORDER BY s.rating DESC, s.ratingCount DESC")
    Page<StickerStoreItem> findTopRated(@Param("status") StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 统计各状态商品数量
     */
    @Query("SELECT s.status, COUNT(s) FROM StickerStoreItem s GROUP BY s.status")
    List<Object[]> countByStatus();

    /**
     * 统计各分类商品数量
     */
    @Query("SELECT s.categoryId, COUNT(s) FROM StickerStoreItem s WHERE s.status = :status GROUP BY s.categoryId")
    List<Object[]> countByCategory(@Param("status") StickerStoreItem.ItemStatus status);

    /**
     * 获取总销售额
     */
    @Query("SELECT SUM(s.price * s.purchaseCount) FROM StickerStoreItem s WHERE s.status = :status")
    BigDecimal getTotalRevenue(@Param("status") StickerStoreItem.ItemStatus status);

    /**
     * 获取平均评分
     */
    @Query("SELECT AVG(s.rating) FROM StickerStoreItem s WHERE s.status = :status AND s.ratingCount > 0")
    BigDecimal getAverageRating(@Param("status") StickerStoreItem.ItemStatus status);

    /**
     * 查找限时商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isLimited = true AND s.limitedEndTime > CURRENT_TIMESTAMP AND s.status = :status ORDER BY s.limitedEndTime ASC")
    Page<StickerStoreItem> findLimitedTimeItems(@Param("status") StickerStoreItem.ItemStatus status, Pageable pageable);

    /**
     * 查找过期的限时商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isLimited = true AND s.limitedEndTime < CURRENT_TIMESTAMP")
    List<StickerStoreItem> findExpiredLimitedItems();

    /**
     * 更新商品排序
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.sortOrder = :sortOrder WHERE s.id = :id")
    void updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.status = :status WHERE s.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") StickerStoreItem.ItemStatus status);

    // ==================== StickerStoreItemService需要的方法 ====================

    /**
     * 根据激活状态查找商品
     */
    Page<StickerStoreItem> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 查找精选商品（带时间范围）
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isFeatured = :featured AND s.isActive = :active AND " +
           "(s.featuredStartTime IS NULL OR s.featuredStartTime <= :now) AND " +
           "(s.featuredEndTime IS NULL OR s.featuredEndTime >= :now)")
    List<StickerStoreItem> findFeaturedItems(@Param("featured") boolean featured, @Param("active") boolean active, @Param("now") Instant now);

    /**
     * 查找促销商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isOnSale = :onSale AND s.isActive = :active AND " +
           "(s.saleStartTime IS NULL OR s.saleStartTime <= :now) AND " +
           "(s.saleEndTime IS NULL OR s.saleEndTime >= :now)")
    List<StickerStoreItem> findSaleItems(@Param("onSale") boolean onSale, @Param("active") boolean active, @Param("now") Instant now);

    /**
     * 查找热门商品（按销量）
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.salesCount >= :threshold AND s.isActive = :active ORDER BY s.salesCount DESC")
    Page<StickerStoreItem> findPopularItems(@Param("threshold") long threshold, @Param("active") boolean active, Pageable pageable);

    /**
     * 查找高评分商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.rating >= :rating AND s.isActive = :active ORDER BY s.rating DESC, s.reviewCount DESC")
    Page<StickerStoreItem> findHighRatedItems(@Param("rating") BigDecimal rating, @Param("active") boolean active, Pageable pageable);

    /**
     * 查找最新商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isActive = :active ORDER BY s.createdAt DESC")
    Page<StickerStoreItem> findLatestItems(@Param("active") boolean active, Pageable pageable);

    /**
     * 根据标签查找商品
     */
    @Query(value = "SELECT * FROM sticker_store_items s WHERE s.is_active = :active AND s.tags && CAST(:tags AS text[])",
           nativeQuery = true)
    List<StickerStoreItem> findByTags(@Param("tags") String[] tags, @Param("active") boolean active);

    /**
     * 搜索商品
     */
    @Query("SELECT s FROM StickerStoreItem s WHERE s.isActive = :active AND " +
           "(LOWER(s.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(s.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<StickerStoreItem> searchItems(@Param("keyword") String keyword, @Param("active") boolean active, Pageable pageable);

    /**
     * 增加查看次数
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.viewCount = s.viewCount + 1 WHERE s.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加销售次数
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.salesCount = s.salesCount + 1 WHERE s.id = :id")
    void incrementSalesCount(@Param("id") Long id);

    // ==================== 新增缺失的方法 ====================

    /**
     * 激活商品
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.isActive = :active WHERE s.id = :id")
    void activateItem(@Param("id") Long id, @Param("active") boolean active);

    /**
     * 停用商品
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.isActive = :active WHERE s.id = :id")
    void deactivateItem(@Param("id") Long id, @Param("active") boolean active);

    /**
     * 统计激活商品数量
     */
    long countByIsActiveTrue();

    /**
     * 统计精选商品数量
     */
    @Query("SELECT COUNT(s) FROM StickerStoreItem s WHERE s.isFeatured = :featured AND s.isActive = :active AND " +
           "(s.featuredStartTime IS NULL OR s.featuredStartTime <= :now) AND " +
           "(s.featuredEndTime IS NULL OR s.featuredEndTime >= :now)")
    long countFeaturedItems(@Param("featured") boolean featured, @Param("active") boolean active, @Param("now") Instant now);

    /**
     * 统计促销商品数量
     */
    @Query("SELECT COUNT(s) FROM StickerStoreItem s WHERE s.isOnSale = :onSale AND s.isActive = :active AND " +
           "(s.saleStartTime IS NULL OR s.saleStartTime <= :now) AND " +
           "(s.saleEndTime IS NULL OR s.saleEndTime >= :now)")
    long countSaleItems(@Param("onSale") boolean onSale, @Param("active") boolean active, @Param("now") Instant now);

    /**
     * 获取销售统计
     */
    @Query("SELECT SUM(s.salesCount), AVG(s.salesCount), MAX(s.salesCount) FROM StickerStoreItem s WHERE s.isActive = true")
    Object[] getSalesStatistics();

    /**
     * 获取评分统计
     */
    @Query("SELECT AVG(s.rating), COUNT(s), SUM(s.ratingCount) FROM StickerStoreItem s WHERE s.isActive = true AND s.rating > 0")
    Object[] getRatingStatistics();

    /**
     * 获取价格统计
     */
    @Query("SELECT AVG(s.price), MIN(s.price), MAX(s.price) FROM StickerStoreItem s WHERE s.isActive = true")
    Object[] getPriceStatistics();

    /**
     * 获取销售趋势
     */
    @Query("SELECT DATE(s.createdAt), COUNT(s), SUM(s.salesCount) FROM StickerStoreItem s WHERE s.createdAt >= :since GROUP BY DATE(s.createdAt) ORDER BY DATE(s.createdAt)")
    List<Object[]> getSalesTrends(@Param("since") Instant since);

    /**
     * 更新过期的精选商品
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.isFeatured = false WHERE s.featuredEndTime < :now")
    int updateExpiredFeaturedItems(@Param("now") Instant now);

    /**
     * 更新过期的促销商品
     */
    @Modifying
    @Query("UPDATE StickerStoreItem s SET s.isOnSale = false WHERE s.saleEndTime < :now")
    int updateExpiredSaleItems(@Param("now") Instant now);
}
