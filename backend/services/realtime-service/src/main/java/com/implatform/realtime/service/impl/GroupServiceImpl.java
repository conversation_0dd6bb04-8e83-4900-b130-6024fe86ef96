package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.GroupErrorCode;
import com.implatform.common.core.enums.UserErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.dto.GroupCreateRequest;
import com.implatform.realtime.dto.GroupDTO;
import com.implatform.realtime.dto.GroupMemberDTO;
import com.implatform.realtime.dto.GroupUpdateRequest;
import com.implatform.realtime.entity.Group;
import com.implatform.realtime.entity.GroupMember;
import com.implatform.realtime.repository.GroupMemberRepository;
import com.implatform.repository.GroupRepository;
import com.implatform.service.GroupMemberService;
import com.implatform.service.GroupService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

/**
 * 群组服务实现类 - IM平台群组管理核心业务逻辑实现（统一处理普通群组和超级群组）
 *
 * <p><strong>实现概述</strong>：
 * 本类实现了GroupService接口定义的所有群组相关业务功能，提供完整的群组生命周期管理。
 * 支持多种群组类型（普通群组、超级群组、频道、广播群组），采用事务管理、权限控制、
 * 缓存优化等技术手段，确保群组操作的安全性、一致性和高性能。
 *
 * <p><strong>核心实现特性</strong>：
 * <ul>
 *   <li><strong>智能类型选择</strong>：根据预期成员数自动选择普通群组（≤500）或超级群组（>500）</li>
 *   <li><strong>统一管理架构</strong>：单一实现类处理所有群组类型，避免代码重复</li>
 *   <li><strong>权限管理体系</strong>：群主、管理员、普通成员的层级权限控制</li>
 *   <li><strong>事务安全保证</strong>：@Transactional确保群组操作的原子性和一致性</li>
 *   <li><strong>成员生命周期</strong>：完整的成员加入、退出、角色变更管理</li>
 *   <li><strong>数据完整性</strong>：群组与成员数据的一致性维护</li>
 * </ul>
 *
 * <p><strong>技术实现架构</strong>：
 * <ul>
 *   <li><strong>数据访问层</strong>：GroupRepository、GroupMemberRepository进行数据CRUD操作</li>
 *   <li><strong>业务逻辑层</strong>：权限验证、状态管理、数据一致性保证</li>
 *   <li><strong>事务管理层</strong>：Spring事务管理确保操作原子性</li>
 *   <li><strong>异常处理层</strong>：统一的业务异常处理和错误码管理</li>
 *   <li><strong>日志审计层</strong>：完整的操作日志记录和性能监控</li>
 * </ul>
 *
 * <p><strong>群组类型智能管理</strong>：
 * <ul>
 *   <li><strong>普通群组</strong>：预期成员≤500，基础功能，适合小团队</li>
 *   <li><strong>超级群组</strong>：预期成员>500，高级功能，适合大型社区</li>
 *   <li><strong>自动选择</strong>：创建时根据expectedMembers参数智能选择类型</li>
 *   <li><strong>功能差异</strong>：超级群组支持用户名、分类、慢速模式等高级功能</li>
 * </ul>
 *
 * <p><strong>权限管理实现</strong>：
 * <ul>
 *   <li><strong>群主权限</strong>：最高权限，可解散群组、设置管理员、修改所有设置</li>
 *   <li><strong>管理员权限</strong>：管理成员、修改群信息、踢出成员</li>
 *   <li><strong>普通成员</strong>：基础聊天权限、查看群信息</li>
 *   <li><strong>权限验证</strong>：每个操作前进行严格的权限检查</li>
 * </ul>
 *
 * <p><strong>业务流程实现</strong>：
 * <ul>
 *   <li><strong>群组创建</strong>：参数验证→类型选择→群组创建→创建者设为群主→成员计数更新</li>
 *   <li><strong>成员管理</strong>：权限验证→成员操作→状态更新→计数维护→日志记录</li>
 *   <li><strong>信息管理</strong>：权限检查→数据验证→信息更新→类型特定处理</li>
 *   <li><strong>群组解散</strong>：权限验证→软删除→成员状态批量更新→资源清理</li>
 * </ul>
 *
 * <p><strong>数据一致性保证</strong>：
 * <ul>
 *   <li><strong>成员计数</strong>：群组成员数与实际成员记录保持一致</li>
 *   <li><strong>管理员计数</strong>：管理员数量与角色分配保持同步</li>
 *   <li><strong>状态同步</strong>：群组状态与成员状态的级联更新</li>
 *   <li><strong>事务边界</strong>：合理的事务范围确保操作原子性</li>
 * </ul>
 *
 * <p><strong>性能优化策略</strong>：
 * <ul>
 *   <li><strong>批量操作</strong>：成员状态批量更新，减少数据库访问</li>
 *   <li><strong>索引优化</strong>：基于查询模式设计的数据库索引</li>
 *   <li><strong>分页查询</strong>：大群组成员列表分页加载</li>
 *   <li><strong>状态缓存</strong>：成员权限和群组信息缓存</li>
 * </ul>
 *
 * <p><strong>监控和审计</strong>：
 * <ul>
 *   <li><strong>操作日志</strong>：群组创建、成员变更、权限修改等关键操作记录</li>
 *   <li><strong>性能监控</strong>：方法执行时间、数据库查询性能追踪</li>
 *   <li><strong>业务指标</strong>：群组数量、成员分布、活跃度统计</li>
 *   <li><strong>异常监控</strong>：权限违规、操作失败、系统异常告警</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupServiceImpl implements GroupService {

    private final GroupRepository groupRepository;
    private final GroupMemberRepository groupMemberRepository;
    private final GroupMemberService groupMemberService;

    /**
     * 创建群组的核心业务逻辑实现
     *
     * <p><strong>实现流程</strong>：
     * <ol>
     *   <li><strong>参数验证</strong>：验证请求对象、用户ID、群组名称的有效性</li>
     *   <li><strong>群组实体创建</strong>：根据请求参数创建Group实体对象</li>
     *   <li><strong>智能类型选择</strong>：根据预期成员数自动选择普通群组或超级群组</li>
     *   <li><strong>特性配置</strong>：根据群组类型设置相应的功能特性</li>
     *   <li><strong>数据持久化</strong>：保存群组信息到数据库</li>
     *   <li><strong>创建者设置</strong>：将创建者添加为群主成员</li>
     *   <li><strong>计数更新</strong>：更新群组成员数量</li>
     * </ol>
     *
     * <p><strong>智能类型选择逻辑</strong>：
     * <ul>
     *   <li><strong>普通群组</strong>：expectedMembers ≤ 500 或未指定时选择</li>
     *   <li><strong>超级群组</strong>：expectedMembers > 500 时自动选择</li>
     *   <li><strong>功能差异</strong>：超级群组支持用户名、分类等高级功能</li>
     *   <li><strong>成员限制</strong>：普通群组最多500人，超级群组最多200,000人</li>
     * </ul>
     *
     * <p><strong>事务管理</strong>：
     * 使用@Transactional确保群组创建的原子性。如果任何步骤失败，
     * 整个创建过程将回滚，避免产生不完整的群组数据。
     *
     * <p><strong>数据验证</strong>：
     * <ul>
     *   <li>请求对象非空验证</li>
     *   <li>用户ID有效性验证</li>
     *   <li>群组名称非空且非空白验证</li>
     *   <li>可选参数的合理性检查</li>
     * </ul>
     *
     * <p><strong>业务规则</strong>：
     * <ul>
     *   <li>新群组默认状态为ACTIVE（激活）</li>
     *   <li>创建者自动成为群主（OWNER角色）</li>
     *   <li>群组成员数初始化为1（创建者）</li>
     *   <li>根据isPublic参数设置群组可见性</li>
     * </ul>
     *
     * <p><strong>超级群组特殊处理</strong>：
     * <ul>
     *   <li>支持设置群组用户名（@username）</li>
     *   <li>支持设置群组分类（category）</li>
     *   <li>支持更多的管理功能和权限设置</li>
     *   <li>支持更大的成员容量</li>
     * </ul>
     *
     * <p><strong>错误处理</strong>：
     * 使用统一的BusinessException处理各种创建异常：
     * <ul>
     *   <li>invalidGroupName：群组名称无效</li>
     *   <li>invalidUserId：用户ID无效</li>
     *   <li>其他业务规则违反的异常</li>
     * </ul>
     *
     * @param request 群组创建请求，包含群组名称、描述、类型等信息
     * @param userId 创建者用户ID，将成为群主
     * @return GroupDTO 创建成功的群组信息
     * @throws BusinessException 当请求参数无效或业务规则违反时抛出
     *
     * @implNote
     * 该方法实现了智能的群组类型选择，根据预期成员数自动选择最适合的群组类型。
     * 创建过程中会自动设置创建者为群主，并初始化相关的计数器。
     */
    @Override
    @Transactional
    public GroupDTO createGroup(GroupCreateRequest request, Long userId) {
        // 参数验证
        if (request == null) {
            throw BusinessException.of(GroupErrorCode.INVALID_GROUP_NAME);
        }
        if (userId == null) {
            throw BusinessException.of(UserErrorCode.INVALID_USER_ID);
        }
        if (request.getGroupName() == null || request.getGroupName().trim().isEmpty()) {
            throw BusinessException.of(GroupErrorCode.INVALID_GROUP_NAME);
        }

        log.info("Creating group: {} by user: {}", request.getGroupName(), userId);

        // 创建群组实体
        Group group = new Group();
        group.setGroupName(request.getGroupName());
        group.setDescription(request.getDescription());
        group.setAvatarUrl(request.getAvatarUrl());
        group.setCreatorUserId(userId);
        group.setGroupStatus(Group.GroupStatus.ACTIVE);
        
        // 根据预期成员数自动选择群组类型
        if (request.getExpectedMembers() != null && request.getExpectedMembers() > 500) {
            group.setAsSuperGroup();
            log.info("Creating as super group for {} expected members", request.getExpectedMembers());
        } else {
            group.setAsNormalGroup();
            log.info("Creating as normal group for {} expected members", 
                    request.getExpectedMembers() != null ? request.getExpectedMembers() : "default");
        }
        
        // 设置公开性
        if (request.getIsPublic() != null) {
            group.setIsPublic(request.getIsPublic());
        }
        
        // 超级群组特有设置
        if (group.isSuperGroup()) {
            if (request.getUsername() != null) {
                group.setUsername(request.getUsername());
            }
            if (request.getCategory() != null) {
                group.setCategory(request.getCategory());
            }
        }

        // 保存群组
        group = groupRepository.save(group);

        // 添加创建者为群主
        GroupMember ownerMember = new GroupMember();
        ownerMember.setGroupId(group.getId());
        ownerMember.setUserId(userId);
        ownerMember.setMemberRole(GroupMember.MemberRole.OWNER);
        ownerMember.setStatus(GroupMember.MemberStatus.ACTIVE);
        ownerMember.setJoinMethod(GroupMember.JoinMethod.SYSTEM_ADDED);
        groupMemberRepository.save(ownerMember);

        // 更新群组成员数
        group.incrementMemberCount();
        groupRepository.save(group);

        log.info("Group created successfully: {} (ID: {})", group.getGroupName(), group.getId());
        return convertToDTO(group);
    }

    @Override
    @Cacheable(value = "group-info", key = "#groupId + ':' + #userId", unless = "#result == null")
    public GroupDTO getGroupInfo(Long groupId, Long userId) {
        log.debug("Getting group info: {} for user: {}", groupId, userId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 检查用户是否有权限查看群组信息
        if (!group.isPublicGroup() && !isGroupMember(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.INSUFFICIENT_PERMISSION);
        }

        return convertToDTO(group);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"group-info", "user-groups"}, allEntries = true)
    public GroupDTO updateGroup(Long groupId, GroupUpdateRequest request, Long userId) {
        log.info("Updating group: {} by user: {}", groupId, userId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 检查权限（只有群主和管理员可以修改）
        if (!hasAdminPermission(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.ADMIN_PERMISSION_REQUIRED);
        }

        // 更新基础信息
        if (request.getGroupName() != null) {
            group.setGroupName(request.getGroupName());
        }
        if (request.getDescription() != null) {
            group.setDescription(request.getDescription());
        }
        if (request.getAvatarUrl() != null) {
            group.setAvatarUrl(request.getAvatarUrl());
        }

        // 超级群组特有更新
        if (group.isSuperGroup()) {
            if (request.getUsername() != null) {
                group.setUsername(request.getUsername());
            }
            if (request.getCategory() != null) {
                group.setCategory(request.getCategory());
            }
            if (request.getSlowModeInterval() != null) {
                group.setSlowModeInterval(request.getSlowModeInterval());
            }
        }

        // 普通群组特有更新
        if (group.isNormalGroup()) {
            if (request.getMuteAll() != null) {
                group.setMuteAll(request.getMuteAll());
            }
            if (request.getAllowMemberEditProfile() != null) {
                group.setAllowMemberEditProfile(request.getAllowMemberEditProfile());
            }
        }

        group = groupRepository.save(group);
        log.info("Group updated successfully: {}", group.getId());
        return convertToDTO(group);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"group-info", "user-groups", "group-members"}, allEntries = true)
    public void deleteGroup(Long groupId, Long userId) {
        log.info("Deleting group: {} by user: {}", groupId, userId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 只有群主可以解散群组
        if (!hasAdminPermission(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.OWNER_PERMISSION_REQUIRED);
        }

        // 软删除群组
        group.setGroupStatus(Group.GroupStatus.DELETED);
        groupRepository.save(group);

        // 更新所有成员状态
        Instant now = Instant.now();
        groupMemberRepository.updateMemberStatusByGroupId(groupId,
                GroupMember.MemberStatus.ACTIVE,
                GroupMember.MemberStatus.LEFT,
                now,
                now);

        log.info("Group deleted successfully: {}", groupId);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"group-info", "user-groups", "group-members"}, key = "#groupId")
    public void joinGroup(Long groupId, Long userId) {
        log.info("User {} joining group: {}", userId, groupId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 检查群组是否可以加入
        if (!group.canAddMember()) {
            if (group.isFull()) {
                throw BusinessException.of(GroupErrorCode.MEMBER_LIMIT_EXCEEDED);
            } else {
                throw BusinessException.of(GroupErrorCode.GROUP_FROZEN);
            }
        }

        // 检查是否已经是成员
        if (isGroupMember(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.MEMBER_ALREADY_EXISTS);
        }

        // 创建成员记录
        GroupMember member = new GroupMember();
        member.setGroupId(groupId);
        member.setUserId(userId);
        member.setMemberRole(GroupMember.MemberRole.MEMBER);
        member.setStatus(GroupMember.MemberStatus.ACTIVE);
        member.setJoinMethod(GroupMember.JoinMethod.SEARCH); // 默认为搜索加入
        groupMemberRepository.save(member);

        // 更新群组成员数
        group.incrementMemberCount();
        groupRepository.save(group);

        log.info("User {} joined group {} successfully", userId, groupId);
    }

    @Override
    @Transactional
    public void leaveGroup(Long groupId, Long userId) {
        log.info("User {} leaving group: {}", userId, groupId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 群主不能直接退出，需要先转让群主
        if (group.isOwner(userId)) {
            throw BusinessException.of(GroupErrorCode.CANNOT_KICK_OWNER);
        }

        // 查找成员记录
        GroupMember member = groupMemberRepository.findByGroupIdAndUserIdAndStatus(
                groupId, userId, GroupMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.MEMBER_NOT_FOUND));

        // 更新成员状态
        member.setStatus(GroupMember.MemberStatus.LEFT);
        member.setLeftAt(Instant.now());
        groupMemberRepository.save(member);

        // 更新群组成员数
        group.decrementMemberCount();
        groupRepository.save(group);

        log.info("User {} left group {} successfully", userId, groupId);
    }

    @Override
    public Page<GroupMemberDTO> getGroupMembers(Long groupId, Long userId, Pageable pageable) {
        log.debug("Getting group members for group: {} by user: {}", groupId, userId);

        // 检查权限
        if (!isGroupMember(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.INSUFFICIENT_PERMISSION);
        }

        Page<GroupMember> members = groupMemberRepository.findByGroupIdAndStatus(
                groupId, GroupMember.MemberStatus.ACTIVE, pageable);

        return members.map(this::convertToMemberDTO);
    }

    @Override
    @Transactional
    public void addGroupMember(Long groupId, Long targetUserId, Long userId) {
        log.info("User {} adding user {} to group: {}", userId, targetUserId, groupId);

        // 检查权限（管理员以上才能添加成员）
        if (!hasAdminPermission(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.ADMIN_PERMISSION_REQUIRED);
        }

        // 检查目标用户是否已经是成员
        if (isGroupMember(groupId, targetUserId)) {
            throw BusinessException.of(GroupErrorCode.MEMBER_ALREADY_EXISTS);
        }

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        if (!group.canAddMember()) {
            if (group.isFull()) {
                throw BusinessException.of(GroupErrorCode.MEMBER_LIMIT_EXCEEDED);
            } else {
                throw BusinessException.of(GroupErrorCode.GROUP_FROZEN);
            }
        }

        // 创建成员记录
        GroupMember member = new GroupMember();
        member.setGroupId(groupId);
        member.setUserId(targetUserId);
        member.setMemberRole(GroupMember.MemberRole.MEMBER);
        member.setStatus(GroupMember.MemberStatus.ACTIVE);
        member.setJoinMethod(GroupMember.JoinMethod.ADMIN_ADDED);
        member.setInvitedByUserId(userId);
        groupMemberRepository.save(member);

        // 更新群组成员数
        group.incrementMemberCount();
        groupRepository.save(group);

        log.info("User {} added to group {} successfully", targetUserId, groupId);
    }

    // 其他方法的实现...
    
    // 辅助方法
    private boolean isGroupMember(Long groupId, Long userId) {
        return groupMemberRepository.existsByGroupIdAndUserIdAndStatus(
                groupId, userId, GroupMember.MemberStatus.ACTIVE);
    }

    private boolean hasAdminPermission(Long groupId, Long userId) {
        return groupMemberRepository.existsByGroupIdAndUserIdAndMemberRoleInAndStatus(
                groupId, userId, 
                java.util.Arrays.asList(GroupMember.MemberRole.OWNER, GroupMember.MemberRole.ADMIN),
                GroupMember.MemberStatus.ACTIVE);
    }

    private GroupDTO convertToDTO(Group group) {
        GroupDTO dto = new GroupDTO();
        dto.setId(group.getId());
        dto.setGroupName(group.getGroupName());
        dto.setDescription(group.getDescription());
        dto.setAvatarUrl(group.getAvatarUrl());
        dto.setGroupType(group.getGroupType().name());
        dto.setGroupStatus(group.getGroupStatus().name());
        dto.setMemberCount(group.getMemberCount());
        dto.setMaxMemberCount(group.getMaxMemberCount());
        dto.setIsPublic(group.getIsPublic());
        dto.setCreatedAt(group.getCreatedAt());
        
        // 超级群组特有字段
        if (group.isSuperGroup()) {
            dto.setUsername(group.getUsername());
            dto.setIsVerified(group.getIsVerified());
            dto.setCategory(group.getCategory());
            dto.setTotalMessageCount(group.getTotalMessageCount());
        }
        
        return dto;
    }

    private GroupMemberDTO convertToMemberDTO(GroupMember member) {
        GroupMemberDTO dto = new GroupMemberDTO();
        dto.setId(member.getId());
        dto.setUserId(member.getUserId());
        dto.setMemberRole(member.getMemberRole().name());
        dto.setNickname(member.getNickname());
        dto.setJoinedAt(member.getJoinedAt());
        
        // 超级群组特有字段
        if (member.getCustomTitle() != null) {
            dto.setCustomTitle(member.getCustomTitle());
        }
        
        return dto;
    }

    @Override
    @Transactional
    public void removeGroupMember(Long groupId, Long memberId, Long userId) {
        log.info("User {} removing member {} from group: {}", userId, memberId, groupId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 检查权限（管理员以上才能移除成员）
        if (!hasAdminPermission(groupId, userId)) {
            throw BusinessException.of(GroupErrorCode.ADMIN_PERMISSION_REQUIRED);
        }

        // 查找要移除的成员
        GroupMember member = groupMemberRepository.findByGroupIdAndUserIdAndStatus(
                groupId, memberId, GroupMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.MEMBER_NOT_FOUND));

        // 群主不能被移除
        if (member.getMemberRole() == GroupMember.MemberRole.OWNER) {
            throw BusinessException.of(GroupErrorCode.CANNOT_KICK_OWNER);
        }

        // 普通管理员不能移除其他管理员（只有群主可以）
        GroupMember operator = groupMemberRepository.findByGroupIdAndUserIdAndStatus(
                groupId, userId, GroupMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.MEMBER_NOT_FOUND));

        if (member.getMemberRole() == GroupMember.MemberRole.ADMIN &&
            operator.getMemberRole() != GroupMember.MemberRole.OWNER) {
            throw BusinessException.of(GroupErrorCode.OWNER_PERMISSION_REQUIRED);
        }

        // 更新成员状态
        member.setStatus(GroupMember.MemberStatus.KICKED);
        member.setLeftAt(Instant.now());
        groupMemberRepository.save(member);

        // 更新群组成员数和管理员数
        group.decrementMemberCount();
        if (member.getMemberRole() == GroupMember.MemberRole.ADMIN) {
            group.decrementAdminCount();
        }
        groupRepository.save(group);

        log.info("Member {} removed from group {} successfully", memberId, groupId);
    }

    @Override
    @Transactional
    public void updateMemberRole(Long groupId, Long memberId, String role, Long userId) {
        log.info("User {} updating member {} role to {} in group: {}", userId, memberId, role, groupId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 只有群主可以修改成员角色
        if (!group.isOwner(userId)) {
            throw BusinessException.of(GroupErrorCode.OWNER_PERMISSION_REQUIRED);
        }

        // 验证角色值
        GroupMember.MemberRole newRole;
        try {
            newRole = GroupMember.MemberRole.valueOf(role.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw BusinessException.of(GroupErrorCode.ROLE_NOT_FOUND);
        }

        // 不能设置为群主
        if (newRole == GroupMember.MemberRole.OWNER) {
            throw BusinessException.of(GroupErrorCode.PERMISSION_DENIED);
        }

        // 查找要更新的成员
        GroupMember member = groupMemberRepository.findByGroupIdAndUserIdAndStatus(
                groupId, memberId, GroupMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.MEMBER_NOT_FOUND));

        // 不能修改群主角色
        if (member.getMemberRole() == GroupMember.MemberRole.OWNER) {
            throw BusinessException.of(GroupErrorCode.PERMISSION_DENIED);
        }

        GroupMember.MemberRole oldRole = member.getMemberRole();
        member.setMemberRole(newRole);
        groupMemberRepository.save(member);

        // 更新群组管理员数量
        if (oldRole == GroupMember.MemberRole.ADMIN && newRole != GroupMember.MemberRole.ADMIN) {
            group.decrementAdminCount();
        } else if (oldRole != GroupMember.MemberRole.ADMIN && newRole == GroupMember.MemberRole.ADMIN) {
            group.incrementAdminCount();
        }
        groupRepository.save(group);

        log.info("Member {} role updated from {} to {} in group {}", memberId, oldRole, newRole, groupId);
    }

    @Override
    public Page<GroupDTO> getUserGroups(Long userId, Pageable pageable) {
        log.debug("Getting groups for user: {}", userId);

        Page<Group> groups = groupRepository.findGroupsByMember(userId, Group.GroupStatus.ACTIVE, pageable);
        return groups.map(this::convertToDTO);
    }

    @Override
    public Page<GroupDTO> searchGroups(String keyword, Long userId, Pageable pageable) {
        log.debug("User {} searching groups with keyword: {}", userId, keyword);

        if (keyword == null || keyword.trim().isEmpty()) {
            throw BusinessException.of(GroupErrorCode.INVALID_GROUP_NAME);
        }

        Page<Group> groups = groupRepository.findPublicGroupsByKeyword(
                keyword.trim(), Group.GroupStatus.ACTIVE, pageable);
        return groups.map(this::convertToDTO);
    }

    @Override
    public Object getGroupStatistics(Long groupId, Long userId) {
        log.debug("User {} getting statistics for group: {}", userId, groupId);

        Group group = groupRepository.findById(groupId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 检查权限（只有管理员以上可以查看统计）
        if (!hasAdminPermission(groupId, userId)) {
            throw new BusinessException(GroupErrorCode.INSUFFICIENT_PERMISSION);
        }

        // 构建统计信息
        GroupStatisticsDTO statistics = new GroupStatisticsDTO();
        statistics.setGroupId(groupId);
        statistics.setGroupName(group.getGroupName());
        statistics.setGroupType(group.getGroupType().name());
        statistics.setMemberCount(group.getMemberCount());
        statistics.setMaxMemberCount(group.getMaxMemberCount());
        statistics.setAdminCount(group.getAdminCount());
        statistics.setCreatedAt(group.getCreatedAt());
        statistics.setLastActivityAt(group.getLastActivityAt());

        // 超级群组特有统计
        if (group.isSuperGroup() && group.getStatisticsEnabled()) {
            statistics.setTotalMessageCount(group.getTotalMessageCount());
            statistics.setTodayMessageCount(group.getTodayMessageCount());
            statistics.setActiveMemberCount(group.getActiveMemberCount());
            statistics.setIsVerified(group.getIsVerified());
        }

        // 获取成员角色分布
        long ownerCount = groupMemberRepository.countByGroupIdAndMemberRoleAndStatus(
                groupId, GroupMember.MemberRole.OWNER, GroupMember.MemberStatus.ACTIVE);
        long adminCount = groupMemberRepository.countByGroupIdAndMemberRoleAndStatus(
                groupId, GroupMember.MemberRole.ADMIN, GroupMember.MemberStatus.ACTIVE);
        long memberCount = groupMemberRepository.countByGroupIdAndMemberRoleAndStatus(
                groupId, GroupMember.MemberRole.MEMBER, GroupMember.MemberStatus.ACTIVE);

        statistics.setOwnerCount(ownerCount);
        statistics.setAdminMemberCount(adminCount);
        statistics.setRegularMemberCount(memberCount);

        return statistics;
    }

    /**
     * 群组统计信息DTO
     */
    @Getter
    @Setter
    public static class GroupStatisticsDTO {
        private Long groupId;
        private String groupName;
        private String groupType;
        private Integer memberCount;
        private Integer maxMemberCount;
        private Integer adminCount;
        private Instant createdAt;
        private Instant lastActivityAt;

        // 超级群组统计
        private Long totalMessageCount;
        private Integer todayMessageCount;
        private Integer activeMemberCount;
        private Boolean isVerified;

        // 成员角色分布
        private Long ownerCount;
        private Long adminMemberCount;
        private Long regularMemberCount;
    }

    // ==================== 管理员接口实现 ====================

    @Override
    public Page<GroupDTO> getGroups(java.util.Map<String, Object> params, Pageable pageable) {
        log.debug("Getting groups with params: {}", params);

        Page<Group> groups = groupRepository.findAll(pageable);
        return groups.map(this::convertToDTO);
    }

    @Override
    public GroupDTO getGroupById(Long id) {
        log.debug("Getting group by id: {}", id);

        Group group = groupRepository.findById(id)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        return convertToDTO(group);
    }

    @Override
    @Transactional
    public void dissolveGroup(Long id) {
        log.info("Dissolving group: {}", id);

        Group group = groupRepository.findById(id)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        // 软删除群组
        group.setGroupStatus(Group.GroupStatus.DELETED);
        groupRepository.save(group);

        // 更新所有成员状态
        Instant now = Instant.now();
        groupMemberRepository.updateMemberStatusByGroupId(id,
                GroupMember.MemberStatus.ACTIVE,
                GroupMember.MemberStatus.LEFT,
                now,
                now);

        log.info("Group dissolved successfully: {}", id);
    }

    @Override
    @Transactional
    public void freezeGroup(Long id) {
        log.info("Freezing group: {}", id);

        Group group = groupRepository.findById(id)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        group.setGroupStatus(Group.GroupStatus.FROZEN);
        groupRepository.save(group);

        log.info("Group frozen successfully: {}", id);
    }

    @Override
    @Transactional
    public void unfreezeGroup(Long id) {
        log.info("Unfreezing group: {}", id);

        Group group = groupRepository.findById(id)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        group.setGroupStatus(Group.GroupStatus.ACTIVE);
        groupRepository.save(group);

        log.info("Group unfrozen successfully: {}", id);
    }

    @Override
    @Transactional
    public void batchRemoveGroupMembers(Long groupId, java.util.List<Long> memberIds) {
        log.info("Batch removing group members: groupId={}, memberIds={}", groupId, memberIds);

        for (Long memberId : memberIds) {
            removeGroupMember(groupId, memberId, 1L); // 使用系统管理员ID
        }

        log.info("Batch removed {} members from group: {}", memberIds.size(), groupId);
    }

    @Override
    public Page<GroupDTO> getActiveGroups(Pageable pageable) {
        log.debug("Getting active groups");

        Page<Group> groups = groupRepository.findByGroupStatusOrderByLastActivityAtDesc(
                Group.GroupStatus.ACTIVE, pageable);
        return groups.map(this::convertToDTO);
    }

    @Override
    public Page<GroupDTO> getLargeGroups(Integer minMembers, Pageable pageable) {
        log.debug("Getting large groups with minMembers: {}", minMembers);

        Page<Group> groups = groupRepository.findByMemberCountGreaterThanEqualOrderByMemberCountDesc(
                minMembers, pageable);
        return groups.map(this::convertToDTO);
    }

    @Override
    @Transactional
    public void reviewGroup(Long id, String action, String reason) {
        log.info("Reviewing group: id={}, action={}, reason={}", id, action, reason);

        Group group = groupRepository.findById(id)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.GROUP_NOT_FOUND));

        switch (action.toUpperCase()) {
            case "APPROVE":
                group.setGroupStatus(Group.GroupStatus.ACTIVE);
                break;
            case "REJECT":
                group.setGroupStatus(Group.GroupStatus.REJECTED);
                break;
            case "BLOCK":
                group.setGroupStatus(Group.GroupStatus.BLOCKED);
                break;
            default:
                throw new IllegalArgumentException("Invalid action: " + action);
        }

        groupRepository.save(group);
        log.info("Group review completed: id={}, action={}", id, action);
    }

    @Override
    public String exportGroups(java.util.Map<String, Object> params) {
        log.info("Exporting groups with params: {}", params);

        // 实现导出逻辑
        // 这里返回一个模拟的文件URL
        return "https://example.com/exports/groups_" + System.currentTimeMillis() + ".xlsx";
    }

    @Override
    public java.util.List<java.util.Map<String, Object>> getGroupCreationTrends(String startDate, String endDate) {
        log.debug("Getting group creation trends: startDate={}, endDate={}", startDate, endDate);

        // 实现趋势统计逻辑
        java.util.List<java.util.Map<String, Object>> trends = new java.util.ArrayList<>();

        // 模拟数据
        java.util.Map<String, Object> trend = new java.util.HashMap<>();
        trend.put("date", startDate);
        trend.put("count", 10);
        trends.add(trend);

        return trends;
    }

    @Override
    public java.util.Map<String, Object> getGroupStatistics() {
        log.debug("Getting overall group statistics");

        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalGroups", groupRepository.count());
        statistics.put("activeGroups", groupRepository.countByGroupStatus(Group.GroupStatus.ACTIVE));
        statistics.put("totalMembers", groupMemberRepository.countByStatus(GroupMember.MemberStatus.ACTIVE));

        return statistics;
    }
}
