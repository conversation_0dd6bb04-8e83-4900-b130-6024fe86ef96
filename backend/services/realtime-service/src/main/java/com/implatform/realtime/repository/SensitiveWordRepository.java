package com.implatform.realtime.repository;

import java.time.Instant;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.realtime.entity.SensitiveWord;

/**
 * 敏感词Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SensitiveWordRepository extends R2dbcRepository<SensitiveWord, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据词汇查询敏感词
     */
    @Query("SELECT * FROM sensitive_words WHERE word = :word")
    Mono<SensitiveWord> findByWord(@Param("word") String word);

    /**
     * 根据词汇和状态查询敏感词
     */
    @Query("SELECT * FROM sensitive_words WHERE word = :word AND is_active = :isActive")
    Mono<SensitiveWord> findByWordAndIsActive(@Param("word") String word, @Param("isActive") Boolean isActive);

    /**
     * 查询所有激活的敏感词
     */
    @Query("SELECT * FROM sensitive_words WHERE is_active = true")
    Flux<SensitiveWord> findByIsActiveTrue();

    /**
     * 根据类型查询激活的敏感词
     */
    @Query("SELECT * FROM sensitive_words WHERE word_type = :wordType AND is_active = true")
    Flux<SensitiveWord> findByWordTypeAndIsActiveTrue(@Param("wordType") String wordType);

    /**
     * 根据分类查询激活的敏感词
     */
    List<SensitiveWord> findByCategoryAndIsActiveTrue(SensitiveWord.Category category);

    /**
     * 根据严重程度查询激活的敏感词
     */
    List<SensitiveWord> findBySeverityAndIsActiveTrue(SensitiveWord.Severity severity);

    // ==================== 分页查询 ====================

    /**
     * 分页查询所有敏感词
     */
    Page<SensitiveWord> findAll(Pageable pageable);

    /**
     * 根据状态分页查询
     */
    Page<SensitiveWord> findByIsActive(Boolean isActive, Pageable pageable);

    /**
     * 根据类型分页查询
     */
    Page<SensitiveWord> findByWordType(SensitiveWord.WordType wordType, Pageable pageable);

    /**
     * 根据分类分页查询
     */
    Page<SensitiveWord> findByCategory(SensitiveWord.Category category, Pageable pageable);

    /**
     * 根据严重程度分页查询
     */
    Page<SensitiveWord> findBySeverity(SensitiveWord.Severity severity, Pageable pageable);

    // ==================== 模糊查询 ====================

    /**
     * 根据词汇模糊查询
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.word LIKE %:keyword% AND s.isActive = true")
    List<SensitiveWord> findByWordContaining(@Param("keyword") String keyword);

    /**
     * 根据描述模糊查询
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.description LIKE %:keyword% AND s.isActive = true")
    List<SensitiveWord> findByDescriptionContaining(@Param("keyword") String keyword);

    // ==================== 统计查询 ====================

    /**
     * 统计激活的敏感词数量
     */
    Long countByIsActiveTrue();

    /**
     * 根据类型统计数量
     */
    Long countByWordTypeAndIsActiveTrue(SensitiveWord.WordType wordType);

    /**
     * 根据分类统计数量
     */
    Long countByCategoryAndIsActiveTrue(SensitiveWord.Category category);

    /**
     * 统计各类型的敏感词数量
     */
    @Query("SELECT s.wordType, COUNT(s) FROM SensitiveWord s WHERE s.isActive = true GROUP BY s.wordType")
    List<Object[]> countByWordType();

    /**
     * 统计各分类的敏感词数量
     */
    @Query("SELECT s.category, COUNT(s) FROM SensitiveWord s WHERE s.isActive = true GROUP BY s.category")
    List<Object[]> countByCategory();

    // ==================== 批量操作 ====================

    /**
     * 批量更新命中统计
     */
    @Modifying
    @Query("UPDATE SensitiveWord s SET s.hitCount = s.hitCount + 1, s.lastHitAt = :hitTime WHERE s.word = :word")
    int updateHitCount(@Param("word") String word, @Param("hitTime") Instant hitTime);

    /**
     * 批量激活敏感词
     */
    @Modifying
    @Query("UPDATE SensitiveWord s SET s.isActive = true, s.updatedAt = :updateTime WHERE s.id IN :ids")
    int batchActivate(@Param("ids") List<Long> ids, @Param("updateTime") Instant updateTime);

    /**
     * 批量禁用敏感词
     */
    @Modifying
    @Query("UPDATE SensitiveWord s SET s.isActive = false, s.updatedAt = :updateTime WHERE s.id IN :ids")
    int batchDeactivate(@Param("ids") List<Long> ids, @Param("updateTime") Instant updateTime);

    /**
     * 批量删除敏感词
     */
    @Modifying
    @Query("DELETE FROM SensitiveWord s WHERE s.id IN :ids")
    int batchDelete(@Param("ids") List<Long> ids);

    // ==================== 高级查询 ====================

    /**
     * 查询最近添加的敏感词
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.createdAt >= :since ORDER BY s.createdAt DESC")
    List<SensitiveWord> findRecentlyAdded(@Param("since") Instant since, Pageable pageable);

    /**
     * 查询命中次数最多的敏感词
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.isActive = true ORDER BY s.hitCount DESC")
    List<SensitiveWord> findMostHit(Pageable pageable);

    /**
     * 查询从未命中的敏感词
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.hitCount = 0 AND s.isActive = true")
    List<SensitiveWord> findNeverHit();

    /**
     * 查询长期未命中的敏感词
     */
    @Query("SELECT s FROM SensitiveWord s WHERE s.isActive = true AND (s.lastHitAt IS NULL OR s.lastHitAt < :threshold)")
    List<SensitiveWord> findLongTimeNoHit(@Param("threshold") Instant threshold);

    // ==================== 复合条件查询 ====================

    /**
     * 根据多个条件查询敏感词
     */
    @Query("SELECT s FROM SensitiveWord s WHERE " +
           "(:wordType IS NULL OR s.wordType = :wordType) AND " +
           "(:category IS NULL OR s.category = :category) AND " +
           "(:severity IS NULL OR s.severity = :severity) AND " +
           "(:isActive IS NULL OR s.isActive = :isActive) AND " +
           "(:keyword IS NULL OR s.word LIKE %:keyword%)")
    Page<SensitiveWord> findByMultipleConditions(
            @Param("wordType") SensitiveWord.WordType wordType,
            @Param("category") SensitiveWord.Category category,
            @Param("severity") SensitiveWord.Severity severity,
            @Param("isActive") Boolean isActive,
            @Param("keyword") String keyword,
            Pageable pageable);

    // ==================== 性能优化查询 ====================

    /**
     * 获取敏感词库版本（基于最后更新时间）
     */
    @Query("SELECT MAX(s.updatedAt) FROM SensitiveWord s WHERE s.isActive = true")
    Instant getLibraryVersion();

    /**
     * 检查敏感词是否存在
     */
    @Query("SELECT COUNT(s) > 0 FROM SensitiveWord s WHERE s.word = :word AND s.isActive = true")
    boolean existsByWordAndActive(@Param("word") String word);

    /**
     * 根据严重程度和激活状态统计数量
     */
    Long countBySeverityAndIsActiveTrue(SensitiveWord.Severity severity);
}
