package com.implatform.realtime.repository;

import com.implatform.realtime.entity.GroupRole;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 群组角色数据访问层 - R2DBC响应式版本
 * 提供群组角色相关的数据库操作方法
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface GroupRoleRepository extends R2dbcRepository<GroupRole, Long> {

    /**
     * 根据群组ID查询所有角色
     *
     * @param groupId 群组ID
     * @return 角色列表
     */
    @Query("SELECT * FROM group_roles WHERE group_id = :groupId AND is_active = true ORDER BY hierarchy_level ASC")
    Flux<GroupRole> findByGroupIdAndIsActiveTrueOrderByHierarchyLevelAsc(@Param("groupId") Long groupId);

    /**
     * 根据群组ID和角色名称查询角色
     *
     * @param groupId 群组ID
     * @param roleName 角色名称
     * @return 角色信息
     */
    @Query("SELECT * FROM group_roles WHERE group_id = :groupId AND role_name = :roleName AND is_active = true")
    Mono<GroupRole> findByGroupIdAndRoleNameAndIsActiveTrue(@Param("groupId") Long groupId, @Param("roleName") String roleName);

    /**
     * 根据群组ID和层级等级查询角色
     *
     * @param groupId 群组ID
     * @param hierarchyLevel 层级等级
     * @return 角色列表
     */
    @Query("SELECT * FROM group_roles WHERE group_id = :groupId AND hierarchy_level = :hierarchyLevel AND is_active = true ORDER BY sort_order")
    Flux<GroupRole> findByGroupIdAndHierarchyLevelAndIsActiveTrueOrderBySortOrder(
            @Param("groupId") Long groupId, @Param("hierarchyLevel") Integer hierarchyLevel);

    /**
     * 查询群组中层级等级小于等于指定值的角色
     * 
     * @param groupId 群组ID
     * @param maxLevel 最大层级等级
     * @return 角色列表
     */
    List<GroupRole> findByGroupIdAndHierarchyLevelLessThanEqualAndIsActiveTrueOrderByHierarchyLevelAsc(
            Long groupId, Integer maxLevel);

    /**
     * 查询群组中的系统默认角色
     * 
     * @param groupId 群组ID
     * @return 系统角色列表
     */
    List<GroupRole> findByGroupIdAndIsSystemRoleTrueAndIsActiveTrueOrderByHierarchyLevelAsc(Long groupId);

    /**
     * 查询群组中的自定义角色
     * 
     * @param groupId 群组ID
     * @return 自定义角色列表
     */
    List<GroupRole> findByGroupIdAndIsSystemRoleFalseAndIsActiveTrueOrderByHierarchyLevelAsc(Long groupId);

    /**
     * 根据父角色ID查询子角色
     * 
     * @param parentRoleId 父角色ID
     * @return 子角色列表
     */
    List<GroupRole> findByParentRoleIdAndIsActiveTrueOrderByHierarchyLevelAsc(Long parentRoleId);

    /**
     * 查询指定角色模板的角色
     * 
     * @param groupId 群组ID
     * @param roleTemplate 角色模板
     * @return 角色列表
     */
    List<GroupRole> findByGroupIdAndRoleTemplateAndIsActiveTrue(
            Long groupId, GroupRole.RoleTemplate roleTemplate);

    /**
     * 检查角色名称是否已存在
     * 
     * @param groupId 群组ID
     * @param roleName 角色名称
     * @return 是否存在
     */
    boolean existsByGroupIdAndRoleNameAndIsActiveTrue(Long groupId, String roleName);

    /**
     * 检查层级等级是否已被占用
     * 
     * @param groupId 群组ID
     * @param hierarchyLevel 层级等级
     * @return 是否被占用
     */
    boolean existsByGroupIdAndHierarchyLevelAndIsActiveTrue(Long groupId, Integer hierarchyLevel);

    /**
     * 查询群组角色统计信息
     * 
     * @param groupId 群组ID
     * @return 统计信息 [总数, 系统角色数, 自定义角色数, 激活角色数]
     */
    @Query("SELECT COUNT(gr), " +
           "COUNT(CASE WHEN gr.isSystemRole = true THEN 1 END), " +
           "COUNT(CASE WHEN gr.isSystemRole = false THEN 1 END), " +
           "COUNT(CASE WHEN gr.isActive = true THEN 1 END) " +
           "FROM GroupRole gr WHERE gr.groupId = :groupId")
    Object[] getRoleStatistics(@Param("groupId") Long groupId);

    /**
     * 查询群组中成员数量最多的角色
     * 
     * @param groupId 群组ID
     * @param pageable 分页参数
     * @return 角色分页结果
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId AND gr.isActive = true " +
           "ORDER BY gr.currentMemberCount DESC")
    Page<GroupRole> findTopRolesByMemberCount(@Param("groupId") Long groupId, Pageable pageable);

    /**
     * 查询可以管理指定角色的角色列表
     * 
     * @param groupId 群组ID
     * @param targetHierarchyLevel 目标角色层级
     * @return 可管理的角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND gr.hierarchyLevel < :targetHierarchyLevel AND gr.isActive = true " +
           "ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findRolesCanManage(@Param("groupId") Long groupId, 
                                      @Param("targetHierarchyLevel") Integer targetHierarchyLevel);

    /**
     * 查询有成员数量限制的角色
     * 
     * @param groupId 群组ID
     * @return 有限制的角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND gr.memberLimit IS NOT NULL AND gr.isActive = true " +
           "ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findRolesWithMemberLimit(@Param("groupId") Long groupId);

    /**
     * 查询已达成员上限的角色
     * 
     * @param groupId 群组ID
     * @return 已达上限的角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND gr.memberLimit IS NOT NULL AND gr.currentMemberCount >= gr.memberLimit " +
           "AND gr.isActive = true ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findRolesAtMemberLimit(@Param("groupId") Long groupId);

    /**
     * 更新角色成员数量
     * 
     * @param roleId 角色ID
     * @param increment 增量（可为负数）
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupRole gr SET gr.currentMemberCount = gr.currentMemberCount + :increment " +
           "WHERE gr.id = :roleId AND gr.currentMemberCount + :increment >= 0")
    int updateMemberCount(@Param("roleId") Long roleId, @Param("increment") Integer increment);

    /**
     * 批量更新角色激活状态
     * 
     * @param roleIds 角色ID列表
     * @param isActive 激活状态
     * @param modifierId 修改人ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE GroupRole gr SET gr.isActive = :isActive, gr.lastModifiedBy = :modifierId " +
           "WHERE gr.id IN :roleIds")
    int batchUpdateActiveStatus(@Param("roleIds") List<Long> roleIds, 
                               @Param("isActive") Boolean isActive, 
                               @Param("modifierId") Long modifierId);

    /**
     * 查询角色层级结构
     * 
     * @param groupId 群组ID
     * @return 层级结构信息
     */
    @Query("SELECT gr.hierarchyLevel, COUNT(gr), MIN(gr.id), MAX(gr.id) " +
           "FROM GroupRole gr WHERE gr.groupId = :groupId AND gr.isActive = true " +
           "GROUP BY gr.hierarchyLevel ORDER BY gr.hierarchyLevel ASC")
    List<Object[]> getRoleHierarchyStructure(@Param("groupId") Long groupId);

    /**
     * 查询可编辑的角色
     * 
     * @param groupId 群组ID
     * @return 可编辑的角色列表
     */
    List<GroupRole> findByGroupIdAndIsEditableTrueAndIsActiveTrueOrderByHierarchyLevelAsc(Long groupId);

    /**
     * 查询可删除的角色
     * 
     * @param groupId 群组ID
     * @return 可删除的角色列表
     */
    List<GroupRole> findByGroupIdAndIsDeletableTrueAndIsActiveTrueOrderByHierarchyLevelAsc(Long groupId);

    /**
     * 根据颜色查询角色
     * 
     * @param groupId 群组ID
     * @param roleColor 角色颜色
     * @return 角色列表
     */
    List<GroupRole> findByGroupIdAndRoleColorAndIsActiveTrue(Long groupId, String roleColor);

    /**
     * 查询最高权限角色（层级等级最小）
     * 
     * @param groupId 群组ID
     * @return 最高权限角色
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId AND gr.isActive = true " +
           "ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findTopRolesByHierarchy(@Param("groupId") Long groupId, Pageable pageable);

    /**
     * 查询群主角色
     * 
     * @param groupId 群组ID
     * @return 群主角色
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND (gr.roleTemplate = 'OWNER' OR gr.hierarchyLevel = 1) " +
           "AND gr.isActive = true")
    Optional<GroupRole> findOwnerRole(@Param("groupId") Long groupId);

    /**
     * 查询管理员角色
     * 
     * @param groupId 群组ID
     * @return 管理员角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND (gr.roleTemplate = 'ADMIN' OR gr.hierarchyLevel <= 2) " +
           "AND gr.isActive = true ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findAdminRoles(@Param("groupId") Long groupId);

    /**
     * 查询版主角色
     * 
     * @param groupId 群组ID
     * @return 版主角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND (gr.roleTemplate = 'MODERATOR' OR gr.hierarchyLevel <= 3) " +
           "AND gr.isActive = true ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findModeratorRoles(@Param("groupId") Long groupId);

    /**
     * 查询普通成员角色
     * 
     * @param groupId 群组ID
     * @return 普通成员角色
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND gr.roleTemplate = 'MEMBER' AND gr.isActive = true")
    Optional<GroupRole> findMemberRole(@Param("groupId") Long groupId);

    /**
     * 查询角色继承关系
     * 
     * @param groupId 群组ID
     * @return 继承关系列表 [子角色ID, 父角色ID, 层级差]
     */
    @Query("SELECT child.id, parent.id, (child.hierarchyLevel - parent.hierarchyLevel) " +
           "FROM GroupRole child, GroupRole parent " +
           "WHERE child.groupId = :groupId AND parent.groupId = :groupId " +
           "AND child.parentRoleId = parent.id AND child.isActive = true AND parent.isActive = true " +
           "ORDER BY child.hierarchyLevel ASC")
    List<Object[]> getRoleInheritanceRelations(@Param("groupId") Long groupId);

    /**
     * 查询孤立角色（无父角色且非顶级角色）
     * 
     * @param groupId 群组ID
     * @return 孤立角色列表
     */
    @Query("SELECT gr FROM GroupRole gr WHERE gr.groupId = :groupId " +
           "AND gr.parentRoleId IS NULL AND gr.hierarchyLevel > 1 " +
           "AND gr.isActive = true ORDER BY gr.hierarchyLevel ASC")
    List<GroupRole> findOrphanRoles(@Param("groupId") Long groupId);

    /**
     * 查询循环继承的角色
     * 
     * @param groupId 群组ID
     * @return 存在循环继承的角色ID列表
     */
    @Query(value = "WITH RECURSIVE role_hierarchy AS (" +
           "  SELECT id, parent_role_id, ARRAY[id] as path " +
           "  FROM group_role WHERE group_id = :groupId AND is_active = true " +
           "  UNION ALL " +
           "  SELECT r.id, r.parent_role_id, rh.path || r.id " +
           "  FROM group_role r " +
           "  JOIN role_hierarchy rh ON r.parent_role_id = rh.id " +
           "  WHERE r.group_id = :groupId AND r.is_active = true " +
           "  AND NOT r.id = ANY(rh.path)" +
           ") " +
           "SELECT DISTINCT unnest(path) FROM role_hierarchy " +
           "WHERE array_length(path, 1) > 10", nativeQuery = true)
    List<Long> findCircularInheritanceRoles(@Param("groupId") Long groupId);

    /**
     * 删除群组的所有角色
     * 
     * @param groupId 群组ID
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM GroupRole gr WHERE gr.groupId = :groupId")
    int deleteByGroupId(@Param("groupId") Long groupId);
}
