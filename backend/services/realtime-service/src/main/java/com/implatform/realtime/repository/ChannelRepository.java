package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Channel;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 频道Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ChannelRepository extends R2dbcRepository<Channel, Long> {

    /**
     * 根据用户名查找频道
     */
    @Query("SELECT * FROM channels WHERE username = :username")
    Mono<Channel> findByUsername(@Param("username") String username);

    /**
     * 检查用户名是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM channels WHERE username = :username")
    Mono<Boolean> existsByUsername(@Param("username") String username);

    /**
     * 根据创建者查找频道
     */
    @Query("SELECT * FROM channels WHERE creator_id = :creatorId AND is_active = true LIMIT :limit OFFSET :offset")
    Flux<Channel> findByCreatorIdAndIsActiveTrue(@Param("creatorId") Long creatorId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找公开频道
     */
    @Query("SELECT * FROM channels WHERE is_public = true AND is_active = true ORDER BY subscriber_count DESC LIMIT :limit OFFSET :offset")
    Flux<Channel> findByIsPublicTrueAndIsActiveTrueOrderBySubscriberCountDesc(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据分类查找频道
     */
    @Query("SELECT * FROM channels WHERE category = :category AND is_public = true AND is_active = true ORDER BY subscriber_count DESC LIMIT :limit OFFSET :offset")
    Flux<Channel> findByCategoryAndIsPublicTrueAndIsActiveTrueOrderBySubscriberCountDesc(
            @Param("category") String category, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 搜索频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.username) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY c.subscriberCount DESC")
    Page<Channel> searchChannels(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取热门频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "c.subscriberCount >= :minSubscribers " +
           "ORDER BY c.subscriberCount DESC, c.viewCount DESC")
    List<Channel> findPopularChannels(@Param("minSubscribers") Long minSubscribers, Pageable pageable);

    /**
     * 获取推荐频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "c.isVerified = true " +
           "ORDER BY c.subscriberCount DESC, c.createdAt DESC")
    List<Channel> findRecommendedChannels(Pageable pageable);

    /**
     * 获取新频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "c.createdAt >= :since " +
           "ORDER BY c.createdAt DESC")
    List<Channel> findNewChannels(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 根据语言查找频道
     */
    Page<Channel> findByLanguageAndIsPublicTrueAndIsActiveTrueOrderBySubscriberCountDesc(
            String language, Pageable pageable);

    /**
     * 根据国家查找频道
     */
    Page<Channel> findByCountryAndIsPublicTrueAndIsActiveTrueOrderBySubscriberCountDesc(
            String country, Pageable pageable);

    /**
     * 搜索公开频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.username) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY c.subscriberCount DESC")
    Page<Channel> searchPublicChannels(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取热门频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "c.subscriberCount >= 1000 " +
           "ORDER BY c.subscriberCount DESC, c.viewCount DESC")
    List<Channel> findPopularChannels(Pageable pageable);

    /**
     * 获取已验证频道
     */
    Page<Channel> findByIsVerifiedTrueAndIsPublicTrueAndIsActiveTrueOrderBySubscriberCountDesc(
            Pageable pageable);

    /**
     * 统计频道数量
     */
    @Query("SELECT COUNT(c) FROM Channel c WHERE c.isActive = true")
    long countActiveChannels();

    /**
     * 统计公开频道数量
     */
    @Query("SELECT COUNT(c) FROM Channel c WHERE c.isPublic = true AND c.isActive = true")
    long countPublicChannels();

    /**
     * 统计已验证频道数量
     */
    @Query("SELECT COUNT(c) FROM Channel c WHERE c.isVerified = true AND c.isActive = true")
    long countVerifiedChannels();

    /**
     * 按分类统计频道数量
     */
    @Query("SELECT c.category, COUNT(c) FROM Channel c WHERE c.isActive = true " +
           "GROUP BY c.category ORDER BY COUNT(c) DESC")
    List<Object[]> countChannelsByCategory();

    /**
     * 获取订阅者数量排行
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true " +
           "ORDER BY c.subscriberCount DESC")
    List<Channel> findTopChannelsBySubscribers(Pageable pageable);

    /**
     * 获取活跃度排行
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true " +
           "ORDER BY c.postCount DESC, c.viewCount DESC")
    List<Channel> findTopChannelsByActivity(Pageable pageable);

    /**
     * 查找相似频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isPublic = true AND c.isActive = true AND " +
           "c.id != :excludeId AND " +
           "(c.category = :category OR " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY c.subscriberCount DESC")
    List<Channel> findSimilarChannels(@Param("excludeId") Long excludeId, 
                                     @Param("category") String category,
                                     @Param("keyword") String keyword, 
                                     Pageable pageable);

    /**
     * 获取用户创建的频道统计
     */
    @Query("SELECT " +
           "COUNT(c) as totalChannels, " +
           "SUM(c.subscriberCount) as totalSubscribers, " +
           "SUM(c.postCount) as totalPosts, " +
           "SUM(c.viewCount) as totalViews " +
           "FROM Channel c WHERE c.creatorId = :creatorId AND c.isActive = true")
    Object[] getUserChannelStats(@Param("creatorId") Long creatorId);

    /**
     * 获取频道增长趋势
     */
    @Query("SELECT DATE(c.createdAt) as date, COUNT(c) as count " +
           "FROM Channel c WHERE c.createdAt >= :startDate AND c.isActive = true " +
           "GROUP BY DATE(c.createdAt) " +
           "ORDER BY date")
    List<Object[]> getChannelGrowthTrend(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找需要更新统计的频道
     */
    @Query("SELECT c FROM Channel c WHERE c.isActive = true AND " +
           "(c.updatedAt < :cutoffTime OR c.updatedAt IS NULL)")
    List<Channel> findChannelsNeedingStatsUpdate(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 批量更新订阅者数量
     */
    @Query("UPDATE Channel c SET c.subscriberCount = :count, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id = :channelId")
    void updateSubscriberCount(@Param("channelId") Long channelId, @Param("count") Long count);

    /**
     * 批量更新帖子数量
     */
    @Query("UPDATE Channel c SET c.postCount = :count, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id = :channelId")
    void updatePostCount(@Param("channelId") Long channelId, @Param("count") Long count);

    /**
     * 批量更新浏览量
     */
    @Query("UPDATE Channel c SET c.viewCount = c.viewCount + :increment, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id = :channelId")
    void incrementViewCount(@Param("channelId") Long channelId, @Param("increment") Long increment);

    /**
     * 查找即将到期的邀请链接
     */
    @Query("SELECT c FROM Channel c WHERE c.inviteLink IS NOT NULL AND " +
           "c.updatedAt < :expiryTime AND c.isActive = true")
    List<Channel> findChannelsWithExpiringInviteLinks(@Param("expiryTime") LocalDateTime expiryTime);

    /**
     * 获取频道分析数据
     */
    @Query("SELECT " +
           "c.id, c.name, c.subscriberCount, c.postCount, c.viewCount, " +
           "c.createdAt, c.category, c.language, c.isVerified " +
           "FROM Channel c WHERE c.isActive = true " +
           "ORDER BY c.subscriberCount DESC")
    List<Object[]> getChannelAnalyticsData();
}
