package com.implatform.realtime.repository;

import com.implatform.realtime.entity.RedPacket;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 红包数据访问接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface RedPacketRepository extends R2dbcRepository<RedPacket, Long> {

    /**
     * 根据红包编号查找红包
     */
    @Query("SELECT * FROM red_packets WHERE packet_no = :packetNo")
    Mono<RedPacket> findByPacketNo(@Param("packetNo") String packetNo);

    /**
     * 根据红包编号查找红包（加悲观锁）
     */
    @Query("SELECT * FROM red_packets WHERE packet_no = :packetNo FOR UPDATE")
    Mono<RedPacket> findByPacketNoWithLock(@Param("packetNo") String packetNo);

    /**
     * 根据发送者ID查找红包列表
     */
    @Query("SELECT * FROM red_packets WHERE sender_id = :senderId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<RedPacket> findBySenderIdOrderByCreatedAtDesc(@Param("senderId") Long senderId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据聊天ID查找红包列表
     */
    @Query("SELECT * FROM red_packets WHERE chat_id = :chatId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<RedPacket> findByChatIdOrderByCreatedAtDesc(@Param("chatId") String chatId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据发送者ID和状态查找红包列表
     */
    Page<RedPacket> findBySenderIdAndStatusOrderByCreatedAtDesc(Long senderId, RedPacket.PacketStatus status, Pageable pageable);

    /**
     * 根据聊天ID和状态查找红包列表
     */
    Page<RedPacket> findByChatIdAndStatusOrderByCreatedAtDesc(String chatId, RedPacket.PacketStatus status, Pageable pageable);

    /**
     * 根据红包类型查找红包列表
     */
    Page<RedPacket> findByPacketTypeOrderByCreatedAtDesc(RedPacket.PacketType packetType, Pageable pageable);

    /**
     * 查找过期但未处理的红包
     */
    @Query("SELECT rp FROM RedPacket rp WHERE rp.status = 'ACTIVE' AND rp.expiredAt < :currentTime")
    List<RedPacket> findExpiredActiveRedPackets(@Param("currentTime") Instant currentTime);

    /**
     * 查找可以退款的红包（过期且有剩余金额）
     */
    @Query("SELECT rp FROM RedPacket rp WHERE rp.status IN ('EXPIRED', 'ACTIVE') " +
           "AND rp.remainingAmount > 0 AND rp.expiredAt < :currentTime")
    List<RedPacket> findRefundableRedPackets(@Param("currentTime") Instant currentTime);

    /**
     * 统计用户发送的红包数量
     */
    @Query("SELECT COUNT(rp) FROM RedPacket rp WHERE rp.senderId = :senderId")
    Long countBySenderId(@Param("senderId") Long senderId);

    /**
     * 统计用户在指定时间范围内发送的红包数量
     */
    @Query("SELECT COUNT(rp) FROM RedPacket rp WHERE rp.senderId = :senderId " +
           "AND rp.createdAt BETWEEN :startTime AND :endTime")
    Long countBySenderIdAndCreatedAtBetween(@Param("senderId") Long senderId, 
                                           @Param("startTime") Instant startTime, 
                                           @Param("endTime") Instant endTime);

    /**
     * 统计聊天中的红包数量
     */
    @Query("SELECT COUNT(rp) FROM RedPacket rp WHERE rp.chatId = :chatId")
    Long countByChatId(@Param("chatId") String chatId);

    /**
     * 批量更新过期红包状态
     */
    @Modifying
    @Query("UPDATE RedPacket rp SET rp.status = 'EXPIRED' WHERE rp.status = 'ACTIVE' AND rp.expiredAt < :currentTime")
    int updateExpiredRedPackets(@Param("currentTime") Instant currentTime);

    /**
     * 查找用户在特定聊天中发送的红包
     */
    @Query("SELECT rp FROM RedPacket rp WHERE rp.senderId = :senderId AND rp.chatId = :chatId " +
           "ORDER BY rp.createdAt DESC")
    Page<RedPacket> findBySenderIdAndChatId(@Param("senderId") Long senderId, 
                                           @Param("chatId") String chatId, 
                                           Pageable pageable);

    /**
     * 查找指定状态的红包数量
     */
    @Query("SELECT COUNT(rp) FROM RedPacket rp WHERE rp.status = :status")
    Long countByStatus(@Param("status") RedPacket.PacketStatus status);

    /**
     * 查找最近的红包（用于生成编号）
     */
    @Query("SELECT rp FROM RedPacket rp ORDER BY rp.createdAt DESC")
    Page<RedPacket> findLatestRedPackets(Pageable pageable);

    /**
     * 检查用户是否已经在指定红包中领取过
     */
    @Query("SELECT COUNT(rpc) > 0 FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId AND rpc.userId = :userId")
    boolean existsClaimByRedPacketIdAndUserId(@Param("redPacketId") Long redPacketId, @Param("userId") Long userId);

    /**
     * 查找用户参与的红包（通过领取记录）
     */
    @Query("SELECT DISTINCT rpc.redPacket FROM RedPacketClaim rpc WHERE rpc.userId = :userId " +
           "ORDER BY rpc.redPacket.createdAt DESC")
    Page<RedPacket> findRedPacketsClaimedByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计红包的总体数据
     */
    @Query("SELECT new map(" +
           "COUNT(rp) as totalCount, " +
           "SUM(rp.totalAmount) as totalAmount, " +
           "SUM(rp.claimedAmount) as claimedAmount, " +
           "AVG(rp.totalAmount) as avgAmount" +
           ") FROM RedPacket rp WHERE rp.createdAt BETWEEN :startTime AND :endTime")
    List<Object> getRedPacketStatistics(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 根据发送者ID和时间范围查找红包列表
     */
    @Query("SELECT rp FROM RedPacket rp WHERE rp.senderId = :senderId " +
           "AND rp.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY rp.createdAt DESC")
    List<RedPacket> findBySenderIdAndCreatedAtBetween(@Param("senderId") Long senderId,
                                                     @Param("startTime") Instant startTime,
                                                     @Param("endTime") Instant endTime);
}
