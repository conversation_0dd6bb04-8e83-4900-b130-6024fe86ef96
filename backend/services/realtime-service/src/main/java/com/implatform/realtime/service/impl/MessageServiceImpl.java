package com.implatform.realtime.service.impl;

import com.implatform.common.core.domain.Result;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.webflux.response.PageResult;


import com.implatform.realtime.config.MessageConfig;
import com.implatform.message.dto.MessageDTO;
import com.implatform.message.dto.NotificationDTO;
import com.implatform.message.dto.SendMessageDTO;
import com.implatform.realtime.entity.Message;
import com.implatform.common.core.enums.MessageErrorCode;
import com.implatform.realtime.repository.MessageRepository;
import com.implatform.realtime.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息服务实现类 - IM平台核心消息处理业务逻辑实现
 *
 * <p><strong>实现概述</strong>：
 * 本类实现了MessageService接口定义的所有消息相关业务功能，提供完整的消息生命周期管理。
 * 采用事务管理、缓存优化、异步处理等技术手段，确保消息处理的高性能、高可靠性和数据一致性。
 *
 * <p><strong>核心实现特性</strong>：
 * <ul>
 *   <li><strong>事务管理</strong>：使用@Transactional确保消息操作的原子性和一致性</li>
 *   <li><strong>缓存策略</strong>：Redis缓存热点数据，提升查询性能</li>
 *   <li><strong>异步处理</strong>：消息推送、通知等耗时操作异步执行</li>
 *   <li><strong>数据验证</strong>：多层次的数据验证和业务规则检查</li>
 *   <li><strong>错误处理</strong>：统一的异常处理和错误码管理</li>
 *   <li><strong>性能优化</strong>：批量操作、分页查询、索引优化</li>
 * </ul>
 *
 * <p><strong>技术实现架构</strong>：
 * <ul>
 *   <li><strong>数据访问层</strong>：通过MessageRepository进行数据库操作</li>
 *   <li><strong>缓存层</strong>：Redis缓存消息状态、未读计数等热点数据</li>
 *   <li><strong>消息队列</strong>：RabbitMQ处理消息推送和异步任务</li>
 *   <li><strong>外部服务</strong>：Feign客户端调用用户服务、会话服务等</li>
 *   <li><strong>安全层</strong>：权限验证、内容审核、敏感信息过滤</li>
 * </ul>
 *
 * <p><strong>业务流程实现</strong>：
 * <ul>
 *   <li><strong>消息发送</strong>：权限验证→内容检查→数据持久化→实时推送→状态更新</li>
 *   <li><strong>消息查询</strong>：权限验证→缓存查询→数据库查询→结果组装→权限过滤</li>
 *   <li><strong>状态管理</strong>：已读状态→未读计数→送达状态→在线状态同步</li>
 *   <li><strong>特殊功能</strong>：消息撤回→自毁消息→定时消息→消息反应</li>
 * </ul>
 *
 * <p><strong>性能优化策略</strong>：
 * <ul>
 *   <li><strong>查询优化</strong>：分页查询、索引使用、查询条件优化</li>
 *   <li><strong>缓存策略</strong>：热点数据缓存、缓存预热、缓存更新策略</li>
 *   <li><strong>批量处理</strong>：批量插入、批量更新、批量删除操作</li>
 *   <li><strong>异步处理</strong>：消息推送、统计计算、日志记录异步化</li>
 * </ul>
 *
 * <p><strong>数据一致性保证</strong>：
 * <ul>
 *   <li><strong>事务边界</strong>：合理的事务范围，避免长事务和死锁</li>
 *   <li><strong>并发控制</strong>：乐观锁、悲观锁、分布式锁的合理使用</li>
 *   <li><strong>数据同步</strong>：缓存与数据库的一致性保证</li>
 *   <li><strong>异常恢复</strong>：失败重试、补偿机制、数据修复</li>
 * </ul>
 *
 * <p><strong>监控和审计</strong>：
 * <ul>
 *   <li><strong>操作日志</strong>：详细的业务操作日志记录</li>
 *   <li><strong>性能监控</strong>：方法执行时间、数据库查询性能</li>
 *   <li><strong>异常监控</strong>：异常捕获、告警通知、问题追踪</li>
 *   <li><strong>业务指标</strong>：消息量统计、用户活跃度、系统负载</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageServiceImpl implements MessageService {

    private final MessageRepository messageRepository;
    private final ConversationServiceWebClient conversationServiceClient;
    private final NotificationServiceWebClient notificationServiceClient;
    private final MessageConfig messageConfig;
    
    /**
     * 发送消息的核心业务逻辑实现
     *
     * <p><strong>实现流程</strong>：
     * <ol>
     *   <li><strong>输入验证</strong>：验证请求参数的完整性和有效性</li>
     *   <li><strong>会话验证</strong>：检查目标会话是否存在且状态正常</li>
     *   <li><strong>权限验证</strong>：验证发送者是否有权限向该会话发送消息</li>
     *   <li><strong>消息创建</strong>：根据请求参数创建Message实体对象</li>
     *   <li><strong>数据持久化</strong>：将消息保存到数据库</li>
     *   <li><strong>会话更新</strong>：更新会话的最后消息信息</li>
     *   <li><strong>结果返回</strong>：转换为DTO并返回给调用方</li>
     * </ol>
     *
     * <p><strong>事务管理</strong>：
     * 使用READ_COMMITTED隔离级别，确保消息发送的原子性。该隔离级别可以避免脏读，
     * 同时允许并发读取已提交的数据，在性能和一致性之间取得平衡。
     *
     * <p><strong>数据验证</strong>：
     * <ul>
     *   <li>参数完整性验证：检查必要参数是否为空</li>
     *   <li>业务规则验证：消息类型、内容长度、媒体URL等</li>
     *   <li>权限验证：发送者权限、会话成员资格等</li>
     *   <li>状态验证：会话状态、用户状态等</li>
     * </ul>
     *
     * <p><strong>性能优化</strong>：
     * <ul>
     *   <li>单次数据库查询获取会话信息</li>
     *   <li>批量验证减少数据库访问</li>
     *   <li>异步处理消息推送（在事务外执行）</li>
     *   <li>缓存热点数据减少重复查询</li>
     * </ul>
     *
     * <p><strong>错误处理</strong>：
     * 使用统一的BusinessException处理各种业务异常，包括：
     * <ul>
     *   <li>CONVERSATION_NOT_FOUND：会话不存在</li>
     *   <li>CONVERSATION_CLOSED：会话已关闭</li>
     *   <li>MESSAGE_CONTENT_INVALID：消息内容无效</li>
     *   <li>MESSAGE_SIZE_EXCEEDED：消息大小超限</li>
     * </ul>
     *
     * @param dto 消息发送请求对象，包含会话ID、消息类型、内容等信息
     * @param senderId 发送者用户ID，用于权限验证和消息归属
     * @return MessageDTO 发送成功的消息信息，包含消息ID、时间戳等
     * @throws BusinessException 当参数无效、会话不存在、权限不足时抛出
     *
     * @implNote
     * 该方法是消息发送的核心实现，需要保证数据一致性和高并发性能。
     * 后续可以考虑添加消息队列异步处理、内容审核、消息加密等功能。
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public MessageDTO sendMessage(SendMessageDTO dto, Long senderId) {
        log.info("用户 {} 发送消息到会话 {}", senderId, dto.getConversationId());

        // 输入验证
        validateSendMessageRequest(dto, senderId);

        // 验证会话是否存在 - 通过Feign客户端调用conversation-service
        // 注意：这里简化处理，实际应该通过Feign客户端验证会话存在性
        validateConversationExists(dto.getConversationId(), senderId);

        // 验证用户是否有权限发送消息到该会话
        validateSendPermission(dto.getConversationId(), senderId);
        
        // 创建消息
        Message message = new Message();
        message.setConversationId(dto.getConversationId());
        message.setSenderId(senderId);
        message.setReceiverId(dto.getReceiverId());
        message.setMessageType(dto.getMessageType());
        message.setContent(dto.getContent());
        message.setMediaUrl(dto.getMediaUrl());
        // 设置媒体名称（如果DTO中有提供）
        if (dto.getMediaUrl() != null && !dto.getMediaUrl().trim().isEmpty()) {
            // 从URL中提取文件名作为媒体名称
            String fileName = extractFileNameFromUrl(dto.getMediaUrl());
            // 注意：Message实体暂时没有mediaName字段，如需要可以添加
        }
        message.setMediaSize(dto.getMediaSize());
        message.setThumbnailUrl(dto.getThumbnailUrl());
        message.setReplyToId(dto.getReplyToId());
        message.setForwardFromId(dto.getForwardFromId());
        message.setExtra(dto.getExtra());
        message.setStatus(Message.MessageStatus.SENT);
        
        // 保存消息
        message = messageRepository.save(message);

        // 更新会话最后消息信息
        updateConversationLastMessage(dto.getConversationId(), message);

        // 发送实时通知
        sendMessageNotification(message);

        log.info("消息发送成功，消息ID: {}", message.getId());
        return convertToDTO(message);
    }
    
    @Override
    public PageResult<MessageDTO> getConversationMessages(Long conversationId, Integer page, Integer size) {
        Pageable pageable = PageRequest.of(page, size);
        log.debug("获取会话 {} 的消息列表", conversationId);
        
        // 验证会话是否存在
        validateConversationExists(conversationId, null);
        
        Page<Message> messagePage = messageRepository.findByConversationIdAndStatusNotOrderByCreatedAtDesc(
                conversationId, Message.MessageStatus.DELETED, pageable);
        
        List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return PageResult.of(messageDTOs, messagePage.getTotalElements(), page, size);
    }
    
    @Override
    @Transactional
    public void recallMessage(Long messageId, Long userId) {
        log.info("用户 {} 撤回消息 {}", userId, messageId);
        
        Message message = messageRepository.findById(messageId)
                .orElseThrow(() -> new BusinessException(MessageErrorCode.MESSAGE_NOT_FOUND));

        // 只有发送者可以撤回消息
        if (!userId.equals(message.getSenderId())) {
            throw new BusinessException(MessageErrorCode.MESSAGE_RECALL_DENIED);
        }

        // 检查撤回时间限制
        Instant recallDeadline = message.getCreatedAt().plus(messageConfig.getTimeLimits().getRecallTimeLimit());
        if (Instant.now().isAfter(recallDeadline)) {
            throw new BusinessException(MessageErrorCode.MESSAGE_RECALL_TIMEOUT);
        }
        
        // 更新消息状态
        message.setStatus(Message.MessageStatus.RECALLED);
        message.setContent("消息已撤回");
        messageRepository.save(message);

        // 发送撤回通知
        sendMessageRecallNotification(message, userId);

        log.info("消息 {} 撤回成功", messageId);
    }
    
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void deleteMessage(Long messageId, Long userId) {
        log.info("用户 {} 删除消息 {}", userId, messageId);
        
        Message message = messageRepository.findById(messageId)
                .orElseThrow(() -> new BusinessException(MessageErrorCode.MESSAGE_NOT_FOUND));

        // 只有发送者可以删除消息
        if (!userId.equals(message.getSenderId())) {
            throw new BusinessException(MessageErrorCode.MESSAGE_DELETE_DENIED);
        }
        
        message.setStatus(Message.MessageStatus.DELETED);
        messageRepository.save(message);

        // 发送删除通知
        sendMessageDeleteNotification(message, userId);

        log.info("消息 {} 删除成功", messageId);
    }
    
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void markAsRead(Long conversationId, Long userId) {
        log.debug("用户 {} 标记会话 {} 所有消息为已读", userId, conversationId);
        
        // 验证会话是否存在
        validateConversationExists(conversationId, userId);
        
        List<Message> unreadMessages = messageRepository.findByConversationIdAndReceiverIdAndIsReadFalse(
                conversationId, userId);
        
        Instant now = Instant.now();
        unreadMessages.forEach(message -> {
            message.setIsRead(true);
            message.setReadAt(now);
        });
        
        messageRepository.saveAll(unreadMessages);
        log.info("会话 {} 中 {} 条消息标记为已读", conversationId, unreadMessages.size());
    }
    
    @Override
    public Integer getUnreadCount(Long userId) {
        log.debug("获取用户 {} 的未读消息数量", userId);
        return messageRepository.countByReceiverIdAndIsReadFalse(userId);
    }
    
    /**
     * 转换Message实体为MessageDTO
     */
    private MessageDTO convertToDTO(Message message) {
        MessageDTO dto = new MessageDTO();
        dto.setId(message.getId());
        dto.setConversationId(message.getConversationId());
        dto.setSenderId(message.getSenderId());
        dto.setReceiverId(message.getReceiverId());
        dto.setContent(message.getContent());
        dto.setMessageType(message.getMessageType().name());
        dto.setReadStatus(message.getIsRead());
        dto.setCreatedAt(message.getCreatedAt() != null ?
            LocalDateTime.ofInstant(message.getCreatedAt(), java.time.ZoneOffset.UTC) : null);
        dto.setUpdatedAt(message.getUpdatedAt() != null ?
            LocalDateTime.ofInstant(message.getUpdatedAt(), java.time.ZoneOffset.UTC) : null);
        return dto;
    }
    
    /**
     * 更新会话最后消息信息
     */
    private void updateConversationLastMessage(Long conversationId, Message message) {
        try {
            log.debug("更新会话最后消息信息: conversationId={}, messageId={}", conversationId, message.getId());

            // 通过Feign客户端调用conversation-service更新最后消息信息
            Result<Void> updateResult = conversationServiceClient.updateLastMessage(conversationId, message.getId());
            if (updateResult.isSuccess()) {
                log.info("会话最后消息信息更新成功: conversationId={}", conversationId);
            } else {
                log.warn("会话最后消息信息更新失败: conversationId={}, error={}", conversationId, updateResult.getMessage());
            }

            // 增加会话消息计数
            Result<Void> countResult = conversationServiceClient.incrementMessageCount(conversationId, 1);
            if (countResult.isSuccess()) {
                log.debug("会话消息计数更新成功: conversationId={}", conversationId);
            } else {
                log.warn("会话消息计数更新失败: conversationId={}, error={}", conversationId, countResult.getMessage());
            }

        } catch (Exception e) {
            log.error("更新会话最后消息信息失败: conversationId={}", conversationId, e);
            // 不抛出异常，避免影响消息发送的主流程
        }
    }
    
    /**
     * 保存消息
     */
    @Override
    public Message saveMessage(Message message) {
        return messageRepository.save(message);
    }

    /**
     * 获取消息显示内容
     */
    private String getDisplayContent(Message message) {
        return switch (message.getMessageType()) {
            case TEXT -> message.getContent();
            case IMAGE -> "[图片]";
            case VIDEO -> "[视频]";
            case AUDIO -> "[语音]";
            case FILE -> "[文件]";
            case LOCATION -> "[位置]";
            case EMOJI -> "[表情]";
            case SYSTEM -> message.getContent();
            case RECALL -> "[消息已撤回]";
            case RED_PACKET -> "[红包]";
            case TRANSFER -> "[转账]";
        };
    }

    @Override
    public SelfDestructController.UserSelfDestructStatistics getUserSelfDestructStatistics(Long userId) {
        log.debug("获取用户自毁消息统计: userId={}", userId);

        try {
            // 查询用户发送的自毁消息统计
            Long totalSentSelfDestructMessages = messageRepository.countUserSelfDestructMessages(userId);

            // 简化实现：暂时使用基础统计，后续可以添加更多查询方法到Repository
            SelfDestructController.UserSelfDestructStatistics stats = new SelfDestructController.UserSelfDestructStatistics();
            stats.setUserId(userId);
            stats.setTotalSentSelfDestructMessages(totalSentSelfDestructMessages);
            // 获取接收的自毁消息数量
            Long totalReceivedSelfDestructMessages = messageRepository.countByReceiverIdAndSelfDestructAtIsNotNull(userId);
            stats.setTotalReceivedSelfDestructMessages(totalReceivedSelfDestructMessages);

            // 获取已销毁的发送消息数量
            Long destructedSentMessages = messageRepository.countBySenderIdAndSelfDestructAtIsNotNullAndSelfDestructAtBefore(userId, Instant.now());
            stats.setDestructedSentMessages(destructedSentMessages);

            // 获取已销毁的接收消息数量
            Long destructedReceivedMessages = messageRepository.countByReceiverIdAndSelfDestructAtIsNotNullAndSelfDestructAtBefore(userId, Instant.now());
            stats.setDestructedReceivedMessages(destructedReceivedMessages);

            return stats;
        } catch (Exception e) {
            log.error("获取用户自毁消息统计失败: userId={}", userId, e);
            return new SelfDestructController.UserSelfDestructStatistics();
        }
    }

    @Override
    public SelfDestructController.SelfDestructStatistics getSelfDestructStatistics() {
        log.debug("获取系统自毁消息统计");

        try {
            Long totalSelfDestructMessages = messageRepository.countSelfDestructMessages();
            Long totalDestructedMessages = messageRepository.countDestructedMessages();

            // 计算活跃的自毁消息数量（总数 - 已销毁数量）
            Long activeSelfDestructMessages = totalSelfDestructMessages - totalDestructedMessages;

            // 计算销毁率
            Double destructionRate = totalSelfDestructMessages > 0 ?
                (double) totalDestructedMessages / totalSelfDestructMessages : 0.0;

            SelfDestructController.SelfDestructStatistics stats = new SelfDestructController.SelfDestructStatistics();
            stats.setTotalSelfDestructMessages(totalSelfDestructMessages);
            stats.setTotalDestructedMessages(totalDestructedMessages);
            stats.setActiveSelfDestructMessages(activeSelfDestructMessages);
            stats.setDestructionRate(destructionRate);

            return stats;
        } catch (Exception e) {
            log.error("获取系统自毁消息统计失败", e);
            return new SelfDestructController.SelfDestructStatistics();
        }
    }

    @Override
    public org.springframework.data.domain.Page<Message> getUserSelfDestructMessages(Long userId, org.springframework.data.domain.Pageable pageable) {
        log.debug("获取用户自毁消息列表: userId={}", userId);

        try {
            return messageRepository.findUserSelfDestructMessages(userId, pageable);
        } catch (Exception e) {
            log.error("获取用户自毁消息列表失败: userId={}", userId, e);
            return org.springframework.data.domain.Page.empty(pageable);
        }
    }

    /**
     * 验证发送消息请求
     */
    private void validateSendMessageRequest(SendMessageDTO dto, Long senderId) {
        if (dto == null) {
            throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
        }

        if (senderId == null || senderId <= 0) {
            throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
        }

        if (dto.getConversationId() == null || dto.getConversationId() <= 0) {
            throw new BusinessException(MessageErrorCode.CONVERSATION_NOT_FOUND);
        }

        if (dto.getMessageType() == null) {
            throw new BusinessException(MessageErrorCode.MESSAGE_TYPE_INVALID);
        }

        // 验证消息内容
        if (dto.getMessageType() == Message.MessageType.TEXT) {
            if (dto.getContent() == null || dto.getContent().trim().isEmpty()) {
                throw new BusinessException(MessageErrorCode.MESSAGE_CONTENT_INVALID);
            }

            // 检查消息长度限制
            if (dto.getContent().length() > messageConfig.getContentLimits().getMaxTextLength()) {
                throw new BusinessException(MessageErrorCode.MESSAGE_SIZE_EXCEEDED);
            }
        }

        // 验证媒体消息
        if (isMediaMessage(dto.getMessageType())) {
            if (dto.getMediaUrl() == null || dto.getMediaUrl().trim().isEmpty()) {
                throw new BusinessException(MessageErrorCode.MESSAGE_CONTENT_INVALID);
            }
        }

        log.debug("消息发送请求验证通过: senderId={}, conversationId={}", senderId, dto.getConversationId());
    }

    /**
     * 验证会话是否存在
     */
    private void validateConversationExists(Long conversationId, Long userId) {
        log.debug("验证会话存在性: conversationId={}, userId={}", conversationId, userId);

        // 基本验证：会话ID不能为空
        if (conversationId == null || conversationId <= 0) {
            throw new BusinessException(MessageErrorCode.CONVERSATION_NOT_FOUND);
        }

        try {
            // 通过Feign客户端调用conversation-service验证会话存在性
            Result<Boolean> existsResult = conversationServiceClient.existsConversation(conversationId);
            if (!existsResult.isSuccess() || !Boolean.TRUE.equals(existsResult.getData())) {
                throw new BusinessException(MessageErrorCode.CONVERSATION_NOT_FOUND);
            }

            // 验证会话是否激活
            Result<Boolean> activeResult = conversationServiceClient.isConversationActive(conversationId);
            if (!activeResult.isSuccess() || !Boolean.TRUE.equals(activeResult.getData())) {
                throw new BusinessException(MessageErrorCode.CONVERSATION_CLOSED);
            }

            // 如果提供了用户ID，验证用户是否为会话成员
            if (userId != null) {
                Result<Boolean> memberResult = conversationServiceClient.isMember(conversationId, userId);
                if (!memberResult.isSuccess() || !Boolean.TRUE.equals(memberResult.getData())) {
                    throw new BusinessException(MessageErrorCode.CONVERSATION_ACCESS_DENIED);
                }
            }

            log.debug("会话验证通过: conversationId={}, userId={}", conversationId, userId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.warn("会话验证失败，使用降级策略: conversationId={}, error={}", conversationId, e.getMessage());
            // 降级策略：如果conversation-service不可用，只进行基本验证
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            String fileName = url.substring(url.lastIndexOf('/') + 1);
            // 移除查询参数
            int queryIndex = fileName.indexOf('?');
            if (queryIndex > 0) {
                fileName = fileName.substring(0, queryIndex);
            }
            return fileName;
        } catch (Exception e) {
            log.warn("无法从URL提取文件名: {}", url, e);
            return "unknown_file";
        }
    }

    /**
     * 验证发送权限
     */
    private void validateSendPermission(Long conversationId, Long senderId) {
        log.debug("验证用户发送权限: senderId={}, conversationId={}", senderId, conversationId);

        // 基本验证：用户ID不能为空
        if (senderId == null || senderId <= 0) {
            throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
        }

        try {
            // 通过Feign客户端调用conversation-service验证发送权限
            Result<Boolean> permissionResult = conversationServiceClient.checkSendPermission(conversationId, senderId);
            if (!permissionResult.isSuccess() || !Boolean.TRUE.equals(permissionResult.getData())) {
                throw new BusinessException(MessageErrorCode.CONVERSATION_ACCESS_DENIED);
            }

            // 检查用户是否被禁言
            Result<Boolean> mutedResult = conversationServiceClient.isMuted(conversationId, senderId);
            if (mutedResult.isSuccess() && Boolean.TRUE.equals(mutedResult.getData())) {
                throw new BusinessException(MessageErrorCode.CONVERSATION_ACCESS_DENIED);
            }

            // 检查会话是否已关闭
            Result<Boolean> closedResult = conversationServiceClient.isClosed(conversationId);
            if (closedResult.isSuccess() && Boolean.TRUE.equals(closedResult.getData())) {
                throw new BusinessException(MessageErrorCode.CONVERSATION_CLOSED);
            }

            log.debug("用户发送权限验证通过: senderId={}, conversationId={}", senderId, conversationId);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.warn("发送权限验证失败，使用降级策略: senderId={}, conversationId={}, error={}",
                    senderId, conversationId, e.getMessage());
            // 降级策略：如果conversation-service不可用，只进行基本验证
        }
    }

    /**
     * 检查是否为媒体消息
     */
    private boolean isMediaMessage(Message.MessageType messageType) {
        return messageType == Message.MessageType.IMAGE ||
               messageType == Message.MessageType.VIDEO ||
               messageType == Message.MessageType.AUDIO ||
               messageType == Message.MessageType.FILE;
    }

    /**
     * 发送消息实时通知
     */
    private void sendMessageNotification(Message message) {
        try {
            // 发送通知服务通知
            NotificationDTO.MessageNotification notification = NotificationDTO.MessageNotification.builder()
                    .type("MESSAGE_SENT")
                    .title("新消息")
                    .content(getDisplayContent(message))
                    .timestamp(message.getCreatedAt())
                    .priority("NORMAL")
                    .messageId(message.getId())
                    .senderId(message.getSenderId())
                    .receiverId(message.getReceiverId())
                    .conversationId(message.getConversationId())
                    .messageType(message.getMessageType().name())
                    .messageContent(message.getContent())
                    .build();

            notificationServiceClient.sendMessageNotification(notification);

            // 发送WebSocket实时通知到会话
            String destination = "/topic/conversation/" + message.getConversationId() + "/messages";
            notificationServiceClient.sendWebSocketNotification(destination, convertToDTO(message));

            // 如果是私聊，也发送到接收者的个人频道
            if (message.getReceiverId() != null) {
                notificationServiceClient.sendUserWebSocketNotification(message.getReceiverId(), convertToDTO(message));
            }

            log.debug("消息实时通知已发送: messageId={}", message.getId());

        } catch (Exception e) {
            log.error("发送消息实时通知失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 发送消息撤回实时通知
     */
    private void sendMessageRecallNotification(Message message, Long operatorId) {
        try {
            // 发送通知服务通知
            NotificationDTO.MessageRecallNotification notification = NotificationDTO.MessageRecallNotification.builder()
                    .type("MESSAGE_RECALLED")
                    .title("消息撤回")
                    .content("消息已被撤回")
                    .timestamp(Instant.now())
                    .priority("LOW")
                    .messageId(message.getId())
                    .senderId(message.getSenderId())
                    .conversationId(message.getConversationId())
                    .recallReason("用户撤回")
                    .build();

            notificationServiceClient.sendMessageRecallNotification(notification);

            // 发送WebSocket实时通知
            String destination = "/topic/conversation/" + message.getConversationId() + "/message-recall";
            notificationServiceClient.sendWebSocketNotification(destination, notification);

            log.debug("消息撤回通知已发送: messageId={}", message.getId());

        } catch (Exception e) {
            log.error("发送消息撤回通知失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 发送消息删除实时通知
     */
    private void sendMessageDeleteNotification(Message message, Long operatorId) {
        try {
            // 发送通知服务通知
            NotificationDTO.MessageDeleteNotification notification = NotificationDTO.MessageDeleteNotification.builder()
                    .type("MESSAGE_DELETED")
                    .title("消息删除")
                    .content("消息已被删除")
                    .timestamp(Instant.now())
                    .priority("LOW")
                    .messageId(message.getId())
                    .operatorId(operatorId)
                    .conversationId(message.getConversationId())
                    .deleteReason("用户删除")
                    .build();

            notificationServiceClient.sendMessageDeleteNotification(notification);

            // 发送WebSocket实时通知
            String destination = "/topic/conversation/" + message.getConversationId() + "/message-delete";
            notificationServiceClient.sendWebSocketNotification(destination, notification);

            log.debug("消息删除通知已发送: messageId={}", message.getId());

        } catch (Exception e) {
            log.error("发送消息删除通知失败: messageId={}", message.getId(), e);
        }
    }

    // ==================== 管理接口方法实现 ====================

    @Override
    @Cacheable(value = "messageList", key = "#params.toString() + ':' + #pageable.pageNumber + ':' + #pageable.pageSize")
    public Page<MessageDTO> getMessages(Map<String, Object> params, Pageable pageable) {
        log.info("获取消息列表: params={}", params);
        try {
            // 参数验证
            if (params == null) {
                params = new HashMap<>();
            }

            // 构建查询条件
            Page<Message> messagePage = buildMessageQuery(params, pageable);

            // 转换为DTO
            List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return new org.springframework.data.domain.PageImpl<>(messageDTOs, pageable, messagePage.getTotalElements());

        } catch (Exception e) {
            log.error("获取消息列表失败: params={}", params, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 构建消息查询
     */
    private Page<Message> buildMessageQuery(Map<String, Object> params, Pageable pageable) {
        Long conversationId = (Long) params.get("conversationId");
        Long senderId = (Long) params.get("senderId");
        String messageType = (String) params.get("messageType");
        String status = (String) params.get("status");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");

        // 根据参数构建不同的查询
        if (conversationId != null) {
            if (StringUtils.hasText(messageType)) {
                try {
                    Message.MessageType type = Message.MessageType.valueOf(messageType.toUpperCase());
                    return messageRepository.findByConversationIdAndMessageTypeOrderByCreatedAtDesc(
                            conversationId, type, pageable);
                } catch (IllegalArgumentException e) {
                    log.warn("无效的消息类型: {}", messageType);
                }
            }
            return messageRepository.findByConversationIdOrderByCreatedAtDesc(conversationId, pageable);
        }

        if (senderId != null) {
            if (StringUtils.hasText(status)) {
                try {
                    Message.MessageStatus messageStatus = Message.MessageStatus.valueOf(status.toUpperCase());
                    return messageRepository.findBySenderIdAndStatusOrderByCreatedAtDesc(
                            senderId, messageStatus, pageable);
                } catch (IllegalArgumentException e) {
                    log.warn("无效的消息状态: {}", status);
                }
            }
            return messageRepository.findBySenderIdOrderByCreatedAtDesc(senderId, pageable);
        }

        // 默认查询所有消息
        return messageRepository.findAll(pageable);
    }

    @Override
    public MessageDTO getMessageById(Long id) {
        log.info("获取消息详情: id={}", id);
        try {
            Message message = messageRepository.findById(id)
                    .orElseThrow(() -> new BusinessException(MessageErrorCode.MESSAGE_NOT_FOUND));
            return convertToDTO(message);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取消息详情失败: id={}", id, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @Transactional
    public void deleteMessages(List<Long> ids) {
        log.info("批量删除消息: ids={}", ids);
        try {
            List<Message> messages = messageRepository.findAllById(ids);
            messages.forEach(message -> message.setStatus(Message.MessageStatus.DELETED));
            messageRepository.saveAll(messages);
        } catch (Exception e) {
            log.error("批量删除消息失败: ids={}", ids, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Page<MessageDTO> getUserMessages(Long userId, Pageable pageable) {
        log.info("获取用户消息: userId={}", userId);
        try {
            Page<Message> messagePage = messageRepository.findBySenderIdOrderByCreatedAtDesc(userId, pageable);
            List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return new org.springframework.data.domain.PageImpl<>(messageDTOs, pageable, messagePage.getTotalElements());
        } catch (Exception e) {
            log.error("获取用户消息失败: userId={}", userId, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @Cacheable(value = "messageSearch", key = "#keyword + ':' + #messageType + ':' + #pageable.pageNumber + ':' + #pageable.pageSize")
    public Page<MessageDTO> searchMessages(String keyword, String messageType, Pageable pageable) {
        log.info("搜索消息: keyword={}, messageType={}", keyword, messageType);
        try {
            // 参数验证
            if (!StringUtils.hasText(keyword)) {
                return new org.springframework.data.domain.PageImpl<>(new ArrayList<>(), pageable, 0);
            }

            Page<Message> messagePage;

            // 根据消息类型进行搜索
            if (StringUtils.hasText(messageType)) {
                try {
                    Message.MessageType type = Message.MessageType.valueOf(messageType.toUpperCase());
                    messagePage = messageRepository.findByContentContainingIgnoreCaseAndMessageType(
                            keyword, type, pageable);
                } catch (IllegalArgumentException e) {
                    log.warn("无效的消息类型: {}", messageType);
                    messagePage = messageRepository.findByContentContainingIgnoreCase(keyword, pageable);
                }
            } else {
                messagePage = messageRepository.findByContentContainingIgnoreCase(keyword, pageable);
            }

            // 转换为DTO
            List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.debug("消息搜索完成: keyword={}, found={}", keyword, messagePage.getTotalElements());

            return new org.springframework.data.domain.PageImpl<>(messageDTOs, pageable, messagePage.getTotalElements());

        } catch (Exception e) {
            log.error("搜索消息失败: keyword={}, messageType={}", keyword, messageType, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Map<String, Object> getMessageStatistics() {
        log.info("获取消息统计");
        try {
            Long totalMessages = messageRepository.count();
            Long todayMessages = messageRepository.countByCreatedAtAfter(
                    Instant.now().minus(java.time.Duration.ofDays(1)));

            // 计算活跃用户数（今日发送过消息的用户）
            Long activeUsers = messageRepository.countDistinctSendersByCreatedAtAfter(
                    Instant.now().minus(java.time.Duration.ofDays(1)));

            // 计算平均每日消息数（最近30天）
            Instant thirtyDaysAgo = Instant.now().minus(java.time.Duration.ofDays(30));
            Long messagesLast30Days = messageRepository.countByCreatedAtAfter(thirtyDaysAgo);
            Long averageMessagesPerDay = messagesLast30Days / 30;

            return Map.of(
                "totalMessages", totalMessages,
                "todayMessages", todayMessages,
                "activeUsers", activeUsers,
                "averageMessagesPerDay", averageMessagesPerDay,
                "messagesLast30Days", messagesLast30Days
            );
        } catch (Exception e) {
            log.error("获取消息统计失败", e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Page<MessageDTO> getPopularMessages(Pageable pageable) {
        log.info("获取热门消息");
        try {
            // 查询热门消息（根据反应数量排序）
            Page<Message> messagePage = messageRepository.findPopularMessages(pageable);
            List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return new org.springframework.data.domain.PageImpl<>(messageDTOs, pageable, messagePage.getTotalElements());
        } catch (Exception e) {
            log.error("获取热门消息失败", e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Page<MessageDTO> getSensitiveMessages(Pageable pageable) {
        log.info("获取敏感消息");
        try {
            // 查询被标记为敏感的消息
            Page<Message> messagePage = messageRepository.findSensitiveMessages(pageable);
            List<MessageDTO> messageDTOs = messagePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return new org.springframework.data.domain.PageImpl<>(messageDTOs, pageable, messagePage.getTotalElements());
        } catch (Exception e) {
            log.error("获取敏感消息失败", e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = {"message", "messageList"}, key = "#id")
    public void reviewMessage(Long id, String action, String reason) {
        log.info("审核消息: id={}, action={}, reason={}", id, action, reason);
        try {
            // 参数验证
            if (id == null || !StringUtils.hasText(action)) {
                throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            Message message = messageRepository.findById(id)
                    .orElseThrow(() -> new BusinessException(MessageErrorCode.MESSAGE_NOT_FOUND));

            // 验证审核动作
            ReviewAction reviewAction;
            try {
                reviewAction = ReviewAction.valueOf(action.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            // 执行审核操作
            switch (reviewAction) {
                case APPROVE:
                    approveMessage(message, reason);
                    break;
                case REJECT:
                    rejectMessage(message, reason);
                    break;
                case BLOCK:
                    blockMessage(message, reason);
                    break;
                case DELETE:
                    deleteMessage(message, reason);
                    break;
                default:
                    throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            // 记录审核日志
            recordReviewLog(message, reviewAction, reason);

            // 发送审核结果通知
            sendReviewNotification(message, reviewAction, reason);

            log.info("消息审核完成: id={}, action={}, status={}", id, action, message.getStatus());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("审核消息失败: id={}", id, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 审核动作枚举
     */
    private enum ReviewAction {
        APPROVE,  // 通过审核
        REJECT,   // 拒绝审核
        BLOCK,    // 屏蔽消息
        DELETE    // 删除消息
    }

    /**
     * 通过消息审核
     */
    private void approveMessage(Message message, String reason) {
        // 如果消息当前状态不是待审核，则不能通过审核
        if (message.getStatus() != Message.MessageStatus.SENT) {
            log.warn("消息状态不正确，无法通过审核: messageId={}, status={}",
                    message.getId(), message.getStatus());
        }

        // 消息已经是正常状态，无需额外操作
        log.info("消息审核通过: messageId={}, reason={}", message.getId(), reason);
    }

    /**
     * 拒绝消息审核
     */
    private void rejectMessage(Message message, String reason) {
        // 标记消息为审核拒绝状态（可以考虑添加新的状态）
        // 这里暂时不修改消息状态，只记录审核结果
        log.info("消息审核拒绝: messageId={}, reason={}", message.getId(), reason);

        // 可以考虑隐藏消息或限制传播
        // message.setVisible(false);
        // messageRepository.save(message);
    }

    /**
     * 屏蔽消息
     */
    private void blockMessage(Message message, String reason) {
        // 将消息状态设置为已删除，但保留数据用于审计
        message.setStatus(Message.MessageStatus.DELETED);
        messageRepository.save(message);

        log.info("消息已屏蔽: messageId={}, reason={}", message.getId(), reason);

        // 发送屏蔽通知给发送者
        sendBlockNotification(message, reason);
    }

    /**
     * 删除消息
     */
    private void deleteMessage(Message message, String reason) {
        // 物理删除消息（谨慎操作）
        messageRepository.delete(message);

        log.warn("消息已删除: messageId={}, senderId={}, reason={}",
                message.getId(), message.getSenderId(), reason);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"message", "messageList"}, key = "#id")
    public void markMessage(Long id, String markType, String reason) {
        log.info("标记消息: id={}, markType={}, reason={}", id, markType, reason);
        try {
            // 参数验证
            if (id == null || !StringUtils.hasText(markType)) {
                throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            Message message = messageRepository.findById(id)
                    .orElseThrow(() -> new BusinessException(MessageErrorCode.MESSAGE_NOT_FOUND));

            // 验证标记类型
            MarkType mark;
            try {
                mark = MarkType.valueOf(markType.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            // 执行标记操作
            switch (mark) {
                case SPAM:
                    markAsSpam(message, reason);
                    break;
                case SENSITIVE:
                    markAsSensitive(message, reason);
                    break;
                case VIOLATION:
                    markAsViolation(message, reason);
                    break;
                case IMPORTANT:
                    markAsImportant(message, reason);
                    break;
                case SUSPICIOUS:
                    markAsSuspicious(message, reason);
                    break;
                case NORMAL:
                    markAsNormal(message, reason);
                    break;
                default:
                    throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            // 记录标记日志
            recordMarkLog(message, mark, reason);

            // 发送标记通知
            sendMarkNotification(message, mark, reason);

            log.info("消息标记完成: id={}, markType={}", id, markType);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("标记消息失败: id={}", id, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 标记类型枚举
     */
    private enum MarkType {
        SPAM,        // 垃圾消息
        SENSITIVE,   // 敏感消息
        VIOLATION,   // 违规消息
        IMPORTANT,   // 重要消息
        SUSPICIOUS,  // 可疑消息
        NORMAL       // 正常消息
    }

    /**
     * 标记为垃圾消息
     */
    private void markAsSpam(Message message, String reason) {
        // 可以在extra字段中记录标记信息
        updateMessageMark(message, "SPAM", reason);

        // 降低消息优先级或隐藏
        log.info("消息已标记为垃圾消息: messageId={}, reason={}", message.getId(), reason);
    }

    /**
     * 标记为敏感消息
     */
    private void markAsSensitive(Message message, String reason) {
        updateMessageMark(message, "SENSITIVE", reason);

        // 可能需要额外的审核流程
        log.info("消息已标记为敏感消息: messageId={}, reason={}", message.getId(), reason);
    }

    /**
     * 标记为违规消息
     */
    private void markAsViolation(Message message, String reason) {
        updateMessageMark(message, "VIOLATION", reason);

        // 违规消息可能需要立即处理
        log.warn("消息已标记为违规消息: messageId={}, senderId={}, reason={}",
                message.getId(), message.getSenderId(), reason);
    }

    /**
     * 标记为重要消息
     */
    private void markAsImportant(Message message, String reason) {
        updateMessageMark(message, "IMPORTANT", reason);

        log.info("消息已标记为重要消息: messageId={}, reason={}", message.getId(), reason);
    }

    /**
     * 标记为可疑消息
     */
    private void markAsSuspicious(Message message, String reason) {
        updateMessageMark(message, "SUSPICIOUS", reason);

        log.info("消息已标记为可疑消息: messageId={}, reason={}", message.getId(), reason);
    }

    /**
     * 标记为正常消息
     */
    private void markAsNormal(Message message, String reason) {
        // 清除之前的标记
        clearMessageMark(message, reason);

        log.info("消息已标记为正常消息: messageId={}, reason={}", message.getId(), reason);
    }

    @Override
    public String exportMessages(Map<String, Object> params) {
        log.info("导出消息: params={}", params);
        try {
            // 参数验证
            if (params == null) {
                params = new HashMap<>();
            }

            // 构建查询条件
            List<Message> messages = buildExportQuery(params);

            // 生成导出文件
            String exportUrl = generateExportFile(messages, params);

            log.info("消息导出完成: count={}, url={}", messages.size(), exportUrl);

            return exportUrl;

        } catch (Exception e) {
            log.error("导出消息失败: params={}", params, e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 构建导出查询
     */
    private List<Message> buildExportQuery(Map<String, Object> params) {
        // 获取查询参数
        Long conversationId = (Long) params.get("conversationId");
        Long senderId = (Long) params.get("senderId");
        String messageType = (String) params.get("messageType");
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        Integer limit = (Integer) params.getOrDefault("limit", 10000);

        // 构建查询条件
        List<Message> messages;

        if (conversationId != null) {
            Pageable pageable = PageRequest.of(0, limit);
            Page<Message> messagePage = messageRepository.findByConversationIdOrderByCreatedAtDesc(conversationId, pageable);
            messages = messagePage.getContent();
        } else if (senderId != null) {
            Pageable pageable = PageRequest.of(0, limit);
            Page<Message> messagePage = messageRepository.findBySenderIdOrderByCreatedAtDesc(senderId, pageable);
            messages = messagePage.getContent();
        } else {
            // 获取所有消息（限制数量）
            Pageable pageable = PageRequest.of(0, limit);
            Page<Message> messagePage = messageRepository.findAll(pageable);
            messages = messagePage.getContent();
        }

        // 应用时间过滤
        if (StringUtils.hasText(startDate) || StringUtils.hasText(endDate)) {
            messages = filterByDateRange(messages, startDate, endDate);
        }

        // 应用类型过滤
        if (StringUtils.hasText(messageType)) {
            try {
                Message.MessageType type = Message.MessageType.valueOf(messageType.toUpperCase());
                messages = messages.stream()
                        .filter(msg -> msg.getMessageType() == type)
                        .collect(Collectors.toList());
            } catch (IllegalArgumentException e) {
                log.warn("无效的消息类型: {}", messageType);
            }
        }

        return messages;
    }

    /**
     * 按日期范围过滤
     */
    private List<Message> filterByDateRange(List<Message> messages, String startDate, String endDate) {
        return messages.stream()
                .filter(msg -> {
                    Instant createdAt = msg.getCreatedAt();

                    if (StringUtils.hasText(startDate)) {
                        try {
                            Instant start = Instant.parse(startDate + "T00:00:00Z");
                            if (createdAt.isBefore(start)) {
                                return false;
                            }
                        } catch (Exception e) {
                            log.warn("无效的开始日期: {}", startDate);
                        }
                    }

                    if (StringUtils.hasText(endDate)) {
                        try {
                            Instant end = Instant.parse(endDate + "T23:59:59Z");
                            if (createdAt.isAfter(end)) {
                                return false;
                            }
                        } catch (Exception e) {
                            log.warn("无效的结束日期: {}", endDate);
                        }
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 生成导出文件
     */
    private String generateExportFile(List<Message> messages, Map<String, Object> params) {
        // 这里应该生成实际的Excel或CSV文件
        // 简化实现，返回一个模拟的下载URL
        String format = (String) params.getOrDefault("format", "xlsx");
        String filename = "messages_export_" + System.currentTimeMillis() + "." + format;

        // 实际实现中应该：
        // 1. 创建Excel/CSV文件
        // 2. 写入消息数据
        // 3. 上传到文件服务器
        // 4. 返回下载URL

        return "http://example.com/exports/" + filename;
    }

    @Override
    public List<Map<String, Object>> getMessageTrends(String startDate, String endDate) {
        log.info("获取消息趋势: startDate={}, endDate={}", startDate, endDate);
        try {
            // 参数验证
            if (!StringUtils.hasText(startDate) || !StringUtils.hasText(endDate)) {
                throw new BusinessException(MessageErrorCode.PARAMETER_INVALID);
            }

            // 解析日期
            Instant start = Instant.parse(startDate + "T00:00:00Z");
            Instant end = Instant.parse(endDate + "T23:59:59Z");

            // 查询每日消息统计
            List<Object[]> dailyStats = messageRepository.getMessageTrendsByDateRange(start, end);

            // 转换为结果格式
            List<Map<String, Object>> trends = new ArrayList<>();
            for (Object[] stat : dailyStats) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", stat[0].toString());
                trend.put("count", stat[1]);
                trends.add(trend);
            }

            return trends;
        } catch (Exception e) {
            log.error("获取消息趋势失败", e);
            throw new BusinessException(MessageErrorCode.SYSTEM_ERROR);
        }
    }

    // ==================== 审核和标记相关辅助方法 ====================

    /**
     * 记录审核日志
     */
    private void recordReviewLog(Message message, ReviewAction action, String reason) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("messageId", message.getId());
            logData.put("senderId", message.getSenderId());
            logData.put("conversationId", message.getConversationId());
            logData.put("action", action.name());
            logData.put("reason", reason);
            logData.put("reviewTime", Instant.now());

            // 记录到审核日志表或日志系统
            log.info("消息审核日志: {}", logData);

        } catch (Exception e) {
            log.error("记录审核日志失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 发送审核结果通知
     */
    private void sendReviewNotification(Message message, ReviewAction action, String reason) {
        try {
            // 构建通知消息
            NotificationDTO.SystemNotification notification = new NotificationDTO.SystemNotification();
            notification.setTargetId(message.getSenderId());
            notification.setTargetType("USER");
            notification.setTitle("消息审核结果");
            notification.setContent(buildReviewNotificationContent(action, reason));
            notification.setType("MESSAGE_REVIEW");
            notification.setData(Map.of(
                "messageId", message.getId(),
                "action", action.name(),
                "reason", reason
            ));

            // 发送通知
            notificationServiceClient.sendSystemNotification(notification);

        } catch (Exception e) {
            log.error("发送审核通知失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 构建审核通知内容
     */
    private String buildReviewNotificationContent(ReviewAction action, String reason) {
        switch (action) {
            case APPROVE:
                return "您的消息已通过审核";
            case REJECT:
                return "您的消息审核未通过，原因：" + (reason != null ? reason : "违反社区规定");
            case BLOCK:
                return "您的消息已被屏蔽，原因：" + (reason != null ? reason : "内容不当");
            case DELETE:
                return "您的消息已被删除，原因：" + (reason != null ? reason : "严重违规");
            default:
                return "您的消息审核状态已更新";
        }
    }

    /**
     * 发送屏蔽通知
     */
    private void sendBlockNotification(Message message, String reason) {
        try {
            NotificationDTO.SystemNotification notification = new NotificationDTO.SystemNotification();
            notification.setTargetId(message.getSenderId());
            notification.setTargetType("USER");
            notification.setTitle("消息屏蔽通知");
            notification.setContent("您的消息因违反社区规定已被屏蔽，原因：" + (reason != null ? reason : "内容不当"));
            notification.setType("MESSAGE_BLOCKED");
            notification.setData(Map.of(
                "messageId", message.getId(),
                "reason", reason
            ));

            notificationServiceClient.sendSystemNotification(notification);

        } catch (Exception e) {
            log.error("发送屏蔽通知失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 记录标记日志
     */
    private void recordMarkLog(Message message, MarkType markType, String reason) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("messageId", message.getId());
            logData.put("senderId", message.getSenderId());
            logData.put("conversationId", message.getConversationId());
            logData.put("markType", markType.name());
            logData.put("reason", reason);
            logData.put("markTime", Instant.now());

            // 记录到标记日志表或日志系统
            log.info("消息标记日志: {}", logData);

        } catch (Exception e) {
            log.error("记录标记日志失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 发送标记通知
     */
    private void sendMarkNotification(Message message, MarkType markType, String reason) {
        try {
            // 只有某些标记类型需要通知用户
            if (markType == MarkType.VIOLATION || markType == MarkType.SPAM) {
                NotificationDTO.SystemNotification notification = new NotificationDTO.SystemNotification();
                notification.setTargetId(message.getSenderId());
                notification.setTargetType("USER");
                notification.setTitle("消息标记通知");
                notification.setContent(buildMarkNotificationContent(markType, reason));
                notification.setType("MESSAGE_MARKED");
                notification.setData(Map.of(
                    "messageId", message.getId(),
                    "markType", markType.name(),
                    "reason", reason
                ));

                notificationServiceClient.sendSystemNotification(notification);
            }

        } catch (Exception e) {
            log.error("发送标记通知失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 构建标记通知内容
     */
    private String buildMarkNotificationContent(MarkType markType, String reason) {
        switch (markType) {
            case SPAM:
                return "您的消息已被标记为垃圾消息，原因：" + (reason != null ? reason : "内容不当");
            case VIOLATION:
                return "您的消息已被标记为违规消息，原因：" + (reason != null ? reason : "违反社区规定");
            case SENSITIVE:
                return "您的消息已被标记为敏感消息，请注意言辞";
            default:
                return "您的消息标记状态已更新";
        }
    }

    /**
     * 更新消息标记
     */
    private void updateMessageMark(Message message, String markType, String reason) {
        try {
            // 在extra字段中记录标记信息
            Map<String, Object> extra = parseExtra(message.getExtra());
            Map<String, Object> markInfo = new HashMap<>();
            markInfo.put("type", markType);
            markInfo.put("reason", reason);
            markInfo.put("markTime", Instant.now().toString());

            extra.put("mark", markInfo);

            // 更新消息
            message.setExtra(convertExtraToString(extra));
            messageRepository.save(message);

        } catch (Exception e) {
            log.error("更新消息标记失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 清除消息标记
     */
    private void clearMessageMark(Message message, String reason) {
        try {
            Map<String, Object> extra = parseExtra(message.getExtra());
            extra.remove("mark");

            message.setExtra(convertExtraToString(extra));
            messageRepository.save(message);

        } catch (Exception e) {
            log.error("清除消息标记失败: messageId={}", message.getId(), e);
        }
    }

    /**
     * 解析extra字段
     */
    private Map<String, Object> parseExtra(String extraStr) {
        if (!StringUtils.hasText(extraStr)) {
            return new HashMap<>();
        }

        try {
            // 这里应该使用JSON库解析，简化实现
            return new HashMap<>();
        } catch (Exception e) {
            log.warn("解析extra字段失败: {}", extraStr, e);
            return new HashMap<>();
        }
    }

    /**
     * 转换extra为字符串
     */
    private String convertExtraToString(Map<String, Object> extra) {
        if (extra == null || extra.isEmpty()) {
            return null;
        }

        try {
            // 这里应该使用JSON库序列化，简化实现
            return extra.toString();
        } catch (Exception e) {
            log.warn("转换extra为字符串失败: {}", extra, e);
            return null;
        }
    }
}