package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Wallet;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 钱包Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface WalletRepository extends R2dbcRepository<Wallet, Long> {

    /**
     * 根据用户ID查找钱包
     */
    @Query("SELECT * FROM wallets WHERE user_id = :userId")
    Mono<Wallet> findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找钱包（使用 SELECT FOR UPDATE 实现悲观锁）
     */
    @Query("SELECT * FROM wallets WHERE user_id = :userId FOR UPDATE")
    Mono<Wallet> findByUserIdWithLock(@Param("userId") Long userId);

    /**
     * 检查用户是否已有钱包
     */
    @Query("SELECT COUNT(*) > 0 FROM wallets WHERE user_id = :userId")
    Mono<Boolean> existsByUserId(@Param("userId") Long userId);

    /**
     * 根据状态查找钱包
     */
    @Query("SELECT * FROM wallets WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<Wallet> findByStatusOrderByCreatedAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找余额大于指定金额的钱包
     */
    @Query("SELECT * FROM wallets WHERE total_balance > :amount AND status = :status LIMIT :limit OFFSET :offset")
    Flux<Wallet> findByTotalBalanceGreaterThan(@Param("amount") BigDecimal amount,
                                              @Param("status") String status,
                                              @Param("limit") int limit,
                                              @Param("offset") long offset);

    /**
     * 查找冻结余额大于0的钱包
     */
    @Query("SELECT * FROM wallets WHERE frozen_balance > 0 AND status = :status LIMIT :limit OFFSET :offset")
    Flux<Wallet> findWalletsWithFrozenBalance(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 统计各状态钱包数量
     */
    @Query("SELECT status, COUNT(*) FROM wallets GROUP BY status")
    Flux<Object[]> countByStatus();

    /**
     * 统计总余额
     */
    @Query("SELECT SUM(total_balance) FROM wallets WHERE status = :status")
    Mono<BigDecimal> sumTotalBalanceByStatus(@Param("status") String status);

    /**
     * 统计总冻结余额
     */
    @Query("SELECT SUM(frozen_balance) FROM wallets WHERE status = :status")
    Mono<BigDecimal> sumFrozenBalanceByStatus(@Param("status") String status);

    /**
     * 查找密码被锁定的钱包
     */
    @Query("SELECT * FROM wallets WHERE password_locked_until > CURRENT_TIMESTAMP")
    Flux<Wallet> findPasswordLockedWallets();

    /**
     * 查找长时间未交易的钱包
     */
    @Query("SELECT * FROM wallets WHERE last_transaction_at < :cutoffDate OR last_transaction_at IS NULL LIMIT :limit OFFSET :offset")
    Flux<Wallet> findInactiveWallets(@Param("cutoffDate") Instant cutoffDate, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 批量重置日使用额度
     */
    @Modifying
    @Query("UPDATE wallets SET daily_used = 0")
    Mono<Integer> resetAllDailyUsed();

    /**
     * 批量重置月使用额度
     */
    @Modifying
    @Query("UPDATE wallets SET monthly_used = 0")
    Mono<Integer> resetAllMonthlyUsed();

    /**
     * 解锁过期的密码锁定
     */
    @Modifying
    @Query("UPDATE wallets SET password_locked_until = NULL, password_error_count = 0 WHERE password_locked_until < CURRENT_TIMESTAMP")
    Mono<Integer> unlockExpiredPasswordLocks();

    /**
     * 根据用户ID列表查找钱包
     */
    @Query("SELECT * FROM wallets WHERE user_id = ANY(:userIds)")
    Flux<Wallet> findByUserIdIn(@Param("userIds") Long[] userIds);

    /**
     * 查找需要同步的钱包（余额不一致）
     */
    @Query("SELECT * FROM wallets WHERE total_balance != (available_balance + frozen_balance)")
    Flux<Wallet> findWalletsNeedingSync();

    /**
     * 更新钱包余额
     */
    @Modifying
    @Query("UPDATE wallets SET available_balance = :availableBalance, frozen_balance = :frozenBalance, total_balance = :totalBalance, last_transaction_at = CURRENT_TIMESTAMP WHERE id = :id")
    Mono<Integer> updateBalance(@Param("id") Long id,
                     @Param("availableBalance") BigDecimal availableBalance,
                     @Param("frozenBalance") BigDecimal frozenBalance,
                     @Param("totalBalance") BigDecimal totalBalance);

    /**
     * 更新钱包状态
     */
    @Modifying
    @Query("UPDATE wallets SET status = :status WHERE id = :id")
    Mono<Integer> updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 批量更新钱包状态
     */
    @Modifying
    @Query("UPDATE wallets SET status = :status WHERE id = ANY(:ids)")
    Mono<Integer> batchUpdateStatus(@Param("ids") Long[] ids, @Param("status") String status);

    /**
     * 获取钱包统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as total_count, " +
           "SUM(total_balance) as total_balance, " +
           "SUM(available_balance) as total_available_balance, " +
           "SUM(frozen_balance) as total_frozen_balance, " +
           "AVG(total_balance) as avg_balance " +
           "FROM wallets WHERE status = :status")
    Mono<Object[]> getWalletStatistics(@Param("status") String status);

    /**
     * 查找余额异常的钱包
     */
    @Query("SELECT * FROM wallets WHERE available_balance < 0 OR frozen_balance < 0 OR total_balance < 0")
    Flux<Wallet> findAnomalousWallets();

    /**
     * 查找超过限额的钱包
     */
    @Query("SELECT * FROM wallets WHERE daily_used > daily_limit OR monthly_used > monthly_limit")
    Flux<Wallet> findWalletsExceedingLimits();
}
