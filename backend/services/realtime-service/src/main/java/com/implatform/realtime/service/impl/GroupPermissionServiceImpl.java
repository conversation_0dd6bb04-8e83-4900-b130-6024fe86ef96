package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.GroupErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.entity.GroupPermission;
import com.implatform.realtime.entity.GroupRole;
import com.implatform.realtime.repository.GroupPermissionRepository;
import com.implatform.realtime.repository.GroupRoleRepository;
import com.implatform.service.GroupPermissionService;
import com.implatform.service.GroupRoleService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群组权限服务实现
 * 提供权限检查、继承解析和批量操作功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(isolation = Isolation.READ_COMMITTED)
public class GroupPermissionServiceImpl implements GroupPermissionService {

    private final GroupPermissionRepository groupPermissionRepository;
    private final GroupRoleRepository groupRoleRepository;
    private final GroupRoleService groupRoleService;

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public GroupPermission grantPermission(@NotNull Long roleId, 
                                         @NotNull GroupPermission.PermissionAction permissionAction, 
                                         @NotNull Long grantedBy) {
        log.info("Granting permission {} to role {}", permissionAction, roleId);
        
        validateRoleExists(roleId);
        
        // 检查权限是否已存在
        Optional<GroupPermission> existingPermission = groupPermissionRepository
                .findByRoleIdAndPermissionAction(roleId, permissionAction);
        
        if (existingPermission.isPresent()) {
            GroupPermission permission = existingPermission.get();
            if (permission.getIsGranted()) {
                log.warn("Permission {} already granted to role {}", permissionAction, roleId);
                return permission;
            } else {
                // 重新授予权限
                permission.grant(grantedBy, "Permission re-granted");
                return groupPermissionRepository.save(permission);
            }
        }
        
        // 创建新权限
        GroupPermission permission = GroupPermission.builder()
                .roleId(roleId)
                .permissionAction(permissionAction)
                .permissionCategory(getPermissionCategory(permissionAction))
                .isGranted(true)
                .isInherited(false)
                .grantedBy(grantedBy)
                .grantedAt(Instant.now())
                .build();
        
        GroupPermission savedPermission = groupPermissionRepository.save(permission);
        log.info("Successfully granted permission {} to role {}", permissionAction, roleId);
        
        return savedPermission;
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public GroupPermission grantPermissionWithExpiry(@NotNull Long roleId, 
                                                    @NotNull GroupPermission.PermissionAction permissionAction,
                                                    @NotNull Instant expiresAt, @NotNull Long grantedBy) {
        log.info("Granting temporary permission {} to role {} until {}", permissionAction, roleId, expiresAt);
        
        GroupPermission permission = grantPermission(roleId, permissionAction, grantedBy);
        permission.setExpiresAt(expiresAt);
        
        GroupPermission savedPermission = groupPermissionRepository.save(permission);
        log.info("Successfully granted temporary permission {} to role {}", permissionAction, roleId);
        
        return savedPermission;
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public boolean revokePermission(@NotNull Long roleId, 
                                  @NotNull GroupPermission.PermissionAction permissionAction, 
                                  @NotNull Long revokedBy) {
        log.info("Revoking permission {} from role {}", permissionAction, roleId);
        
        Optional<GroupPermission> permission = groupPermissionRepository
                .findByRoleIdAndPermissionAction(roleId, permissionAction);
        
        if (permission.isEmpty() || !permission.get().getIsGranted()) {
            log.warn("Permission {} not found or already revoked for role {}", permissionAction, roleId);
            return false;
        }
        
        GroupPermission perm = permission.get();
        perm.revoke(revokedBy, "Permission revoked");
        groupPermissionRepository.save(perm);
        
        log.info("Successfully revoked permission {} from role {}", permissionAction, roleId);
        return true;
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public int batchGrantPermissions(@NotNull Long roleId, 
                                   @NotNull List<GroupPermission.PermissionAction> permissionActions,
                                   @NotNull Long grantedBy) {
        log.info("Batch granting {} permissions to role {}", permissionActions.size(), roleId);
        
        validateRoleExists(roleId);
        
        int granted = 0;
        for (GroupPermission.PermissionAction action : permissionActions) {
            try {
                grantPermission(roleId, action, grantedBy);
                granted++;
            } catch (Exception e) {
                log.warn("Failed to grant permission {} to role {}: {}", action, roleId, e.getMessage());
            }
        }
        
        log.info("Successfully granted {} out of {} permissions to role {}", granted, permissionActions.size(), roleId);
        return granted;
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public int batchRevokePermissions(@NotNull Long roleId, 
                                    @NotNull List<GroupPermission.PermissionAction> permissionActions,
                                    @NotNull Long revokedBy) {
        log.info("Batch revoking {} permissions from role {}", permissionActions.size(), roleId);
        
        int revoked = 0;
        for (GroupPermission.PermissionAction action : permissionActions) {
            if (revokePermission(roleId, action, revokedBy)) {
                revoked++;
            }
        }
        
        log.info("Successfully revoked {} out of {} permissions from role {}", revoked, permissionActions.size(), roleId);
        return revoked;
    }

    @Override
    @Transactional
    public int copyPermissions(@NotNull Long sourceRoleId, @NotNull Long targetRoleId, @NotNull Long copiedBy) {
        log.info("Copying permissions from role {} to role {}", sourceRoleId, targetRoleId);
        
        validateRoleExists(sourceRoleId);
        validateRoleExists(targetRoleId);
        
        int copied = groupPermissionRepository.copyPermissionsToRole(sourceRoleId, targetRoleId, copiedBy);
        
        // 清除目标角色的权限缓存
        refreshPermissionCache(targetRoleId);
        
        log.info("Successfully copied {} permissions from role {} to role {}", copied, sourceRoleId, targetRoleId);
        return copied;
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#roleId + '_has_' + #permissionAction")
    public boolean hasPermission(@NotNull Long roleId, @NotNull GroupPermission.PermissionAction permissionAction) {
        // 首先检查直接权限
        if (groupPermissionRepository.hasPermission(roleId, permissionAction)) {
            return true;
        }
        
        // 检查继承权限
        List<GroupRole> inheritanceChain = groupRoleService.getRoleInheritanceChain(roleId);
        for (GroupRole role : inheritanceChain) {
            if (!role.getId().equals(roleId) && 
                groupPermissionRepository.hasPermission(role.getId(), permissionAction)) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    @Cacheable(value = "userGroupPermissions", key = "#userId + '_' + #groupId + '_' + #permissionAction")
    public boolean hasGroupPermission(@NotNull Long userId, @NotNull Long groupId, 
                                    @NotNull GroupPermission.PermissionAction permissionAction) {
        // 这里需要获取用户在群组中的角色，然后检查权限
        // 由于这个方法需要与用户-群组关系服务集成，这里提供基本实现框架
        
        // TODO: 集成用户-群组关系服务获取用户角色
        // List<GroupRole> userRoles = getUserRolesInGroup(userId, groupId);
        
        // 临时实现：假设用户有默认成员角色
        Optional<GroupRole> memberRole = groupRoleService.getMemberRole(groupId);
        if (memberRole.isPresent()) {
            return hasPermission(memberRole.get().getId(), permissionAction);
        }
        
        return false;
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#roleId + '_effective'")
    public Set<GroupPermission.PermissionAction> getEffectivePermissions(@NotNull Long roleId) {
        Set<GroupPermission.PermissionAction> effectivePermissions = new HashSet<>();
        
        // 获取直接权限
        List<GroupPermission> directPermissions = getDirectPermissions(roleId);
        effectivePermissions.addAll(directPermissions.stream()
                .filter(GroupPermission::getIsGranted)
                .filter(p -> p.getExpiresAt() == null || p.getExpiresAt().isAfter(Instant.now()))
                .map(GroupPermission::getPermissionAction)
                .collect(Collectors.toSet()));
        
        // 获取继承权限
        List<GroupRole> inheritanceChain = groupRoleService.getRoleInheritanceChain(roleId);
        for (GroupRole role : inheritanceChain) {
            if (!role.getId().equals(roleId)) {
                List<GroupPermission> inheritedPermissions = getDirectPermissions(role.getId());
                effectivePermissions.addAll(inheritedPermissions.stream()
                        .filter(GroupPermission::getIsGranted)
                        .filter(p -> p.getExpiresAt() == null || p.getExpiresAt().isAfter(Instant.now()))
                        .map(GroupPermission::getPermissionAction)
                        .collect(Collectors.toSet()));
            }
        }
        
        return effectivePermissions;
    }

    @Override
    public Set<GroupPermission.PermissionAction> getUserGroupPermissions(@NotNull Long userId, @NotNull Long groupId) {
        // TODO: 集成用户-群组关系服务
        // 临时实现
        Optional<GroupRole> memberRole = groupRoleService.getMemberRole(groupId);
        if (memberRole.isPresent()) {
            return getEffectivePermissions(memberRole.get().getId());
        }
        
        return Collections.emptySet();
    }

    @Override
    public List<GroupPermission> getDirectPermissions(@NotNull Long roleId) {
        return groupPermissionRepository.findByRoleIdOrderByPermissionCategoryAscPermissionActionAsc(roleId);
    }

    @Override
    public List<GroupPermission> getInheritedPermissions(@NotNull Long roleId) {
        return groupPermissionRepository.findByRoleIdAndIsInheritedTrueOrderByPermissionCategoryAsc(roleId);
    }

    @Override
    public Map<GroupPermission.PermissionCategory, List<GroupPermission>> getPermissionsByCategory(@NotNull Long roleId) {
        List<GroupPermission> permissions = getDirectPermissions(roleId);
        return permissions.stream()
                .collect(Collectors.groupingBy(GroupPermission::getPermissionCategory));
    }

    @Override
    public List<GroupPermission> getTemporaryPermissions(@NotNull Long roleId) {
        return groupPermissionRepository.findTemporaryPermissions(roleId);
    }

    @Override
    @Transactional
    public int updateInheritedPermissions(@NotNull Long parentRoleId, @NotNull Long childRoleId) {
        log.info("Updating inherited permissions from parent {} to child {}", parentRoleId, childRoleId);
        
        int updated = groupPermissionRepository.updateInheritedPermissions(parentRoleId, childRoleId);
        
        // 清除子角色的权限缓存
        refreshPermissionCache(childRoleId);
        
        log.info("Updated {} inherited permissions for child role {}", updated, childRoleId);
        return updated;
    }

    @Override
    @Transactional
    public int removeInheritedPermissions(@NotNull Long childRoleId, @NotNull Long parentRoleId) {
        log.info("Removing inherited permissions from child {} (parent {})", childRoleId, parentRoleId);
        
        int removed = groupPermissionRepository.removeInheritedPermissions(childRoleId, parentRoleId);
        
        // 清除子角色的权限缓存
        refreshPermissionCache(childRoleId);
        
        log.info("Removed {} inherited permissions from child role {}", removed, childRoleId);
        return removed;
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public void refreshPermissionCache(@NotNull Long roleId) {
        log.debug("Refreshing permission cache for role {}", roleId);
        // 缓存会在下次访问时重新加载
    }

    @Override
    @CacheEvict(value = {"rolePermissions", "userGroupPermissions"}, allEntries = true)
    public void refreshGroupPermissionCache(@NotNull Long groupId) {
        log.debug("Refreshing group permission cache for group {}", groupId);
        // 清除所有相关缓存
    }

    /**
     * 验证角色是否存在
     */
    private void validateRoleExists(Long roleId) {
        if (!groupRoleRepository.existsById(roleId)) {
            throw BusinessException.of(GroupErrorCode.ROLE_NOT_FOUND);
        }
    }

    /**
     * 获取权限类别
     */
    private GroupPermission.PermissionCategory getPermissionCategory(GroupPermission.PermissionAction action) {
        // 根据权限动作确定类别
        String actionName = action.name();
        
        if (actionName.contains("MESSAGE")) {
            return GroupPermission.PermissionCategory.MESSAGE;
        } else if (actionName.contains("MEMBER") || actionName.contains("INVITE") || actionName.contains("KICK") || 
                   actionName.contains("BAN") || actionName.contains("MUTE")) {
            return GroupPermission.PermissionCategory.MEMBER;
        } else if (actionName.contains("ROLE")) {
            return GroupPermission.PermissionCategory.ROLE;
        } else if (actionName.contains("GROUP")) {
            return GroupPermission.PermissionCategory.GROUP;
        } else if (actionName.contains("FILE")) {
            return GroupPermission.PermissionCategory.FILE;
        } else if (actionName.contains("AUDIT") || actionName.contains("ANALYTICS") || actionName.contains("MODERATE")) {
            return GroupPermission.PermissionCategory.ADVANCED;
        } else {
            return GroupPermission.PermissionCategory.SYSTEM;
        }
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId")
    public boolean setPermissionExpiry(@NotNull Long roleId,
                                     @NotNull GroupPermission.PermissionAction permissionAction,
                                     Instant expiresAt, @NotNull Long modifiedBy) {
        log.info("Setting expiry for permission {} on role {}: expires={}", permissionAction, roleId, expiresAt);

        Optional<GroupPermission> permission = groupPermissionRepository
                .findByRoleIdAndPermissionAction(roleId, permissionAction);

        if (permission.isEmpty()) {
            log.warn("Permission {} not found for role {}", permissionAction, roleId);
            return false;
        }

        GroupPermission perm = permission.get();
        perm.setExpiresAt(expiresAt);
        perm.setLastModifiedBy(modifiedBy);
        perm.setModificationNotes("Permission expiry updated");

        groupPermissionRepository.save(perm);
        log.info("Successfully set expiry for permission {} on role {}", permissionAction, roleId);

        return true;
    }

    @Override
    @Transactional
    public int processExpiredPermissions() {
        log.info("Processing expired permissions");

        int processed = groupPermissionRepository.markExpiredPermissions(Instant.now());

        if (processed > 0) {
            log.info("Processed {} expired permissions", processed);
            // 清除所有权限缓存，因为权限状态已更改
            refreshGroupPermissionCache(null);
        }

        return processed;
    }

    @Override
    public Page<GroupPermission> getExpiringPermissions(@NotNull Instant expiryThreshold, Pageable pageable) {
        return groupPermissionRepository.findExpiringPermissions(expiryThreshold, pageable);
    }

    @Override
    public GroupPermissionService.PermissionStatistics getPermissionStatistics(@NotNull Long roleId) {
        Object[] stats = groupPermissionRepository.getPermissionStatistics(roleId);

        int totalPermissions = ((Number) stats[0]).intValue();
        int grantedPermissions = ((Number) stats[1]).intValue();
        int inheritedPermissions = ((Number) stats[2]).intValue();
        int expiredPermissions = ((Number) stats[3]).intValue();

        // 计算临时权限数量
        List<GroupPermission> temporaryPermissions = getTemporaryPermissions(roleId);
        int temporaryCount = temporaryPermissions.size();

        return new GroupPermissionService.PermissionStatistics(totalPermissions, grantedPermissions,
                inheritedPermissions, expiredPermissions, temporaryCount);
    }

    @Override
    public Map<GroupPermission.PermissionCategory, GroupPermissionService.PermissionCategoryStats> getPermissionCategoryStatistics(@NotNull Long roleId) {
        List<Object[]> stats = groupPermissionRepository.getPermissionCategoryStatistics(roleId);

        Map<GroupPermission.PermissionCategory, GroupPermissionService.PermissionCategoryStats> categoryStats = new HashMap<>();

        for (Object[] stat : stats) {
            GroupPermission.PermissionCategory category = (GroupPermission.PermissionCategory) stat[0];
            int totalPermissions = ((Number) stat[1]).intValue();
            int grantedPermissions = ((Number) stat[2]).intValue();

            categoryStats.put(category, new GroupPermissionService.PermissionCategoryStats(category, totalPermissions, grantedPermissions));
        }

        return categoryStats;
    }

    @Override
    public List<GroupPermissionService.PermissionUsageStats> getPermissionUsageStatistics() {
        List<Object[]> stats = groupPermissionRepository.getPermissionUsageStatistics();

        return stats.stream()
                .map(stat -> new GroupPermissionService.PermissionUsageStats(
                        (GroupPermission.PermissionAction) stat[0],
                        ((Number) stat[1]).longValue()))
                .collect(Collectors.toList());
    }

    @Override
    public GroupPermissionService.PermissionValidationResult validatePermissionConfiguration(@NotNull Long roleId) {
        log.info("Validating permission configuration for role {}", roleId);

        List<String> issues = new ArrayList<>();
        List<String> recommendations = new ArrayList<>();

        // 检查权限冲突
        List<GroupPermission.PermissionAction> conflicts = detectPermissionConflicts(roleId);
        if (!conflicts.isEmpty()) {
            issues.add(String.format("Found %d conflicting permissions", conflicts.size()));
        }

        // 检查过期权限
        List<GroupPermission> expiredPermissions = groupPermissionRepository
                .findExpiredPermissions(Instant.now(), Pageable.unpaged()).getContent();
        if (!expiredPermissions.isEmpty()) {
            issues.add(String.format("Found %d expired permissions that need cleanup", expiredPermissions.size()));
        }

        // 检查权限完整性
        Set<GroupPermission.PermissionAction> effectivePermissions = getEffectivePermissions(roleId);
        if (effectivePermissions.isEmpty()) {
            recommendations.add("Role has no permissions, consider granting basic permissions");
        }

        // 检查高风险权限组合
        if (effectivePermissions.contains(GroupPermission.PermissionAction.DELETE_GROUP) &&
            effectivePermissions.contains(GroupPermission.PermissionAction.TRANSFER_OWNERSHIP)) {
            recommendations.add("Role has both DELETE_GROUP and TRANSFER_OWNERSHIP permissions, consider review");
        }

        boolean isValid = issues.isEmpty();

        log.info("Permission validation for role {} completed: valid={}, issues={}, recommendations={}",
                roleId, isValid, issues.size(), recommendations.size());

        return new GroupPermissionService.PermissionValidationResult(isValid, issues, recommendations);
    }

    @Override
    public List<GroupPermission.PermissionAction> detectPermissionConflicts(@NotNull Long roleId) {
        return groupPermissionRepository.findConflictingPermissions(roleId);
    }

    @Override
    public GroupPermissionService.GroupPermissionMatrix getGroupPermissionMatrix(@NotNull Long groupId) {
        log.info("Building permission matrix for group {}", groupId);

        List<Object[]> matrixData = groupPermissionRepository.getGroupPermissionMatrix(groupId);

        Map<Long, String> roleNames = new HashMap<>();
        Map<Long, Map<GroupPermission.PermissionAction, Boolean>> permissionMatrix = new HashMap<>();

        for (Object[] row : matrixData) {
            Long roleId = (Long) row[0];
            String roleName = (String) row[1];
            GroupPermission.PermissionAction action = (GroupPermission.PermissionAction) row[2];
            Boolean isGranted = (Boolean) row[3];

            roleNames.put(roleId, roleName);

            permissionMatrix.computeIfAbsent(roleId, k -> new HashMap<>())
                    .put(action, isGranted);
        }

        return new GroupPermissionService.GroupPermissionMatrix(groupId, roleNames, permissionMatrix);
    }

    @Override
    public String exportPermissionConfiguration(@NotNull Long roleId) {
        log.info("Exporting permission configuration for role {}", roleId);

        List<GroupPermission> permissions = getDirectPermissions(roleId);

        // 构建JSON配置（这里简化实现，实际应使用JSON库）
        StringBuilder json = new StringBuilder();
        json.append("{\"roleId\":").append(roleId).append(",\"permissions\":[");

        for (int i = 0; i < permissions.size(); i++) {
            GroupPermission permission = permissions.get(i);
            if (i > 0) json.append(",");

            json.append("{")
                .append("\"action\":\"").append(permission.getPermissionAction()).append("\",")
                .append("\"category\":\"").append(permission.getPermissionCategory()).append("\",")
                .append("\"granted\":").append(permission.getIsGranted()).append(",")
                .append("\"inherited\":").append(permission.getIsInherited());

            if (permission.getExpiresAt() != null) {
                json.append(",\"expiresAt\":\"").append(permission.getExpiresAt()).append("\"");
            }

            json.append("}");
        }

        json.append("]}");

        log.info("Exported permission configuration for role {}: {} permissions", roleId, permissions.size());
        return json.toString();
    }

    @Override
    @Transactional
    public int importPermissionConfiguration(@NotNull Long roleId, @NotNull String configurationJson,
                                           @NotNull Long importedBy) {
        log.info("Importing permission configuration for role {}", roleId);

        // 这里应该解析JSON并导入权限
        // 简化实现，实际应使用JSON解析库

        // TODO: 实现JSON解析和权限导入逻辑

        log.warn("Permission import not fully implemented yet for role {}", roleId);
        return 0;
    }
}
