package com.implatform.realtime.service;

import com.implatform.realtime.entity.GroupSettings;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 群组设置服务接口
 * 提供群组配置管理、验证和优化建议功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface GroupSettingsService {

    /**
     * 创建群组默认设置
     * 
     * @param groupId 群组ID
     * @return 群组设置
     */
    GroupSettings createDefaultSettings(Long groupId);

    /**
     * 获取群组设置
     * 
     * @param groupId 群组ID
     * @return 群组设置
     */
    Optional<GroupSettings> getGroupSettings(Long groupId);

    /**
     * 更新基本设置
     * 
     * @param groupId 群组ID
     * @param memberLimit 成员数量限制
     * @param joinApprovalRequired 是否需要加群审批
     * @param allowMemberInvite 是否允许成员邀请
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateBasicSettings(Long groupId, Integer memberLimit, Boolean joinApprovalRequired,
                                    Boolean allowMemberInvite, Long modifierId);

    /**
     * 更新消息设置
     * 
     * @param groupId 群组ID
     * @param messageModerationEnabled 是否启用消息审核
     * @param messageModerationMode 消息审核模式
     * @param messageRecallEnabled 是否启用消息撤回
     * @param messageRecallTimeLimit 消息撤回时间限制
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateMessageSettings(Long groupId, Boolean messageModerationEnabled,
                                      GroupSettings.ModerationMode messageModerationMode,
                                      Boolean messageRecallEnabled, Integer messageRecallTimeLimit,
                                      Long modifierId);

    /**
     * 更新文件分享设置
     * 
     * @param groupId 群组ID
     * @param fileSharingEnabled 是否启用文件分享
     * @param fileSizeLimitMb 文件大小限制（MB）
     * @param allowedFileTypes 允许的文件类型
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateFileSharingSettings(Long groupId, Boolean fileSharingEnabled,
                                           Integer fileSizeLimitMb, String allowedFileTypes,
                                           Long modifierId);

    /**
     * 更新权限设置
     * 
     * @param groupId 群组ID
     * @param mentionAllEnabled 是否启用@全体成员
     * @param mentionAllPermissionLevel @全体成员权限级别
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updatePermissionSettings(Long groupId, Boolean mentionAllEnabled,
                                         Integer mentionAllPermissionLevel, Long modifierId);

    /**
     * 更新机器人设置
     * 
     * @param groupId 群组ID
     * @param botEnabled 是否启用机器人
     * @param botConfiguration 机器人配置
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateBotSettings(Long groupId, Boolean botEnabled, String botConfiguration,
                                  Long modifierId);

    /**
     * 更新通知设置
     * 
     * @param groupId 群组ID
     * @param notificationSetting 通知设置
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateNotificationSettings(Long groupId, GroupSettings.NotificationSetting notificationSetting,
                                            Long modifierId);

    /**
     * 更新加密设置
     * 
     * @param groupId 群组ID
     * @param messageEncryptionEnabled 是否启用消息加密
     * @param encryptionAlgorithm 加密算法
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateEncryptionSettings(Long groupId, Boolean messageEncryptionEnabled,
                                         String encryptionAlgorithm, Long modifierId);

    /**
     * 更新历史记录设置
     * 
     * @param groupId 群组ID
     * @param messageHistoryEnabled 是否启用消息历史
     * @param messageHistoryRetentionDays 消息历史保留天数
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateHistorySettings(Long groupId, Boolean messageHistoryEnabled,
                                       Integer messageHistoryRetentionDays, Long modifierId);

    /**
     * 更新敏感词过滤设置
     * 
     * @param groupId 群组ID
     * @param sensitiveWordFilterEnabled 是否启用敏感词过滤
     * @param sensitiveWordAction 敏感词处理方式
     * @param customSensitiveWords 自定义敏感词列表
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateSensitiveWordSettings(Long groupId, Boolean sensitiveWordFilterEnabled,
                                             GroupSettings.SensitiveWordAction sensitiveWordAction,
                                             String customSensitiveWords, Long modifierId);

    /**
     * 更新防刷屏设置
     * 
     * @param groupId 群组ID
     * @param antiSpamEnabled 是否启用防刷屏
     * @param antiSpamTimeWindow 防刷屏时间窗口
     * @param antiSpamMessageLimit 防刷屏消息数量限制
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateAntiSpamSettings(Long groupId, Boolean antiSpamEnabled,
                                        Integer antiSpamTimeWindow, Integer antiSpamMessageLimit,
                                        Long modifierId);

    /**
     * 更新可见性设置
     * 
     * @param groupId 群组ID
     * @param groupVisibility 群组可见性
     * @param searchable 是否可搜索
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateVisibilitySettings(Long groupId, GroupSettings.GroupVisibility groupVisibility,
                                          Boolean searchable, Long modifierId);

    /**
     * 更新群组标签
     * 
     * @param groupId 群组ID
     * @param groupTags 群组标签
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateGroupTags(Long groupId, String groupTags, Long modifierId);

    /**
     * 更新自定义设置
     * 
     * @param groupId 群组ID
     * @param customSettings 自定义设置
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings updateCustomSettings(Long groupId, String customSettings, Long modifierId);

    /**
     * 检查文件类型是否允许
     * 
     * @param groupId 群组ID
     * @param fileType 文件类型
     * @return 是否允许
     */
    boolean isFileTypeAllowed(Long groupId, String fileType);

    /**
     * 检查文件大小是否符合限制
     * 
     * @param groupId 群组ID
     * @param fileSizeBytes 文件大小（字节）
     * @return 是否符合限制
     */
    boolean isFileSizeValid(Long groupId, long fileSizeBytes);

    /**
     * 检查是否启用了审核
     * 
     * @param groupId 群组ID
     * @return 是否启用审核
     */
    boolean isModerationEnabled(Long groupId);

    /**
     * 验证设置配置的合理性
     * 
     * @param groupId 群组ID
     * @return 验证结果
     */
    SettingsValidationResult validateSettings(Long groupId);

    /**
     * 获取设置优化建议
     * 
     * @param groupId 群组ID
     * @return 优化建议列表
     */
    List<SettingsOptimizationSuggestion> getOptimizationSuggestions(Long groupId);

    /**
     * 应用推荐设置
     * 
     * @param groupId 群组ID
     * @param settingsTemplate 设置模板
     * @param modifierId 修改人ID
     * @return 更新后的设置
     */
    GroupSettings applyRecommendedSettings(Long groupId, SettingsTemplate settingsTemplate, Long modifierId);

    /**
     * 重置为默认设置
     * 
     * @param groupId 群组ID
     * @param modifierId 修改人ID
     * @return 重置后的设置
     */
    GroupSettings resetToDefaultSettings(Long groupId, Long modifierId);

    /**
     * 导出设置配置
     * 
     * @param groupId 群组ID
     * @return 设置配置JSON
     */
    String exportSettings(Long groupId);

    /**
     * 导入设置配置
     * 
     * @param groupId 群组ID
     * @param settingsJson 设置配置JSON
     * @param modifierId 修改人ID
     * @return 导入后的设置
     */
    GroupSettings importSettings(Long groupId, String settingsJson, Long modifierId);

    /**
     * 获取设置统计信息
     * 
     * @return 设置统计信息
     */
    SettingsStatistics getSettingsStatistics();

    /**
     * 获取可见性分布统计
     * 
     * @return 可见性分布
     */
    Map<GroupSettings.GroupVisibility, Long> getVisibilityDistribution();

    /**
     * 获取审核模式分布统计
     * 
     * @return 审核模式分布
     */
    Map<GroupSettings.ModerationMode, Long> getModerationModeDistribution();

    /**
     * 获取通知设置分布统计
     * 
     * @return 通知设置分布
     */
    Map<GroupSettings.NotificationSetting, Long> getNotificationSettingDistribution();

    /**
     * 获取成员限制分布统计
     * 
     * @return 成员限制分布
     */
    MemberLimitDistribution getMemberLimitDistribution();

    /**
     * 获取文件大小限制分布统计
     * 
     * @return 文件大小限制分布
     */
    FileSizeLimitDistribution getFileSizeLimitDistribution();

    /**
     * 获取高级功能使用率
     * 
     * @return 高级功能使用率
     */
    AdvancedFeatureUsageRates getAdvancedFeatureUsageRates();

    /**
     * 查询需要配置优化的群组
     * 
     * @return 需要优化的群组设置列表
     */
    List<GroupSettings> getGroupsNeedingOptimization();

    /**
     * 查询安全配置较弱的群组
     * 
     * @return 安全配置弱的群组设置列表
     */
    List<GroupSettings> getGroupsWithWeakSecurity();

    /**
     * 查询有配置问题的群组
     * 
     * @return 有配置问题的群组列表
     */
    List<GroupSettings> getGroupsWithConfigurationIssues();

    /**
     * 批量更新成员限制
     * 
     * @param groupIds 群组ID列表
     * @param memberLimit 新的成员限制
     * @param modifierId 修改人ID
     * @return 更新的群组数量
     */
    int batchUpdateMemberLimit(List<Long> groupIds, Integer memberLimit, Long modifierId);

    /**
     * 批量更新文件分享设置
     * 
     * @param groupIds 群组ID列表
     * @param fileSharingEnabled 是否启用文件分享
     * @param modifierId 修改人ID
     * @return 更新的群组数量
     */
    int batchUpdateFileSharingSetting(List<Long> groupIds, Boolean fileSharingEnabled, Long modifierId);

    /**
     * 批量更新消息审核设置
     * 
     * @param groupIds 群组ID列表
     * @param moderationEnabled 是否启用审核
     * @param moderationMode 审核模式
     * @param modifierId 修改人ID
     * @return 更新的群组数量
     */
    int batchUpdateModerationSettings(List<Long> groupIds, Boolean moderationEnabled,
                                    GroupSettings.ModerationMode moderationMode, Long modifierId);

    /**
     * 删除群组设置
     * 
     * @param groupId 群组ID
     * @return 是否成功删除
     */
    boolean deleteGroupSettings(Long groupId);

    /**
     * 设置验证结果
     */
    class SettingsValidationResult {
        private final boolean isValid;
        private final List<String> issues;
        private final List<String> warnings;

        public SettingsValidationResult(boolean isValid, List<String> issues, List<String> warnings) {
            this.isValid = isValid;
            this.issues = issues;
            this.warnings = warnings;
        }

        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getIssues() { return issues; }
        public List<String> getWarnings() { return warnings; }
    }

    /**
     * 设置优化建议
     */
    class SettingsOptimizationSuggestion {
        private final String category;
        private final String suggestion;
        private final String reason;
        private final String priority;

        public SettingsOptimizationSuggestion(String category, String suggestion, String reason, String priority) {
            this.category = category;
            this.suggestion = suggestion;
            this.reason = reason;
            this.priority = priority;
        }

        // Getters
        public String getCategory() { return category; }
        public String getSuggestion() { return suggestion; }
        public String getReason() { return reason; }
        public String getPriority() { return priority; }
    }

    /**
     * 设置模板枚举
     */
    enum SettingsTemplate {
        SMALL_GROUP("小型群组", "适合50人以下的小型群组"),
        MEDIUM_GROUP("中型群组", "适合50-200人的中型群组"),
        LARGE_GROUP("大型群组", "适合200-500人的大型群组"),
        ENTERPRISE_GROUP("企业群组", "适合企业内部使用的群组"),
        PUBLIC_GROUP("公开群组", "适合公开讨论的群组"),
        PRIVATE_GROUP("私密群组", "适合私密讨论的群组"),
        STUDY_GROUP("学习群组", "适合学习交流的群组"),
        GAMING_GROUP("游戏群组", "适合游戏玩家的群组");

        private final String chineseName;
        private final String description;

        SettingsTemplate(String chineseName, String description) {
            this.chineseName = chineseName;
            this.description = description;
        }

        public String getChineseName() { return chineseName; }
        public String getDescription() { return description; }
    }

    /**
     * 设置统计信息
     */
    class SettingsStatistics {
        private final long totalGroups;
        private final long groupsWithApproval;
        private final long groupsWithModeration;
        private final long groupsWithFileSharing;
        private final long groupsWithBots;

        public SettingsStatistics(long totalGroups, long groupsWithApproval, long groupsWithModeration,
                                long groupsWithFileSharing, long groupsWithBots) {
            this.totalGroups = totalGroups;
            this.groupsWithApproval = groupsWithApproval;
            this.groupsWithModeration = groupsWithModeration;
            this.groupsWithFileSharing = groupsWithFileSharing;
            this.groupsWithBots = groupsWithBots;
        }

        // Getters
        public long getTotalGroups() { return totalGroups; }
        public long getGroupsWithApproval() { return groupsWithApproval; }
        public long getGroupsWithModeration() { return groupsWithModeration; }
        public long getGroupsWithFileSharing() { return groupsWithFileSharing; }
        public long getGroupsWithBots() { return groupsWithBots; }
    }

    /**
     * 成员限制分布
     */
    class MemberLimitDistribution {
        private final long small;    // <= 50
        private final long medium;   // 51-200
        private final long large;    // 201-500
        private final long xlarge;   // > 500

        public MemberLimitDistribution(long small, long medium, long large, long xlarge) {
            this.small = small;
            this.medium = medium;
            this.large = large;
            this.xlarge = xlarge;
        }

        // Getters
        public long getSmall() { return small; }
        public long getMedium() { return medium; }
        public long getLarge() { return large; }
        public long getXlarge() { return xlarge; }
    }

    /**
     * 文件大小限制分布
     */
    class FileSizeLimitDistribution {
        private final long small;    // <= 10MB
        private final long medium;   // 11-50MB
        private final long large;    // 51-100MB
        private final long xlarge;   // > 100MB

        public FileSizeLimitDistribution(long small, long medium, long large, long xlarge) {
            this.small = small;
            this.medium = medium;
            this.large = large;
            this.xlarge = xlarge;
        }

        // Getters
        public long getSmall() { return small; }
        public long getMedium() { return medium; }
        public long getLarge() { return large; }
        public long getXlarge() { return xlarge; }
    }

    /**
     * 高级功能使用率
     */
    class AdvancedFeatureUsageRates {
        private final double encryptionRate;
        private final double botRate;
        private final double moderationRate;

        public AdvancedFeatureUsageRates(double encryptionRate, double botRate, double moderationRate) {
            this.encryptionRate = encryptionRate;
            this.botRate = botRate;
            this.moderationRate = moderationRate;
        }

        // Getters
        public double getEncryptionRate() { return encryptionRate; }
        public double getBotRate() { return botRate; }
        public double getModerationRate() { return moderationRate; }
    }
}
