package com.implatform.realtime.dto;

import com.implatform.realtime.entity.Payment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 支付相关DTO
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public class PaymentDTO {

    /**
     * 支付信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentInfo {
        private Long id;
        private String paymentOrderNo;
        private String externalTransactionId;
        private Long userId;
        private BigDecimal amount;
        private String currency;
        private Payment.PaymentMethod paymentMethod;
        private String paymentMethodDescription;
        private Payment.PaymentProvider paymentProvider;
        private String paymentProviderDescription;
        private Payment.PaymentStatus status;
        private String statusDescription;
        private Payment.PaymentType paymentType;
        private String paymentTypeDescription;
        private Long businessOrderId;
        private String businessType;
        private String description;
        private Instant paidAt;
        private Instant expiresAt;
        private BigDecimal refundedAmount;
        private BigDecimal feeAmount;
        private BigDecimal actualAmount;
        private String errorMessage;
        private Instant createdAt;
        private Instant updatedAt;

        public static PaymentInfo fromEntity(Payment payment) {
            return PaymentInfo.builder()
                    .id(payment.getId())
                    .paymentOrderNo(payment.getPaymentOrderNo())
                    .externalTransactionId(payment.getExternalTransactionId())
                    .userId(payment.getUserId())
                    .amount(payment.getAmount())
                    .currency(payment.getCurrency())
                    .paymentMethod(payment.getPaymentMethod())
                    .paymentMethodDescription(payment.getPaymentMethod().getDescription())
                    .paymentProvider(payment.getPaymentProvider())
                    .paymentProviderDescription(payment.getPaymentProvider().getDescription())
                    .status(payment.getStatus())
                    .statusDescription(payment.getStatus().getDescription())
                    .paymentType(payment.getPaymentType())
                    .paymentTypeDescription(payment.getPaymentType().getDescription())
                    .businessOrderId(payment.getBusinessOrderId())
                    .businessType(payment.getBusinessType())
                    .description(payment.getDescription())
                    .paidAt(payment.getPaidAt())
                    .expiresAt(payment.getExpiresAt())
                    .refundedAmount(payment.getRefundedAmount())
                    .feeAmount(payment.getFeeAmount())
                    .actualAmount(payment.getActualAmount())
                    .errorMessage(payment.getErrorMessage())
                    .createdAt(payment.getCreatedAt())
                    .updatedAt(payment.getUpdatedAt())
                    .build();
        }
    }

    /**
     * 创建支付请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreatePaymentRequest {
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @NotNull(message = "支付金额不能为空")
        @DecimalMin(value = "0.01", message = "支付金额必须大于0")
        private BigDecimal amount;

        @NotBlank(message = "货币类型不能为空")
        private String currency = "CNY";

        @NotNull(message = "支付方式不能为空")
        private Payment.PaymentMethod paymentMethod;

        @NotNull(message = "支付提供商不能为空")
        private Payment.PaymentProvider paymentProvider;

        @NotNull(message = "支付类型不能为空")
        private Payment.PaymentType paymentType;

        private Long businessOrderId;
        private String businessType;

        @Size(max = 500, message = "描述长度不能超过500字符")
        private String description;

        private String notifyUrl;
        private String returnUrl;
        private Integer expirationMinutes = 30; // 默认30分钟过期
        private Map<String, Object> extraData;
    }

    /**
     * 处理支付请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessPaymentRequest {
        private String paymentCode; // 支付码或授权码
        private String authCode; // 授权码
        private Map<String, Object> paymentData; // 支付相关数据
        private String clientIp;
        private String userAgent;
    }

    /**
     * 支付结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentResult {
        private boolean success;
        private String message;
        private String paymentOrderNo;
        private String paymentUrl; // 支付跳转URL
        private String qrCode; // 二维码内容
        private Map<String, Object> extraData;
    }

    /**
     * 退款信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundInfo {
        private Long id;
        private String refundNo;
        private String paymentOrderNo;
        private BigDecimal refundAmount;
        private String reason;
        private String status;
        private String statusDescription;
        private Instant refundedAt;
        private Instant createdAt;
    }

    /**
     * 退款请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundRequest {
        @NotNull(message = "退款金额不能为空")
        @DecimalMin(value = "0.01", message = "退款金额必须大于0")
        private BigDecimal refundAmount;

        @NotBlank(message = "退款原因不能为空")
        @Size(max = 500, message = "退款原因长度不能超过500字符")
        private String reason;

        private String notifyUrl;
    }

    /**
     * 处理退款请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessRefundRequest {
        private boolean approved;
        private String reason;
        private String operatorId;
    }

    /**
     * 支付查询请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentQueryRequest {
        private Long userId;
        private Payment.PaymentStatus status;
        private Payment.PaymentMethod paymentMethod;
        private Payment.PaymentProvider paymentProvider;
        private Payment.PaymentType paymentType;
        private String businessType;
        private Instant startDate;
        private Instant endDate;
        private BigDecimal minAmount;
        private BigDecimal maxAmount;
        private String keyword; // 搜索关键词
    }

    /**
     * 支付统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentStatistics {
        private Long totalCount;
        private BigDecimal totalAmount;
        private Long successCount;
        private BigDecimal successAmount;
        private Long failedCount;
        private BigDecimal failedAmount;
        private BigDecimal successRate;
        private BigDecimal totalFee;
        private BigDecimal totalRefunded;
    }

    /**
     * 用户支付统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPaymentStatistics {
        private Long userId;
        private Long totalPayments;
        private BigDecimal totalAmount;
        private BigDecimal totalRefunded;
        private Instant firstPaymentAt;
        private Instant lastPaymentAt;
        private Payment.PaymentMethod preferredMethod;
    }

    /**
     * 支付方式统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentMethodStatistics {
        private Payment.PaymentMethod method;
        private String methodDescription;
        private Long count;
        private BigDecimal amount;
        private BigDecimal percentage;
    }

    /**
     * 每日统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyStatistics {
        private String date;
        private Long count;
        private BigDecimal amount;
    }

    /**
     * 月度统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyStatistics {
        private String month;
        private Long count;
        private BigDecimal amount;
    }

    /**
     * 验证结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private String message;
        private List<String> errors;
    }

    /**
     * 风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        private String riskLevel; // LOW, MEDIUM, HIGH
        private Integer riskScore; // 0-100
        private List<String> riskFactors;
        private boolean requiresManualReview;
    }

    /**
     * 支付限额
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentLimits {
        private BigDecimal singleLimit;
        private BigDecimal dailyLimit;
        private BigDecimal monthlyLimit;
        private BigDecimal remainingDaily;
        private BigDecimal remainingMonthly;
    }

    /**
     * 支付配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentConfig {
        private Payment.PaymentProvider provider;
        private boolean enabled;
        private String appId;
        private String secretKey;
        private String apiUrl;
        private BigDecimal feeRate;
        private Map<String, Object> extraConfig;
    }

    /**
     * 配置测试结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfigTestResult {
        private boolean success;
        private String message;
        private Long responseTime;
    }

    /**
     * 财务报表
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FinancialReport {
        private Instant startDate;
        private Instant endDate;
        private BigDecimal totalRevenue;
        private BigDecimal totalFee;
        private BigDecimal netRevenue;
        private Long totalTransactions;
        private List<PaymentMethodStatistics> methodBreakdown;
        private List<DailyStatistics> dailyTrend;
    }

    /**
     * 导出请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportRequest {
        private Instant startDate;
        private Instant endDate;
        private Payment.PaymentStatus status;
        private Payment.PaymentMethod method;
        private String format = "CSV"; // CSV, EXCEL
    }

    /**
     * 收入趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RevenueTrend {
        private String period;
        private BigDecimal revenue;
        private Long transactionCount;
        private BigDecimal growthRate;
    }

    /**
     * 批量处理结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchProcessResult {
        private int totalCount;
        private int successCount;
        private int failedCount;
        private List<String> failedItems;
        private String message;
    }

    /**
     * 批量退款结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchRefundResult {
        private int totalCount;
        private int successCount;
        private int failedCount;
        private BigDecimal totalRefundAmount;
        private List<String> failedItems;
    }

    /**
     * 数据一致性检查结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConsistencyCheckResult {
        private boolean consistent;
        private List<String> issues;
        private int fixedCount;
        private Instant checkTime;
    }
}
