package com.implatform.realtime.service;

import com.implatform.realtime.dto.checkin.CoinAccountDTO;
import com.implatform.realtime.entity.CoinAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 金币账户服务接口
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface CoinAccountService {

    // ==================== 账户管理 ====================

    /**
     * 创建金币账户
     */
    @Transactional
    CoinAccount createAccount(Long userId);

    /**
     * 获取用户金币账户
     */
    Optional<CoinAccount> getAccount(Long userId);

    /**
     * 获取或创建用户金币账户
     */
    CoinAccount getOrCreateAccount(Long userId);

    /**
     * 冻结账户
     */
    @Transactional
    void freezeAccount(Long userId, String reason, String operatorId);

    /**
     * 解冻账户
     */
    @Transactional
    void unfreezeAccount(Long userId, String operatorId);

    /**
     * 删除账户
     */
    @Transactional
    void deleteAccount(Long userId, String operatorId);

    // ==================== 余额操作 ====================

    /**
     * 增加金币
     */
    @Transactional
    CoinAccount addCoins(Long userId, BigDecimal amount, String source, String description);

    /**
     * 扣除金币
     */
    @Transactional
    CoinAccount deductCoins(Long userId, BigDecimal amount, String reason, String description);

    /**
     * 转账金币
     */
    @Transactional
    void transferCoins(Long fromUserId, Long toUserId, BigDecimal amount, String description);

    /**
     * 检查余额是否充足
     */
    boolean hasSufficientBalance(Long userId, BigDecimal amount);

    /**
     * 获取账户余额
     */
    BigDecimal getBalance(Long userId);

    /**
     * 获取冻结余额
     */
    BigDecimal getFrozenBalance(Long userId);

    /**
     * 获取可用余额
     */
    BigDecimal getAvailableBalance(Long userId);

    // ==================== 账户查询 ====================

    /**
     * 分页查询账户列表
     */
    Page<CoinAccount> getAccounts(CoinAccount.AccountStatus status, Pageable pageable);

    /**
     * 搜索账户
     */
    Page<CoinAccount> searchAccounts(String keyword, Pageable pageable);

    /**
     * 获取余额排行榜
     */
    List<CoinAccount> getBalanceRanking(int limit);

    /**
     * 获取活跃账户
     */
    List<CoinAccount> getActiveAccounts(int days, int limit);

    // ==================== 统计分析 ====================

    /**
     * 获取异常账户
     */
    List<CoinAccount> getAbnormalAccounts();

    /**
     * 标记可疑账户
     */
    @Transactional
    void markSuspiciousAccount(Long userId, String reason, String operatorId);

    /**
     * 解除可疑标记
     */
    @Transactional
    void unmarkSuspiciousAccount(Long userId, String operatorId);

    // ==================== 批量操作 ====================

    /**
     * 批量创建账户
     */
    @Transactional
    List<CoinAccount> batchCreateAccounts(List<Long> userIds);

    /**
     * 批量增加金币
     */
    @Transactional
    void batchAddCoins(List<Long> userIds, BigDecimal amount, String source, String description);

    /**
     * 批量扣除金币
     */
    @Transactional
    void batchDeductCoins(List<Long> userIds, BigDecimal amount, String reason, String description);

    // ==================== 维护清理 ====================

    /**
     * 清理零余额账户
     */
    @Transactional
    int cleanupZeroBalanceAccounts(int days);

    /**
     * 修复账户数据
     */
    @Transactional
    void repairAccountData(Long userId);

    /**
     * 重新计算账户余额
     */
    @Transactional
    void recalculateBalance(Long userId);

    /**
     * 同步账户状态
     */
    @Transactional
    void syncAccountStatus();
}
