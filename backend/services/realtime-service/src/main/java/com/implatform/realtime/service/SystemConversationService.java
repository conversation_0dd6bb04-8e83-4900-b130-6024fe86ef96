package com.implatform.realtime.service;

import com.implatform.realtime.entity.Conversation;

/**
 * 系统对话管理服务接口
 * 
 * 负责管理系统通知对话的创建、维护和管理
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface SystemConversationService {
    
    /**
     * 系统通知用户ID
     */
    Long SYSTEM_NOTIFICATION_USER_ID = 1000000L;
    
    /**
     * 系统通知对话标题
     */
    String SYSTEM_NOTIFICATION_TITLE = "系统通知";
    
    /**
     * 获取或创建用户的系统通知对话
     * 如果不存在则自动创建
     * 
     * @param userId 用户ID
     * @return 系统通知对话
     */
    Conversation getOrCreateSystemNotificationConversation(Long userId);
    
    /**
     * 创建系统通知对话
     * 
     * @param userId 用户ID
     * @return 创建的系统通知对话
     */
    Conversation createSystemNotificationConversation(Long userId);
    
    /**
     * 检查用户是否已有系统通知对话
     * 
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean hasSystemNotificationConversation(Long userId);
    
    /**
     * 获取用户的系统通知对话
     * 
     * @param userId 用户ID
     * @return 系统通知对话，如果不存在返回null
     */
    Conversation getSystemNotificationConversation(Long userId);
    
    /**
     * 初始化用户的系统对话
     * 在用户首次登录时调用
     * 
     * @param userId 用户ID
     */
    void initializeUserSystemConversations(Long userId);
    
    /**
     * 更新系统对话的最后活动时间
     * 
     * @param conversationId 对话ID
     */
    void updateLastActivityTime(Long conversationId);
    
    /**
     * 检查对话是否为系统通知对话
     * 
     * @param conversationId 对话ID
     * @return 是否为系统通知对话
     */
    boolean isSystemNotificationConversation(Long conversationId);
    
    /**
     * 获取系统对话的未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    long getUnreadSystemMessageCount(Long userId);
}
