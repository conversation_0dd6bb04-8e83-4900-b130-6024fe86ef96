package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.GroupErrorCode;
import com.implatform.common.core.enums.UserErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.entity.GroupInvitation;
import com.implatform.realtime.repository.GroupInvitationRepository;
import com.implatform.realtime.service.GroupInvitationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.constraints.NotNull;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 群组邀请服务实现
 * 提供邀请码生成、邀请流程管理和统计分析功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(isolation = Isolation.READ_COMMITTED)
public class GroupInvitationServiceImpl implements GroupInvitationService {

    private final GroupInvitationRepository groupInvitationRepository;
    private final SecureRandom secureRandom = new SecureRandom();

    // 邀请码字符集（排除容易混淆的字符）
    private static final String INVITATION_CODE_CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final int INVITATION_CODE_LENGTH = 8;

    @Override
    public GroupInvitation createDirectInvitation(@NotNull Long groupId, @NotNull Long inviterId, 
                                                 @NotNull Long inviteeId, String message, Instant expiresAt) {
        log.info("Creating direct invitation for group {}: inviter={}, invitee={}", groupId, inviterId, inviteeId);
        
        // 验证参数
        validateInvitationParameters(groupId, inviterId);
        
        // 检查用户是否已被邀请
        if (isUserAlreadyInvited(groupId, inviteeId)) {
            throw new IllegalStateException(String.format("[GRP-INV-001] User %d already invited to group %d", 
                    inviteeId, groupId));
        }
        
        // 创建邀请
        GroupInvitation invitation = GroupInvitation.builder()
                .groupId(groupId)
                .inviterId(inviterId)
                .inviteeId(inviteeId)
                .invitationCode(generateUniqueInvitationCode())
                .invitationType(GroupInvitation.InvitationType.DIRECT)
                .status(GroupInvitation.InvitationStatus.PENDING)
                .invitationMessage(message)
                .expiresAt(expiresAt)
                .usageLimit(1)
                .usedCount(0)
                .requiresApproval(false)
                .build();
        
        GroupInvitation savedInvitation = groupInvitationRepository.save(invitation);
        log.info("Successfully created direct invitation {} for group {}", savedInvitation.getId(), groupId);
        
        return savedInvitation;
    }

    @Override
    public GroupInvitation createLinkInvitation(@NotNull Long groupId, @NotNull Long inviterId, 
                                               @NotNull Integer usageLimit, Instant expiresAt, Long targetRoleId) {
        log.info("Creating link invitation for group {}: inviter={}, usageLimit={}", groupId, inviterId, usageLimit);
        
        validateInvitationParameters(groupId, inviterId);
        
        if (usageLimit <= 0) {
            throw BusinessException.of(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
        
        GroupInvitation invitation = GroupInvitation.builder()
                .groupId(groupId)
                .inviterId(inviterId)
                .invitationCode(generateUniqueInvitationCode())
                .invitationType(GroupInvitation.InvitationType.LINK)
                .status(GroupInvitation.InvitationStatus.PENDING)
                .expiresAt(expiresAt)
                .usageLimit(usageLimit)
                .usedCount(0)
                .requiresApproval(false)
                .targetRoleId(targetRoleId)
                .build();
        
        GroupInvitation savedInvitation = groupInvitationRepository.save(invitation);
        log.info("Successfully created link invitation {} for group {}", savedInvitation.getId(), groupId);
        
        return savedInvitation;
    }

    @Override
    public GroupInvitation createQrCodeInvitation(@NotNull Long groupId, @NotNull Long inviterId, 
                                                 @NotNull Integer usageLimit, Instant expiresAt) {
        log.info("Creating QR code invitation for group {}: inviter={}, usageLimit={}", groupId, inviterId, usageLimit);
        
        validateInvitationParameters(groupId, inviterId);
        
        if (usageLimit <= 0) {
            throw BusinessException.of(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
        
        GroupInvitation invitation = GroupInvitation.builder()
                .groupId(groupId)
                .inviterId(inviterId)
                .invitationCode(generateUniqueInvitationCode())
                .invitationType(GroupInvitation.InvitationType.QR_CODE)
                .status(GroupInvitation.InvitationStatus.PENDING)
                .expiresAt(expiresAt)
                .usageLimit(usageLimit)
                .usedCount(0)
                .requiresApproval(false)
                .build();
        
        GroupInvitation savedInvitation = groupInvitationRepository.save(invitation);
        log.info("Successfully created QR code invitation {} for group {}", savedInvitation.getId(), groupId);
        
        return savedInvitation;
    }

    @Override
    @Transactional
    public List<GroupInvitation> createBatchInvitations(@NotNull Long groupId, @NotNull Long inviterId, 
                                                       @NotNull List<Long> inviteeIds, String message, Instant expiresAt) {
        log.info("Creating batch invitations for group {}: inviter={}, count={}", groupId, inviterId, inviteeIds.size());
        
        validateInvitationParameters(groupId, inviterId);
        
        if (inviteeIds.isEmpty()) {
            throw BusinessException.of(GroupErrorCode.INVALID_MEMBER_ID);
        }
        
        List<GroupInvitation> invitations = new ArrayList<>();
        
        for (Long inviteeId : inviteeIds) {
            try {
                // 跳过已被邀请的用户
                if (isUserAlreadyInvited(groupId, inviteeId)) {
                    log.warn("User {} already invited to group {}, skipping", inviteeId, groupId);
                    continue;
                }
                
                GroupInvitation invitation = GroupInvitation.builder()
                        .groupId(groupId)
                        .inviterId(inviterId)
                        .inviteeId(inviteeId)
                        .invitationCode(generateUniqueInvitationCode())
                        .invitationType(GroupInvitation.InvitationType.BATCH)
                        .status(GroupInvitation.InvitationStatus.PENDING)
                        .invitationMessage(message)
                        .expiresAt(expiresAt)
                        .usageLimit(1)
                        .usedCount(0)
                        .requiresApproval(false)
                        .build();
                
                invitations.add(groupInvitationRepository.save(invitation));
                
            } catch (Exception e) {
                log.warn("Failed to create invitation for user {} in group {}: {}", inviteeId, groupId, e.getMessage());
            }
        }
        
        log.info("Successfully created {} batch invitations for group {}", invitations.size(), groupId);
        return invitations;
    }

    @Override
    @Transactional
    public InvitationUseResult useInvitation(@NotNull String invitationCode, @NotNull Long userId, 
                                           String clientIp, String userAgent) {
        log.info("Using invitation code {} for user {}", invitationCode, userId);
        
        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findByInvitationCode(invitationCode);
        
        if (invitationOpt.isEmpty()) {
            return new InvitationUseResult(false, "Invitation code not found", null, null);
        }
        
        GroupInvitation invitation = invitationOpt.get();
        
        // 验证邀请状态
        if (!isInvitationValid(invitationCode)) {
            return new InvitationUseResult(false, "Invitation is not valid or has expired", invitation, invitation.getGroupId());
        }
        
        // 检查使用次数限制
        if (invitation.getUsedCount() >= invitation.getUsageLimit()) {
            return new InvitationUseResult(false, "Invitation usage limit exceeded", invitation, invitation.getGroupId());
        }
        
        // 对于直接邀请，检查是否为指定用户
        if (invitation.getInvitationType() == GroupInvitation.InvitationType.DIRECT &&
            invitation.getInviteeId() != null && !invitation.getInviteeId().equals(userId)) {
            return new InvitationUseResult(false, "This invitation is not for you", invitation, invitation.getGroupId());
        }
        
        // 更新邀请使用信息
        invitation.use(userId, clientIp, userAgent);
        groupInvitationRepository.save(invitation);
        
        log.info("Successfully used invitation {} for user {} to join group {}", 
                invitation.getId(), userId, invitation.getGroupId());
        
        return new InvitationUseResult(true, "Invitation used successfully", invitation, invitation.getGroupId());
    }

    @Override
    public boolean acceptInvitation(@NotNull Long invitationId, @NotNull Long userId) {
        log.info("Accepting invitation {} by user {}", invitationId, userId);
        
        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);
        
        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }
        
        GroupInvitation invitation = invitationOpt.get();
        
        // 验证用户权限
        if (invitation.getInviteeId() != null && !invitation.getInviteeId().equals(userId)) {
            log.warn("User {} not authorized to accept invitation {}", userId, invitationId);
            return false;
        }
        
        if (invitation.getStatus() != GroupInvitation.InvitationStatus.PENDING) {
            log.warn("Invitation {} is not in pending status", invitationId);
            return false;
        }
        
        invitation.accept();
        groupInvitationRepository.save(invitation);
        
        log.info("Successfully accepted invitation {} by user {}", invitationId, userId);
        return true;
    }

    @Override
    public boolean rejectInvitation(@NotNull Long invitationId, @NotNull Long userId, String reason) {
        log.info("Rejecting invitation {} by user {}: reason={}", invitationId, userId, reason);
        
        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);
        
        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }
        
        GroupInvitation invitation = invitationOpt.get();
        
        // 验证用户权限
        if (invitation.getInviteeId() != null && !invitation.getInviteeId().equals(userId)) {
            log.warn("User {} not authorized to reject invitation {}", userId, invitationId);
            return false;
        }
        
        if (invitation.getStatus() != GroupInvitation.InvitationStatus.PENDING) {
            log.warn("Invitation {} is not in pending status", invitationId);
            return false;
        }
        
        invitation.reject(reason);
        groupInvitationRepository.save(invitation);
        
        log.info("Successfully rejected invitation {} by user {}", invitationId, userId);
        return true;
    }

    @Override
    public boolean cancelInvitation(@NotNull Long invitationId, @NotNull Long cancelledBy) {
        log.info("Cancelling invitation {} by user {}", invitationId, cancelledBy);
        
        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);
        
        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }
        
        GroupInvitation invitation = invitationOpt.get();
        
        // 验证取消权限（只有邀请人或管理员可以取消）
        if (!invitation.getInviterId().equals(cancelledBy)) {
            // TODO: 检查是否为群组管理员
            log.warn("User {} not authorized to cancel invitation {}", cancelledBy, invitationId);
            return false;
        }
        
        if (invitation.getStatus() != GroupInvitation.InvitationStatus.PENDING) {
            log.warn("Invitation {} is not in pending status", invitationId);
            return false;
        }
        
        invitation.cancel();
        groupInvitationRepository.save(invitation);
        
        log.info("Successfully cancelled invitation {} by user {}", invitationId, cancelledBy);
        return true;
    }

    @Override
    public boolean approveInvitation(@NotNull Long invitationId, @NotNull Long approverId,
                                   boolean approved, String notes) {
        log.info("Approving invitation {}: approved={}, approver={}", invitationId, approved, approverId);

        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);

        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }

        GroupInvitation invitation = invitationOpt.get();

        if (!invitation.getRequiresApproval()) {
            log.warn("Invitation {} does not require approval", invitationId);
            return false;
        }

        if (invitation.getStatus() != GroupInvitation.InvitationStatus.PENDING_APPROVAL) {
            log.warn("Invitation {} is not in pending approval status", invitationId);
            return false;
        }

        invitation.approve(approverId, approved, notes);
        groupInvitationRepository.save(invitation);

        log.info("Successfully {} invitation {} by approver {}",
                approved ? "approved" : "rejected", invitationId, approverId);
        return true;
    }

    @Override
    public boolean extendInvitationExpiry(@NotNull Long invitationId, @NotNull Instant newExpiresAt,
                                        @NotNull Long modifiedBy) {
        log.info("Extending invitation {} expiry to {}", invitationId, newExpiresAt);

        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);

        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }

        GroupInvitation invitation = invitationOpt.get();

        if (invitation.getStatus() != GroupInvitation.InvitationStatus.PENDING) {
            log.warn("Cannot extend expiry for invitation {} in status {}", invitationId, invitation.getStatus());
            return false;
        }

        invitation.extendExpiry(newExpiresAt);
        groupInvitationRepository.save(invitation);

        log.info("Successfully extended invitation {} expiry to {}", invitationId, newExpiresAt);
        return true;
    }

    @Override
    public boolean increaseUsageLimit(@NotNull Long invitationId, @NotNull Integer additionalUsage,
                                    @NotNull Long modifiedBy) {
        log.info("Increasing usage limit for invitation {} by {}", invitationId, additionalUsage);

        Optional<GroupInvitation> invitationOpt = groupInvitationRepository.findById(invitationId);

        if (invitationOpt.isEmpty()) {
            log.warn("Invitation {} not found", invitationId);
            return false;
        }

        GroupInvitation invitation = invitationOpt.get();

        if (additionalUsage <= 0) {
            throw BusinessException.of(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }

        invitation.increaseUsageLimit(additionalUsage);
        groupInvitationRepository.save(invitation);

        log.info("Successfully increased usage limit for invitation {} by {}", invitationId, additionalUsage);
        return true;
    }

    @Override
    @Transactional
    public int processExpiredInvitations() {
        log.info("Processing expired invitations");

        int processed = groupInvitationRepository.markExpiredInvitations(Instant.now());

        if (processed > 0) {
            log.info("Processed {} expired invitations", processed);
        }

        return processed;
    }

    @Override
    public Optional<GroupInvitation> getInvitationById(@NotNull Long invitationId) {
        return groupInvitationRepository.findById(invitationId);
    }

    @Override
    public Optional<GroupInvitation> getInvitationByCode(@NotNull String invitationCode) {
        return groupInvitationRepository.findByInvitationCode(invitationCode);
    }

    @Override
    public Page<GroupInvitation> getGroupInvitations(@NotNull Long groupId, Pageable pageable) {
        return groupInvitationRepository.findByGroupIdOrderByCreatedAtDesc(groupId, pageable);
    }

    @Override
    public Page<GroupInvitation> getUserSentInvitations(@NotNull Long inviterId, Pageable pageable) {
        return groupInvitationRepository.findByInviterIdOrderByCreatedAtDesc(inviterId, pageable);
    }

    @Override
    public Page<GroupInvitation> getUserReceivedInvitations(@NotNull Long inviteeId, Pageable pageable) {
        return groupInvitationRepository.findByInviteeIdOrderByCreatedAtDesc(inviteeId, pageable);
    }

    @Override
    public Page<GroupInvitation> getPendingInvitations(@NotNull Long groupId, Pageable pageable) {
        return groupInvitationRepository.findByGroupIdAndStatusOrderByCreatedAtDesc(
                groupId, GroupInvitation.InvitationStatus.PENDING, pageable);
    }

    @Override
    public Page<GroupInvitation> getInvitationsNeedingApproval(@NotNull Long groupId, Pageable pageable) {
        return groupInvitationRepository.findByGroupIdAndRequiresApprovalTrueAndStatusOrderByCreatedAtDesc(
                groupId, GroupInvitation.InvitationStatus.PENDING_APPROVAL, pageable);
    }

    @Override
    public List<GroupInvitation> getActiveInvitations(@NotNull Long groupId) {
        return groupInvitationRepository.findActiveInvitations(groupId);
    }

    @Override
    public Page<GroupInvitation> getExpiringInvitations(@NotNull Instant expiryThreshold, Pageable pageable) {
        return groupInvitationRepository.findExpiringInvitations(expiryThreshold, pageable);
    }

    @Override
    public boolean isInvitationValid(@NotNull String invitationCode) {
        Optional<GroupInvitation> invitation = groupInvitationRepository.findByInvitationCode(invitationCode);

        if (invitation.isEmpty()) {
            return false;
        }

        GroupInvitation inv = invitation.get();

        // 检查状态
        if (inv.getStatus() != GroupInvitation.InvitationStatus.PENDING) {
            return false;
        }

        // 检查过期时间
        if (inv.getExpiresAt() != null && inv.getExpiresAt().isBefore(Instant.now())) {
            return false;
        }

        // 检查使用次数
        if (inv.getUsedCount() >= inv.getUsageLimit()) {
            return false;
        }

        return true;
    }

    @Override
    public boolean isUserAlreadyInvited(@NotNull Long groupId, @NotNull Long userId) {
        return groupInvitationRepository.existsByGroupIdAndInviteeIdAndStatus(
                groupId, userId, GroupInvitation.InvitationStatus.PENDING);
    }

    @Override
    public boolean isDailyInvitationLimitExceeded(@NotNull Long inviterId, @NotNull Integer dailyLimit) {
        Instant startOfDay = LocalDate.now().atStartOfDay().toInstant(ZoneOffset.UTC);
        long todayCount = groupInvitationRepository.countTodayInvitations(inviterId, startOfDay);

        return todayCount >= dailyLimit;
    }

    @Override
    public boolean isInvitationRateLimitExceeded(@NotNull String clientIp, @NotNull Integer timeWindowHours,
                                               @NotNull Integer maxInvitations) {
        Instant timeThreshold = Instant.now().minusSeconds(timeWindowHours * 3600L);
        long count = groupInvitationRepository.countInvitationsByIpInTimeWindow(clientIp, timeThreshold);

        return count >= maxInvitations;
    }

    @Override
    public InvitationStatistics getInvitationStatistics(@NotNull Long groupId) {
        Object[] stats = groupInvitationRepository.getInvitationStatistics(groupId);

        int totalInvitations = ((Number) stats[0]).intValue();
        int pendingInvitations = ((Number) stats[1]).intValue();
        int acceptedInvitations = ((Number) stats[2]).intValue();
        int rejectedInvitations = ((Number) stats[3]).intValue();
        int expiredInvitations = ((Number) stats[4]).intValue();

        double acceptanceRate = totalInvitations > 0 ?
                (acceptedInvitations * 100.0 / totalInvitations) : 0.0;

        return new InvitationStatistics(totalInvitations, pendingInvitations, acceptedInvitations,
                rejectedInvitations, expiredInvitations, acceptanceRate);
    }

    @Override
    public UserInvitationStatistics getUserInvitationStatistics(@NotNull Long inviterId) {
        Object[] stats = groupInvitationRepository.getUserInvitationStatistics(inviterId);

        int totalSent = ((Number) stats[0]).intValue();
        int totalAccepted = ((Number) stats[1]).intValue();
        int totalRejected = ((Number) stats[2]).intValue();

        double successRate = totalSent > 0 ? (totalAccepted * 100.0 / totalSent) : 0.0;

        return new UserInvitationStatistics(inviterId, totalSent, totalAccepted, totalRejected, successRate);
    }

    @Override
    public List<InvitationTypeStats> getInvitationTypeStatistics(@NotNull Long groupId) {
        List<Object[]> stats = groupInvitationRepository.getInvitationTypeStatistics(groupId);

        return stats.stream()
                .map(stat -> {
                    GroupInvitation.InvitationType type = (GroupInvitation.InvitationType) stat[0];
                    int totalInvitations = ((Number) stat[1]).intValue();
                    int acceptedInvitations = ((Number) stat[2]).intValue();
                    double acceptanceRate = totalInvitations > 0 ?
                            (acceptedInvitations * 100.0 / totalInvitations) : 0.0;

                    return new InvitationTypeStats(type, totalInvitations, acceptedInvitations, acceptanceRate);
                })
                .collect(Collectors.toList());
    }

    @Override
    public Page<InvitationSuccessRateStats> getInvitationSuccessRateLeaderboard(Pageable pageable) {
        return groupInvitationRepository.findInvitationSuccessRateLeaderboard(pageable)
                .map(stat -> new InvitationSuccessRateStats(
                        (Long) stat[0],
                        ((Number) stat[1]).intValue(),
                        ((Number) stat[2]).intValue(),
                        ((Number) stat[3]).doubleValue()));
    }

    @Override
    public List<InvitationTrendStats> getInvitationTrend(@NotNull Long groupId, @NotNull Instant startTime,
                                                       @NotNull Instant endTime) {
        List<Object[]> trends = groupInvitationRepository.findInvitationTrend(groupId, startTime, endTime);

        return trends.stream()
                .map(trend -> new InvitationTrendStats(
                        (String) trend[0],
                        ((Number) trend[1]).intValue(),
                        ((Number) trend[2]).intValue()))
                .collect(Collectors.toList());
    }

    @Override
    public InvitationResponseTimeStats getInvitationResponseTimeStats(@NotNull Long groupId) {
        Object[] stats = groupInvitationRepository.getInvitationResponseTimeStats(groupId);

        if (stats[0] == null) {
            return new InvitationResponseTimeStats(0.0, 0.0, 0.0);
        }

        double avgResponseTime = ((Number) stats[0]).doubleValue();
        double minResponseTime = ((Number) stats[1]).doubleValue();
        double maxResponseTime = ((Number) stats[2]).doubleValue();

        return new InvitationResponseTimeStats(avgResponseTime, minResponseTime, maxResponseTime);
    }

    @Override
    public List<InvitationSourceStats> getPopularInvitationSources(@NotNull Long groupId) {
        List<Object[]> sources = groupInvitationRepository.findPopularInvitationSources(groupId);

        return sources.stream()
                .map(source -> new InvitationSourceStats(
                        (String) source[0],
                        ((Number) source[1]).intValue(),
                        ((Number) source[2]).intValue()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public int batchCancelPendingInvitations(@NotNull Long inviterId) {
        log.info("Batch cancelling pending invitations for inviter {}", inviterId);

        int cancelled = groupInvitationRepository.cancelPendingInvitationsByInviter(inviterId);

        log.info("Successfully cancelled {} pending invitations for inviter {}", cancelled, inviterId);
        return cancelled;
    }

    @Override
    @Transactional
    public int cleanupOldInvitations(@NotNull Instant beforeTime) {
        log.info("Cleaning up old invitations before {}", beforeTime);

        int cleaned = groupInvitationRepository.deleteOldProcessedInvitations(beforeTime);

        log.info("Successfully cleaned up {} old invitation records", cleaned);
        return cleaned;
    }

    @Override
    public String generateUniqueInvitationCode() {
        String code;
        int attempts = 0;
        final int maxAttempts = 10;

        do {
            code = generateRandomCode();
            attempts++;

            if (attempts > maxAttempts) {
                throw new RuntimeException("[GRP-INV-008] Failed to generate unique invitation code after " + maxAttempts + " attempts");
            }
        } while (groupInvitationRepository.existsByInvitationCode(code));

        return code;
    }

    /**
     * 生成随机邀请码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(INVITATION_CODE_LENGTH);

        for (int i = 0; i < INVITATION_CODE_LENGTH; i++) {
            int index = secureRandom.nextInt(INVITATION_CODE_CHARS.length());
            code.append(INVITATION_CODE_CHARS.charAt(index));
        }

        return code.toString();
    }

    /**
     * 验证邀请参数
     */
    private void validateInvitationParameters(Long groupId, Long inviterId) {
        if (groupId == null || groupId <= 0) {
            throw BusinessException.of(GroupErrorCode.INVALID_GROUP_ID);
        }

        if (inviterId == null || inviterId <= 0) {
            throw BusinessException.of(UserErrorCode.INVALID_USER_ID);
        }
    }
}
