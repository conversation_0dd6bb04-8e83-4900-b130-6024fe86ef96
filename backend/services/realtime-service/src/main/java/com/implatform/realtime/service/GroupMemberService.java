package com.implatform.realtime.service;

import com.implatform.realtime.entity.GroupMember;
import com.implatform.realtime.dto.GroupMemberDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 群组成员服务接口
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface GroupMemberService {

    // ==================== 成员管理 ====================

    /**
     * 添加群组成员
     */
    @Transactional
    GroupMember addMember(Long groupId, Long userId, GroupMember.MemberRole role, 
                         GroupMember.JoinMethod joinMethod, String inviterId);

    /**
     * 移除群组成员
     */
    @Transactional
    void removeMember(Long groupId, Long userId, String operatorId);

    /**
     * 更新成员角色
     */
    @Transactional
    GroupMember updateMemberRole(Long groupId, Long userId, GroupMember.MemberRole newRole, String operatorId);

    /**
     * 获取群组成员
     */
    Optional<GroupMember> getMember(Long groupId, Long userId);

    /**
     * 根据ID获取群组成员
     */
    Optional<GroupMember> getGroupMemberById(Long id);

    /**
     * 检查用户是否为群组成员
     */
    boolean isMember(Long groupId, Long userId);

    /**
     * 检查用户是否为群主
     */
    boolean isOwner(Long groupId, Long userId);

    /**
     * 检查用户是否为管理员
     */
    boolean isAdmin(Long groupId, Long userId);

    /**
     * 检查用户是否有管理权限
     */
    boolean hasAdminPermission(Long groupId, Long userId);

    // ==================== 成员查询 ====================

    /**
     * 获取群组成员列表
     */
    Page<GroupMember> getGroupMembers(Long groupId, GroupMember.MemberStatus status, Pageable pageable);

    /**
     * 获取群组成员数量
     */
    long getGroupMemberCount(Long groupId);

    /**
     * 获取活跃成员数量
     */
    long getActiveMemberCount(Long groupId);

    /**
     * 获取用户加入的群组列表
     */
    Page<GroupMember> getUserGroups(Long userId, GroupMember.MemberStatus status, Pageable pageable);

    /**
     * 搜索群组成员
     */
    Page<GroupMember> searchMembers(Long groupId, String keyword, Pageable pageable);

    /**
     * 获取群组管理员列表
     */
    List<GroupMember> getGroupAdmins(Long groupId);

    /**
     * 获取群主
     */
    Optional<GroupMember> getGroupOwner(Long groupId);

    // ==================== 成员操作 ====================

    /**
     * 禁言成员
     */
    @Transactional
    void muteMember(Long groupId, Long userId, Instant mutedUntil, String operatorId, String reason);

    /**
     * 解除禁言
     */
    @Transactional
    void unmuteMember(Long groupId, Long userId, String operatorId);

    /**
     * 踢出成员
     */
    @Transactional
    void kickMember(Long groupId, Long userId, String operatorId, String reason);

    /**
     * 封禁成员
     */
    @Transactional
    void banMember(Long groupId, Long userId, String operatorId, String reason);

    /**
     * 解封成员
     */
    @Transactional
    void unbanMember(Long groupId, Long userId, String operatorId);

    /**
     * 转让群主
     */
    @Transactional
    void transferOwnership(Long groupId, Long newOwnerId, String currentOwnerId);

    // ==================== 批量操作 ====================

    /**
     * 批量添加成员
     */
    @Transactional
    List<GroupMember> batchAddMembers(Long groupId, List<Long> userIds, 
                                     GroupMember.JoinMethod joinMethod, String operatorId);

    /**
     * 批量移除成员
     */
    @Transactional
    void batchRemoveMembers(Long groupId, List<Long> userIds, String operatorId);

    /**
     * 批量更新成员角色
     */
    @Transactional
    void batchUpdateMemberRoles(Long groupId, List<Long> userIds, 
                               GroupMember.MemberRole newRole, String operatorId);

    // ==================== 统计分析 ====================

    /**
     * 获取成员统计信息
     */
    GroupMemberDTO.MemberStatistics getMemberStatistics(Long groupId);

    /**
     * 获取成员活跃度统计
     */
    List<GroupMemberDTO.ActivityStatistics> getMemberActivityStatistics(Long groupId, int days);

    /**
     * 获取成员加入趋势
     */
    List<GroupMemberDTO.JoinTrend> getMemberJoinTrend(Long groupId, int days);

    // ==================== 维护清理 ====================

    /**
     * 清理过期的禁言状态
     */
    @Transactional
    int cleanupExpiredMutes();

    /**
     * 清理已退出的成员记录
     */
    @Transactional
    int cleanupLeftMembers(int days);

    /**
     * 修复成员数据
     */
    @Transactional
    void repairMemberData(Long groupId);

    // ==================== 管理员接口 ====================

    /**
     * 移除群组成员（管理员接口）
     */
    @Transactional
    void removeGroupMember(Long memberId);
}
