package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BroadcastTask;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

/**
 * 广播任务Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BroadcastTaskRepository extends R2dbcRepository<BroadcastTask, Long> {

    /**
     * 根据频道查找广播任务
     */
    @Query("SELECT * FROM broadcast_tasks WHERE channel_id = :channelId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastTask> findByChannelIdOrderByCreatedAtDesc(@Param("channelId") Long channelId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据创建者查找广播任务
     */
    @Query("SELECT * FROM broadcast_tasks WHERE creator_id = :creatorId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastTask> findByCreatorIdOrderByCreatedAtDesc(@Param("creatorId") Long creatorId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态查找广播任务
     */
    @Query("SELECT * FROM broadcast_tasks WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BroadcastTask> findByStatusOrderByCreatedAtDesc(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找待执行的任务
     */
    @Query("SELECT * FROM broadcast_tasks WHERE status = 'PENDING' AND " +
           "(scheduled_at IS NULL OR scheduled_at <= :now) " +
           "ORDER BY created_at ASC")
    Flux<BroadcastTask> findPendingTasks(@Param("now") LocalDateTime now);

    /**
     * 查找定时任务
     */
    @Query("SELECT * FROM broadcast_tasks WHERE status = 'PENDING' AND " +
           "scheduled_at IS NOT NULL AND scheduled_at > :now " +
           "ORDER BY t.scheduledAt ASC")
    List<BroadcastTask> findScheduledTasks(@Param("now") LocalDateTime now);

    /**
     * 查找正在执行的任务
     */
    List<BroadcastTask> findByStatusOrderByStartedAtDesc(BroadcastTask.TaskStatus status);

    /**
     * 查找超时的任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.status = 'PROCESSING' AND " +
           "t.startedAt < :timeoutTime")
    List<BroadcastTask> findTimeoutTasks(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 根据频道和状态查找任务
     */
    Page<BroadcastTask> findByChannelIdAndStatusOrderByCreatedAtDesc(
            Long channelId, BroadcastTask.TaskStatus status, Pageable pageable);

    /**
     * 统计频道的广播任务数量
     */
    @Query("SELECT COUNT(t) FROM BroadcastTask t WHERE t.channelId = :channelId")
    long countByChannelId(@Param("channelId") Long channelId);

    /**
     * 统计用户的广播任务数量
     */
    @Query("SELECT COUNT(t) FROM BroadcastTask t WHERE t.creatorId = :creatorId")
    long countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 按状态统计任务数量
     */
    @Query("SELECT t.status, COUNT(t) FROM BroadcastTask t GROUP BY t.status")
    List<Object[]> countTasksByStatus();

    /**
     * 获取频道广播统计
     */
    @Query("SELECT " +
           "COUNT(t) as totalTasks, " +
           "SUM(t.totalRecipients) as totalRecipients, " +
           "SUM(t.sentCount) as totalSent, " +
           "SUM(t.deliveredCount) as totalDelivered, " +
           "SUM(t.failedCount) as totalFailed " +
           "FROM BroadcastTask t WHERE t.channelId = :channelId")
    Object[] getChannelBroadcastStats(@Param("channelId") Long channelId);

    /**
     * 获取用户广播统计
     */
    @Query("SELECT " +
           "COUNT(t) as totalTasks, " +
           "SUM(t.totalRecipients) as totalRecipients, " +
           "SUM(t.sentCount) as totalSent, " +
           "SUM(t.deliveredCount) as totalDelivered, " +
           "SUM(t.failedCount) as totalFailed " +
           "FROM BroadcastTask t WHERE t.creatorId = :creatorId")
    Object[] getUserBroadcastStats(@Param("creatorId") Long creatorId);

    /**
     * 获取广播趋势数据
     */
    @Query("SELECT DATE(t.createdAt) as date, COUNT(t) as taskCount, " +
           "SUM(t.totalRecipients) as totalRecipients " +
           "FROM BroadcastTask t WHERE t.createdAt >= :startDate " +
           "GROUP BY DATE(t.createdAt) " +
           "ORDER BY date")
    List<Object[]> getBroadcastTrend(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取最近完成的任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.status IN ('COMPLETED', 'FAILED') " +
           "ORDER BY t.completedAt DESC")
    List<BroadcastTask> findRecentCompletedTasks(Pageable pageable);

    /**
     * 查找需要重试的任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.status = 'FAILED' AND " +
           "t.failedCount < t.totalRecipients * 0.5 AND " +
           "t.completedAt > :retryAfter")
    List<BroadcastTask> findTasksNeedingRetry(@Param("retryAfter") LocalDateTime retryAfter);

    /**
     * 获取频道最近的广播任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.channelId = :channelId " +
           "ORDER BY t.createdAt DESC")
    List<BroadcastTask> findRecentTasksByChannel(@Param("channelId") Long channelId, Pageable pageable);

    /**
     * 查找长时间运行的任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.status = 'PROCESSING' AND " +
           "t.startedAt < :longRunningThreshold")
    List<BroadcastTask> findLongRunningTasks(@Param("longRunningThreshold") LocalDateTime longRunningThreshold);

    /**
     * 按消息类型统计任务
     */
    @Query("SELECT t.messageType, COUNT(t) FROM BroadcastTask t " +
           "WHERE t.createdAt >= :startDate " +
           "GROUP BY t.messageType " +
           "ORDER BY COUNT(t) DESC")
    List<Object[]> countTasksByMessageType(@Param("startDate") LocalDateTime startDate);

    /**
     * 按目标类型统计任务
     */
    @Query("SELECT t.targetType, COUNT(t) FROM BroadcastTask t " +
           "WHERE t.createdAt >= :startDate " +
           "GROUP BY t.targetType " +
           "ORDER BY COUNT(t) DESC")
    List<Object[]> countTasksByTargetType(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取成功率最高的任务
     */
    @Query("SELECT t FROM BroadcastTask t WHERE t.status = 'COMPLETED' AND " +
           "t.totalRecipients > 0 " +
           "ORDER BY (CAST(t.deliveredCount AS DOUBLE) / t.totalRecipients) DESC")
    List<BroadcastTask> findMostSuccessfulTasks(Pageable pageable);

    /**
     * 获取平均执行时间（秒）
     */
    @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM completed_at) - EXTRACT(EPOCH FROM started_at)) " +
           "FROM broadcast_tasks WHERE status = 'COMPLETED' AND " +
           "started_at IS NOT NULL AND completed_at IS NOT NULL", nativeQuery = true)
    Double getAverageExecutionTime();

    /**
     * 删除旧的已完成任务
     */
    @Query("DELETE FROM BroadcastTask t WHERE t.status IN ('COMPLETED', 'FAILED', 'CANCELLED') " +
           "AND t.completedAt < :cutoffDate")
    void deleteOldCompletedTasks(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * 获取任务执行效率统计
     */
    @Query(value = "SELECT " +
           "COUNT(*) as totalTasks, " +
           "AVG(CAST(delivered_count AS DOUBLE PRECISION) / NULLIF(total_recipients, 0)) as avgSuccessRate, " +
           "AVG(EXTRACT(EPOCH FROM completed_at) - EXTRACT(EPOCH FROM started_at)) as avgExecutionTime " +
           "FROM broadcast_tasks WHERE status = 'COMPLETED' AND " +
           "started_at IS NOT NULL AND completed_at IS NOT NULL AND " +
           "created_at >= :startDate", nativeQuery = true)
    Object[] getTaskEfficiencyStats(@Param("startDate") LocalDateTime startDate);
}
