package com.implatform.realtime.repository;

import com.implatform.realtime.entity.ShortVideo;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 短视频Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface ShortVideoRepository extends R2dbcRepository<ShortVideo, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据创建者ID查找视频
     */
    @Query("SELECT * FROM short_videos WHERE creator_id = :creatorId AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByCreatorIdAndIsDeletedFalse(@Param("creatorId") Long creatorId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据创建者ID和状态查找视频
     */
    @Query("SELECT * FROM short_videos WHERE creator_id = :creatorId AND status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByCreatorIdAndStatusAndIsDeletedFalse(@Param("creatorId") Long creatorId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据状态查找视频
     */
    @Query("SELECT * FROM short_videos WHERE status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByStatusAndIsDeletedFalse(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据可见性查找视频
     */
    @Query("SELECT * FROM short_videos WHERE visibility = :visibility AND status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByVisibilityAndStatusAndIsDeletedFalse(@Param("visibility") String visibility, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据审核状态查找视频
     */
    @Query("SELECT * FROM short_videos WHERE review_status = :reviewStatus AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByReviewStatusAndIsDeletedFalse(@Param("reviewStatus") String reviewStatus, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找推荐视频
     */
    @Query("SELECT * FROM short_videos WHERE is_featured = true AND status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByIsFeaturedTrueAndStatusAndIsDeletedFalse(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找置顶视频
     */
    @Query("SELECT * FROM short_videos WHERE is_pinned = true AND creator_id = :creatorId AND is_deleted = false")
    Flux<ShortVideo> findByIsPinnedTrueAndCreatorIdAndIsDeletedFalse(@Param("creatorId") Long creatorId);

    // ==================== 标签和话题查询 ====================

    /**
     * 根据标签查找视频
     */
    @Query("SELECT * FROM short_videos WHERE tags @> CAST(:tag AS jsonb) AND status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByTagAndStatus(@Param("tag") String tag, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据话题查找视频
     */
    @Query("SELECT * FROM short_videos WHERE topics @> CAST(:topic AS jsonb) AND status = :status AND is_deleted = false LIMIT :limit OFFSET :offset")
    Flux<ShortVideo> findByTopicAndStatus(@Param("topic") String topic, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取热门标签
     */
    @Query("SELECT tag, COUNT(*) as count FROM (" +
           "SELECT jsonb_array_elements_text(tags) as tag FROM short_videos " +
           "WHERE status = :status AND is_deleted = false" +
           ") t GROUP BY tag ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findPopularTags(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取热门话题
     */
    @Query("SELECT topic, COUNT(*) as count FROM (" +
           "SELECT jsonb_array_elements_text(topics) as topic FROM short_videos " +
           "WHERE status = :status AND is_deleted = false" +
           ") t GROUP BY topic ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findPopularTopics(@Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取用户使用的标签
     */
    @Query("SELECT DISTINCT jsonb_array_elements_text(tags) as tag FROM short_videos " +
           "WHERE creator_id = :creatorId AND is_deleted = false ORDER BY tag")
    Flux<String> findTagsByCreatorId(@Param("creatorId") Long creatorId);

    // ==================== 地理位置查询 ====================

    /**
     * 根据地理位置查找附近视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND v.longitude IS NOT NULL AND v.latitude IS NOT NULL " +
           "AND (6371 * acos(cos(radians(:latitude)) * cos(radians(v.latitude)) * " +
           "cos(radians(v.longitude) - radians(:longitude)) + sin(radians(:latitude)) * " +
           "sin(radians(v.latitude)))) <= :radius")
    Page<ShortVideo> findNearbyVideos(@Param("latitude") Double latitude, 
                                     @Param("longitude") Double longitude, 
                                     @Param("radius") Double radius,
                                     @Param("status") ShortVideo.VideoStatus status,
                                     Pageable pageable);

    /**
     * 根据位置名称查找视频
     */
    Page<ShortVideo> findByLocationContainingIgnoreCaseAndStatusAndIsDeletedFalse(String location, ShortVideo.VideoStatus status, Pageable pageable);

    // ==================== 搜索查询 ====================

    /**
     * 全文搜索视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND (LOWER(v.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(v.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<ShortVideo> searchVideos(@Param("keyword") String keyword, @Param("status") ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 高级搜索视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND (:keyword IS NULL OR LOWER(v.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "OR LOWER(v.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "AND (:quality IS NULL OR v.quality = :quality) " +
           "AND (:visibility IS NULL OR v.visibility = :visibility) " +
           "AND (:minDuration IS NULL OR v.duration >= :minDuration) " +
           "AND (:maxDuration IS NULL OR v.duration <= :maxDuration) " +
           "AND (:startDate IS NULL OR v.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR v.createdAt <= :endDate)")
    Page<ShortVideo> advancedSearchVideos(@Param("keyword") String keyword,
                                         @Param("status") ShortVideo.VideoStatus status,
                                         @Param("quality") ShortVideo.VideoQuality quality,
                                         @Param("visibility") ShortVideo.VideoVisibility visibility,
                                         @Param("minDuration") Integer minDuration,
                                         @Param("maxDuration") Integer maxDuration,
                                         @Param("startDate") Instant startDate,
                                         @Param("endDate") Instant endDate,
                                         Pageable pageable);

    // ==================== 统计查询 ====================

    /**
     * 统计用户视频数量
     */
    long countByCreatorIdAndIsDeletedFalse(Long creatorId);

    /**
     * 统计用户各状态视频数量
     */
    long countByCreatorIdAndStatusAndIsDeletedFalse(Long creatorId, ShortVideo.VideoStatus status);

    /**
     * 获取用户总观看数
     */
    @Query("SELECT SUM(v.viewCount) FROM ShortVideo v WHERE v.creatorId = :creatorId AND v.isDeleted = false")
    Long getTotalViewCountByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取用户总点赞数
     */
    @Query("SELECT SUM(v.likeCount) FROM ShortVideo v WHERE v.creatorId = :creatorId AND v.isDeleted = false")
    Long getTotalLikeCountByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取用户总评论数
     */
    @Query("SELECT SUM(v.commentCount) FROM ShortVideo v WHERE v.creatorId = :creatorId AND v.isDeleted = false")
    Long getTotalCommentCountByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取用户总分享数
     */
    @Query("SELECT SUM(v.shareCount) FROM ShortVideo v WHERE v.creatorId = :creatorId AND v.isDeleted = false")
    Long getTotalShareCountByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 获取用户总收藏数
     */
    @Query("SELECT SUM(v.favoriteCount) FROM ShortVideo v WHERE v.creatorId = :creatorId AND v.isDeleted = false")
    Long getTotalFavoriteCountByCreatorId(@Param("creatorId") Long creatorId);

    // ==================== 排行榜查询 ====================

    /**
     * 获取观看数排行榜
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByViewCountDesc(ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 获取点赞数排行榜
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByLikeCountDesc(ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 获取评论数排行榜
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByCommentCountDesc(ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 获取分享数排行榜
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByShareCountDesc(ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 获取最新视频
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByCreatedAtDesc(ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 获取最新发布视频
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseOrderByPublishedAtDesc(ShortVideo.VideoStatus status, Pageable pageable);

    // ==================== 时间范围查询 ====================

    /**
     * 获取指定时间范围内的视频
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseAndCreatedAtBetween(ShortVideo.VideoStatus status, Instant startTime, Instant endTime, Pageable pageable);

    /**
     * 获取指定时间范围内的发布视频
     */
    Page<ShortVideo> findByStatusAndIsDeletedFalseAndPublishedAtBetween(ShortVideo.VideoStatus status, Instant startTime, Instant endTime, Pageable pageable);

    /**
     * 获取今日视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND v.createdAt >= :todayStart AND v.createdAt < :tomorrowStart")
    Page<ShortVideo> findTodayVideos(@Param("status") ShortVideo.VideoStatus status,
                                   @Param("todayStart") Instant todayStart,
                                   @Param("tomorrowStart") Instant tomorrowStart,
                                   Pageable pageable);

    /**
     * 获取本周视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND v.createdAt >= :weekStart")
    Page<ShortVideo> findThisWeekVideos(@Param("status") ShortVideo.VideoStatus status, @Param("weekStart") Instant weekStart, Pageable pageable);

    /**
     * 获取本月视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND v.createdAt >= :monthStart")
    Page<ShortVideo> findThisMonthVideos(@Param("status") ShortVideo.VideoStatus status, @Param("monthStart") Instant monthStart, Pageable pageable);

    // ==================== 更新操作 ====================

    /**
     * 增加观看次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.viewCount = v.viewCount + 1 WHERE v.id = :videoId")
    int incrementViewCount(@Param("videoId") Long videoId);

    /**
     * 增加点赞次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.likeCount = v.likeCount + 1 WHERE v.id = :videoId")
    int incrementLikeCount(@Param("videoId") Long videoId);

    /**
     * 减少点赞次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.likeCount = GREATEST(v.likeCount - 1, 0) WHERE v.id = :videoId")
    int decrementLikeCount(@Param("videoId") Long videoId);

    /**
     * 增加评论次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.commentCount = v.commentCount + 1 WHERE v.id = :videoId")
    int incrementCommentCount(@Param("videoId") Long videoId);

    /**
     * 减少评论次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.commentCount = GREATEST(v.commentCount - 1, 0) WHERE v.id = :videoId")
    int decrementCommentCount(@Param("videoId") Long videoId);

    /**
     * 增加分享次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.shareCount = v.shareCount + 1 WHERE v.id = :videoId")
    int incrementShareCount(@Param("videoId") Long videoId);

    /**
     * 增加收藏次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.favoriteCount = v.favoriteCount + 1 WHERE v.id = :videoId")
    int incrementFavoriteCount(@Param("videoId") Long videoId);

    /**
     * 减少收藏次数
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.favoriteCount = GREATEST(v.favoriteCount - 1, 0) WHERE v.id = :videoId")
    int decrementFavoriteCount(@Param("videoId") Long videoId);

    /**
     * 批量更新视频状态
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.status = :status WHERE v.id IN :videoIds AND v.creatorId = :creatorId")
    int batchUpdateStatus(@Param("videoIds") List<Long> videoIds, @Param("creatorId") Long creatorId, @Param("status") ShortVideo.VideoStatus status);

    /**
     * 批量更新视频可见性
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.visibility = :visibility WHERE v.id IN :videoIds AND v.creatorId = :creatorId")
    int batchUpdateVisibility(@Param("videoIds") List<Long> videoIds, @Param("creatorId") Long creatorId, @Param("visibility") ShortVideo.VideoVisibility visibility);

    /**
     * 批量删除视频
     */
    @Modifying
    @Query("UPDATE ShortVideo v SET v.isDeleted = true WHERE v.id IN :videoIds AND v.creatorId = :creatorId")
    int batchDeleteVideos(@Param("videoIds") List<Long> videoIds, @Param("creatorId") Long creatorId);

    // ==================== 清理操作 ====================

    /**
     * 查找过期的已删除视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.isDeleted = true AND v.updatedAt < :expireTime")
    List<ShortVideo> findExpiredDeletedVideos(@Param("expireTime") Instant expireTime);

    /**
     * 物理删除过期视频
     */
    @Modifying
    @Query("DELETE FROM ShortVideo v WHERE v.isDeleted = true AND v.updatedAt < :expireTime")
    int physicalDeleteExpiredVideos(@Param("expireTime") Instant expireTime);

    // ==================== 特殊查询 ====================

    /**
     * 查找相似视频（基于标签）
     */
    @Query("SELECT v, COUNT(t) as commonTags FROM ShortVideo v JOIN v.tags t " +
           "WHERE t IN :tags AND v.id != :videoId AND v.status = :status AND v.isDeleted = false " +
           "GROUP BY v ORDER BY commonTags DESC")
    List<Object[]> findSimilarVideosByTags(@Param("videoId") Long videoId, @Param("tags") List<String> tags, @Param("status") ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 查找用户可能感兴趣的视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.status = :status AND v.isDeleted = false " +
           "AND v.creatorId != :userId " +
           "AND (EXISTS (SELECT 1 FROM ShortVideo v2 JOIN v2.tags t WHERE v2.creatorId = :userId AND t MEMBER OF v.tags) " +
           "OR EXISTS (SELECT 1 FROM ShortVideo v3 JOIN v3.topics tp WHERE v3.creatorId = :userId AND tp MEMBER OF v.topics))")
    Page<ShortVideo> findRecommendedVideosForUser(@Param("userId") Long userId, @Param("status") ShortVideo.VideoStatus status, Pageable pageable);

    /**
     * 查找空标签的视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.tags IS EMPTY AND v.isDeleted = false")
    List<ShortVideo> findVideosWithoutTags();

    /**
     * 查找空话题的视频
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.topics IS EMPTY AND v.isDeleted = false")
    List<ShortVideo> findVideosWithoutTopics();

    /**
     * 检查视频是否存在且属于用户
     */
    boolean existsByIdAndCreatorIdAndIsDeletedFalse(Long videoId, Long creatorId);

    /**
     * 获取用户视频详情（包含权限检查）
     * 注意：暂时简化权限检查，不包含好友关系验证
     */
    @Query("SELECT v FROM ShortVideo v WHERE v.id = :videoId AND " +
           "(v.creatorId = :userId OR v.visibility = 'PUBLIC') " +
           "AND v.isDeleted = false")
    Optional<ShortVideo> findVideoWithPermissionCheck(@Param("videoId") Long videoId, @Param("userId") Long userId);
}
