package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BotInstallation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 机器人安装Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BotInstallationRepository extends R2dbcRepository<BotInstallation, String> {

    /**
     * 查找用户安装的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :installedBy AND status = :status ORDER BY last_used_at DESC")
    Flux<BotInstallation> findByInstalledByAndStatusOrderByLastUsedAtDesc(
            @Param("installedBy") String installedBy, @Param("status") String status);

    /**
     * 分页查找用户安装的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :installedBy AND status = :status ORDER BY last_used_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findByInstalledByAndStatusOrderByLastUsedAtDesc(
            @Param("installedBy") String installedBy, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找机器人的所有安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = :status ORDER BY installed_at DESC")
    Flux<BotInstallation> findByBotIdAndStatusOrderByInstalledAtDesc(
            @Param("botId") Long botId, @Param("status") String status);

    /**
     * 分页查找机器人的安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = :status ORDER BY installed_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findByBotIdAndStatusOrderByInstalledAtDesc(
            @Param("botId") Long botId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找特定安装
     */
    Optional<BotInstallation> findByBotIdAndInstalledByAndInstallationTypeAndTargetId(
            Long botId, String installedBy, BotInstallation.InstallationType installationType, String targetId);

    /**
     * 查找私聊安装
     */
    Optional<BotInstallation> findByBotIdAndInstalledByAndInstallationType(
            Long botId, String installedBy, BotInstallation.InstallationType installationType);

    /**
     * 根据机器人ID和安装类型查找安装
     */
    List<BotInstallation> findByBotIdAndInstallationType(
            Long botId, BotInstallation.InstallationType installationType);

    /**
     * 根据机器人ID、聊天ID和安装类型查找安装
     */
    Optional<BotInstallation> findByBotIdAndChatIdAndInstallationType(
            Long botId, String chatId, BotInstallation.InstallationType installationType);

    /**
     * 检查机器人是否已在指定聊天中安装（带状态）
     */
    boolean existsByBotIdAndChatIdAndInstallationTypeAndStatus(
            Long botId, String chatId, BotInstallation.InstallationType installationType,
            BotInstallation.InstallationStatus status);

    /**
     * 查找群组/频道的机器人安装
     */
    List<BotInstallation> findByInstallationTypeAndTargetIdAndStatusOrderByInstalledAtDesc(
            BotInstallation.InstallationType installationType, String targetId, 
            BotInstallation.InstallationStatus status);

    /**
     * 查找用户在特定群组/频道安装的机器人
     */
    List<BotInstallation> findByInstalledByAndInstallationTypeAndTargetIdAndStatus(
            String installedBy, BotInstallation.InstallationType installationType, String targetId,
            BotInstallation.InstallationStatus status);

    /**
     * 检查机器人是否已安装
     */
    @Query("SELECT COUNT(bi) > 0 FROM BotInstallation bi WHERE bi.botId = :botId AND " +
           "bi.installedBy = :installedBy AND bi.installationType = :installationType AND " +
           "(:targetId IS NULL OR bi.targetId = :targetId) AND bi.status = 'ACTIVE'")
    boolean isInstalled(@Param("botId") Long botId, @Param("installedBy") String installedBy,
                       @Param("installationType") BotInstallation.InstallationType installationType,
                       @Param("targetId") String targetId);

    /**
     * 统计机器人的安装数量
     */
    @Query("SELECT COUNT(bi) FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE'")
    Long countActiveInstallationsByBotId(@Param("botId") Long botId);

    /**
     * 统计用户安装的机器人数量
     */
    @Query("SELECT COUNT(bi) FROM BotInstallation bi WHERE bi.installedBy = :userId AND bi.status = 'ACTIVE'")
    Long countActiveInstallationsByUserId(@Param("userId") Long userId);

    /**
     * 统计各安装类型的数量
     */
    @Query("SELECT bi.installationType, COUNT(bi) FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE' GROUP BY bi.installationType")
    List<Object[]> countInstallationsByType(@Param("botId") Long botId);

    /**
     * 查找活跃安装（最近使用）
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE' AND " +
           "bi.lastUsedAt >= :since ORDER BY bi.lastUsedAt DESC")
    List<BotInstallation> findActiveInstallationsSince(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找非活跃安装
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE' AND " +
           "(bi.lastUsedAt IS NULL OR bi.lastUsedAt < :threshold)")
    List<BotInstallation> findInactiveInstallations(@Param("botId") Long botId, @Param("threshold") LocalDateTime threshold);

    /**
     * 查找最近安装
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE' AND " +
           "bi.installedAt >= :since ORDER BY bi.installedAt DESC")
    List<BotInstallation> findRecentInstallations(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找需要更新活跃时间的安装
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.status = 'ACTIVE' AND " +
           "(bi.lastUsedAt IS NULL OR bi.lastUsedAt < :threshold)")
    List<BotInstallation> findInstallationsNeedingActivityUpdate(@Param("threshold") LocalDateTime threshold);

    /**
     * 批量更新最后使用时间
     */
    @Modifying
    @Query("UPDATE BotInstallation bi SET bi.lastUsedAt = :lastUsedAt, bi.usageCount = bi.usageCount + 1 " +
           "WHERE bi.id IN :installationIds")
    int updateLastUsedTime(@Param("installationIds") List<String> installationIds, @Param("lastUsedAt") LocalDateTime lastUsedAt);

    /**
     * 批量更新安装状态
     */
    @Modifying
    @Query("UPDATE BotInstallation bi SET bi.status = :status WHERE bi.id IN :installationIds")
    int updateInstallationStatus(@Param("installationIds") List<String> installationIds,
                                @Param("status") BotInstallation.InstallationStatus status);

    /**
     * 移除机器人的所有安装
     */
    @Modifying
    @Query("UPDATE BotInstallation bi SET bi.status = 'REMOVED' WHERE bi.botId = :botId")
    int removeAllInstallationsByBotId(@Param("botId") Long botId);

    /**
     * 移除用户的所有安装
     */
    @Modifying
    @Query("UPDATE BotInstallation bi SET bi.status = 'REMOVED' WHERE bi.installedBy = :userId")
    int removeAllInstallationsByUserId(@Param("userId") Long userId);

    /**
     * 获取安装统计信息
     */
    @Query("SELECT " +
           "COUNT(bi) as totalInstallations, " +
           "COUNT(CASE WHEN bi.status = 'ACTIVE' THEN 1 END) as activeInstallations, " +
           "COUNT(CASE WHEN bi.installationType = 'PRIVATE' THEN 1 END) as privateInstallations, " +
           "COUNT(CASE WHEN bi.installationType = 'GROUP' THEN 1 END) as groupInstallations, " +
           "COUNT(CASE WHEN bi.installationType = 'CHANNEL' THEN 1 END) as channelInstallations, " +
           "AVG(bi.usageCount) as averageUsage " +
           "FROM BotInstallation bi WHERE bi.botId = :botId")
    Object[] getInstallationStatistics(@Param("botId") Long botId);

    /**
     * 查找安装趋势
     */
    @Query("SELECT DATE(bi.installedAt) as date, COUNT(bi) as count FROM BotInstallation bi " +
           "WHERE bi.botId = :botId AND bi.installedAt >= :since " +
           "GROUP BY DATE(bi.installedAt) ORDER BY date")
    List<Object[]> getInstallationTrend(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找使用趋势
     */
    @Query("SELECT DATE(bi.lastUsedAt) as date, COUNT(bi) as count FROM BotInstallation bi " +
           "WHERE bi.botId = :botId AND bi.lastUsedAt >= :since " +
           "GROUP BY DATE(bi.lastUsedAt) ORDER BY date")
    List<Object[]> getUsageTrend(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找热门安装目标
     */
    @Query("SELECT bi.targetId, bi.installationType, COUNT(bi) as count FROM BotInstallation bi " +
           "WHERE bi.status = 'ACTIVE' AND bi.targetId IS NOT NULL " +
           "GROUP BY bi.targetId, bi.installationType ORDER BY count DESC")
    List<Object[]> findPopularInstallationTargets(Pageable pageable);

    /**
     * 查找用户最常用的机器人
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.installedBy = :userId AND bi.status = 'ACTIVE' " +
           "ORDER BY bi.usageCount DESC, bi.lastUsedAt DESC NULLS LAST")
    List<BotInstallation> findMostUsedBotsByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找机器人的重度用户
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.botId = :botId AND bi.status = 'ACTIVE' " +
           "ORDER BY bi.usageCount DESC, bi.lastUsedAt DESC")
    List<BotInstallation> findHeavyUsersByBot(@Param("botId") Long botId, Pageable pageable);

    /**
     * 查找需要清理的安装
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.status = 'REMOVED' AND bi.installedAt < :cutoffTime")
    List<BotInstallation> findInstallationsForCleanup(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除旧的已移除安装
     */
    @Modifying
    @Query("DELETE FROM BotInstallation bi WHERE bi.status = 'REMOVED' AND bi.installedAt < :cutoffTime")
    int deleteOldRemovedInstallations(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找重复安装
     */
    @Query("SELECT bi.botId, bi.installedBy, bi.installationType, bi.targetId, COUNT(bi) as count " +
           "FROM BotInstallation bi WHERE bi.status = 'ACTIVE' " +
           "GROUP BY bi.botId, bi.installedBy, bi.installationType, bi.targetId HAVING COUNT(bi) > 1")
    List<Object[]> findDuplicateInstallations();

    /**
     * 查找安装冲突
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.botId = :botId AND bi.installedBy = :installedBy AND " +
           "bi.installationType = :installationType AND " +
           "(:targetId IS NULL OR bi.targetId = :targetId) AND bi.status = 'ACTIVE'")
    List<BotInstallation> findInstallationConflicts(@Param("botId") Long botId, @Param("installedBy") Long installedBy,
                                                   @Param("installationType") BotInstallation.InstallationType installationType,
                                                   @Param("targetId") Long targetId);

    /**
     * 查找用户在群组中安装的所有机器人
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.installedBy = :userId AND " +
           "bi.installationType IN ('GROUP', 'CHANNEL') AND bi.status = 'ACTIVE' " +
           "ORDER BY bi.lastUsedAt DESC NULLS LAST")
    List<BotInstallation> findUserGroupInstallations(@Param("userId") Long userId);

    /**
     * 查找用户的私聊机器人
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.installedBy = :userId AND " +
           "bi.installationType = 'PRIVATE' AND bi.status = 'ACTIVE' " +
           "ORDER BY bi.lastUsedAt DESC NULLS LAST")
    List<BotInstallation> findUserPrivateInstallations(@Param("userId") Long userId);

    /**
     * 统计用户各类型安装数量
     */
    @Query("SELECT bi.installationType, COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.installedBy = :userId AND bi.status = 'ACTIVE' GROUP BY bi.installationType")
    List<Object[]> countUserInstallationsByType(@Param("userId") Long userId);

    // ============================================================================
    // Missing Methods Required by BotInstallationService
    // ============================================================================

    /**
     * 根据机器人ID、聊天ID、安装类型查找活跃安装
     */
    Optional<BotInstallation> findByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(
            Long botId, String chatId, BotInstallation.InstallationType installationType);

    /**
     * 检查机器人是否已在指定聊天中安装
     */
    boolean existsByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(
            Long botId, String chatId, BotInstallation.InstallationType installationType);

    /**
     * 根据用户ID查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByUserIdAndIsActiveTrueOrderByInstalledAtDesc(String userId);

    /**
     * 根据聊天ID查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByChatIdAndIsActiveTrueOrderByInstalledAtDesc(String chatId);

    /**
     * 根据机器人ID查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByBotIdAndIsActiveTrueOrderByInstalledAtDesc(Long botId);

    /**
     * 根据安装类型查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByInstallationTypeAndIsActiveTrueOrderByInstalledAtDesc(
            BotInstallation.InstallationType installationType);

    /**
     * 统计各安装类型数量
     */
    @Query("SELECT bi.installationType, COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.installationType")
    List<Object[]> countByInstallationType();

    /**
     * 统计各机器人的安装数量
     */
    @Query("SELECT bi.botId, COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.botId ORDER BY COUNT(bi) DESC")
    List<Object[]> countByBot();

    /**
     * 获取安装活动统计
     */
    @Query("SELECT " +
           "COUNT(bi) as totalInstallations, " +
           "COUNT(CASE WHEN bi.isActive = true THEN 1 END) as activeInstallations, " +
           "COUNT(CASE WHEN bi.lastUsedAt >= :recentThreshold THEN 1 END) as recentlyUsed " +
           "FROM BotInstallation bi")
    Object[] getInstallationActivityStats(@Param("recentThreshold") Instant recentThreshold);

    /**
     * 获取安装趋势
     */
    @Query("SELECT DATE(bi.installedAt), COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.installedAt >= :since " +
           "GROUP BY DATE(bi.installedAt) ORDER BY DATE(bi.installedAt)")
    List<Object[]> getInstallationTrends(@Param("since") Instant since);

    /**
     * 统计活跃安装数量
     */
    long countByIsActiveTrue();

    /**
     * 查找最多安装的机器人
     */
    @Query("SELECT bi.botId, COUNT(bi) as installCount FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.botId ORDER BY installCount DESC")
    List<Object[]> findMostInstalledBots();

    /**
     * 查找非活跃安装（重载方法）
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.isActive = :isActive AND " +
           "(bi.lastUsedAt IS NULL OR bi.lastUsedAt < :threshold)")
    List<BotInstallation> findInactiveInstallations(@Param("threshold") Long thresholdDays, @Param("isActive") boolean isActive);

    /**
     * 根据用户ID分页查找安装（按安装时间排序）
     */
    Page<BotInstallation> findByUserIdOrderByInstalledAtDesc(String userId, Pageable pageable);

    /**
     * 根据聊天ID分页查找安装（按安装时间排序）
     */
    Page<BotInstallation> findByChatIdOrderByInstalledAtDesc(String chatId, Pageable pageable);

    /**
     * 统计用户活跃安装数量
     */
    long countByUserIdAndIsActiveTrue(String userId);

    /**
     * 统计聊天活跃安装数量
     */
    long countByChatIdAndIsActiveTrue(String chatId);

    /**
     * 根据机器人ID统计安装数量
     */
    long countByBotId(Long botId);

    /**
     * 统计指定时间以来的活跃安装数量
     */
    @Query("SELECT COUNT(bi) FROM BotInstallation bi WHERE bi.botId = :botId AND " +
           "bi.status = 'ACTIVE' AND bi.lastUsedAt >= :since")
    long countActiveInstallationsSince(@Param("botId") Long botId, @Param("since") Instant since);

    /**
     * 根据机器人ID查找所有安装
     */
    List<BotInstallation> findByBotId(Long botId);

    /**
     * 根据用户ID、机器人ID和状态查找安装
     */
    List<BotInstallation> findByUserIdAndBotIdAndStatus(String userId, Long botId,
                                                       BotInstallation.InstallationStatus status);
}
