package com.implatform.realtime.service.impl;

import com.implatform.common.core.enums.GroupErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.realtime.entity.GroupRole;
import com.implatform.realtime.repository.GroupRoleRepository;
import com.implatform.service.GroupRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群组角色服务实现
 * 提供群组角色管理、层级控制和权限继承功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(isolation = Isolation.READ_COMMITTED)
public class GroupRoleServiceImpl implements GroupRoleService {

    private final GroupRoleRepository groupRoleRepository;

    @Override
    @CacheEvict(value = "groupRoles", key = "#groupId")
    public GroupRole createRole(@NotNull Long groupId, @NotNull String roleName, 
                               String displayName, String description, @NotNull Integer hierarchyLevel, 
                               Long parentRoleId, @NotNull Long createdBy) {
        log.info("Creating role for group {}: name={}, level={}", groupId, roleName, hierarchyLevel);
        
        // 验证参数
        validateRoleCreationParameters(groupId, roleName, hierarchyLevel);
        
        // 检查角色名称是否已存在
        if (groupRoleRepository.existsByGroupIdAndRoleNameAndIsActiveTrue(groupId, roleName)) {
            throw new IllegalStateException(String.format("[GRP-ROLE-001] Role name '%s' already exists in group %d", 
                    roleName, groupId));
        }
        
        // 检查层级等级是否已被占用
        if (groupRoleRepository.existsByGroupIdAndHierarchyLevelAndIsActiveTrue(groupId, hierarchyLevel)) {
            throw new IllegalStateException(String.format("[GRP-ROLE-002] Hierarchy level %d already occupied in group %d", 
                    hierarchyLevel, groupId));
        }
        
        // 验证父角色关系
        if (parentRoleId != null) {
            validateParentRoleRelation(groupId, parentRoleId, hierarchyLevel);
        }
        
        // 创建角色
        GroupRole role = GroupRole.builder()
                .groupId(groupId)
                .roleName(roleName)
                .displayName(StringUtils.hasText(displayName) ? displayName : roleName)
                .description(description)
                .hierarchyLevel(hierarchyLevel)
                .parentRoleId(parentRoleId)
                .isSystemRole(false)
                .isDeletable(true)
                .isEditable(true)
                .isActive(true)
                .currentMemberCount(0)
                .sortOrder(hierarchyLevel * 10)
                .createdBy(createdBy)
                .build();
        
        GroupRole savedRole = groupRoleRepository.save(role);
        log.info("Successfully created role {} for group {}", savedRole.getId(), groupId);
        
        return savedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#groupId")
    public GroupRole createRoleFromTemplate(@NotNull Long groupId, @NotNull GroupRole.RoleTemplate roleTemplate,
                                           @NotNull Integer hierarchyLevel, @NotNull Long createdBy) {
        log.info("Creating role from template for group {}: template={}, level={}", 
                groupId, roleTemplate, hierarchyLevel);
        
        // 检查模板角色是否已存在
        List<GroupRole> existingTemplateRoles = groupRoleRepository.findByGroupIdAndRoleTemplateAndIsActiveTrue(
                groupId, roleTemplate);
        if (!existingTemplateRoles.isEmpty()) {
            throw new IllegalStateException(String.format("[GRP-ROLE-003] Template role '%s' already exists in group %d", 
                    roleTemplate, groupId));
        }
        
        // 创建基于模板的角色
        GroupRole role = GroupRole.createSystemRole(groupId, roleTemplate, hierarchyLevel, createdBy);
        
        GroupRole savedRole = groupRoleRepository.save(role);
        log.info("Successfully created template role {} for group {}", savedRole.getId(), groupId);
        
        return savedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole updateRole(@NotNull Long roleId, String displayName, String description, 
                               String roleColor, @NotNull Long modifierId) {
        log.info("Updating role {}: displayName={}, color={}", roleId, displayName, roleColor);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> BusinessException.of(GroupErrorCode.ROLE_NOT_FOUND));
        
        if (!role.getIsEditable()) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
        
        role.updateRole(displayName, description, roleColor, modifierId, "Role information updated");
        
        GroupRole updatedRole = groupRoleRepository.save(role);
        log.info("Successfully updated role {}", roleId);
        
        return updatedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public void deleteRole(@NotNull Long roleId, @NotNull Long deletedBy) {
        log.info("Deleting role {}", roleId);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-006] Role %d not found", roleId)));
        
        if (!role.getIsDeletable()) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
        
        if (role.getCurrentMemberCount() > 0) {
            throw new IllegalStateException(String.format("[GRP-ROLE-008] Role %d still has %d members", 
                    roleId, role.getCurrentMemberCount()));
        }
        
        // 软删除：设置为非激活状态
        role.deactivate(deletedBy);
        groupRoleRepository.save(role);
        
        log.info("Successfully deleted role {}", roleId);
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole activateRole(@NotNull Long roleId, @NotNull Long activatedBy) {
        log.info("Activating role {}", roleId);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-009] Role %d not found", roleId)));
        
        role.activate(activatedBy);
        GroupRole activatedRole = groupRoleRepository.save(role);
        
        log.info("Successfully activated role {}", roleId);
        return activatedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole deactivateRole(@NotNull Long roleId, @NotNull Long deactivatedBy) {
        log.info("Deactivating role {}", roleId);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-010] Role %d not found", roleId)));
        
        role.deactivate(deactivatedBy);
        GroupRole deactivatedRole = groupRoleRepository.save(role);
        
        log.info("Successfully deactivated role {}", roleId);
        return deactivatedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole setParentRole(@NotNull Long roleId, Long parentRoleId, @NotNull Long modifierId) {
        log.info("Setting parent role for {}: parentId={}", roleId, parentRoleId);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-011] Role %d not found", roleId)));
        
        if (parentRoleId != null) {
            validateParentRoleRelation(role.getGroupId(), parentRoleId, role.getHierarchyLevel());
            
            // 检查是否会产生循环继承
            if (wouldCreateCircularInheritance(roleId, parentRoleId)) {
                throw new IllegalStateException(String.format("[GRP-ROLE-012] Setting parent %d for role %d would create circular inheritance", 
                        parentRoleId, roleId));
            }
        }
        
        role.setParentRole(parentRoleId, modifierId);
        GroupRole updatedRole = groupRoleRepository.save(role);
        
        log.info("Successfully set parent role for {}", roleId);
        return updatedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole removeParentRole(@NotNull Long roleId, @NotNull Long modifierId) {
        log.info("Removing parent role for {}", roleId);
        
        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-013] Role %d not found", roleId)));
        
        role.setParentRole(null, modifierId);
        GroupRole updatedRole = groupRoleRepository.save(role);
        
        log.info("Successfully removed parent role for {}", roleId);
        return updatedRole;
    }

    @Override
    public boolean incrementMemberCount(@NotNull Long roleId) {
        log.debug("Incrementing member count for role {}", roleId);
        
        int updated = groupRoleRepository.updateMemberCount(roleId, 1);
        boolean success = updated > 0;
        
        if (success) {
            log.debug("Successfully incremented member count for role {}", roleId);
        } else {
            log.warn("Failed to increment member count for role {}", roleId);
        }
        
        return success;
    }

    @Override
    public boolean decrementMemberCount(@NotNull Long roleId) {
        log.debug("Decrementing member count for role {}", roleId);
        
        int updated = groupRoleRepository.updateMemberCount(roleId, -1);
        boolean success = updated > 0;
        
        if (success) {
            log.debug("Successfully decremented member count for role {}", roleId);
        } else {
            log.warn("Failed to decrement member count for role {}", roleId);
        }
        
        return success;
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#managerRoleId + '_can_manage_' + #targetRoleId")
    public boolean canManageRole(@NotNull Long managerRoleId, @NotNull Long targetRoleId) {
        if (managerRoleId.equals(targetRoleId)) {
            return false; // 不能管理自己
        }
        
        Optional<GroupRole> managerRole = groupRoleRepository.findById(managerRoleId);
        Optional<GroupRole> targetRole = groupRoleRepository.findById(targetRoleId);
        
        if (managerRole.isEmpty() || targetRole.isEmpty()) {
            return false;
        }
        
        return managerRole.get().canManageRole(targetRole.get());
    }

    @Override
    public boolean isMemberLimitReached(@NotNull Long roleId) {
        return groupRoleRepository.findById(roleId)
                .map(GroupRole::isMemberLimitReached)
                .orElse(false);
    }

    @Override
    @Cacheable(value = "groupRoles", key = "#roleId")
    public Optional<GroupRole> getRoleById(@NotNull Long roleId) {
        return groupRoleRepository.findById(roleId);
    }

    @Override
    @Cacheable(value = "groupRoles", key = "#groupId + '_' + #roleName")
    public Optional<GroupRole> getRoleByName(@NotNull Long groupId, @NotNull String roleName) {
        return groupRoleRepository.findByGroupIdAndRoleNameAndIsActiveTrue(groupId, roleName);
    }

    @Override
    @Cacheable(value = "groupRoles", key = "#groupId")
    public List<GroupRole> getGroupRoles(@NotNull Long groupId) {
        return groupRoleRepository.findByGroupIdAndIsActiveTrueOrderByHierarchyLevelAsc(groupId);
    }

    @Override
    public List<GroupRole> getSystemRoles(@NotNull Long groupId) {
        return groupRoleRepository.findByGroupIdAndIsSystemRoleTrueAndIsActiveTrueOrderByHierarchyLevelAsc(groupId);
    }

    @Override
    public List<GroupRole> getCustomRoles(@NotNull Long groupId) {
        return groupRoleRepository.findByGroupIdAndIsSystemRoleFalseAndIsActiveTrueOrderByHierarchyLevelAsc(groupId);
    }

    @Override
    public List<GroupRole> getChildRoles(@NotNull Long parentRoleId) {
        return groupRoleRepository.findByParentRoleIdAndIsActiveTrueOrderByHierarchyLevelAsc(parentRoleId);
    }

    @Override
    public List<GroupRole> getRoleInheritanceChain(@NotNull Long roleId) {
        List<GroupRole> chain = new ArrayList<>();
        Optional<GroupRole> currentRole = groupRoleRepository.findById(roleId);
        
        while (currentRole.isPresent()) {
            chain.add(currentRole.get());
            Long parentId = currentRole.get().getParentRoleId();
            if (parentId != null) {
                currentRole = groupRoleRepository.findById(parentId);
            } else {
                break;
            }
        }
        
        return chain;
    }

    @Override
    public Optional<GroupRole> getOwnerRole(@NotNull Long groupId) {
        return groupRoleRepository.findOwnerRole(groupId);
    }

    @Override
    public List<GroupRole> getAdminRoles(@NotNull Long groupId) {
        return groupRoleRepository.findAdminRoles(groupId);
    }

    @Override
    public List<GroupRole> getModeratorRoles(@NotNull Long groupId) {
        return groupRoleRepository.findModeratorRoles(groupId);
    }

    @Override
    public Optional<GroupRole> getMemberRole(@NotNull Long groupId) {
        return groupRoleRepository.findMemberRole(groupId);
    }

    @Override
    public RoleStatistics getRoleStatistics(@NotNull Long groupId) {
        Object[] stats = groupRoleRepository.getRoleStatistics(groupId);
        
        int totalRoles = ((Number) stats[0]).intValue();
        int systemRoles = ((Number) stats[1]).intValue();
        int customRoles = ((Number) stats[2]).intValue();
        int activeRoles = ((Number) stats[3]).intValue();
        
        // 计算有成员的角色数量
        List<GroupRole> roles = getGroupRoles(groupId);
        int rolesWithMembers = (int) roles.stream()
                .filter(role -> role.getCurrentMemberCount() > 0)
                .count();
        
        return new RoleStatistics(totalRoles, systemRoles, customRoles, activeRoles, rolesWithMembers);
    }

    @Override
    public Page<GroupRole> getTopRolesByMemberCount(@NotNull Long groupId, Pageable pageable) {
        return groupRoleRepository.findTopRolesByMemberCount(groupId, pageable);
    }

    /**
     * 验证角色创建参数
     */
    private void validateRoleCreationParameters(Long groupId, String roleName, Integer hierarchyLevel) {
        if (groupId == null || groupId <= 0) {
            throw new BusinessException(GroupErrorCode.INVALID_GROUP_ID);
        }
        
        if (!StringUtils.hasText(roleName)) {
            throw new BusinessException(GroupErrorCode.INVALID_GROUP_NAME);
        }
        
        if (hierarchyLevel == null || hierarchyLevel < 1 || hierarchyLevel > 10) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 验证父角色关系
     */
    private void validateParentRoleRelation(Long groupId, Long parentRoleId, Integer childHierarchyLevel) {
        GroupRole parentRole = groupRoleRepository.findById(parentRoleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-017] Parent role %d not found", parentRoleId)));
        
        if (!parentRole.getGroupId().equals(groupId)) {
            throw new BusinessException(GroupErrorCode.INVALID_GROUP_ID);
        }
        
        if (!parentRole.getIsActive()) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
        
        if (parentRole.getHierarchyLevel() >= childHierarchyLevel) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }
    }

    /**
     * 检查是否会产生循环继承
     */
    private boolean wouldCreateCircularInheritance(Long roleId, Long parentRoleId) {
        Set<Long> visited = new HashSet<>();
        Long currentId = parentRoleId;
        
        while (currentId != null && !visited.contains(currentId)) {
            if (currentId.equals(roleId)) {
                return true; // 发现循环
            }
            
            visited.add(currentId);
            Optional<GroupRole> currentRole = groupRoleRepository.findById(currentId);
            currentId = currentRole.map(GroupRole::getParentRoleId).orElse(null);
        }
        
        return false;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole adjustHierarchyLevel(@NotNull Long roleId, @NotNull Integer newHierarchyLevel,
                                         @NotNull Long modifierId) {
        log.info("Adjusting hierarchy level for role {}: newLevel={}", roleId, newHierarchyLevel);

        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-021] Role %d not found", roleId)));

        if (newHierarchyLevel < 1 || newHierarchyLevel > 10) {
            throw new BusinessException(GroupErrorCode.OPERATION_NOT_ALLOWED);
        }

        // 检查新层级是否已被占用
        if (groupRoleRepository.existsByGroupIdAndHierarchyLevelAndIsActiveTrue(
                role.getGroupId(), newHierarchyLevel)) {
            throw new IllegalStateException(String.format("[GRP-ROLE-023] Hierarchy level %d already occupied",
                    newHierarchyLevel));
        }

        role.adjustHierarchyLevel(newHierarchyLevel, modifierId);
        GroupRole updatedRole = groupRoleRepository.save(role);

        log.info("Successfully adjusted hierarchy level for role {}", roleId);
        return updatedRole;
    }

    @Override
    @CacheEvict(value = "groupRoles", key = "#result.groupId")
    public GroupRole setMemberLimit(@NotNull Long roleId, Integer memberLimit, @NotNull Long modifierId) {
        log.info("Setting member limit for role {}: limit={}", roleId, memberLimit);

        GroupRole role = groupRoleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-024] Role %d not found", roleId)));

        role.setMemberLimit(memberLimit, modifierId);
        GroupRole updatedRole = groupRoleRepository.save(role);

        log.info("Successfully set member limit for role {}", roleId);
        return updatedRole;
    }

    @Override
    public List<GroupRole> getRolesCanManage(@NotNull Long groupId, @NotNull Long targetRoleId) {
        GroupRole targetRole = groupRoleRepository.findById(targetRoleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-025] Target role %d not found", targetRoleId)));

        return groupRoleRepository.findRolesCanManage(groupId, targetRole.getHierarchyLevel());
    }

    @Override
    public RoleHierarchyValidationResult validateRoleHierarchy(@NotNull Long groupId) {
        log.info("Validating role hierarchy for group {}", groupId);

        List<String> issues = new ArrayList<>();
        List<Long> orphanRoles = new ArrayList<>();
        List<Long> circularRoles = new ArrayList<>();

        // 检查孤立角色
        List<GroupRole> orphans = groupRoleRepository.findOrphanRoles(groupId);
        orphanRoles.addAll(orphans.stream().map(GroupRole::getId).collect(Collectors.toList()));
        if (!orphans.isEmpty()) {
            issues.add(String.format("Found %d orphan roles (non-root roles without parent)", orphans.size()));
        }

        // 检查循环继承
        List<Long> circular = groupRoleRepository.findCircularInheritanceRoles(groupId);
        circularRoles.addAll(circular);
        if (!circular.isEmpty()) {
            issues.add(String.format("Found %d roles with circular inheritance", circular.size()));
        }

        // 检查层级结构
        List<Object[]> hierarchy = groupRoleRepository.getRoleHierarchyStructure(groupId);
        for (Object[] level : hierarchy) {
            Integer hierarchyLevel = (Integer) level[0];
            Long count = (Long) level[1];
            if (count > 1) {
                issues.add(String.format("Hierarchy level %d has %d roles (should be unique)",
                        hierarchyLevel, count));
            }
        }

        boolean isValid = issues.isEmpty();

        log.info("Role hierarchy validation for group {} completed: valid={}, issues={}",
                groupId, isValid, issues.size());

        return new RoleHierarchyValidationResult(isValid, issues, orphanRoles, circularRoles);
    }

    @Override
    @Transactional
    public RoleHierarchyFixResult fixRoleHierarchyIssues(@NotNull Long groupId, @NotNull Long fixedBy) {
        log.info("Fixing role hierarchy issues for group {}", groupId);

        List<String> fixedProblems = new ArrayList<>();
        List<String> remainingIssues = new ArrayList<>();
        int fixedCount = 0;

        // 修复孤立角色：将它们设置为普通成员角色的子角色
        List<GroupRole> orphanRoles = groupRoleRepository.findOrphanRoles(groupId);
        if (!orphanRoles.isEmpty()) {
            Optional<GroupRole> memberRole = getMemberRole(groupId);
            if (memberRole.isPresent()) {
                for (GroupRole orphan : orphanRoles) {
                    orphan.setParentRole(memberRole.get().getId(), fixedBy);
                    groupRoleRepository.save(orphan);
                    fixedCount++;
                }
                fixedProblems.add(String.format("Fixed %d orphan roles by setting parent to member role",
                        orphanRoles.size()));
            } else {
                remainingIssues.add("Cannot fix orphan roles: no member role found");
            }
        }

        // 其他修复逻辑可以在这里添加...

        log.info("Role hierarchy fix for group {} completed: fixed={}", groupId, fixedCount);

        return new RoleHierarchyFixResult(fixedCount, fixedProblems, remainingIssues);
    }

    @Override
    @Transactional
    public int batchUpdateRoleStatus(@NotNull List<Long> roleIds, @NotNull Boolean isActive,
                                   @NotNull Long modifierId) {
        log.info("Batch updating role status: roleIds={}, active={}", roleIds.size(), isActive);

        int updated = groupRoleRepository.batchUpdateActiveStatus(roleIds, isActive, modifierId);

        // 清除相关缓存
        roleIds.forEach(roleId -> {
            Optional<GroupRole> role = groupRoleRepository.findById(roleId);
            role.ifPresent(r -> {
                // 这里可以添加缓存清除逻辑
            });
        });

        log.info("Batch updated {} role statuses", updated);
        return updated;
    }

    @Override
    @Transactional
    public GroupRole copyRoleToGroup(@NotNull Long sourceRoleId, @NotNull Long targetGroupId,
                                   @NotNull Long copiedBy) {
        log.info("Copying role {} to group {}", sourceRoleId, targetGroupId);

        GroupRole sourceRole = groupRoleRepository.findById(sourceRoleId)
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("[GRP-ROLE-026] Source role %d not found", sourceRoleId)));

        // 检查目标群组中是否已有同名角色
        if (groupRoleRepository.existsByGroupIdAndRoleNameAndIsActiveTrue(
                targetGroupId, sourceRole.getRoleName())) {
            throw new IllegalStateException(String.format("[GRP-ROLE-027] Role name '%s' already exists in target group",
                    sourceRole.getRoleName()));
        }

        // 创建角色副本
        GroupRole copiedRole = GroupRole.builder()
                .groupId(targetGroupId)
                .roleName(sourceRole.getRoleName())
                .displayName(sourceRole.getDisplayName())
                .description(sourceRole.getDescription() + " (Copied)")
                .hierarchyLevel(sourceRole.getHierarchyLevel())
                .roleTemplate(sourceRole.getRoleTemplate())
                .roleColor(sourceRole.getRoleColor())
                .iconUrl(sourceRole.getIconUrl())
                .isSystemRole(false) // 复制的角色不是系统角色
                .isDeletable(true)
                .isEditable(true)
                .isActive(true)
                .memberLimit(sourceRole.getMemberLimit())
                .currentMemberCount(0)
                .sortOrder(sourceRole.getSortOrder())
                .createdBy(copiedBy)
                .build();

        GroupRole savedRole = groupRoleRepository.save(copiedRole);
        log.info("Successfully copied role {} to group {} as role {}",
                sourceRoleId, targetGroupId, savedRole.getId());

        return savedRole;
    }
}
