package com.implatform.realtime.repository;

import com.implatform.realtime.entity.MessageEditHistory;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 消息编辑历史Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface MessageEditHistoryRepository extends R2dbcRepository<MessageEditHistory, Long> {

    /**
     * 根据消息ID查找编辑历史
     */
    @Query("SELECT * FROM message_edit_histories WHERE message_id = :messageId ORDER BY edit_version ASC")
    Flux<MessageEditHistory> findByMessageIdOrderByEditVersionAsc(@Param("messageId") Long messageId);

    /**
     * 根据消息ID查找编辑历史（分页）
     */
    @Query("SELECT * FROM message_edit_histories WHERE message_id = :messageId ORDER BY edit_version DESC LIMIT :limit OFFSET :offset")
    Flux<MessageEditHistory> findByMessageIdOrderByEditVersionDesc(@Param("messageId") Long messageId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据消息ID和版本号查找编辑历史
     */
    @Query("SELECT * FROM message_edit_histories WHERE message_id = :messageId AND edit_version = :editVersion")
    Mono<MessageEditHistory> findByMessageIdAndEditVersion(@Param("messageId") Long messageId, @Param("editVersion") Integer editVersion);

    /**
     * 获取消息的最新编辑记录
     */
    @Query("SELECT * FROM message_edit_histories WHERE message_id = :messageId " +
           "ORDER BY edit_version DESC LIMIT 1")
    Mono<MessageEditHistory> findLatestEditByMessageId(@Param("messageId") Long messageId);

    /**
     * 获取消息的第一次编辑记录
     */
    @Query("SELECT * FROM message_edit_histories WHERE message_id = :messageId " +
           "ORDER BY edit_version ASC LIMIT 1")
    Optional<MessageEditHistory> findFirstEditByMessageId(@Param("messageId") Long messageId);

    /**
     * 统计消息的编辑次数
     */
    @Query("SELECT COUNT(h) FROM MessageEditHistory h WHERE h.messageId = :messageId")
    Long countEditsByMessageId(@Param("messageId") Long messageId);

    /**
     * 查找用户的编辑历史
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.editorUserId = :userId " +
           "ORDER BY h.createdAt DESC")
    Page<MessageEditHistory> findUserEditHistory(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找指定时间段内的编辑记录
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY h.createdAt DESC")
    List<MessageEditHistory> findEditsBetweenTime(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查找指定编辑类型的记录
     */
    List<MessageEditHistory> findByEditTypeOrderByCreatedAtDesc(MessageEditHistory.EditType editType);

    /**
     * 统计用户的编辑次数
     */
    @Query("SELECT COUNT(h) FROM MessageEditHistory h WHERE h.editorUserId = :userId")
    Long countEditsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户在指定时间段内的编辑次数
     */
    @Query("SELECT COUNT(h) FROM MessageEditHistory h WHERE h.editorUserId = :userId " +
           "AND h.createdAt BETWEEN :startTime AND :endTime")
    Long countUserEditsBetweenTime(@Param("userId") Long userId,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查找频繁编辑的消息
     */
    @Query("SELECT h.messageId, COUNT(h) as editCount FROM MessageEditHistory h " +
           "GROUP BY h.messageId HAVING COUNT(h) >= :minEdits " +
           "ORDER BY editCount DESC")
    List<Object[]> findFrequentlyEditedMessages(@Param("minEdits") Integer minEdits, Pageable pageable);

    /**
     * 查找活跃的编辑用户
     */
    @Query("SELECT h.editorUserId, COUNT(h) as editCount FROM MessageEditHistory h " +
           "WHERE h.createdAt >= :since " +
           "GROUP BY h.editorUserId ORDER BY editCount DESC")
    List<Object[]> findActiveEditors(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 获取编辑统计信息
     */
    @Query("SELECT " +
           "COUNT(h) as totalEdits, " +
           "COUNT(DISTINCT h.messageId) as editedMessages, " +
           "COUNT(DISTINCT h.editorUserId) as activeEditors, " +
           "AVG(h.editVersion) as avgEditsPerMessage " +
           "FROM MessageEditHistory h WHERE h.createdAt >= :since")
    Object[] getEditStatistics(@Param("since") LocalDateTime since);

    /**
     * 按编辑类型统计
     */
    @Query("SELECT h.editType, COUNT(h) FROM MessageEditHistory h " +
           "WHERE h.createdAt >= :since " +
           "GROUP BY h.editType")
    List<Object[]> getEditStatsByType(@Param("since") LocalDateTime since);

    /**
     * 查找最近编辑的消息
     */
    @Query("SELECT DISTINCT h.messageId FROM MessageEditHistory h " +
           "WHERE h.createdAt >= :since " +
           "ORDER BY MAX(h.createdAt) DESC")
    List<Long> findRecentlyEditedMessages(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查找有编辑原因的记录
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.editReason IS NOT NULL " +
           "AND h.editReason != '' ORDER BY h.createdAt DESC")
    List<MessageEditHistory> findEditsWithReason(Pageable pageable);

    /**
     * 查找指定IP地址的编辑记录
     */
    List<MessageEditHistory> findByIpAddressOrderByCreatedAtDesc(String ipAddress);

    /**
     * 查找指定客户端的编辑记录
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.clientInfo LIKE %:clientInfo% " +
           "ORDER BY h.createdAt DESC")
    List<MessageEditHistory> findByClientInfo(@Param("clientInfo") String clientInfo, Pageable pageable);

    /**
     * 删除旧的编辑历史记录
     */
    @Query("DELETE FROM MessageEditHistory h WHERE h.createdAt < :cutoffTime")
    int deleteOldEditHistory(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找消息的编辑版本范围
     */
    @Query("SELECT MIN(h.editVersion), MAX(h.editVersion) FROM MessageEditHistory h " +
           "WHERE h.messageId = :messageId")
    Object[] getMessageEditVersionRange(@Param("messageId") Long messageId);

    /**
     * 查找指定版本之后的编辑记录
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.messageId = :messageId " +
           "AND h.editVersion > :fromVersion ORDER BY h.editVersion ASC")
    List<MessageEditHistory> findEditsAfterVersion(@Param("messageId") Long messageId,
                                                   @Param("fromVersion") Integer fromVersion);

    /**
     * 查找指定版本之前的编辑记录
     */
    @Query("SELECT h FROM MessageEditHistory h WHERE h.messageId = :messageId " +
           "AND h.editVersion < :toVersion ORDER BY h.editVersion DESC")
    List<MessageEditHistory> findEditsBeforeVersion(@Param("messageId") Long messageId,
                                                    @Param("toVersion") Integer toVersion);

    /**
     * 获取消息编辑时间线
     */
    @Query("SELECT h.editVersion, h.createdAt, h.editType, h.editReason " +
           "FROM MessageEditHistory h WHERE h.messageId = :messageId " +
           "ORDER BY h.editVersion ASC")
    List<Object[]> getMessageEditTimeline(@Param("messageId") Long messageId);
}
