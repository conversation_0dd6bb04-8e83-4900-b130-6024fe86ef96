package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CheckinLeaderboard;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 签到排行榜Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CheckinLeaderboardRepository extends R2dbcRepository<CheckinLeaderboard, Long> {

    /**
     * 根据用户ID、排行类型和周期查找排行榜记录
     */
    @Query("SELECT * FROM checkin_leaderboards WHERE user_id = :userId AND ranking_type = :rankingType AND ranking_period = :rankingPeriod")
    Mono<CheckinLeaderboard> findByUserIdAndRankingTypeAndRankingPeriod(
            @Param("userId") Long userId, @Param("rankingType") String rankingType, @Param("rankingPeriod") String rankingPeriod);

    /**
     * 检查用户是否在指定排行榜中
     */
    @Query("SELECT COUNT(*) > 0 FROM checkin_leaderboards WHERE user_id = :userId AND ranking_type = :rankingType AND ranking_period = :rankingPeriod")
    Mono<Boolean> existsByUserIdAndRankingTypeAndRankingPeriod(
            @Param("userId") Long userId, @Param("rankingType") String rankingType, @Param("rankingPeriod") String rankingPeriod);

    /**
     * 获取指定类型和周期的排行榜（分页）
     */
    @Query("SELECT * FROM checkin_leaderboards WHERE ranking_type = :rankingType AND ranking_period = :rankingPeriod ORDER BY rank_position ASC LIMIT :limit OFFSET :offset")
    Flux<CheckinLeaderboard> findByRankingTypeAndRankingPeriodOrderByRankPositionAsc(
            @Param("rankingType") String rankingType, @Param("rankingPeriod") String rankingPeriod, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 获取指定类型和周期的排行榜（按分数排序）
     */
    @Query("SELECT * FROM checkin_leaderboards WHERE ranking_type = :rankingType AND ranking_period = :rankingPeriod ORDER BY score DESC, rank_position ASC")
    Flux<CheckinLeaderboard> findByRankingTypeAndRankingPeriodOrderByScoreDescRankPositionAsc(
            @Param("rankingType") String rankingType, @Param("rankingPeriod") String rankingPeriod);

    /**
     * 获取用户在指定排行榜中的排名
     */
    @Query("SELECT l.rankPosition FROM CheckinLeaderboard l WHERE l.userId = :userId " +
           "AND l.rankingType = :rankingType AND l.rankingPeriod = :rankingPeriod")
    Optional<Integer> getUserRankPosition(@Param("userId") Long userId,
                                          @Param("rankingType") CheckinLeaderboard.RankingType rankingType,
                                          @Param("rankingPeriod") String rankingPeriod);

    /**
     * 获取指定排名位置的记录
     */
    Optional<CheckinLeaderboard> findByRankingTypeAndRankingPeriodAndRankPosition(
            CheckinLeaderboard.RankingType rankingType, String rankingPeriod, Integer rankPosition);

    /**
     * 获取指定排名范围的记录
     */
    List<CheckinLeaderboard> findByRankingTypeAndRankingPeriodAndRankPositionBetweenOrderByRankPositionAsc(
            CheckinLeaderboard.RankingType rankingType, String rankingPeriod, 
            Integer startRank, Integer endRank);

    /**
     * 获取用户的所有排行榜记录
     */
    List<CheckinLeaderboard> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 获取用户指定类型的排行榜记录
     */
    List<CheckinLeaderboard> findByUserIdAndRankingTypeOrderByRankingPeriodDesc(
            Long userId, CheckinLeaderboard.RankingType rankingType);

    /**
     * 统计指定排行榜的总人数
     */
    long countByRankingTypeAndRankingPeriod(CheckinLeaderboard.RankingType rankingType, String rankingPeriod);

    /**
     * 获取指定分数以上的排名数量
     */
    @Query("SELECT COUNT(l) FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType " +
           "AND l.rankingPeriod = :rankingPeriod AND l.score > :score")
    long countByScoreGreaterThan(@Param("rankingType") CheckinLeaderboard.RankingType rankingType,
                                 @Param("rankingPeriod") String rankingPeriod,
                                 @Param("score") Integer score);

    /**
     * 获取最高分记录
     */
    Optional<CheckinLeaderboard> findTopByRankingTypeAndRankingPeriodOrderByScoreDesc(
            CheckinLeaderboard.RankingType rankingType, String rankingPeriod);

    /**
     * 获取平均分数
     */
    @Query("SELECT AVG(l.score) FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType " +
           "AND l.rankingPeriod = :rankingPeriod")
    Double getAverageScore(@Param("rankingType") CheckinLeaderboard.RankingType rankingType,
                           @Param("rankingPeriod") String rankingPeriod);

    /**
     * 获取所有排行榜类型
     */
    @Query("SELECT DISTINCT l.rankingType FROM CheckinLeaderboard l ORDER BY l.rankingType")
    List<CheckinLeaderboard.RankingType> findAllRankingTypes();

    /**
     * 获取指定类型的所有周期
     */
    @Query("SELECT DISTINCT l.rankingPeriod FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType " +
           "ORDER BY l.rankingPeriod DESC")
    List<String> findAllPeriodsByType(@Param("rankingType") CheckinLeaderboard.RankingType rankingType);

    /**
     * 获取最新的排行榜周期
     */
    @Query("SELECT MAX(l.rankingPeriod) FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType")
    Optional<String> findLatestPeriodByType(@Param("rankingType") CheckinLeaderboard.RankingType rankingType);

    /**
     * 批量更新排名位置
     */
    @Modifying
    @Query("UPDATE CheckinLeaderboard l SET l.rankPosition = :rankPosition WHERE l.id = :id")
    int updateRankPosition(@Param("id") Long id, @Param("rankPosition") Integer rankPosition);

    /**
     * 批量更新点赞数
     */
    @Modifying
    @Query("UPDATE CheckinLeaderboard l SET l.likeCount = l.likeCount + :increment WHERE l.id = :id")
    int updateLikeCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 删除指定周期之前的排行榜数据
     */
    @Modifying
    @Transactional
    void deleteByRankingTypeAndRankingPeriodBefore(CheckinLeaderboard.RankingType rankingType, String period);

    /**
     * 删除指定类型的所有排行榜数据
     */
    @Modifying
    @Transactional
    void deleteByRankingType(CheckinLeaderboard.RankingType rankingType);

    /**
     * 获取排行榜统计信息
     */
    @Query("SELECT l.rankingType, l.rankingPeriod, COUNT(l), MAX(l.score), AVG(l.score), SUM(l.likeCount) " +
           "FROM CheckinLeaderboard l GROUP BY l.rankingType, l.rankingPeriod " +
           "ORDER BY l.rankingType, l.rankingPeriod DESC")
    List<Object[]> getLeaderboardStatistics();

    /**
     * 获取热门排行榜（按点赞数排序）
     */
    List<CheckinLeaderboard> findByRankingTypeAndRankingPeriodOrderByLikeCountDescRankPositionAsc(
            CheckinLeaderboard.RankingType rankingType, String rankingPeriod, Pageable pageable);

    /**
     * 获取用户的最佳排名
     */
    @Query("SELECT MIN(l.rankPosition) FROM CheckinLeaderboard l WHERE l.userId = :userId " +
           "AND l.rankingType = :rankingType")
    Optional<Integer> getUserBestRank(@Param("userId") Long userId,
                                      @Param("rankingType") CheckinLeaderboard.RankingType rankingType);

    /**
     * 获取用户的排行榜参与次数
     */
    @Query("SELECT COUNT(DISTINCT l.rankingPeriod) FROM CheckinLeaderboard l WHERE l.userId = :userId " +
           "AND l.rankingType = :rankingType")
    long getUserParticipationCount(@Param("userId") Long userId,
                                   @Param("rankingType") CheckinLeaderboard.RankingType rankingType);

    /**
     * 查找需要更新的排行榜记录（分数变化）
     */
    @Query("SELECT l FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType " +
           "AND l.rankingPeriod = :rankingPeriod AND l.score != :expectedScore")
    List<CheckinLeaderboard> findRecordsNeedingUpdate(@Param("rankingType") CheckinLeaderboard.RankingType rankingType,
                                                       @Param("rankingPeriod") String rankingPeriod,
                                                       @Param("expectedScore") Integer expectedScore);

    /**
     * 获取前N名用户ID列表
     */
    @Query("SELECT l.userId FROM CheckinLeaderboard l WHERE l.rankingType = :rankingType " +
           "AND l.rankingPeriod = :rankingPeriod ORDER BY l.rankPosition ASC")
    List<Long> getTopUserIds(@Param("rankingType") CheckinLeaderboard.RankingType rankingType,
                             @Param("rankingPeriod") String rankingPeriod,
                             Pageable pageable);
}
