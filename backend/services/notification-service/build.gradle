dependencies {

    // Common Modules (排除JPA依赖，使用R2DBC)
    implementation project(':common:common-core')
    implementation project(':common:common-webflux')
    implementation project(':common:common-messaging')
    implementation project(':common:common-api')
    implementation project(':common:common-security')
    implementation project(':common:common-protobuf')
    implementation(project(':common:common-data')) {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-jpa'
        exclude group: 'com.zaxxer', module: 'HikariCP'
    }

    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

    // Spring Boot Actuator for health checks
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // JPush SDK - 极光推送服务端SDK (使用官方推荐的新版本)
    implementation 'io.github.jpush:jiguang-sdk:5.2.3'
}
