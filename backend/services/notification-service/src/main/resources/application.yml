# =============================================
# notification-service Bootstrap Configuration
# Minimal local config - Business logic in Consul
# =============================================

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}


  # Mail Configuration for Email Services
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-mail-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 30000
          timeout: 30000
          writetimeout: 30000
        debug: false


app:
  # Push通知配置
  push:
    enabled: ${PUSH_NOTIFICATION_ENABLED:true}
    # 移动设备Push配置
    mobile:
      # FCM (Firebase Cloud Messaging) for Android
      fcm:
        server-key: ${FCM_SERVER_KEY:}
        project-id: ${FCM_PROJECT_ID:}
        enabled: ${FCM_ENABLED:false}
        api-url: https://fcm.googleapis.com/fcm/send
        timeout: 10000ms
      # APNs (Apple Push Notification service) for iOS
      apns:
        key-id: ${APNS_KEY_ID:}
        team-id: ${APNS_TEAM_ID:}
        bundle-id: ${APNS_BUNDLE_ID:}
        private-key-path: ${APNS_PRIVATE_KEY_PATH:}
        enabled: ${APNS_ENABLED:false}
        production: ${APNS_PRODUCTION:false}
        timeout: 10000ms
      # JPush (极光推送) for Android & iOS
      jpush:
        app-key: ${JPUSH_APP_KEY:b6142d6ac84333800e640457}
        master-secret: ${JPUSH_MASTER_SECRET:743f849a92c244c42a3599a1}
        enabled: ${JPUSH_ENABLED:true}
        production: ${JPUSH_PRODUCTION:false}
        timeout: 10000
        # 厂商推送通道配置
        vendor-channels:
          xiaomi:
            enabled: ${JPUSH_XIAOMI_ENABLED:true}
          huawei:
            enabled: ${JPUSH_HUAWEI_ENABLED:true}
          oppo:
            enabled: ${JPUSH_OPPO_ENABLED:true}
          vivo:
            enabled: ${JPUSH_VIVO_ENABLED:true}
          meizu:
            enabled: ${JPUSH_MEIZU_ENABLED:true}
        # 推送统计配置
        statistics:
          enabled: ${JPUSH_STATISTICS_ENABLED:true}
          report-interval: ${JPUSH_REPORT_INTERVAL:300000} # 5分钟
    # Web Push配置
    web:
      vapid:
        public-key: ${WEB_PUSH_VAPID_PUBLIC_KEY:}
        private-key: ${WEB_PUSH_VAPID_PRIVATE_KEY:}
        subject: ${WEB_PUSH_VAPID_SUBJECT:mailto:<EMAIL>}
      enabled: ${WEB_PUSH_ENABLED:false}
      timeout: 10000ms
    # 实时消息配置
    realtime:
      enabled: ${REALTIME_PUSH_ENABLED:true}
      websocket-endpoint: /ws/notifications
      heartbeat-interval: 30000ms
      max-connections-per-user: 5
    # 批量处理配置
    batch:
      max-size: ${PUSH_BATCH_MAX_SIZE:1000}
      timeout: ${PUSH_BATCH_TIMEOUT:30000}
      retry-attempts: ${PUSH_BATCH_RETRY_ATTEMPTS:3}
    # 设备管理配置
    device:
      token-expiry-days: ${PUSH_DEVICE_TOKEN_EXPIRY_DAYS:90}
      max-devices-per-user: ${PUSH_MAX_DEVICES_PER_USER:10}
      cleanup-interval: ${PUSH_DEVICE_CLEANUP_INTERVAL:24h}

  # 厂商推送通道配置
  vendor:
    # 小米推送
    xiaomi:
      enabled: ${XIAOMI_PUSH_ENABLED:false}
      app-id: ${XIAOMI_APP_ID:}
      app-key: ${XIAOMI_APP_KEY:}
      app-secret: ${XIAOMI_APP_SECRET:}
      package-name: ${XIAOMI_PACKAGE_NAME:}
      production: ${XIAOMI_PRODUCTION:false}
    # 华为推送
    huawei:
      enabled: ${HUAWEI_PUSH_ENABLED:false}
      app-id: ${HUAWEI_APP_ID:}
      app-secret: ${HUAWEI_APP_SECRET:}
      client-id: ${HUAWEI_CLIENT_ID:}
      client-secret: ${HUAWEI_CLIENT_SECRET:}
      production: ${HUAWEI_PRODUCTION:false}
    # OPPO推送
    oppo:
      enabled: ${OPPO_PUSH_ENABLED:false}
      app-key: ${OPPO_APP_KEY:}
      app-secret: ${OPPO_APP_SECRET:}
      master-secret: ${OPPO_MASTER_SECRET:}
      production: ${OPPO_PRODUCTION:false}
    # vivo推送
    vivo:
      enabled: ${VIVO_PUSH_ENABLED:false}
      app-id: ${VIVO_APP_ID:}
      app-key: ${VIVO_APP_KEY:}
      app-secret: ${VIVO_APP_SECRET:}
      production: ${VIVO_PRODUCTION:false}
    # 魅族推送
    meizu:
      enabled: ${MEIZU_PUSH_ENABLED:false}
      app-id: ${MEIZU_APP_ID:}
      app-key: ${MEIZU_APP_KEY:}
      app-secret: ${MEIZU_APP_SECRET:}
      production: ${MEIZU_PRODUCTION:false}

  # 邮件通知配置
  email:
    enabled: ${EMAIL_NOTIFICATION_ENABLED:true}
    template-path: classpath:templates/email/
    default-from: ${MAIL_USERNAME:}
    default-from-name: ${MAIL_FROM_NAME:IM Platform}

  # 短信配置
  sms:
    enabled: ${SMS_ENABLED:false}
    aliyun:
      access-key-id: ${ALIYUN_ACCESS_KEY_ID:}
      access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:}
      sign-name: ${SMS_SIGN_NAME:IM平台}
      region: ${SMS_REGION:cn-hangzhou}
      endpoint: dysmsapi.aliyuncs.com

  # 异步处理配置
  async:
    core-pool-size: ${NOTIFICATION_ASYNC_CORE_POOL_SIZE:5}
    max-pool-size: ${NOTIFICATION_ASYNC_MAX_POOL_SIZE:20}
    queue-capacity: ${NOTIFICATION_ASYNC_QUEUE_CAPACITY:100}
    thread-name-prefix: notification-

  # Netty网关集成配置
  netty:
    gateway:
      url: ${NETTY_GATEWAY_URL:http://netty-gateway-service:8080}
      notification-endpoint: /api/v1/gateway/notification
      health-endpoint: /health
      timeout: 5000ms

  # 通知处理配置
  processing:
    batch-size: ${NOTIFICATION_BATCH_SIZE:100}
    retry-max-attempts: ${NOTIFICATION_RETRY_MAX_ATTEMPTS:3}
    retry-delay: ${NOTIFICATION_RETRY_DELAY:5000}
    cleanup-days: ${NOTIFICATION_CLEANUP_DAYS:30}

  # 限流配置
  rate-limit:
    enabled: ${NOTIFICATION_RATE_LIMIT_ENABLED:true}
    per-user-per-minute: ${NOTIFICATION_RATE_LIMIT_PER_USER:60}
    per-user-per-hour: ${NOTIFICATION_RATE_LIMIT_PER_HOUR:1000}


# Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      user-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
      realtime-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10

---
# Development Environment Overrides
spring:
  config:
    activate:
      on-profile: dev

# =============================================
# SpringDoc OpenAPI配置 - 仅提供API文档，不启用UI
# =============================================
springdoc:
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.implatform.notification.controller

app:
  push:
    mobile:
      fcm:
        enabled: false # 开发环境禁用FCM
      apns:
        enabled: false # 开发环境禁用APNs
    web:
      enabled: false # 开发环境禁用Web Push
    realtime:
      enabled: true # 开发环境启用实时Push
  sms:
    enabled: false # 开发环境禁用短信

logging:
  level:
    com.implatform.notification: INFO
  performance:
    slow-threshold: 5000
  # 通知服务特殊配置
  notification-events:
    enabled: true
    log-delivery-status: true
    log-retry-attempts: true

---
# Production Environment Overrides
spring:
  config:
    activate:
      on-profile: prod

app:
  push:
    mobile:
      fcm:
        enabled: true # 生产环境启用FCM
      apns:
        enabled: true # 生产环境启用APNs
        production: true # 生产环境使用APNs生产服务器
    web:
      enabled: true # 生产环境启用Web Push
    realtime:
      enabled: true # 生产环境启用实时Push
  sms:
    enabled: true
