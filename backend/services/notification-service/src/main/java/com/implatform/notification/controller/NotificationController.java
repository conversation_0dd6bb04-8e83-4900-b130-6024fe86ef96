package com.implatform.notification.controller;

import com.implatform.common.webflux.response.PageResult;
import com.implatform.common.core.domain.Result;
import com.implatform.notification.dto.notification.NotificationRequestDTO.*;
import com.implatform.notification.dto.notification.NotificationResponseDTO.*;
import com.implatform.common.core.enums.NotificationErrorCode;
import com.implatform.notification.service.impl.NotificationChannelServiceImpl;
import com.implatform.notification.service.NotificationService;
import com.implatform.notification.service.impl.NotificationTemplateServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 通知管理控制器
 * 提供通知发送、接收、管理等功能
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/notifications")
@RequiredArgsConstructor
@Validated
@Tag(name = "Notification Management", description = "通知管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class NotificationController {
    
    private final NotificationService notificationService;
    private final NotificationTemplateServiceImpl templateService;
    private final NotificationChannelServiceImpl channelService;
    
    /**
     * 发送通知
     */
    @Operation(summary = "发送通知", description = "发送通知给指定用户或用户组")
    @ApiResponses({
        @ApiResponse(responseCode = "201", description = "通知发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "未认证"),
        @ApiResponse(responseCode = "403", description = "无权限发送")
    })
    @PostMapping
    public ResponseEntity<Result<NotificationResponse>> sendNotification(
            @Valid @RequestBody SendNotificationRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long senderId) {
        
        log.info("Send notification: senderId={}, type={}, recipientCount={}", 
                senderId, request.getNotificationType(), 
                request.getRecipientIds() != null ? request.getRecipientIds().size() : 0);
        
        try {
            NotificationResponse response = notificationService.sendNotification(senderId, request);
            
            log.info("Notification sent successfully: notificationId={}", response.getNotificationId());
            
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(Result.success(response));

        } catch (IllegalArgumentException e) {
            log.warn("Send notification failed - validation error: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.INVALID_NOTIFICATION_TYPE, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Send notification failed - permission denied: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Send notification failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED, "Notification send failed"));
        }
    }
    
    /**
     * 批量发送通知
     */
    @Operation(summary = "批量发送通知", description = "批量发送通知给多个用户")
    @ApiResponses({
        @ApiResponse(responseCode = "202", description = "批量发送任务已创建"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "未认证"),
        @ApiResponse(responseCode = "403", description = "无权限发送")
    })
    @PostMapping("/batch")
    public ResponseEntity<Result<BatchNotificationResponse>> batchSendNotifications(
            @Valid @RequestBody BatchSendNotificationRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long senderId) {
        
        log.info("Batch send notifications: senderId={}, batchCount={}", 
                senderId, request.getNotifications().size());
        
        try {
            BatchNotificationResponse response = notificationService.batchSendNotifications(senderId, request);
            
            log.info("Batch notification task created: taskId={}", response.getTaskId());
            
            return ResponseEntity.status(HttpStatus.ACCEPTED)
                    .body(Result.success(response));

        } catch (IllegalArgumentException e) {
            log.warn("Batch send notifications failed - validation error: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.BATCH_SIZE_EXCEEDED, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Batch send notifications failed - permission denied: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Batch send notifications failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED, "Batch notification send failed"));
        }
    }
    
    /**
     * 获取用户通知列表
     */
    @Operation(summary = "获取通知列表", description = "获取用户的通知列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @GetMapping
    public ResponseEntity<Result<PageResult<NotificationListResponse>>> getUserNotifications(
            @Parameter(description = "通知类型") @RequestParam(required = false) String notificationType,
            @Parameter(description = "通知状态") @RequestParam(required = false) String status,
            @Parameter(description = "是否只显示未读") @RequestParam(defaultValue = "false") boolean unreadOnly,
            @PageableDefault(size = 20) Pageable pageable,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Get user notifications: userId={}, type={}, unreadOnly={}", 
                userId, notificationType, unreadOnly);
        
        try {
            GetUserNotificationsRequest request = GetUserNotificationsRequest.builder()
                    .notificationType(notificationType)
                    .status(status)
                    .unreadOnly(unreadOnly)
                    .pageable(pageable)
                    .build();
                    
            PageResult<NotificationListResponse> response = notificationService.getUserNotifications(userId, request);
            
            return ResponseEntity.ok(Result.success(response));

        } catch (Exception e) {
            log.error("Get user notifications failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED, "Failed to get user notifications"));
        }
    }
    
    /**
     * 获取通知详情
     */
    @Operation(summary = "获取通知详情", description = "获取指定通知的详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "403", description = "无权限访问"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @GetMapping("/{notificationId}")
    public ResponseEntity<Result<NotificationDetailResponse>> getNotificationDetail(
            @Parameter(description = "通知ID") @PathVariable @Positive Long notificationId,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Get notification detail: notificationId={}, userId={}", notificationId, userId);
        
        try {
            NotificationDetailResponse response = notificationService.getNotificationDetail(notificationId, userId);
            
            return ResponseEntity.ok(Result.success(response));

        } catch (IllegalArgumentException e) {
            log.warn("Notification not found: notificationId={}", notificationId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Notification access denied: notificationId={}, userId={}", notificationId, userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Get notification detail failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED, "Failed to get notification detail"));
        }
    }
    
    /**
     * 标记通知为已读
     */
    @Operation(summary = "标记已读", description = "标记指定通知为已读状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "标记成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "403", description = "无权限操作"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping("/{notificationId}/read")
    public ResponseEntity<Result<Void>> markNotificationAsRead(
            @Parameter(description = "通知ID") @PathVariable @Positive Long notificationId,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Mark notification as read: notificationId={}, userId={}", notificationId, userId);
        
        try {
            notificationService.markNotificationAsRead(notificationId, userId);

            return ResponseEntity.ok(Result.successMessage("Notification marked as read"));

        } catch (IllegalArgumentException e) {
            log.warn("Notification not found: notificationId={}", notificationId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Mark notification access denied: notificationId={}, userId={}", notificationId, userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Mark notification as read failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Failed to mark notification as read"));
        }
    }
    
    /**
     * 批量标记通知为已读
     */
    @Operation(summary = "批量标记已读", description = "批量标记多个通知为已读状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "批量标记成功"),
        @ApiResponse(responseCode = "400", description = "通知ID列表无效"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping("/batch-read")
    public ResponseEntity<Result<BatchReadResponse>> batchMarkAsRead(
            @Valid @RequestBody BatchMarkReadRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Batch mark notifications as read: userId={}, count={}", 
                userId, request.getNotificationIds().size());
        
        try {
            BatchReadResponse response = notificationService.batchMarkAsRead(request.getNotificationIds(), userId);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (IllegalArgumentException e) {
            log.warn("Batch mark as read failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.INVALID_NOTIFICATION_TYPE, e.getMessage()));
        } catch (Exception e) {
            log.error("Batch mark as read failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Batch mark as read failed"));
        }
    }
    
    /**
     * 标记所有通知为已读
     */
    @Operation(summary = "标记全部已读", description = "标记用户所有通知为已读状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "标记成功"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping("/read-all")
    public ResponseEntity<Result<Void>> markAllNotificationsAsRead(
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.info("Mark all notifications as read: userId={}", userId);
        
        try {
            notificationService.markAllNotificationsAsRead(userId);

            return ResponseEntity.ok(Result.successMessage("All notifications marked as read"));
            
        } catch (Exception e) {
            log.error("Mark all notifications as read failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Failed to mark all notifications as read"));
        }
    }
    
    /**
     * 删除通知
     */
    @Operation(summary = "删除通知", description = "删除指定通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "403", description = "无权限删除"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @DeleteMapping("/{notificationId}")
    public ResponseEntity<Result<Void>> deleteNotification(
            @Parameter(description = "通知ID") @PathVariable @Positive Long notificationId,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.info("Delete notification: notificationId={}, userId={}", notificationId, userId);
        
        try {
            notificationService.deleteNotification(notificationId, userId);

            return ResponseEntity.ok(Result.successMessage("Notification deleted successfully"));
            
        } catch (IllegalArgumentException e) {
            log.warn("Notification not found: notificationId={}", notificationId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Delete notification access denied: notificationId={}, userId={}", notificationId, userId);
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Delete notification failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Failed to delete notification"));
        }
    }
    
    /**
     * 批量删除通知
     */
    @Operation(summary = "批量删除通知", description = "批量删除多个通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "批量删除成功"),
        @ApiResponse(responseCode = "400", description = "通知ID列表无效"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PostMapping("/batch-delete")
    public ResponseEntity<Result<BatchDeleteResponse>> batchDeleteNotifications(
            @Valid @RequestBody BatchDeleteNotificationRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.info("Batch delete notifications: userId={}, count={}", 
                userId, request.getNotificationIds().size());
        
        try {
            BatchDeleteResponse response = notificationService.batchDeleteNotifications(
                    request.getNotificationIds(), userId);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete notifications failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.INVALID_NOTIFICATION_TYPE, e.getMessage()));
        } catch (Exception e) {
            log.error("Batch delete notifications failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Batch delete notifications failed"));
        }
    }
    
    /**
     * 获取通知设置
     */
    @Operation(summary = "获取通知设置", description = "获取用户的通知设置")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @GetMapping("/settings")
    public ResponseEntity<Result<NotificationSettingsResponse>> getNotificationSettings(
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Get notification settings: userId={}", userId);
        
        try {
            NotificationSettingsResponse response = notificationService.getNotificationSettings(userId);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (Exception e) {
            log.error("Get notification settings failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED, "Failed to get notification settings"));
        }
    }
    
    /**
     * 更新通知设置
     */
    @Operation(summary = "更新通知设置", description = "更新用户的通知设置")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "设置参数无效"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @PutMapping("/settings")
    public ResponseEntity<Result<NotificationSettingsResponse>> updateNotificationSettings(
            @Valid @RequestBody UpdateNotificationSettingsRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.info("Update notification settings: userId={}", userId);
        
        try {
            NotificationSettingsResponse response = notificationService.updateNotificationSettings(userId, request);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (IllegalArgumentException e) {
            log.warn("Update notification settings failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.INVALID_NOTIFICATION_TYPE, e.getMessage()));
        } catch (Exception e) {
            log.error("Update notification settings failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED, "Failed to update notification settings"));
        }
    }
    
    /**
     * 获取未读通知数量
     */
    @Operation(summary = "获取未读数量", description = "获取用户的未读通知数量")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未认证")
    })
    @GetMapping("/unread-count")
    public ResponseEntity<Result<UnreadCountResponse>> getUnreadNotificationCount(
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.debug("Get unread notification count: userId={}", userId);
        
        try {
            UnreadCountResponse response = notificationService.getUnreadNotificationCount(userId);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (Exception e) {
            log.error("Get unread count failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED, "Failed to get unread count"));
        }
    }
    
    /**
     * 获取通知统计信息
     */
    @Operation(summary = "获取通知统计", description = "获取通知的统计信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未认证"),
        @ApiResponse(responseCode = "403", description = "无权限访问")
    })
    @GetMapping("/statistics")
    public ResponseEntity<Result<Map<String, Object>>> getNotificationStatistics(
            @Parameter(description = "统计周期(天)") @RequestParam(defaultValue = "30") int days) {
        
        log.debug("Get notification statistics: days={}", days);
        
        try {
            Map<String, Object> statistics = notificationService.getNotificationStatistics(days);

            return ResponseEntity.ok(Result.success(statistics));
            
        } catch (SecurityException e) {
            log.warn("Notification statistics access denied: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Get notification statistics failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED, "Failed to get notification statistics"));
        }
    }
    
    /**
     * 测试通知发送
     */
    @Operation(summary = "测试通知发送", description = "测试通知渠道的发送功能")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "测试成功"),
        @ApiResponse(responseCode = "400", description = "测试参数无效"),
        @ApiResponse(responseCode = "401", description = "未认证"),
        @ApiResponse(responseCode = "403", description = "无权限测试")
    })
    @PostMapping("/test")
    public ResponseEntity<Result<NotificationTestResponse>> testNotification(
            @Valid @RequestBody TestNotificationRequest request,
            @Parameter(hidden = true) @RequestAttribute("userId") Long userId) {
        
        log.info("Test notification: userId={}, channel={}", userId, request.getChannel());
        
        try {
            NotificationTestResponse response = notificationService.testNotification(request, userId);

            return ResponseEntity.ok(Result.success(response));
            
        } catch (IllegalArgumentException e) {
            log.warn("Test notification failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Result.error(NotificationErrorCode.INVALID_NOTIFICATION_TYPE, e.getMessage()));
        } catch (SecurityException e) {
            log.warn("Test notification access denied: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_BLACKLISTED, e.getMessage()));
        } catch (Exception e) {
            log.error("Test notification failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED, "Notification test failed"));
        }
    }

    /**
     * 发送系统通知给指定用户
     */
    @Operation(summary = "发送系统通知", description = "发送系统通知给指定用户")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "系统通知发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "系统通知发送失败")
    })
    @PostMapping("/system/user")
    public ResponseEntity<Result<Void>> sendSystemNotificationToUser(
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId,
            @Parameter(description = "通知标题", required = true) @RequestParam String title,
            @Parameter(description = "通知内容", required = true) @RequestParam String content,
            @Parameter(description = "扩展数据") @RequestBody(required = false) Map<String, String> data) {

        log.info("发送系统通知给用户: userId={}, title={}", userId, title);

        try {
            notificationService.sendSystemNotificationToUser(userId, title, content, data);

            log.info("系统通知发送成功: userId={}, title={}", userId, title);

            return ResponseEntity.ok(Result.success());

        } catch (Exception e) {
            log.error("系统通知发送失败: userId={}, title={}", userId, title, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED, "系统通知发送失败"));
        }
    }
}
