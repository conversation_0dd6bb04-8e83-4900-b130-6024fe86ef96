package com.implatform.notification.service.impl;

import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.webflux.response.PageResult;
import com.implatform.notification.dto.notification.NotificationRequestDTO.*;
import com.implatform.notification.dto.notification.NotificationResponseDTO.*;
import com.implatform.notification.entity.Notification;
import com.implatform.notification.entity.NotificationPreference;
import com.implatform.notification.entity.UserNotificationSettings;
import com.implatform.common.core.enums.NotificationErrorCode;
import com.implatform.common.core.service.ErrorMessageService;
import com.implatform.common.core.service.I18nService;
import com.implatform.notification.repository.NotificationRepository;
import com.implatform.notification.repository.NotificationPreferenceRepository;
import com.implatform.notification.repository.UserNotificationSettingsRepository;
import com.implatform.notification.service.NotificationService;
import com.implatform.notification.service.PushNotificationService;
import com.implatform.notification.service.NotificationRateLimitService;
import com.implatform.notification.monitoring.SystemNotificationMetrics;
import com.implatform.notification.security.NotificationSecurityService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Locale;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import org.springframework.scheduling.annotation.Async;

/**
 * 通知服务实现类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class NotificationServiceImpl implements NotificationService {

    private final NotificationRepository notificationRepository;
    private final NotificationPreferenceRepository notificationPreferenceRepository;
    private final UserNotificationSettingsRepository userNotificationSettingsRepository;
    private final PushNotificationService pushNotificationService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ErrorMessageService errorMessageService;
    private final I18nService i18nService;
    private final Executor taskExecutor;
    private final NotificationRateLimitService rateLimitService;
    private final SystemNotificationMetrics metrics;
    private final NotificationSecurityService securityService;

    public NotificationServiceImpl(
            NotificationRepository notificationRepository,
            NotificationPreferenceRepository notificationPreferenceRepository,
            UserNotificationSettingsRepository userNotificationSettingsRepository,
            PushNotificationService pushNotificationService,
            RedisTemplate<String, Object> redisTemplate,
            ErrorMessageService errorMessageService,
            I18nService i18nService,
            @Qualifier("notificationTaskExecutor") Executor taskExecutor,
            NotificationRateLimitService rateLimitService,
            SystemNotificationMetrics metrics,
            NotificationSecurityService securityService) {
        this.notificationRepository = notificationRepository;
        this.notificationPreferenceRepository = notificationPreferenceRepository;
        this.userNotificationSettingsRepository = userNotificationSettingsRepository;
        this.pushNotificationService = pushNotificationService;
        this.redisTemplate = redisTemplate;
        this.errorMessageService = errorMessageService;
        this.i18nService = i18nService;
        this.taskExecutor = taskExecutor;
        this.rateLimitService = rateLimitService;
        this.metrics = metrics;
        this.securityService = securityService;
    }

    // ==================== 错误处理和国际化辅助方法 ====================

    /**
     * 创建带国际化消息的业务异常
     */
    private BusinessException createBusinessException(NotificationErrorCode errorCode) {
        return new BusinessException(errorCode);
    }

    /**
     * 创建带国际化消息的业务异常（带原因）
     */
    private BusinessException createBusinessException(NotificationErrorCode errorCode, Throwable cause) {
        return new BusinessException(errorCode, cause);
    }

    /**
     * 获取本地化错误消息
     */
    private String getLocalizedErrorMessage(NotificationErrorCode errorCode) {
        String locale = getCurrentLocale();
        return errorCode.getLocalizedMessage(locale);
    }

    /**
     * 获取当前请求的语言环境
     */
    private String getCurrentLocale() {
        try {
            return i18nService.getCurrentLocale().toLanguageTag();
        } catch (Exception e) {
            log.debug("获取当前语言环境失败，使用默认语言: {}", e.getMessage());
            return "zh-CN"; // 默认中文
        }
    }

    /**
     * 记录错误并抛出业务异常
     */
    private void logErrorAndThrow(String operation, Object... params) {
        logErrorAndThrow(NotificationErrorCode.NOTIFICATION_SEND_FAILED, operation, null, params);
    }

    /**
     * 记录错误并抛出业务异常（带错误码）
     */
    private void logErrorAndThrow(NotificationErrorCode errorCode, String operation, Throwable cause, Object... params) {
        if (cause != null) {
            log.error("{}失败: params={}, error={}", operation, Arrays.toString(params), cause.getMessage(), cause);
            throw createBusinessException(errorCode, cause);
        } else {
            log.error("{}失败: params={}", operation, Arrays.toString(params));
            throw createBusinessException(errorCode);
        }
    }

    // ==================== 参数验证方法 ====================

    /**
     * 验证发送通知的参数
     */
    private void validateSendNotificationParams(Long userId, String title, String content, String type) {
        if (userId == null || userId <= 0) {
            throw createBusinessException(NotificationErrorCode.INVALID_USER_ID);
        }

        if (title == null || title.trim().isEmpty()) {
            throw createBusinessException(NotificationErrorCode.NOTIFICATION_TITLE_EMPTY);
        }

        if (title.length() > 200) {
            throw createBusinessException(NotificationErrorCode.NOTIFICATION_TITLE_TOO_LONG);
        }

        if (content == null || content.trim().isEmpty()) {
            throw createBusinessException(NotificationErrorCode.NOTIFICATION_CONTENT_EMPTY);
        }

        if (content.length() > 1000) {
            throw createBusinessException(NotificationErrorCode.NOTIFICATION_CONTENT_TOO_LONG);
        }

        if (type == null || type.trim().isEmpty()) {
            throw createBusinessException(NotificationErrorCode.INVALID_NOTIFICATION_TYPE);
        }

        // 验证通知类型是否有效
        try {
            parseNotificationType(type);
        } catch (Exception e) {
            throw createBusinessException(NotificationErrorCode.INVALID_NOTIFICATION_TYPE);
        }
    }

    /**
     * 验证批量发送通知的参数
     */
    private void validateBatchNotificationParams(List<Long> userIds, String title, String content, String type) {
        if (userIds == null || userIds.isEmpty()) {
            throw createBusinessException(NotificationErrorCode.EMPTY_RECIPIENT_LIST);
        }

        if (userIds.size() > 1000) { // 限制批量发送数量
            throw createBusinessException(NotificationErrorCode.BATCH_SIZE_EXCEEDED);
        }

        // 验证用户ID列表
        for (Long userId : userIds) {
            if (userId == null || userId <= 0) {
                throw createBusinessException(NotificationErrorCode.INVALID_USER_ID);
            }
        }

        // 复用单个通知的验证逻辑（使用第一个用户ID进行验证）
        validateSendNotificationParams(userIds.get(0), title, content, type);
    }

    /**
     * 验证用户ID参数
     */
    private void validateUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw createBusinessException(NotificationErrorCode.INVALID_USER_ID);
        }
    }

    /**
     * 验证通知ID参数
     */
    private void validateNotificationId(Long notificationId) {
        if (notificationId == null || notificationId <= 0) {
            throw createBusinessException(NotificationErrorCode.INVALID_NOTIFICATION_ID);
        }
    }

    /**
     * 验证通知ID列表参数
     */
    private void validateNotificationIds(List<Long> notificationIds) {
        if (notificationIds == null || notificationIds.isEmpty()) {
            throw createBusinessException(NotificationErrorCode.EMPTY_RECIPIENT_LIST);
        }

        if (notificationIds.size() > 100) { // 限制批量操作数量
            throw createBusinessException(NotificationErrorCode.BATCH_SIZE_EXCEEDED);
        }

        for (Long notificationId : notificationIds) {
            validateNotificationId(notificationId);
        }
    }

    // ==================== 性能优化辅助方法 ====================

    /**
     * 处理单批通知发送
     * @return 启用通知的用户数量
     */
    private int processBatchNotification(List<Long> userIds, String title, String content, String type) {
        List<Notification> notifications = new ArrayList<>();
        List<Long> enabledUserIds = new ArrayList<>();

        // 检查每个用户的通知设置
        for (Long userId : userIds) {
            if (isNotificationEnabled(userId, type)) {
                Notification notification = createNotification(userId, title, content, type, null);
                notifications.add(notification);
                enabledUserIds.add(userId);
            }
        }

        if (enabledUserIds.isEmpty()) {
            log.debug("当前批次没有启用此类型通知的用户: type={}, batchSize={}", type, userIds.size());
            return 0;
        }

        // 批量保存通知
        notifications = notificationRepository.saveAll(notifications);

        // 异步发送推送通知，提升性能
        sendPushNotificationsAsync(notifications);

        log.debug("批次通知处理完成: batchTotal={}, enabled={}", userIds.size(), enabledUserIds.size());
        return enabledUserIds.size();
    }

    /**
     * 异步发送推送通知
     */
    @Async
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public CompletableFuture<Void> sendPushNotificationsAsync(List<Notification> notifications) {
        return CompletableFuture.runAsync(() -> {
            for (Notification notification : notifications) {
                try {
                    // 构建推送数据
                    Map<String, Object> pushData = Map.of(
                        "notificationId", notification.getId(),
                        "type", notification.getNotificationType().name()
                    );

                    sendPushNotification(notification.getRecipientId(),
                                       notification.getTitle(),
                                       notification.getContent(),
                                       pushData);

                    // 更新状态为已发送
                    notification.setStatus(Notification.NotificationStatus.SENT);
                    notification.setSentAt(Instant.now());
                } catch (Exception e) {
                    log.warn("推送通知失败: notificationId={}, userId={}, error={}",
                            notification.getId(), notification.getRecipientId(), e.getMessage());
                    // 更新通知状态为失败
                    notification.setStatus(Notification.NotificationStatus.FAILED);
                }
            }

            // 批量更新状态
            try {
                notificationRepository.saveAll(notifications);
            } catch (Exception e) {
                log.error("批量更新通知状态失败: count={}, error={}",
                         notifications.size(), e.getMessage(), e);
            }
        }, taskExecutor);
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 优化的批量标记已读方法
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    @CacheEvict(value = {"notification:unread", "notification:list"}, key = "#userId")
    public BatchReadResponse batchMarkAsReadOptimized(List<Long> notificationIds, Long userId) {
        log.debug("优化批量标记为已读: userId={}, count={}", userId, notificationIds.size());

        // 参数验证
        validateUserId(userId);
        validateNotificationIds(notificationIds);

        try {
            // 分批处理，避免大事务
            int batchSize = 50;
            List<List<Long>> batches = partitionList(notificationIds, batchSize);

            int totalCount = notificationIds.size();
            int successCount = 0;
            int failedCount = 0;
            List<String> failedReasons = new ArrayList<>();

            for (List<Long> batch : batches) {
                try {
                    // 批量更新，使用原生SQL提升性能
                    int updated = notificationRepository.batchMarkAsRead(batch, userId);
                    successCount += updated;

                    if (updated < batch.size()) {
                        int failed = batch.size() - updated;
                        failedCount += failed;
                        failedReasons.add(String.format("批次中%d个通知更新失败", failed));
                    }
                } catch (Exception e) {
                    failedCount += batch.size();
                    failedReasons.add("批次处理异常: " + e.getMessage());
                    log.error("批量标记已读失败: batchSize={}, error={}", batch.size(), e.getMessage(), e);
                }
            }

            return BatchReadResponse.builder()
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failedCount(failedCount)
                    .processedAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("批量标记已读失败: userId={}, count={}, error={}", userId, notificationIds.size(), e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    // ==================== 国际化支持方法 ====================

    /**
     * 获取国际化通知标题
     */
    private String getLocalizedTitle(String titleKey, String locale, Object... params) {
        try {
            Locale localeObj = locale != null ? Locale.forLanguageTag(locale) : Locale.getDefault();
            return i18nService.getMessage(titleKey, params, localeObj);
        } catch (Exception e) {
            log.warn("获取国际化标题失败: titleKey={}, locale={}, error={}",
                    titleKey, locale, e.getMessage());
            return titleKey; // 返回原始key作为fallback
        }
    }

    /**
     * 获取国际化通知内容
     */
    private String getLocalizedContent(String contentKey, String locale, Object... params) {
        try {
            Locale localeObj = locale != null ? Locale.forLanguageTag(locale) : Locale.getDefault();
            return i18nService.getMessage(contentKey, params, localeObj);
        } catch (Exception e) {
            log.warn("获取国际化内容失败: contentKey={}, locale={}, error={}",
                    contentKey, locale, e.getMessage());
            return contentKey; // 返回原始key作为fallback
        }
    }

    /**
     * 获取用户的语言偏好
     */
    private String getUserLocale(Long userId) {
        try {
            // 这里可以从用户设置中获取语言偏好
            // 暂时返回默认语言，后续可以实现用户语言偏好查询
            return getCurrentLocale();
        } catch (Exception e) {
            log.debug("获取用户语言偏好失败: userId={}, error={}", userId, e.getMessage());
            return "zh-CN"; // 默认中文
        }
    }

    /**
     * 发送国际化通知
     */
    private void sendLocalizedNotification(Long userId, String titleKey, String contentKey,
                                         String type, Object... params) {
        String userLocale = getUserLocale(userId);
        String localizedTitle = getLocalizedTitle(titleKey, userLocale, params);
        String localizedContent = getLocalizedContent(contentKey, userLocale, params);

        log.debug("发送国际化通知: userId={}, locale={}, titleKey={}, contentKey={}",
                 userId, userLocale, titleKey, contentKey);

        sendNotification(userId, localizedTitle, localizedContent, type);
    }

    /**
     * 批量发送国际化通知
     */
    private void sendBatchLocalizedNotification(List<Long> userIds, String titleKey,
                                              String contentKey, String type, Object... params) {
        // 按用户语言分组发送
        Map<String, List<Long>> usersByLocale = new HashMap<>();

        for (Long userId : userIds) {
            String userLocale = getUserLocale(userId);
            usersByLocale.computeIfAbsent(userLocale, k -> new ArrayList<>()).add(userId);
        }

        // 为每种语言生成对应的通知内容并批量发送
        for (Map.Entry<String, List<Long>> entry : usersByLocale.entrySet()) {
            String locale = entry.getKey();
            List<Long> usersForLocale = entry.getValue();

            String localizedTitle = getLocalizedTitle(titleKey, locale, params);
            String localizedContent = getLocalizedContent(contentKey, locale, params);

            log.debug("批量发送国际化通知: locale={}, userCount={}, titleKey={}, contentKey={}",
                     locale, usersForLocale.size(), titleKey, contentKey);

            sendBatchNotification(usersForLocale, localizedTitle, localizedContent, type);
        }
    }

    /**
     * 创建国际化通知模板数据
     */
    private Map<String, Object> createNotificationTemplateData(String templateKey, String locale, Object... params) {
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("templateKey", templateKey);
        templateData.put("locale", locale);
        templateData.put("params", params);
        templateData.put("timestamp", System.currentTimeMillis());

        return templateData;
    }

    // ==================== 原有接口方法实现 ====================

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendNotification(Long userId, String title, String content, String type) {
        log.info("发送通知: userId={}, title={}, type={}", userId, title, type);

        // 输入参数验证
        validateSendNotificationParams(userId, title, content, type);

        try {
            // 检查用户通知设置
            if (!isNotificationEnabled(userId, type)) {
                log.debug("用户已关闭此类型通知: userId={}, type={}", userId, type);
                return;
            }

            // 创建通知记录
            Notification notification = createNotification(userId, title, content, type, null);
            notificationRepository.save(notification);

            // 发送Push通知
            sendPushNotification(userId, title, content, Map.of("type", type, "notificationId", notification.getId()));

            log.debug("通知发送成功: notificationId={}", notification.getId());

        } catch (Exception e) {
            logErrorAndThrow(NotificationErrorCode.NOTIFICATION_SEND_FAILED, "发送通知", e, userId, type);
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendBatchNotification(List<Long> userIds, String title, String content, String type) {
        log.info("批量发送通知: userCount={}, title={}, type={}", userIds.size(), title, type);

        // 输入参数验证
        validateBatchNotificationParams(userIds, title, content, type);

        try {
            // 大批量数据分批处理，避免内存溢出和数据库压力
            int batchSize = 100; // 每批处理100个用户
            List<List<Long>> batches = partitionList(userIds, batchSize);

            log.debug("分批处理批量通知: totalUsers={}, batchCount={}, batchSize={}",
                     userIds.size(), batches.size(), batchSize);

            int totalEnabledUsers = 0;
            for (int i = 0; i < batches.size(); i++) {
                List<Long> batch = batches.get(i);
                log.debug("处理第{}批通知: batchSize={}", i + 1, batch.size());

                try {
                    int enabledInBatch = processBatchNotification(batch, title, content, type);
                    totalEnabledUsers += enabledInBatch;
                } catch (Exception e) {
                    log.error("第{}批通知处理失败: batchSize={}, error={}",
                             i + 1, batch.size(), e.getMessage(), e);
                    // 继续处理下一批，不因为一批失败而中断整个流程
                }
            }

            log.info("批量通知发送完成: totalUsers={}, enabledUsers={}, batchCount={}",
                    userIds.size(), totalEnabledUsers, batches.size());

        } catch (Exception e) {
            log.error("发送批量通知失败: userCount={}, type={}, error={}", userIds.size(), type, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendMessageNotification(Long userId, Long messageId, String senderName, String content) {
        log.info("发送消息通知: userId={}, messageId={}, senderName={}", userId, messageId, senderName);

        String title = "新消息";
        String notificationContent = String.format("%s: %s", senderName, content);
        Map<String, Object> data = Map.of(
            "type", "MESSAGE",
            "messageId", messageId,
            "senderName", senderName
        );

        sendNotificationWithData(userId, title, notificationContent, "MESSAGE", data);
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendSystemNotification(Long userId, String content) {
        log.info("发送系统通知: userId={}, content={}", userId, content);

        sendNotification(userId, "系统通知", content, "SYSTEM");
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendFriendRequestNotification(Long userId, Long requesterId, String requesterName) {
        log.info("发送好友请求通知: userId={}, requesterId={}, requesterName={}", userId, requesterId, requesterName);

        // 使用国际化消息
        String userLocale = getUserLocale(userId);
        String title = getLocalizedTitle("notification.friend_request.title", userLocale);
        String content = getLocalizedContent("notification.friend_request.content", userLocale, requesterName);

        Map<String, Object> data = Map.of(
            "type", "FRIEND_REQUEST",
            "requesterId", requesterId,
            "requesterName", requesterName
        );

        sendNotificationWithData(userId, title, content, "FRIEND_REQUEST", data);
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendGroupInviteNotification(Long userId, Long groupId, String groupName, String inviterName) {
        log.info("发送群组邀请通知: userId={}, groupId={}, groupName={}, inviterName={}",
                userId, groupId, groupName, inviterName);

        // 使用国际化消息
        String userLocale = getUserLocale(userId);
        String title = getLocalizedTitle("notification.group_invite.title", userLocale);
        String content = getLocalizedContent("notification.group_invite.content", userLocale, inviterName, groupName);

        Map<String, Object> data = Map.of(
            "type", "GROUP_INVITE",
            "groupId", groupId,
            "groupName", groupName,
            "inviterName", inviterName
        );

        sendNotificationWithData(userId, title, content, "GROUP_INVITE", data);
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendChannelNotification(Long channelId, String title, String content) {
        log.info("发送频道通知: channelId={}, title={}", channelId, title);

        try {
            // 获取频道订阅者列表
            List<Long> subscriberIds = getChannelSubscribers(channelId);

            if (!subscriberIds.isEmpty()) {
                sendBatchNotification(subscriberIds, title, content, "CHANNEL");
            }

        } catch (Exception e) {
            log.error("发送频道通知失败: channelId={}, error={}", channelId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional
    public void sendSelfDestructNotification(Long userId, Long messageId) {
        log.info("发送自毁消息通知: userId={}, messageId={}", userId, messageId);

        try {
            // 创建自毁消息通知
            Notification notification = new Notification();
            notification.setNotificationUuid(UUID.randomUUID().toString());
            notification.setRecipientId(userId);
            notification.setNotificationType(Notification.NotificationType.MESSAGE);
            notification.setCategory(Notification.NotificationCategory.SYSTEM);
            notification.setTitle("消息已自毁");
            notification.setContent("您的消息已按设定时间自动销毁");
            notification.setPriority(Notification.Priority.NORMAL);
            notification.setStatus(Notification.NotificationStatus.PENDING);
            notification.setNotificationData("{\"messageId\":" + messageId + ",\"type\":\"self_destruct\"}");

            notificationRepository.save(notification);
            log.info("自毁消息通知已创建: userId={}, messageId={}", userId, messageId);
        } catch (Exception e) {
            log.error("发送自毁消息通知失败: userId={}, messageId={}", userId, messageId, e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional
    public void sendBroadcastNotification(Long broadcastId, String title, String content, List<Long> targetUserIds) {
        log.info("发送广播消息通知: broadcastId={}, title={}, targetUserCount={}", broadcastId, title, targetUserIds.size());

        try {
            // 批量创建广播通知
            List<Notification> notifications = new ArrayList<>();
            for (Long userId : targetUserIds) {
                Notification notification = new Notification();
                notification.setNotificationUuid(UUID.randomUUID().toString());
                notification.setRecipientId(userId);
                notification.setNotificationType(Notification.NotificationType.ANNOUNCEMENT);
                notification.setCategory(Notification.NotificationCategory.SYSTEM);
                notification.setTitle(title);
                notification.setContent(content);
                notification.setPriority(Notification.Priority.HIGH);
                notification.setStatus(Notification.NotificationStatus.PENDING);
                notification.setNotificationData("{\"broadcastId\":" + broadcastId + ",\"type\":\"broadcast\"}");
                notifications.add(notification);
            }

            notificationRepository.saveAll(notifications);
            log.info("广播消息通知已创建: broadcastId={}, count={}", broadcastId, notifications.size());
        } catch (Exception e) {
            log.error("发送广播消息通知失败: broadcastId={}", broadcastId, e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional
    public void sendMessageReadAndDestructedNotification(Long senderId, Long messageId, Long readerId) {
        log.info("发送消息阅读并销毁通知: senderId={}, messageId={}, readerId={}", senderId, messageId, readerId);

        try {
            // 向发送者发送消息已读并销毁通知
            Notification notification = new Notification();
            notification.setNotificationUuid(UUID.randomUUID().toString());
            notification.setRecipientId(senderId);
            notification.setNotificationType(Notification.NotificationType.MESSAGE);
            notification.setCategory(Notification.NotificationCategory.SYSTEM);
            notification.setTitle("消息已读并销毁");
            notification.setContent("您的消息已被对方阅读并自动销毁");
            notification.setPriority(Notification.Priority.NORMAL);
            notification.setStatus(Notification.NotificationStatus.PENDING);
            notification.setNotificationData("{\"messageId\":" + messageId + ",\"readerId\":" + readerId + ",\"type\":\"read_and_destructed\"}");

            notificationRepository.save(notification);
            log.info("消息阅读并销毁通知已创建: senderId={}, messageId={}", senderId, messageId);
        } catch (Exception e) {
            log.error("发送消息阅读并销毁通知失败: senderId={}, messageId={}", senderId, messageId, e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional
    public void sendMessageDestructedNotification(Long senderId, Long receiverId, Long messageId, Object reason) {
        log.info("发送消息销毁通知: senderId={}, receiverId={}, messageId={}, reason={}", senderId, receiverId, messageId, reason);

        try {
            // 向接收者发送消息销毁通知
            Notification notification = new Notification();
            notification.setNotificationUuid(UUID.randomUUID().toString());
            notification.setRecipientId(receiverId);
            notification.setNotificationType(Notification.NotificationType.MESSAGE);
            notification.setCategory(Notification.NotificationCategory.SYSTEM);
            notification.setTitle("消息已销毁");
            notification.setContent("一条消息已被销毁");
            notification.setPriority(Notification.Priority.NORMAL);
            notification.setStatus(Notification.NotificationStatus.PENDING);
            notification.setNotificationData("{\"messageId\":" + messageId + ",\"senderId\":" + senderId + ",\"reason\":\"" + reason + "\",\"type\":\"destructed\"}");

            notificationRepository.save(notification);
            log.info("消息销毁通知已创建: receiverId={}, messageId={}", receiverId, messageId);
        } catch (Exception e) {
            log.error("发送消息销毁通知失败: receiverId={}, messageId={}", receiverId, messageId, e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 发送频道消息通知
     *
     * <p>当频道中有新消息时，向订阅该频道的用户发送通知。
     * 通知内容包含消息发送者、消息内容摘要等信息。</p>
     *
     * @param userId 用户ID - 接收通知的用户
     * @param message 频道消息对象 - 包含消息详情
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendChannelMessageNotification(Long userId, Object message) {
        log.info("发送频道消息通知: userId={}, message={}", userId, message);

        try {
            // 解析消息对象获取基本信息
            String title = "频道新消息";
            String content = "您关注的频道有新消息";

            // 创建通知记录
            Notification notification = createNotification(userId, title, content, "CHANNEL_MESSAGE", null);
            notificationRepository.save(notification);

            // 发送Push通知
            Map<String, Object> data = Map.of(
                "type", "CHANNEL_MESSAGE",
                "messageData", message != null ? message.toString() : "",
                "notificationId", notification.getId()
            );
            sendPushNotification(userId, title, content, data);

            log.debug("频道消息通知发送成功: userId={}, notificationId={}", userId, notification.getId());

        } catch (Exception e) {
            log.error("发送频道消息通知失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 发送频道订阅通知
     *
     * <p>当用户订阅或取消订阅频道时，向频道管理员发送通知。
     * 帮助管理员了解频道的订阅情况变化。</p>
     *
     * @param channelId 频道ID - 被订阅的频道
     * @param userId 用户ID - 执行订阅操作的用户
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendChannelSubscriptionNotification(Long channelId, Long userId) {
        log.info("发送频道订阅通知: channelId={}, userId={}", channelId, userId);

        try {
            // 获取频道管理员列表（这里需要调用其他服务获取）
            List<Long> adminIds = getChannelAdmins(channelId);

            if (!adminIds.isEmpty()) {
                String title = "频道订阅通知";
                String content = String.format("用户 %d 订阅了您管理的频道", userId);

                // 批量发送给管理员
                sendBatchNotification(adminIds, title, content, "CHANNEL_SUBSCRIPTION");
            }

            log.debug("频道订阅通知发送成功: channelId={}, userId={}", channelId, userId);

        } catch (Exception e) {
            log.error("发送频道订阅通知失败: channelId={}, userId={}, error={}", channelId, userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 发送频道管理员通知
     *
     * <p>当用户被设置为频道管理员或管理员权限发生变化时发送通知。
     * 通知用户其在频道中的权限变更情况。</p>
     *
     * @param channelId 频道ID - 相关频道
     * @param userId 用户ID - 权限变更的用户
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void sendChannelAdminNotification(Long channelId, Long userId) {
        log.info("发送频道管理员通知: channelId={}, userId={}", channelId, userId);

        try {
            String title = "频道管理员权限通知";
            String content = "您已被设置为频道管理员，可以管理频道内容和成员";

            Map<String, Object> data = Map.of(
                "type", "CHANNEL_ADMIN",
                "channelId", channelId,
                "action", "ADMIN_GRANTED"
            );

            sendNotificationWithData(userId, title, content, "CHANNEL_ADMIN", data);

            log.debug("频道管理员通知发送成功: channelId={}, userId={}", channelId, userId);

        } catch (Exception e) {
            log.error("发送频道管理员通知失败: channelId={}, userId={}, error={}", channelId, userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    // ==================== Controller需要的新方法 ====================

    /**
     * 发送通知（Controller使用）
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public NotificationResponse sendNotification(Long senderId, SendNotificationRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始发送通知: senderId={}, type={}, recipientCount={}, title={}, priority={}",
                senderId, request.getNotificationType(),
                request.getRecipientIds() != null ? request.getRecipientIds().size() : 0,
                request.getTitle(), request.getPriority());

        log.debug("发送通知详细参数: senderId={}, request={}", senderId, request);

        try {
            // 验证请求参数
            if (request.getRecipientIds() == null || request.getRecipientIds().isEmpty()) {
                throw new BusinessException(NotificationErrorCode.EMPTY_RECIPIENT_LIST);
            }

            // 创建通知实体
            Notification notification = new Notification();
            notification.setNotificationUuid(UUID.randomUUID().toString());
            notification.setRecipientId(request.getRecipientIds().get(0)); // 单个通知只取第一个接收者
            notification.setSenderId(senderId);
            notification.setNotificationType(parseNotificationType(request.getNotificationType()));
            notification.setTitle(request.getTitle());
            notification.setContent(request.getContent());
            notification.setImageUrl(request.getImageUrl());
            notification.setClickAction(request.getActionUrl());
            notification.setPriority(Notification.Priority.valueOf(request.getPriority()));
            notification.setStatus(Notification.NotificationStatus.PENDING);
            notification.setIsRead(false);
            notification.setNotificationData(request.getData() != null ? request.getData().toString() : null);
            notification.setGroupKey(request.getGroupKey());
            notification.setTags(request.getTags() != null ? String.join(",", request.getTags()) : null);
            notification.setScheduledAt(request.getScheduledAt() != null ?
                    request.getScheduledAt().atZone(java.time.ZoneId.systemDefault()).toInstant() : null);
            notification.setExpiresAt(request.getExpiresAt() != null ?
                    request.getExpiresAt().atZone(java.time.ZoneId.systemDefault()).toInstant() : null);

            // 保存通知
            notification = notificationRepository.save(notification);

            // 发送通知
            if (notification.shouldSendImmediately()) {
                sendPushNotification(notification.getRecipientId(), notification.getTitle(),
                        notification.getContent(), Map.of("notificationId", notification.getId()));
                notification.markAsSent();
                notificationRepository.save(notification);
            }

            // 构建响应
            NotificationResponse response = NotificationResponse.builder()
                    .notificationId(notification.getId())
                    .notificationUuid(notification.getNotificationUuid())
                    .notificationType(request.getNotificationType())
                    .senderId(senderId)
                    .recipientId(notification.getRecipientId())
                    .title(notification.getTitle())
                    .content(notification.getContent())
                    .imageUrl(notification.getImageUrl())
                    .actionUrl(notification.getClickAction())
                    .priority(request.getPriority())
                    .status(notification.getStatus().name())
                    .isRead(notification.getIsRead())
                    .createdAt(notification.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                    .sentAt(notification.getSentAt() != null ?
                            notification.getSentAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                    .scheduledAt(request.getScheduledAt())
                    .expiresAt(request.getExpiresAt())
                    .data(request.getData())
                    .groupKey(request.getGroupKey())
                    .tags(request.getTags())
                    .retryCount(notification.getRetryCount())
                    .maxRetryCount(notification.getMaxRetryCount())
                    .build();

            long endTime = System.currentTimeMillis();
            log.info("通知发送成功: senderId={}, notificationId={}, 耗时={}ms",
                    senderId, notification.getId(), endTime - startTime);
            log.debug("通知发送结果详情: notificationId={}, status={}, recipientId={}",
                     response.getNotificationId(), response.getStatus(), response.getRecipientId());

            return response;

        } catch (BusinessException e) {
            long endTime = System.currentTimeMillis();
            log.warn("通知发送业务异常: senderId={}, 耗时={}ms, error={}",
                    senderId, endTime - startTime, e.getMessage());
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("通知发送系统异常: senderId={}, 耗时={}ms, error={}",
                     senderId, endTime - startTime, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 批量发送通知（Controller使用）
     *
     * <p>处理批量通知发送请求，支持多个不同的通知内容同时发送。
     * 使用异步处理提高性能，返回批次任务信息供客户端跟踪进度。</p>
     *
     * @param senderId 发送者ID
     * @param request 批量发送请求，包含多个通知内容
     * @return 批量发送响应，包含任务ID和状态信息
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public BatchNotificationResponse batchSendNotifications(Long senderId, BatchSendNotificationRequest request) {
        long startTime = System.currentTimeMillis();
        int batchCount = request.getNotifications().size();
        log.info("开始批量发送通知: senderId={}, batchCount={}, taskId={}",
                senderId, batchCount, UUID.randomUUID().toString());

        log.debug("批量发送通知详细参数: senderId={}, batchSize={}", senderId, batchCount);

        try {
            String taskId = UUID.randomUUID().toString();
            int totalCount = request.getNotifications().size();
            int successCount = 0;
            int failedCount = 0;

            // 逐个处理通知发送
            for (SendNotificationRequest notificationRequest : request.getNotifications()) {
                try {
                    sendNotification(senderId, notificationRequest);
                    successCount++;
                } catch (Exception e) {
                    log.error("批量通知中单个通知发送失败: senderId={}, error={}", senderId, e.getMessage(), e);
                    failedCount++;
                }
            }

            String batchStatus = failedCount == 0 ? "COMPLETED" :
                               successCount == 0 ? "FAILED" : "PARTIAL_SUCCESS";

            log.info("批量通知发送完成: taskId={}, total={}, success={}, failed={}",
                    taskId, totalCount, successCount, failedCount);

            return BatchNotificationResponse.builder()
                    .taskId(taskId)
                    .batchName(request.getBatchName())
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failedCount(failedCount)
                    .processingCount(0)
                    .batchStatus(batchStatus)
                    .createdAt(LocalDateTime.now())
                    .startedAt(LocalDateTime.now())
                    .completedAt(LocalDateTime.now())
                    .progressPercentage(100.0)
                    .build();

        } catch (Exception e) {
            log.error("批量发送通知失败: senderId={}, error={}", senderId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 获取用户通知列表（Controller使用）
     */
    public PageResult<NotificationListResponse> getUserNotifications(Long userId, GetUserNotificationsRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始获取用户通知列表: userId={}, type={}, unreadOnly={}, page={}, size={}",
                userId, request.getNotificationType(), request.getUnreadOnly(),
                request.getPageable() != null ? request.getPageable().getPageNumber() : 0,
                request.getPageable() != null ? request.getPageable().getPageSize() : 20);

        log.debug("获取通知列表详细参数: userId={}, request={}", userId, request);

        try {
            Pageable pageable = request.getPageable() != null ? request.getPageable() :
                    PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<Notification> notifications;

            if (request.getUnreadOnly()) {
                // 只获取未读通知
                notifications = notificationRepository.findByRecipientIdAndIsReadFalseOrderByCreatedAtDesc(
                        userId, pageable);
            } else {
                // 获取所有通知
                notifications = notificationRepository.findByRecipientIdOrderByCreatedAtDesc(
                        userId, pageable);
            }

            // 转换为响应DTO
            List<NotificationListResponse> responseList = notifications.getContent().stream()
                    .map(notification -> convertToNotificationListResponse(notification))
                    .toList();

            return PageResult.of(responseList, notifications.getTotalElements(),
                    pageable.getPageNumber(), pageable.getPageSize());

        } catch (Exception e) {
            log.error("获取用户通知列表失败: userId={}", userId, e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    /**
     * 获取通知详情（Controller使用）
     *
     * <p>根据通知ID获取通知的详细信息，包括发送者信息、内容、状态等。
     * 验证用户权限，确保只能查看属于自己的通知。</p>
     *
     * @param notificationId 通知ID
     * @param userId 用户ID - 用于权限验证
     * @return 通知详情响应
     * @throws BusinessException 当通知不存在或无权限访问时抛出
     */
    public NotificationDetailResponse getNotificationDetail(Long notificationId, Long userId) {
        log.debug("获取通知详情: notificationId={}, userId={}", notificationId, userId);

        try {
            // 查找通知
            Notification notification = notificationRepository.findById(notificationId)
                    .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            // 验证权限：只能查看属于自己的通知
            if (!notification.getRecipientId().equals(userId)) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_BLACKLISTED);
            }

            // 构建发送者信息
            SenderInfo senderInfo = null;
            if (notification.getSenderId() != null) {
                senderInfo = SenderInfo.builder()
                        .senderId(notification.getSenderId())
                        .senderName("系统") // 从用户服务获取真实用户名
                        .senderType("SYSTEM")
                        .build();
            }

            // 构建响应
            return NotificationDetailResponse.builder()
                    .notificationId(notification.getId())
                    .notificationUuid(notification.getNotificationUuid())
                    .notificationType(notification.getNotificationType().name())
                    .senderId(notification.getSenderId())
                    .recipientId(notification.getRecipientId())
                    .title(notification.getTitle())
                    .content(notification.getContent())
                    .imageUrl(notification.getImageUrl())
                    .actionUrl(notification.getClickAction())
                    .priority(notification.getPriority().name())
                    .status(notification.getStatus().name())
                    .isRead(notification.getIsRead())
                    .senderInfo(senderInfo)
                    .createdAt(notification.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                    .sentAt(notification.getSentAt() != null ?
                            notification.getSentAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                    .readAt(notification.getReadAt() != null ?
                            notification.getReadAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                    .scheduledAt(notification.getScheduledAt() != null ?
                            notification.getScheduledAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                    .expiresAt(notification.getExpiresAt() != null ?
                            notification.getExpiresAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                    .groupKey(notification.getGroupKey())
                    .tags(notification.getTags() != null ?
                            List.of(notification.getTags().split(",")) : List.of())
                    .retryCount(notification.getRetryCount())
                    .maxRetryCount(notification.getMaxRetryCount())
                    .build();

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取通知详情失败: notificationId={}, userId={}, error={}",
                    notificationId, userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    /**
     * 标记通知为已读（Controller使用）
     *
     * <p>将指定的通知标记为已读状态，并记录读取时间。
     * 验证用户权限，确保只能标记属于自己的通知。</p>
     *
     * @param notificationId 通知ID
     * @param userId 用户ID - 用于权限验证
     * @throws BusinessException 当通知不存在或无权限访问时抛出
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void markNotificationAsRead(Long notificationId, Long userId) {
        log.debug("标记通知为已读: notificationId={}, userId={}", notificationId, userId);

        try {
            // 查找通知
            Notification notification = notificationRepository.findById(notificationId)
                    .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            // 验证权限：只能标记属于自己的通知
            if (!notification.getRecipientId().equals(userId)) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_BLACKLISTED);
            }

            // 如果已经是已读状态，直接返回
            if (notification.getIsRead()) {
                log.debug("通知已经是已读状态: notificationId={}", notificationId);
                return;
            }

            // 标记为已读
            notification.setIsRead(true);
            notification.setReadAt(Instant.now());
            notificationRepository.save(notification);

            log.debug("通知标记为已读成功: notificationId={}, userId={}", notificationId, userId);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("标记通知为已读失败: notificationId={}, userId={}, error={}",
                    notificationId, userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 批量标记为已读（Controller使用）
     *
     * <p>批量将多个通知标记为已读状态。
     * 对每个通知进行权限验证，只处理属于当前用户的通知。</p>
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID - 用于权限验证
     * @return 批量处理结果，包含成功和失败的数量
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    @CacheEvict(value = {"notification:unread", "notification:list"}, key = "#userId")
    public BatchReadResponse batchMarkAsRead(List<Long> notificationIds, Long userId) {
        log.debug("批量标记为已读: userId={}, count={}", userId, notificationIds.size());

        int totalCount = notificationIds.size();
        int successCount = 0;
        int failedCount = 0;

        try {
            for (Long notificationId : notificationIds) {
                try {
                    markNotificationAsRead(notificationId, userId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量标记中单个通知处理失败: notificationId={}, userId={}, error={}",
                            notificationId, userId, e.getMessage());
                    failedCount++;
                }
            }

            log.info("批量标记为已读完成: userId={}, total={}, success={}, failed={}",
                    userId, totalCount, successCount, failedCount);

            return BatchReadResponse.builder()
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failedCount(failedCount)
                    .processedAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("批量标记为已读失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 标记所有通知为已读（Controller使用）
     *
     * <p>将用户的所有未读通知标记为已读状态。
     * 使用批量更新提高性能。</p>
     *
     * @param userId 用户ID
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void markAllNotificationsAsRead(Long userId) {
        log.info("标记所有通知为已读: userId={}", userId);

        try {
            // 批量更新所有未读通知
            int updatedCount = notificationRepository.markAllAsReadByRecipientId(userId, Instant.now());

            log.info("标记所有通知为已读完成: userId={}, updatedCount={}", userId, updatedCount);

        } catch (Exception e) {
            log.error("标记所有通知为已读失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 删除通知（Controller使用）
     *
     * <p>删除指定的通知记录。验证用户权限，确保只能删除属于自己的通知。
     * 采用软删除方式，将通知状态标记为已删除而不是物理删除。</p>
     *
     * @param notificationId 通知ID
     * @param userId 用户ID - 用于权限验证
     * @throws BusinessException 当通知不存在或无权限访问时抛出
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void deleteNotification(Long notificationId, Long userId) {
        log.info("删除通知: notificationId={}, userId={}", notificationId, userId);

        try {
            // 查找通知
            Notification notification = notificationRepository.findById(notificationId)
                    .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            // 验证权限：只能删除属于自己的通知
            if (!notification.getRecipientId().equals(userId)) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_BLACKLISTED);
            }

            // 软删除：标记为已删除状态
            notification.setStatus(Notification.NotificationStatus.CANCELLED);
            notification.setUpdatedAt(Instant.now());
            notificationRepository.save(notification);

            log.info("通知删除成功: notificationId={}, userId={}", notificationId, userId);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除通知失败: notificationId={}, userId={}, error={}",
                    notificationId, userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 批量删除通知（Controller使用）
     *
     * <p>批量删除多个通知记录。对每个通知进行权限验证，
     * 只处理属于当前用户的通知。</p>
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID - 用于权限验证
     * @return 批量删除结果，包含成功和失败的数量
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    public BatchDeleteResponse batchDeleteNotifications(List<Long> notificationIds, Long userId) {
        log.info("批量删除通知: userId={}, count={}", userId, notificationIds.size());

        int totalCount = notificationIds.size();
        int successCount = 0;
        int failedCount = 0;

        try {
            for (Long notificationId : notificationIds) {
                try {
                    deleteNotification(notificationId, userId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量删除中单个通知处理失败: notificationId={}, userId={}, error={}",
                            notificationId, userId, e.getMessage());
                    failedCount++;
                }
            }

            log.info("批量删除通知完成: userId={}, total={}, success={}, failed={}",
                    userId, totalCount, successCount, failedCount);

            return BatchDeleteResponse.builder()
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failedCount(failedCount)
                    .processedAt(LocalDateTime.now())
                    .deleteReason("用户删除")
                    .build();

        } catch (Exception e) {
            log.error("批量删除通知失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 获取通知设置（Controller使用）
     *
     * <p>获取用户的通知设置信息，包括通知开关、免打扰模式、
     * 优先级过滤、声音设置等个性化配置。支持Redis缓存提高性能。</p>
     *
     * @param userId 用户ID
     * @return 通知设置响应，包含用户的完整通知配置
     */
    @Cacheable(value = "notification:settings", key = "#userId")
    public NotificationSettingsResponse getNotificationSettings(Long userId) {
        log.debug("获取通知设置: userId={}", userId);

        try {
            // 从数据库查询用户通知设置
            Optional<UserNotificationSettings> settingsOpt = userNotificationSettingsRepository.findByUserId(userId);

            if (settingsOpt.isPresent()) {
                UserNotificationSettings settings = settingsOpt.get();

                return NotificationSettingsResponse.builder()
                        .userId(userId)
                        .enabled(settings.getEnabled())
                        .doNotDisturb(settings.getDoNotDisturb())
                        .doNotDisturbStart(settings.getQuietHoursStart())
                        .doNotDisturbEnd(settings.getQuietHoursEnd())
                        .minPriority(settings.getMinPriority().name())
                        .sound(settings.getSound())
                        .vibration(settings.getVibration())
                        .maxNotifications(settings.getMaxNotifications())
                        .retentionHours(settings.getRetentionHours())
                        .frequencyLimitType(settings.getFrequencyLimitType().name())
                        .frequencyLimitValue(settings.getFrequencyLimitValue())
                        .updatedAt(settings.getUpdatedAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                        .build();
            } else {
                // 创建默认设置
                UserNotificationSettings defaultSettings = createDefaultNotificationSettings(userId);
                userNotificationSettingsRepository.save(defaultSettings);

                return NotificationSettingsResponse.builder()
                        .userId(userId)
                        .enabled(true)
                        .doNotDisturb(false)
                        .minPriority("NORMAL")
                        .sound("default")
                        .vibration(true)
                        .maxNotifications(50)
                        .retentionHours(168) // 7天
                        .frequencyLimitType("NONE")
                        .frequencyLimitValue(0)
                        .updatedAt(LocalDateTime.now())
                        .build();
            }

        } catch (Exception e) {
            log.error("获取通知设置失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    /**
     * 创建默认通知设置
     *
     * @param userId 用户ID
     * @return 默认通知设置对象
     */
    private UserNotificationSettings createDefaultNotificationSettings(Long userId) {
        UserNotificationSettings settings = new UserNotificationSettings();
        settings.setUserId(userId);
        settings.setEnabled(true);
        settings.setDoNotDisturb(false);
        settings.setQuietHoursStart("22:00");
        settings.setQuietHoursEnd("08:00");
        settings.setMinPriority(Notification.Priority.NORMAL);
        settings.setSound("default");
        settings.setVibration(true);
        settings.setPreview(true);
        settings.setMaxNotifications(50);
        settings.setRetentionHours(168); // 7天
        settings.setFrequencyLimitType(UserNotificationSettings.FrequencyLimitType.NONE);
        settings.setFrequencyLimitValue(0);
        settings.setChannelSettings(getDefaultChannelSettingsJson());
        settings.setCreatedAt(Instant.now());
        settings.setUpdatedAt(Instant.now());
        return settings;
    }

    /**
     * 解析频道设置
     *
     * @param channelSettingsJson JSON格式的频道设置
     * @return 频道设置Map
     */
    private Map<String, Object> parseChannelSettings(String channelSettingsJson) {
        if (channelSettingsJson == null || channelSettingsJson.trim().isEmpty()) {
            return getDefaultChannelSettings();
        }

        try {
            // 这里应该使用JSON解析库，简化实现返回默认设置
            return getDefaultChannelSettings();
        } catch (Exception e) {
            log.warn("解析频道设置失败，使用默认设置: {}", e.getMessage());
            return getDefaultChannelSettings();
        }
    }

    /**
     * 获取默认频道设置
     *
     * @return 默认频道设置Map
     */
    private Map<String, Object> getDefaultChannelSettings() {
        Map<String, Object> settings = new HashMap<>();
        settings.put("push", true);
        settings.put("email", false);
        settings.put("sms", false);
        settings.put("inApp", true);
        return settings;
    }

    /**
     * 获取默认频道设置JSON
     *
     * @return JSON格式的默认频道设置
     */
    private String getDefaultChannelSettingsJson() {
        return "{\"push\":true,\"email\":false,\"sms\":false,\"inApp\":true}";
    }

    /**
     * 更新通知设置（Controller使用）
     *
     * <p>更新用户的通知设置，支持部分更新。更新后清除Redis缓存，
     * 确保下次获取时能获得最新的设置信息。</p>
     *
     * @param userId 用户ID
     * @param request 更新通知设置请求，包含要更新的设置项
     * @return 更新后的通知设置响应
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
    @CacheEvict(value = "notification:settings", key = "#userId")
    public NotificationSettingsResponse updateNotificationSettings(Long userId, UpdateNotificationSettingsRequest request) {
        log.info("更新通知设置: userId={}", userId);

        try {
            // 查找或创建通知设置
            UserNotificationSettings settings = userNotificationSettingsRepository.findByUserId(userId)
                    .orElse(createDefaultNotificationSettings(userId));

            // 更新设置字段（只更新非null的字段）
            if (request.getEnabled() != null) {
                settings.setEnabled(request.getEnabled());
            }
            if (request.getDoNotDisturb() != null) {
                settings.setDoNotDisturb(request.getDoNotDisturb());
            }
            if (request.getDoNotDisturbStart() != null) {
                settings.setQuietHoursStart(request.getDoNotDisturbStart());
            }
            if (request.getDoNotDisturbEnd() != null) {
                settings.setQuietHoursEnd(request.getDoNotDisturbEnd());
            }
            if (request.getMinPriority() != null) {
                settings.setMinPriority(Notification.Priority.valueOf(request.getMinPriority()));
            }
            if (request.getSound() != null) {
                settings.setSound(request.getSound());
            }
            if (request.getVibration() != null) {
                settings.setVibration(request.getVibration());
            }

            if (request.getMaxNotifications() != null) {
                settings.setMaxNotifications(request.getMaxNotifications());
            }
            if (request.getRetentionHours() != null) {
                settings.setRetentionHours(request.getRetentionHours());
            }
            if (request.getFrequencyLimitType() != null) {
                settings.setFrequencyLimitType(UserNotificationSettings.FrequencyLimitType.valueOf(request.getFrequencyLimitType()));
            }
            if (request.getFrequencyLimitValue() != null) {
                settings.setFrequencyLimitValue(request.getFrequencyLimitValue());
            }


            settings.setUpdatedAt(Instant.now());

            // 保存更新
            settings = userNotificationSettingsRepository.save(settings);

            log.info("通知设置更新成功: userId={}", userId);

            // 返回更新后的设置
            return getNotificationSettings(userId);

        } catch (Exception e) {
            log.error("更新通知设置失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    /**
     * 将频道设置转换为JSON字符串
     *
     * @param channels 频道设置Map
     * @return JSON字符串
     */
    private String convertChannelSettingsToJson(Map<String, Object> channels) {
        if (channels == null || channels.isEmpty()) {
            return getDefaultChannelSettingsJson();
        }

        try {
            // 简化实现：构建基本的JSON字符串
            StringBuilder json = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : channels.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                json.append("\"").append(entry.getKey()).append("\":")
                    .append(entry.getValue() instanceof String ? "\"" + entry.getValue() + "\"" : entry.getValue());
                first = false;
            }
            json.append("}");
            return json.toString();
        } catch (Exception e) {
            log.warn("转换频道设置为JSON失败，使用默认设置: {}", e.getMessage());
            return getDefaultChannelSettingsJson();
        }
    }

    /**
     * 获取未读通知数量（Controller使用）
     *
     * <p>统计用户的未读通知数量，按通知类型分类统计。
     * 提供总数和各类型的详细数量。</p>
     *
     * @param userId 用户ID
     * @return 未读通知数量响应，包含总数和分类统计
     */
    @Cacheable(value = "notification:unread", key = "#userId", unless = "#result.totalUnreadCount == 0")
    public UnreadCountResponse getUnreadNotificationCount(Long userId) {
        log.debug("获取未读通知数量: userId={}", userId);

        try {
            // 获取总的未读通知数量
            long totalUnreadCount = notificationRepository.countUnreadByRecipientId(userId);

            // 按类型统计未读数量
            Map<String, Long> unreadCountByType = new HashMap<>();
            for (Notification.NotificationType type : Notification.NotificationType.values()) {
                Long count = notificationRepository.countUnreadByRecipientIdAndType(userId, type);
                unreadCountByType.put(type.name(), count != null ? count : 0L);
            }

            // 按优先级统计未读数量（简化实现）
            Map<String, Long> unreadCountByPriority = new HashMap<>();
            for (Notification.Priority priority : Notification.Priority.values()) {
                unreadCountByPriority.put(priority.name(), 0L); // 暂时设为0，后续可以实现具体查询
            }

            log.debug("未读通知数量统计: userId={}, total={}", userId, totalUnreadCount);

            return UnreadCountResponse.builder()
                    .userId(userId)
                    .totalUnreadCount(totalUnreadCount)
                    .unreadCountByType(unreadCountByType)
                    .unreadCountByPriority(unreadCountByPriority)
                    .latestUnreadTime(LocalDateTime.now())
                    .statisticsTime(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("获取未读通知数量失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    /**
     * 获取通知统计信息（Controller使用）
     *
     * <p>获取指定时间段内的通知统计信息，包括发送量、成功率、
     * 各种状态的通知数量等。用于管理后台的数据分析。</p>
     *
     * @param days 统计天数
     * @return 统计信息Map，包含各种指标数据
     */
    @Cacheable(value = "notification:push-stats", key = "#days")
    public Map<String, Object> getNotificationStatistics(int days) {
        log.debug("获取通知统计信息: days={}", days);

        try {
            Instant startTime = Instant.now().minus(days, java.time.temporal.ChronoUnit.DAYS);

            // 获取指定时间段内的统计数据

            // 使用Repository方法获取基本统计（简化版本）
            List<Notification> recentNotifications = notificationRepository.findByTimeRange(startTime, Instant.now());
            long totalNotifications = recentNotifications.size();
            long sentNotifications = recentNotifications.stream()
                    .mapToLong(n -> n.getStatus() == Notification.NotificationStatus.SENT ? 1 : 0).sum();
            long deliveredNotifications = recentNotifications.stream()
                    .mapToLong(n -> n.getStatus() == Notification.NotificationStatus.DELIVERED ? 1 : 0).sum();
            long failedNotifications = recentNotifications.stream()
                    .mapToLong(n -> n.getStatus() == Notification.NotificationStatus.FAILED ? 1 : 0).sum();
            long readNotifications = recentNotifications.stream()
                    .mapToLong(n -> n.getIsRead() ? 1 : 0).sum();

            // 计算成功率和阅读率
            double successRate = totalNotifications > 0 ?
                    (double) (sentNotifications + deliveredNotifications) / totalNotifications * 100 : 0.0;
            double readRate = sentNotifications > 0 ?
                    (double) readNotifications / sentNotifications * 100 : 0.0;
            double failureRate = totalNotifications > 0 ?
                    (double) failedNotifications / totalNotifications * 100 : 0.0;

            // 按通知类型统计
            Map<String, Long> typeStatistics = new HashMap<>();
            for (Notification.NotificationType type : Notification.NotificationType.values()) {
                long count = recentNotifications.stream()
                        .mapToLong(n -> n.getNotificationType() == type ? 1 : 0).sum();
                typeStatistics.put(type.name(), count);
            }

            // 按优先级统计
            Map<String, Long> priorityStatistics = new HashMap<>();
            for (Notification.Priority priority : Notification.Priority.values()) {
                long count = recentNotifications.stream()
                        .mapToLong(n -> n.getPriority() == priority ? 1 : 0).sum();
                priorityStatistics.put(priority.name(), count);
            }

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalNotifications", totalNotifications);
            statistics.put("sentNotifications", sentNotifications);
            statistics.put("deliveredNotifications", deliveredNotifications);
            statistics.put("readNotifications", readNotifications);
            statistics.put("failedNotifications", failedNotifications);
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            statistics.put("readRate", Math.round(readRate * 100.0) / 100.0);
            statistics.put("failureRate", Math.round(failureRate * 100.0) / 100.0);
            statistics.put("typeStatistics", typeStatistics);
            statistics.put("priorityStatistics", priorityStatistics);
            statistics.put("period", Map.of(
                    "days", days,
                    "startDate", startTime.atZone(java.time.ZoneId.systemDefault()).toLocalDateTime(),
                    "endDate", LocalDateTime.now()
            ));
            statistics.put("generatedAt", LocalDateTime.now());

            log.debug("通知统计信息: days={}, total={}, read={}, readRate={}%",
                    days, totalNotifications, readNotifications, readRate);

            return statistics;

        } catch (Exception e) {
            log.error("获取通知统计信息失败: days={}, error={}", days, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.STATISTICS_NOT_AVAILABLE);
        }
    }

    /**
     * 测试通知（Controller使用）
     *
     * <p>发送测试通知以验证通知系统的功能。
     * 支持多种通知渠道的测试，包括Push、邮件、短信等。</p>
     *
     * @param request 测试通知请求，包含渠道和测试内容
     * @param userId 用户ID
     * @return 测试结果响应
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public NotificationTestResponse testNotification(TestNotificationRequest request, Long userId) {
        log.info("测试通知: userId={}, channel={}", userId, request.getChannel());

        String testId = UUID.randomUUID().toString();
        boolean success = true;
        String testStatus = "SUCCESS";
        long startTime = System.currentTimeMillis();

        try {
            // 根据渠道类型发送测试通知
            String testTitle = "测试通知";
            String testContent = request.getTestMessage() != null ? request.getTestMessage() : "这是一条测试通知";

            switch (request.getChannel().toUpperCase()) {
                case "PUSH":
                    sendPushNotification(userId, testTitle, testContent,
                            Map.of("testId", testId, "type", "TEST"));
                    break;
                case "IN_APP":
                    sendNotification(userId, testTitle, testContent, "TEST");
                    break;
                default:
                    log.info("测试通知渠道: {}", request.getChannel());
                    break;
            }

            log.info("测试通知发送完成: testId={}, userId={}, channel={}",
                    testId, userId, request.getChannel());

        } catch (Exception e) {
            log.error("测试通知发送失败: testId={}, userId={}, channel={}, error={}",
                    testId, userId, request.getChannel(), e.getMessage(), e);
            success = false;
            testStatus = "FAILED";
        }

        long responseTime = System.currentTimeMillis() - startTime;

        return NotificationTestResponse.builder()
                .testId(testId)
                .channel(request.getChannel())
                .testMessage(request.getTestMessage())
                .testStatus(testStatus)
                .success(success)
                .testTime(LocalDateTime.now())
                .responseTimeMs(responseTime)
                .build();
    }

    // ==================== Push通知相关方法实现 ====================

    /**
     * 发送Push通知
     */
    @Override
    @Transactional
    public void sendPushNotification(Long userId, String title, String content, Map<String, Object> data) {
        log.info("发送Push通知: userId={}, title={}", userId, title);

        try {
            // 检查用户是否在线，决定发送策略
            if (pushNotificationService.isUserOnline(userId)) {
                // 用户在线，发送实时WebSocket消息
                pushNotificationService.sendRealtimeMessage(userId, "NOTIFICATION", Map.of(
                    "title", title,
                    "content", content,
                    "data", data != null ? data : Map.of()
                ));
            } else {
                // 用户离线，发送Push通知到设备
                List<String> activeDevices = pushNotificationService.getUserActiveDevices(userId);
                if (!activeDevices.isEmpty()) {
                    pushNotificationService.sendToMobileDevices(userId, title, content, data, activeDevices);
                }
                // 同时发送Web Push通知
                pushNotificationService.sendToWebBrowser(userId, title, content, data);
            }

            log.debug("Push通知发送成功: userId={}", userId);
        } catch (Exception e) {
            log.error("发送Push通知失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new RuntimeException("发送Push通知失败", e);
        }
    }

    /**
     * 批量发送Push通知
     */
    @Override
    @Transactional
    public void batchSendPushNotifications(List<Long> userIds, String title, String content, Map<String, Object> data) {
        log.info("批量发送Push通知: userCount={}, title={}", userIds.size(), title);

        try {
            // 使用Push服务的批量发送功能
            pushNotificationService.batchSendPushNotifications(userIds, title, content, data);

            log.debug("批量Push通知发送成功: userCount={}", userIds.size());
        } catch (Exception e) {
            log.error("批量发送Push通知失败: userCount={}, error={}", userIds.size(), e.getMessage(), e);
            throw new RuntimeException("批量发送Push通知失败", e);
        }
    }

    /**
     * 发送实时通知
     */
    @Override
    @Transactional
    public void sendRealtimeNotification(Long userId, String messageType, Object payload) {
        log.info("发送实时通知: userId={}, messageType={}", userId, messageType);

        try {
            pushNotificationService.sendRealtimeMessage(userId, messageType, payload);

            log.debug("实时通知发送成功: userId={}, messageType={}", userId, messageType);
        } catch (Exception e) {
            log.error("发送实时通知失败: userId={}, messageType={}, error={}",
                    userId, messageType, e.getMessage(), e);
            throw new RuntimeException("发送实时通知失败", e);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 转换Notification为NotificationListResponse
     */
    private NotificationListResponse convertToNotificationListResponse(Notification notification) {
        // 创建发送者信息
        SenderInfo senderInfo = null;
        if (notification.getSenderId() != null) {
            // 从用户服务获取真实用户信息
            UserInfo userInfo = getUserInfo(notification.getSenderId());
            String senderName = userInfo != null ? userInfo.getNickname() : "系统";
            String senderType = userInfo != null ? "USER" : "SYSTEM";

            senderInfo = SenderInfo.builder()
                    .senderId(notification.getSenderId())
                    .senderName(senderName)
                    .senderType(senderType)
                    .build();
        }

        return NotificationListResponse.builder()
                .notificationId(notification.getId())
                .notificationUuid(notification.getNotificationUuid())
                .notificationType(notification.getNotificationType().name())
                .title(notification.getTitle())
                .contentSummary(notification.getContent().length() > 100 ?
                        notification.getContent().substring(0, 100) + "..." : notification.getContent())
                .imageUrl(notification.getImageUrl())
                .actionUrl(notification.getClickAction())
                .priority(notification.getPriority().name())
                .status(notification.getStatus().name())
                .isRead(notification.getIsRead())
                .createdAt(notification.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime())
                .readAt(notification.getReadAt() != null ?
                        notification.getReadAt().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null)
                .groupKey(notification.getGroupKey())
                .tags(notification.getTags() != null ?
                        Arrays.asList(notification.getTags().split(",")) : null)
                .senderInfo(senderInfo)
                .build();
    }

    /**
     * 检查用户是否启用了指定类型的通知
     */
    private boolean isNotificationEnabled(Long userId, String type) {
        try {
            // 查询用户对该通知类型的偏好设置
            Notification.NotificationType notificationType = parseNotificationType(type);
            List<NotificationPreference> preferences = notificationPreferenceRepository
                    .findByUserIdAndNotificationType(userId, notificationType);

            if (preferences.isEmpty()) {
                // 如果没有设置，默认启用所有通知
                return true;
            }

            // 检查是否有任何渠道启用了该通知类型
            for (NotificationPreference preference : preferences) {
                if (preference.getIsEnabled() &&
                    preference.shouldReceiveNotification(Notification.Priority.NORMAL)) {
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("检查通知设置失败: userId={}, type={}, error={}", userId, type, e.getMessage(), e);
            // 出错时默认允许发送
            return true;
        }
    }

    /**
     * 解析通知类型字符串
     */
    private Notification.NotificationType parseNotificationType(String type) {
        try {
            return Notification.NotificationType.valueOf(type.toUpperCase());
        } catch (Exception e) {
            log.warn("无法解析通知类型: {}, 使用默认类型SYSTEM", type);
            return Notification.NotificationType.SYSTEM;
        }
    }

    /**
     * 创建通知记录
     */
    private Notification createNotification(Long userId, String title, String content, String type, Map<String, Object> data) {
        Instant now = Instant.now();

        Notification notification = new Notification();
        notification.setRecipientId(userId);
        notification.setTitle(title);
        notification.setContent(content);
        notification.setNotificationType(parseNotificationType(type));
        notification.setNotificationData(data != null ? data.toString() : null);
        notification.setIsRead(false);
        notification.setStatus(Notification.NotificationStatus.SENT);
        notification.setPriority(Notification.Priority.NORMAL);
        notification.setCreatedAt(now);
        notification.setUpdatedAt(now);

        return notification;
    }

    /**
     * 发送带数据的通知
     */
    private void sendNotificationWithData(Long userId, String title, String content, String type, Map<String, Object> data) {
        try {
            if (!isNotificationEnabled(userId, type)) {
                log.debug("用户已关闭此类型通知: userId={}, type={}", userId, type);
                return;
            }

            Notification notification = createNotification(userId, title, content, type, data);
            notificationRepository.save(notification);

            // 发送Push通知
            Map<String, Object> pushData = new HashMap<>(data);
            pushData.put("notificationId", notification.getId());
            sendPushNotification(userId, title, content, pushData);

        } catch (Exception e) {
            log.error("发送通知失败: userId={}, type={}, error={}", userId, type, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 批量发送带数据的通知
     */
    private void sendBatchNotificationWithData(List<Long> userIds, String title, String content, String type, Map<String, Object> data) {
        try {
            List<Notification> notifications = new ArrayList<>();
            List<Long> enabledUserIds = new ArrayList<>();

            for (Long userId : userIds) {
                if (isNotificationEnabled(userId, type)) {
                    Notification notification = createNotification(userId, title, content, type, data);
                    notifications.add(notification);
                    enabledUserIds.add(userId);
                }
            }

            if (!notifications.isEmpty()) {
                notificationRepository.saveAll(notifications);
                batchSendPushNotifications(enabledUserIds, title, content, data);
            }

        } catch (Exception e) {
            log.error("批量发送通知失败: userCount={}, type={}, error={}", userIds.size(), type, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 获取频道订阅者列表
     *
     * <p>通过调用social-service获取指定频道的所有订阅者用户ID列表。
     * 用于向频道订阅者批量发送通知。支持Redis缓存提高性能。</p>
     *
     * @param channelId 频道ID
     * @return 订阅者用户ID列表
     */
    @Cacheable(value = "notification:channel:subscribers", key = "#channelId", unless = "#result.isEmpty()")
    public List<Long> getChannelSubscribers(Long channelId) {
        try {
            // 调用social-service获取频道订阅者
            String cacheKey = "channel:subscribers:" + channelId;

            // 先从Redis缓存获取
            @SuppressWarnings("unchecked")
            List<Long> cachedSubscribers = (List<Long>) redisTemplate.opsForValue().get(cacheKey);
            if (cachedSubscribers != null) {
                log.debug("从缓存获取频道订阅者: channelId={}, count={}", channelId, cachedSubscribers.size());
                return cachedSubscribers;
            }

            // 模拟调用social-service获取订阅者（实际应该使用Feign客户端）
            List<Long> subscribers = fetchChannelSubscribersFromSocialService(channelId);

            // 缓存结果（5分钟过期）
            if (!subscribers.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, subscribers, java.time.Duration.ofMinutes(5));
            }

            log.debug("获取频道订阅者成功: channelId={}, count={}", channelId, subscribers.size());
            return subscribers;

        } catch (Exception e) {
            log.error("获取频道订阅者失败: channelId={}, error={}", channelId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 从social-service获取频道订阅者
     *
     * @param channelId 频道ID
     * @return 订阅者用户ID列表
     */
    private List<Long> fetchChannelSubscribersFromSocialService(Long channelId) {
        try {
            // 这里应该调用social-service的Feign客户端
            // 暂时返回模拟数据，实际实现时需要替换为真实的Feign调用
            log.info("调用social-service获取频道订阅者: channelId={}", channelId);

            // 模拟返回一些订阅者ID
            return List.of(1001L, 1002L, 1003L);

        } catch (Exception e) {
            log.error("调用social-service失败: channelId={}, error={}", channelId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 获取频道管理员列表
     *
     * <p>通过调用social-service获取指定频道的管理员用户ID列表。
     * 用于向频道管理员发送管理相关通知。支持Redis缓存提高性能。</p>
     *
     * @param channelId 频道ID
     * @return 管理员用户ID列表
     */
    @Cacheable(value = "notification:channel:admins", key = "#channelId", unless = "#result.isEmpty()")
    public List<Long> getChannelAdmins(Long channelId) {
        try {
            // 调用social-service获取频道管理员
            String cacheKey = "channel:admins:" + channelId;

            // 先从Redis缓存获取
            @SuppressWarnings("unchecked")
            List<Long> cachedAdmins = (List<Long>) redisTemplate.opsForValue().get(cacheKey);
            if (cachedAdmins != null) {
                log.debug("从缓存获取频道管理员: channelId={}, count={}", channelId, cachedAdmins.size());
                return cachedAdmins;
            }

            // 模拟调用social-service获取管理员（实际应该使用Feign客户端）
            List<Long> admins = fetchChannelAdminsFromSocialService(channelId);

            // 缓存结果（10分钟过期，管理员变更频率较低）
            if (!admins.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, admins, java.time.Duration.ofMinutes(10));
            }

            log.debug("获取频道管理员成功: channelId={}, count={}", channelId, admins.size());
            return admins;

        } catch (Exception e) {
            log.error("获取频道管理员失败: channelId={}, error={}", channelId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 从social-service获取频道管理员
     *
     * @param channelId 频道ID
     * @return 管理员用户ID列表
     */
    private List<Long> fetchChannelAdminsFromSocialService(Long channelId) {
        try {
            // 这里应该调用social-service的Feign客户端
            // 暂时返回模拟数据，实际实现时需要替换为真实的Feign调用
            log.info("调用social-service获取频道管理员: channelId={}", channelId);

            // 模拟返回一些管理员ID
            return List.of(2001L, 2002L);

        } catch (Exception e) {
            log.error("调用social-service获取频道管理员失败: channelId={}, error={}", channelId, e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 获取所有活跃用户列表
     *
     * <p>通过调用user-service获取系统中所有活跃用户的ID列表。
     * 用于系统广播通知等场景。支持Redis缓存提高性能。</p>
     *
     * @return 活跃用户ID列表
     */
    @Cacheable(value = "notification:active:users", unless = "#result.isEmpty()")
    public List<Long> getAllActiveUserIds() {
        try {
            // 调用user-service获取活跃用户
            String cacheKey = "active:users:all";

            // 先从Redis缓存获取
            @SuppressWarnings("unchecked")
            List<Long> cachedUsers = (List<Long>) redisTemplate.opsForValue().get(cacheKey);
            if (cachedUsers != null) {
                log.debug("从缓存获取活跃用户: count={}", cachedUsers.size());
                return cachedUsers;
            }

            // 模拟调用user-service获取活跃用户（实际应该使用Feign客户端）
            List<Long> activeUsers = fetchActiveUsersFromUserService();

            // 缓存结果（2分钟过期，活跃用户变化较频繁）
            if (!activeUsers.isEmpty()) {
                redisTemplate.opsForValue().set(cacheKey, activeUsers, java.time.Duration.ofMinutes(2));
            }

            log.debug("获取活跃用户成功: count={}", activeUsers.size());
            return activeUsers;

        } catch (Exception e) {
            log.error("获取所有活跃用户失败: error={}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 从user-service获取活跃用户
     *
     * @return 活跃用户ID列表
     */
    private List<Long> fetchActiveUsersFromUserService() {
        try {
            // 这里应该调用user-service的Feign客户端
            // 暂时返回模拟数据，实际实现时需要替换为真实的Feign调用
            log.info("调用user-service获取活跃用户");

            // 模拟返回一些活跃用户ID
            List<Long> activeUsers = new ArrayList<>();
            for (long i = 1001L; i <= 1100L; i++) {
                activeUsers.add(i);
            }
            return activeUsers;

        } catch (Exception e) {
            log.error("调用user-service获取活跃用户失败: error={}", e.getMessage(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    /**
     * 获取用户信息
     *
     * <p>从user-service获取用户的基本信息，用于构建发送者信息等。
     * 支持Redis缓存提高性能。</p>
     *
     * @param userId 用户ID
     * @return 用户信息，如果获取失败返回null
     */
    @Cacheable(value = "notification:user:info", key = "#userId")
    public UserInfo getUserInfo(Long userId) {
        try {
            String cacheKey = "user:info:" + userId;

            // 先从Redis缓存获取
            UserInfo cachedUser = (UserInfo) redisTemplate.opsForValue().get(cacheKey);
            if (cachedUser != null) {
                log.debug("从缓存获取用户信息: userId={}", userId);
                return cachedUser;
            }

            // 模拟调用user-service获取用户信息（实际应该使用Feign客户端）
            UserInfo userInfo = fetchUserInfoFromUserService(userId);

            // 缓存结果（30分钟过期）
            if (userInfo != null) {
                redisTemplate.opsForValue().set(cacheKey, userInfo, java.time.Duration.ofMinutes(30));
            }

            return userInfo;

        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从user-service获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    private UserInfo fetchUserInfoFromUserService(Long userId) {
        try {
            // 这里应该调用user-service的Feign客户端
            // 暂时返回模拟数据，实际实现时需要替换为真实的Feign调用
            log.debug("调用user-service获取用户信息: userId={}", userId);

            // 模拟返回用户信息
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(userId);
            userInfo.setUsername("user_" + userId);
            userInfo.setNickname("用户" + userId);
            userInfo.setAvatar("https://example.com/avatar/" + userId + ".jpg");
            return userInfo;

        } catch (Exception e) {
            log.error("调用user-service获取用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 用户信息内部类
     */
    @Getter
    @Setter
    private static class UserInfo {
        private Long userId;
        private String username;
        private String nickname;
        private String avatar;

    }

    // ==================== Admin管理功能实现 ====================

    @Override
    @Transactional
    public void retryNotification(Long adminId, RetryNotificationRequest request) {
        log.info("重试发送通知: adminId={}, notificationId={}", adminId, request.getNotificationId());

        try {
            Notification notification = notificationRepository.findById(request.getNotificationId())
                .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            // 检查通知是否可以重试
            if (!notification.canRetry()) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
            }

            // 更新重试次数和状态
            notification.setRetryCount(notification.getRetryCount() + 1);
            notification.setStatus(Notification.NotificationStatus.PENDING);
            // notification.setFailureReason(null); // 暂时注释，字段可能不存在
            notification.setUpdatedAt(Instant.now());

            notificationRepository.save(notification);

            // 重新发送通知 - 简化实现
            log.info("通知已标记为重试: notificationId={}", notification.getId());

            log.info("通知重试成功: notificationId={}", request.getNotificationId());

        } catch (BusinessException e) {
            log.error("重试通知失败: notificationId={}, error={}", request.getNotificationId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("重试通知失败: notificationId={}", request.getNotificationId(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Override
    @Transactional
    public BatchOperationResponse batchRetryNotifications(Long adminId, BatchRetryNotificationsRequest request) {
        log.info("批量重试通知: adminId={}, count={}", adminId, request.getNotificationIds().size());

        int totalCount = request.getNotificationIds().size();
        int successCount = 0;
        int failedCount = 0;
        List<String> failedReasons = new ArrayList<>();

        for (Long notificationId : request.getNotificationIds()) {
            try {
                RetryNotificationRequest retryRequest = RetryNotificationRequest.builder()
                    .notificationId(notificationId)
                    .reason(request.getReason())
                    .build();

                retryNotification(adminId, retryRequest);
                successCount++;

            } catch (Exception e) {
                failedCount++;
                failedReasons.add("通知ID " + notificationId + ": " + e.getMessage());
                log.warn("批量重试中单个通知失败: notificationId={}, error={}", notificationId, e.getMessage());
            }
        }

        return BatchOperationResponse.builder()
            .totalCount(totalCount)
            .successCount(successCount)
            .failedCount(failedCount)
            .failedReasons(failedReasons)
            .processedAt(LocalDateTime.now())
            .operationDetails(Map.of("operation", "batch_retry", "adminId", adminId))
            .build();
    }

    @Override
    public PageResult<NotificationListResponse> searchNotifications(Long adminId, SearchNotificationsRequest request) {
        log.info("搜索通知: adminId={}, keyword={}", adminId, request.getKeyword());

        try {
            // 构建查询条件
            Pageable pageable = request.getPageable() != null ? request.getPageable() :
                PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, "createdAt"));

            // 这里应该使用更复杂的查询逻辑，暂时简化实现
            Page<Notification> notifications;

            if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
                notifications = notificationRepository.findByTitleContainingOrContentContaining(
                    request.getKeyword(), request.getKeyword(), pageable);
            } else {
                notifications = notificationRepository.findAll(pageable);
            }

            List<NotificationListResponse> responseList = notifications.getContent().stream()
                .map(this::convertToNotificationListResponse)
                .toList();

            return PageResult.<NotificationListResponse>builder()
                .records(responseList)
                .totalElements(notifications.getTotalElements())
                .totalPages(notifications.getTotalPages())
                .currentPage(notifications.getNumber())
                .pageSize(notifications.getSize())
                .hasNext(notifications.hasNext())
                .hasPrevious(notifications.hasPrevious())
                .build();

        } catch (Exception e) {
            log.error("搜索通知失败: adminId={}, keyword={}", adminId, request.getKeyword(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Override
    public NotificationStatisticsResponse getNotificationStatistics(Long adminId, GetNotificationStatisticsRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始获取通知统计: adminId={}, startDate={}, endDate={}, 统计类型={}",
                adminId, request.getStartDate(), request.getEndDate(), "全量统计");

        log.debug("通知统计详细参数: adminId={}, request={}", adminId, request);

        try {
            LocalDateTime startDate = request.getStartDate() != null ? request.getStartDate() :
                LocalDateTime.now().minusDays(30);
            LocalDateTime endDate = request.getEndDate() != null ? request.getEndDate() :
                LocalDateTime.now();

            // 基础统计
            Long totalSent = notificationRepository.countByCreatedAtBetween(
                startDate.atZone(java.time.ZoneId.systemDefault()).toInstant(),
                endDate.atZone(java.time.ZoneId.systemDefault()).toInstant());

            Long totalDelivered = notificationRepository.countByStatusAndCreatedAtBetween(
                Notification.NotificationStatus.DELIVERED,
                startDate.atZone(java.time.ZoneId.systemDefault()).toInstant(),
                endDate.atZone(java.time.ZoneId.systemDefault()).toInstant());

            Long totalRead = notificationRepository.countByIsReadTrueAndCreatedAtBetween(
                startDate.atZone(java.time.ZoneId.systemDefault()).toInstant(),
                endDate.atZone(java.time.ZoneId.systemDefault()).toInstant());

            Long totalFailed = notificationRepository.countByStatusAndCreatedAtBetween(
                Notification.NotificationStatus.FAILED,
                startDate.atZone(java.time.ZoneId.systemDefault()).toInstant(),
                endDate.atZone(java.time.ZoneId.systemDefault()).toInstant());

            // 计算比率
            double deliveryRate = totalSent > 0 ? (double) totalDelivered / totalSent * 100 : 0.0;
            double readRate = totalDelivered > 0 ? (double) totalRead / totalDelivered * 100 : 0.0;
            double failureRate = totalSent > 0 ? (double) totalFailed / totalSent * 100 : 0.0;

            // 计算点击统计（基于通知数据中的点击记录）
            Long totalClicked = getClickedNotificationCount(startDate, endDate);
            double clickRate = totalDelivered > 0 ? (double) totalClicked / totalDelivered * 100 : 0.0;

            return NotificationStatisticsResponse.builder()
                .totalSent(totalSent)
                .totalDelivered(totalDelivered)
                .totalRead(totalRead)
                .totalClicked(totalClicked)
                .totalFailed(totalFailed)
                .deliveryRate(deliveryRate)
                .readRate(readRate)
                .clickRate(clickRate)
                .failureRate(failureRate)
                .startDate(startDate)
                .endDate(endDate)
                .statisticsByType(getStatisticsByType(startDate, endDate))
                .statisticsByStatus(getStatisticsByStatus(startDate, endDate))
                .statisticsByPriority(getStatisticsByPriority(startDate, endDate))
                .statisticsByChannel(getStatisticsByChannel(startDate, endDate))
                .timeSeriesData(getTimeSeriesData(startDate, endDate))
                .build();

        } catch (Exception e) {
            log.error("获取通知统计失败: adminId={}", adminId, e);
            throw new BusinessException(NotificationErrorCode.STATISTICS_NOT_AVAILABLE);
        }
    }

    @Override
    public PageResult<NotificationListResponse> getFailedNotifications(Long adminId, GetFailedNotificationsRequest request) {
        log.info("获取失败通知: adminId={}", adminId);

        try {
            Pageable pageable = request.getPageable() != null ? request.getPageable() :
                PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<Notification> notifications = notificationRepository.findByStatus(
                Notification.NotificationStatus.FAILED, pageable);

            List<NotificationListResponse> responseList = notifications.getContent().stream()
                .map(this::convertToNotificationListResponse)
                .toList();

            return PageResult.<NotificationListResponse>builder()
                .records(responseList)
                .totalElements(notifications.getTotalElements())
                .totalPages(notifications.getTotalPages())
                .currentPage(notifications.getNumber())
                .pageSize(notifications.getSize())
                .hasNext(notifications.hasNext())
                .hasPrevious(notifications.hasPrevious())
                .build();

        } catch (Exception e) {
            log.error("获取失败通知失败: adminId={}", adminId, e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Override
    public PageResult<NotificationListResponse> getPendingNotifications(Long adminId, GetPendingNotificationsRequest request) {
        log.info("获取待发送通知: adminId={}", adminId);

        try {
            Pageable pageable = request.getPageable() != null ? request.getPageable() :
                PageRequest.of(0, 20, Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<Notification> notifications = notificationRepository.findByStatus(
                Notification.NotificationStatus.PENDING, pageable);

            List<NotificationListResponse> responseList = notifications.getContent().stream()
                .map(this::convertToNotificationListResponse)
                .toList();

            return PageResult.<NotificationListResponse>builder()
                .records(responseList)
                .totalElements(notifications.getTotalElements())
                .totalPages(notifications.getTotalPages())
                .currentPage(notifications.getNumber())
                .pageSize(notifications.getSize())
                .hasNext(notifications.hasNext())
                .hasPrevious(notifications.hasPrevious())
                .build();

        } catch (Exception e) {
            log.error("获取待发送通知失败: adminId={}", adminId, e);
            throw new BusinessException(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Override
    @Transactional
    public void cancelNotification(Long adminId, CancelNotificationRequest request) {
        log.info("取消通知: adminId={}, notificationId={}", adminId, request.getNotificationId());

        try {
            Notification notification = notificationRepository.findById(request.getNotificationId())
                .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            // 检查通知状态是否允许取消
            if (notification.getStatus() == Notification.NotificationStatus.SENT ||
                notification.getStatus() == Notification.NotificationStatus.DELIVERED ||
                notification.getStatus() == Notification.NotificationStatus.READ) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_ALREADY_SENT);
            }

            if (notification.getStatus() == Notification.NotificationStatus.CANCELLED) {
                throw new BusinessException(NotificationErrorCode.NOTIFICATION_ALREADY_CANCELLED);
            }

            // 更新状态为已取消
            notification.setStatus(Notification.NotificationStatus.CANCELLED);
            notification.setUpdatedAt(Instant.now());

            notificationRepository.save(notification);

            log.info("通知取消成功: notificationId={}", request.getNotificationId());

        } catch (BusinessException e) {
            log.error("取消通知失败: notificationId={}, error={}", request.getNotificationId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("取消通知失败: notificationId={}", request.getNotificationId(), e);
            throw new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND);
        }
    }

    @Override
    @Transactional
    public void deleteNotification(Long adminId, DeleteNotificationRequest request) {
        log.info("删除通知: adminId={}, notificationId={}", adminId, request.getNotificationId());

        try {
            Notification notification = notificationRepository.findById(request.getNotificationId())
                .orElseThrow(() -> new BusinessException(NotificationErrorCode.NOTIFICATION_NOT_FOUND));

            notificationRepository.delete(notification);

            log.info("通知删除成功: notificationId={}", request.getNotificationId());

        } catch (BusinessException e) {
            log.error("删除通知失败: notificationId={}, error={}", request.getNotificationId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("删除通知失败: notificationId={}", request.getNotificationId(), e);
            throw new BusinessException(NotificationErrorCode.DATABASE_DELETE_FAILED);
        }
    }

    @Override
    @Transactional
    public BatchDeleteResponse batchDeleteNotifications(Long adminId, BatchDeleteNotificationsRequest request) {
        log.info("批量删除通知: adminId={}, count={}", adminId, request.getNotificationIds().size());

        int totalCount = request.getNotificationIds().size();
        int successCount = 0;
        int failedCount = 0;
        List<String> failedReasons = new ArrayList<>();

        for (Long notificationId : request.getNotificationIds()) {
            try {
                DeleteNotificationRequest deleteRequest = DeleteNotificationRequest.builder()
                    .notificationId(notificationId)
                    .reason(request.getReason())
                    .build();

                deleteNotification(adminId, deleteRequest);
                successCount++;

            } catch (Exception e) {
                failedCount++;
                failedReasons.add("通知ID " + notificationId + ": " + e.getMessage());
                log.warn("批量删除中单个通知失败: notificationId={}, error={}", notificationId, e.getMessage());
            }
        }

        return BatchDeleteResponse.builder()
            .totalCount(totalCount)
            .successCount(successCount)
            .failedCount(failedCount)
            .failedReasons(failedReasons)
            .processedAt(LocalDateTime.now())
            .build();
    }

    // ==================== 辅助方法 ====================

    /**
     * 按通知类型统计
     */
    private Map<String, Long> getStatisticsByType(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取按类型统计数据: startDate={}, endDate={}", startDate, endDate);

        try {
            Instant startInstant = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant();
            Instant endInstant = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant();

            Map<String, Long> statistics = new HashMap<>();

            // 统计各种通知类型
            for (Notification.NotificationType type : Notification.NotificationType.values()) {
                Long count = notificationRepository.countByNotificationTypeAndCreatedAtBetween(
                    type, startInstant, endInstant);
                statistics.put(type.name(), count != null ? count : 0L);
            }

            return statistics;

        } catch (Exception e) {
            log.error("获取按类型统计失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            // 返回默认值
            return Arrays.stream(Notification.NotificationType.values())
                .collect(Collectors.toMap(Enum::name, type -> 0L));
        }
    }

    /**
     * 按通知状态统计
     */
    private Map<String, Long> getStatisticsByStatus(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取按状态统计数据: startDate={}, endDate={}", startDate, endDate);

        try {
            Instant startInstant = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant();
            Instant endInstant = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant();

            Map<String, Long> statistics = new HashMap<>();

            // 统计各种通知状态
            for (Notification.NotificationStatus status : Notification.NotificationStatus.values()) {
                Long count = notificationRepository.countByStatusAndCreatedAtBetween(
                    status, startInstant, endInstant);
                statistics.put(status.name(), count != null ? count : 0L);
            }

            return statistics;

        } catch (Exception e) {
            log.error("获取按状态统计失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            // 返回默认值
            return Arrays.stream(Notification.NotificationStatus.values())
                .collect(Collectors.toMap(Enum::name, status -> 0L));
        }
    }

    /**
     * 按优先级统计
     */
    private Map<String, Long> getStatisticsByPriority(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取按优先级统计数据: startDate={}, endDate={}", startDate, endDate);

        try {
            Instant startInstant = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant();
            Instant endInstant = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant();

            Map<String, Long> statistics = new HashMap<>();

            // 统计各种优先级
            for (Notification.Priority priority : Notification.Priority.values()) {
                Long count = notificationRepository.countByPriorityAndCreatedAtBetween(
                    priority, startInstant, endInstant);
                statistics.put(priority.name(), count != null ? count : 0L);
            }

            return statistics;

        } catch (Exception e) {
            log.error("获取按优先级统计失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            // 返回默认值
            return Arrays.stream(Notification.Priority.values())
                .collect(Collectors.toMap(Enum::name, priority -> 0L));
        }
    }

    /**
     * 获取时间序列数据
     */
    private List<TimeSeriesData> getTimeSeriesData(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取时间序列数据: startDate={}, endDate={}", startDate, endDate);

        try {
            List<TimeSeriesData> timeSeriesData = new ArrayList<>();

            // 按天分组统计
            LocalDateTime current = startDate.toLocalDate().atStartOfDay();
            while (!current.isAfter(endDate)) {
                LocalDateTime dayStart = current;
                LocalDateTime dayEnd = current.plusDays(1).minusSeconds(1);

                Instant startInstant = dayStart.atZone(java.time.ZoneId.systemDefault()).toInstant();
                Instant endInstant = dayEnd.atZone(java.time.ZoneId.systemDefault()).toInstant();

                // 统计当天各种状态的通知数量
                Long sentCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    Notification.NotificationStatus.SENT, startInstant, endInstant);
                Long deliveredCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    Notification.NotificationStatus.DELIVERED, startInstant, endInstant);
                Long readCount = notificationRepository.countByIsReadTrueAndCreatedAtBetween(
                    startInstant, endInstant);
                Long failedCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    Notification.NotificationStatus.FAILED, startInstant, endInstant);

                TimeSeriesData data = TimeSeriesData.builder()
                    .timestamp(current)
                    .sentCount(sentCount != null ? sentCount : 0L)
                    .deliveredCount(deliveredCount != null ? deliveredCount : 0L)
                    .readCount(readCount != null ? readCount : 0L)
                    .failedCount(failedCount != null ? failedCount : 0L)
                    .build();

                timeSeriesData.add(data);
                current = current.plusDays(1);
            }

            return timeSeriesData;

        } catch (Exception e) {
            log.error("获取时间序列数据失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取点击通知数量统计
     */
    private Long getClickedNotificationCount(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取点击通知数量: startDate={}, endDate={}", startDate, endDate);

        try {
            Instant startInstant = startDate.atZone(java.time.ZoneId.systemDefault()).toInstant();
            Instant endInstant = endDate.atZone(java.time.ZoneId.systemDefault()).toInstant();

            // 这里可以通过查询通知数据中的点击记录来统计
            // 暂时返回0，后续可以根据实际需求实现
            return 0L;

        } catch (Exception e) {
            log.error("获取点击通知数量失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 按渠道统计
     */
    private Map<String, Long> getStatisticsByChannel(LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("获取按渠道统计数据: startDate={}, endDate={}", startDate, endDate);

        try {
            // 这里可以根据通知的发送渠道进行统计
            // 暂时返回默认值，后续可以根据实际需求实现
            Map<String, Long> statistics = new HashMap<>();
            statistics.put("PUSH", 0L);
            statistics.put("EMAIL", 0L);
            statistics.put("SMS", 0L);
            statistics.put("WEBSOCKET", 0L);

            return statistics;

        } catch (Exception e) {
            log.error("获取按渠道统计失败: startDate={}, endDate={}, error={}",
                     startDate, endDate, e.getMessage(), e);
            return Map.of(
                "PUSH", 0L,
                "EMAIL", 0L,
                "SMS", 0L,
                "WEBSOCKET", 0L
            );
        }
    }

    @Override
    @Transactional
    public void sendSystemNotificationToUser(Long userId, String title, String content, Map<String, String> data) {
        log.info("发送系统通知给用户: userId={}, title={}", userId, title);

        // 开始性能监控
        var processingTimer = metrics.startNotificationProcessing();

        try {
            // 1. 安全验证
            var securityResult = securityService.validateNotificationContent(title, content, data);
            if (!securityResult.isValid()) {
                log.warn("系统通知安全验证失败: userId={}, errors={}", userId, securityResult.getErrors());
                metrics.recordNotificationFailed("SYSTEM_NOTIFICATION", "SECURITY_VALIDATION_FAILED");
                return;
            }

            // 2. 频率限制检查
            if (!rateLimitService.canSendNotification(userId, "SYSTEM_NOTIFICATION")) {
                log.debug("系统通知频率限制: userId={}", userId);
                metrics.recordRateLimitHit("USER_RATE_LIMIT");
                return;
            }

            // 3. 检查用户通知设置
            if (!isNotificationEnabled(userId, "SYSTEM")) {
                log.debug("用户已关闭系统通知: userId={}", userId);
                return;
            }

            // 4. 脱敏处理
            String sanitizedContent = securityService.sanitizeSensitiveInfo(content);

            // 5. 创建系统通知记录
            Map<String, Object> notificationData = data != null ? new HashMap<>(data) : new HashMap<>();
            Notification notification = createNotification(userId, title, sanitizedContent, "SYSTEM", notificationData);
            notificationRepository.save(notification);

            // 6. 记录频率限制
            rateLimitService.recordNotificationSent(userId, "SYSTEM_NOTIFICATION");

            // 7. 发送实时WebSocket通知
            if (pushNotificationService.isUserOnline(userId)) {
                var deliveryTimer = metrics.startWebsocketDelivery();
                try {
                    Map<String, Object> payload = Map.of(
                        "type", "SYSTEM_NOTIFICATION",
                        "title", title,
                        "content", sanitizedContent,
                        "data", data != null ? data : Map.of(),
                        "timestamp", System.currentTimeMillis()
                    );
                    pushNotificationService.sendRealtimeMessage(userId, "SYSTEM_NOTIFICATION", payload);
                    metrics.finishWebsocketDelivery(deliveryTimer);
                } catch (Exception e) {
                    metrics.finishWebsocketDelivery(deliveryTimer);
                    throw e;
                }
            }

            // 8. 记录成功指标
            metrics.recordNotificationSent("SYSTEM_NOTIFICATION");
            metrics.finishNotificationProcessing(processingTimer);

            log.debug("系统通知发送成功: userId={}, notificationId={}", userId, notification.getId());

        } catch (Exception e) {
            metrics.recordNotificationFailed("SYSTEM_NOTIFICATION", "PROCESSING_ERROR");
            metrics.finishNotificationProcessing(processingTimer);
            log.error("发送系统通知失败: userId={}, title={}", userId, title, e);
            throw new RuntimeException("发送系统通知失败", e);
        }
    }
}
