package com.implatform.push.controller;

import com.implatform.proto.push.v1.*;
import com.implatform.proto.common.v1.ResponseStatus;
import com.implatform.push.service.OnlineStatusManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;

/**
 * 在线状态管理控制器 (Protobuf)
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/push/online-status")
@Tag(name = "在线状态管理", description = "用户在线状态的查询和管理 (Protobuf)")
public class OnlineStatusController {

    @Autowired
    private OnlineStatusManagementService onlineStatusManagementService;

    /**
     * 检查用户是否在线 (Protobuf)
     */
    @Operation(summary = "检查用户是否在线", description = "检查指定用户的在线状态 (Protobuf)")
    @PostMapping(value = "/check", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public IsUserOnlineResponse isUserOnline(@RequestBody IsUserOnlineRequest request) {
        
        try {
            log.info("检查用户在线状态: userId={}", request.getUserId());
            
            boolean isOnline = onlineStatusManagementService.isUserOnline(request.getUserId());
            
            return IsUserOnlineResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("检查用户在线状态成功")
                            .build())
                    .setIsOnline(isOnline)
                    .build();
                    
        } catch (Exception e) {
            log.error("检查用户在线状态失败: userId={}", request.getUserId(), e);
            return IsUserOnlineResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("检查用户在线状态失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }

    /**
     * 获取用户详细在线状态 (Protobuf)
     */
    @Operation(summary = "获取用户详细在线状态", description = "获取指定用户的详细在线状态信息 (Protobuf)")
    @PostMapping(value = "/details", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public GetUserOnlineStatusResponse getUserOnlineStatus(@RequestBody GetUserOnlineStatusRequest request) {
        
        try {
            log.info("获取用户详细在线状态: userId={}", request.getUserId());
            
            OnlineStatusManagementService.UserOnlineStatus status = onlineStatusManagementService.getUserOnlineStatus(request.getUserId());
            
            // 转换为protobuf格式
            UserOnlineStatus.Builder statusBuilder = UserOnlineStatus.newBuilder()
                    .setUserId(status.getUserId())
                    .setIsOnline(status.getIsOnline())
                    .setDeviceCount(status.getDeviceCount())
                    .setSessionCount(status.getSessionCount())
                    .setOnlineDuration(status.getOnlineDuration());
            
            // 添加活跃设备
            if (status.getActiveDevices() != null) {
                statusBuilder.addAllActiveDevices(status.getActiveDevices());
            }
            
            // 添加活跃会话
            if (status.getActiveSessions() != null) {
                for (Map<String, Object> session : status.getActiveSessions()) {
                    OnlineSession protobufSession = OnlineSession.newBuilder()
                            .setSessionId((String) session.get("sessionId"))
                            .setDeviceType((String) session.get("deviceType"))
                            .build();
                    statusBuilder.addActiveSessions(protobufSession);
                }
            }
            
            return GetUserOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("获取用户详细在线状态成功")
                            .build())
                    .setOnlineStatus(statusBuilder.build())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取用户详细在线状态失败: userId={}", request.getUserId(), e);
            return GetUserOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("获取用户详细在线状态失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }

    /**
     * 更新用户在线状态 (Protobuf)
     */
    @Operation(summary = "更新用户在线状态", description = "更新指定用户的在线状态 (Protobuf)")
    @PostMapping(value = "/update", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public UpdateUserOnlineStatusResponse updateUserOnlineStatus(@RequestBody UpdateUserOnlineStatusRequest request) {
        
        try {
            log.info("更新用户在线状态: userId={}, isOnline={}", request.getUserId(), request.getIsOnline());
            
            onlineStatusManagementService.updateUserOnlineStatus(request.getUserId(), request.getIsOnline());
            
            return UpdateUserOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("用户在线状态更新成功")
                            .build())
                    .build();
                    
        } catch (Exception e) {
            log.error("更新用户在线状态失败: userId={}, isOnline={}", request.getUserId(), request.getIsOnline(), e);
            return UpdateUserOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("更新用户在线状态失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }

    /**
     * 获取所有在线用户 (Protobuf)
     */
    @Operation(summary = "获取所有在线用户", description = "获取当前所有在线用户的ID列表 (Protobuf)")
    @PostMapping(value = "/all", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public GetAllOnlineUsersResponse getAllOnlineUsers(@RequestBody GetAllOnlineUsersRequest request) {
        
        try {
            log.info("获取所有在线用户");
            
            Set<String> onlineUsers = onlineStatusManagementService.getAllOnlineUsers();
            
            return GetAllOnlineUsersResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("获取在线用户列表成功")
                            .build())
                    .addAllOnlineUsers(onlineUsers)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
            return GetAllOnlineUsersResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("获取在线用户列表失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }

    /**
     * 获取在线用户数量 (Protobuf)
     */
    @Operation(summary = "获取在线用户数量", description = "获取当前在线用户的总数量 (Protobuf)")
    @PostMapping(value = "/count", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public GetOnlineUserCountResponse getOnlineUserCount(@RequestBody GetOnlineUserCountRequest request) {
        
        try {
            log.info("获取在线用户数量");
            
            long count = onlineStatusManagementService.getOnlineUserCount();
            
            return GetOnlineUserCountResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("获取在线用户数量成功")
                            .build())
                    .setCount(count)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return GetOnlineUserCountResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("获取在线用户数量失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }

    /**
     * 批量检查用户在线状态 (Protobuf)
     */
    @Operation(summary = "批量检查用户在线状态", description = "批量检查多个用户的在线状态 (Protobuf)")
    @PostMapping(value = "/batch-check", 
                consumes = "application/x-protobuf", 
                produces = "application/x-protobuf")
    public BatchCheckOnlineStatusResponse batchCheckOnlineStatus(@RequestBody BatchCheckOnlineStatusRequest request) {
        
        try {
            log.info("批量检查用户在线状态: userCount={}", request.getUserIdsList().size());
            
            Map<Long, Boolean> statusMap = onlineStatusManagementService.batchCheckOnlineStatus(request.getUserIdsList());
            
            return BatchCheckOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(0)
                            .setMessage("批量检查用户在线状态成功")
                            .build())
                    .putAllStatusMap(statusMap)
                    .build();
                    
        } catch (Exception e) {
            log.error("批量检查用户在线状态失败: userIds={}", request.getUserIdsList(), e);
            return BatchCheckOnlineStatusResponse.newBuilder()
                    .setStatus(ResponseStatus.newBuilder()
                            .setCode(500)
                            .setMessage("批量检查用户在线状态失败: " + e.getMessage())
                            .build())
                    .build();
        }
    }
}
