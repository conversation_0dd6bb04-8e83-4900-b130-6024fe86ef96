package com.implatform.notification.controller.admin;

import com.implatform.common.core.domain.Result;
import com.implatform.common.core.enums.NotificationErrorCode;
import com.implatform.common.core.exception.BusinessException;
import com.implatform.common.webflux.response.PageResult;
import com.implatform.notification.dto.notification.NotificationRequestDTO;
import com.implatform.notification.dto.notification.NotificationResponseDTO;
import com.implatform.notification.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知管理Controller - Admin接口
 *
 * <p>提供完整的通知管理功能，包括：</p>
 * <ul>
 *   <li>通知的CRUD操作</li>
 *   <li>批量操作和重试机制</li>
 *   <li>统计和分析功能</li>
 *   <li>系统通知发送</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/notifications/admin")
@RequiredArgsConstructor
@Validated
@Tag(name = "通知管理", description = "通知管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class AdminNotificationController {

    private final NotificationService notificationService;

    // ==================== 基础CRUD操作 ====================

    @Operation(summary = "获取通知列表", description = "分页获取通知列表，支持搜索和过滤")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping
    public Result<PageResult<NotificationResponseDTO.NotificationListResponse>> getNotifications(
            @Parameter(description = "查询参数") @RequestParam Map<String, Object> params,
            Pageable pageable) {
        log.info("获取通知列表: params={}", params);
        try {
            // 构建查询请求
            NotificationRequestDTO.GetUserNotificationsRequest request =
                new NotificationRequestDTO.GetUserNotificationsRequest();
            // Admin可以查看所有通知，不限制用户ID

            PageResult<NotificationResponseDTO.NotificationListResponse> notifications =
                notificationService.getUserNotifications(null, request);
            return Result.success(notifications);
        } catch (BusinessException e) {
            log.error("获取通知列表失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        } catch (Exception e) {
            log.error("获取通知列表失败", e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Operation(summary = "根据ID获取通知", description = "根据通知ID获取通知详情")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<NotificationResponseDTO.NotificationDetailResponse> getNotificationById(
            @Parameter(description = "通知ID") @PathVariable @NotNull Long id) {
        log.info("获取通知详情: id={}", id);
        try {
            NotificationResponseDTO.NotificationDetailResponse notification =
                notificationService.getNotificationDetail(id, null); // Admin可以查看任何通知
            return Result.success(notification);
        } catch (BusinessException e) {
            log.error("获取通知详情失败: id={}, error={}", id, e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND);
        } catch (Exception e) {
            log.error("获取通知详情失败: id={}", id, e);
            return Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND);
        }
    }

    @Operation(summary = "创建通知", description = "创建新的通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping
    public Result<NotificationResponseDTO.NotificationResponse> createNotification(
            @Valid @RequestBody NotificationRequestDTO.SendNotificationRequest request) {
        log.info("创建通知: request={}", request);
        try {
            NotificationResponseDTO.NotificationResponse notification =
                notificationService.sendNotification(null, request); // Admin发送，senderId为null
            return Result.success(notification);
        } catch (BusinessException e) {
            log.error("创建通知失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        } catch (Exception e) {
            log.error("创建通知失败", e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Operation(summary = "更新通知", description = "更新通知信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public Result<NotificationResponseDTO.NotificationDetailResponse> updateNotification(
            @Parameter(description = "通知ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody Map<String, Object> request) {
        log.info("更新通知: id={}, request={}", id, request);
        try {
            // 先获取通知详情
            NotificationResponseDTO.NotificationDetailResponse notification =
                notificationService.getNotificationDetail(id, null);
            if (notification == null) {
                return Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND);
            }
            // TODO: 实现更新逻辑，暂时返回原通知
            log.warn("通知更新功能待实现: id={}", id);
            return Result.success(notification);
        } catch (BusinessException e) {
            log.error("更新通知失败: id={}, error={}", id, e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        } catch (Exception e) {
            log.error("更新通知失败: id={}", id, e);
            return Result.error(NotificationErrorCode.DATABASE_UPDATE_FAILED);
        }
    }

    @Operation(summary = "删除通知", description = "删除指定通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{id}")
    public Result<Void> deleteNotification(
            @Parameter(description = "通知ID") @PathVariable @NotNull Long id) {
        log.info("删除通知: id={}", id);
        try {
            NotificationRequestDTO.DeleteNotificationRequest request =
                new NotificationRequestDTO.DeleteNotificationRequest();
            request.setNotificationId(id);

            notificationService.deleteNotification(null, request); // Admin可以删除任何通知
            return Result.success();
        } catch (BusinessException e) {
            log.error("删除通知失败: id={}, error={}", id, e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_DELETE_FAILED);
        } catch (Exception e) {
            log.error("删除通知失败: id={}", id, e);
            return Result.error(NotificationErrorCode.DATABASE_DELETE_FAILED);
        }
    }

    @Operation(summary = "批量删除通知", description = "批量删除多个通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/batch")
    public Result<Void> deleteNotifications(
            @RequestBody @NotEmpty List<@NotNull Long> ids) {
        log.info("批量删除通知: ids={}", ids);
        try {
            NotificationRequestDTO.BatchDeleteNotificationsRequest request =
                new NotificationRequestDTO.BatchDeleteNotificationsRequest();
            request.setNotificationIds(ids);

            notificationService.batchDeleteNotifications(null, request); // Admin可以删除任何通知
            return Result.success();
        } catch (BusinessException e) {
            log.error("批量删除通知失败: ids={}, error={}", ids, e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_DELETE_FAILED);
        } catch (Exception e) {
            log.error("批量删除通知失败: ids={}", ids, e);
            return Result.error(NotificationErrorCode.DATABASE_DELETE_FAILED);
        }
    }

    // ==================== 重试和恢复操作 ====================

    @Operation(summary = "重试发送通知", description = "重试发送失败的通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重试成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "400", description = "通知状态不允许重试"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/{id}/retry")
    public Result<Void> retryNotification(
            @Parameter(description = "通知ID") @PathVariable @NotNull Long id) {
        log.info("重试发送通知: id={}", id);
        try {
            NotificationRequestDTO.RetryNotificationRequest request =
                NotificationRequestDTO.RetryNotificationRequest.builder()
                    .notificationId(id)
                    .reason("Admin手动重试")
                    .build();

            notificationService.retryNotification(null, request);
            return Result.success();
        } catch (BusinessException e) {
            log.error("重试发送通知失败: id={}, error={}", id, e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        } catch (Exception e) {
            log.error("重试发送通知失败: id={}", id, e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    @Operation(summary = "批量重试通知", description = "批量重试发送失败的通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重试成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/batch-retry")
    public Result<NotificationResponseDTO.BatchOperationResponse> batchRetryNotifications(
            @RequestBody @NotEmpty List<@NotNull Long> ids) {
        log.info("批量重试通知: ids={}", ids);
        try {
            NotificationRequestDTO.BatchRetryNotificationsRequest request =
                NotificationRequestDTO.BatchRetryNotificationsRequest.builder()
                    .notificationIds(ids)
                    .reason("Admin批量重试")
                    .build();

            NotificationResponseDTO.BatchOperationResponse result =
                notificationService.batchRetryNotifications(null, request);
            return Result.success(result);
        } catch (BusinessException e) {
            log.error("批量重试通知失败: ids={}, error={}", ids, e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        } catch (Exception e) {
            log.error("批量重试通知失败: ids={}", ids, e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    // ==================== 系统通知发送 ====================

    @Operation(summary = "发送系统通知", description = "发送系统广播通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/system")
    public Result<NotificationResponseDTO.NotificationResponse> sendSystemNotification(
            @Valid @RequestBody Map<String, Object> request) {
        log.info("发送系统通知: request={}", request);
        try {
            // 构建系统通知请求
            NotificationRequestDTO.SendNotificationRequest notificationRequest =
                new NotificationRequestDTO.SendNotificationRequest();
            notificationRequest.setTitle((String) request.get("title"));
            notificationRequest.setContent((String) request.get("content"));
            notificationRequest.setNotificationType("SYSTEM");
            notificationRequest.setPriority("HIGH");
            notificationRequest.setRecipientIds(List.of()); // 系统通知，暂时设为空列表

            NotificationResponseDTO.NotificationResponse notification =
                notificationService.sendNotification(null, notificationRequest);
            return Result.success(notification);
        } catch (BusinessException e) {
            log.error("发送系统通知失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        } catch (Exception e) {
            log.error("发送系统通知失败", e);
            return Result.error(NotificationErrorCode.NOTIFICATION_SEND_FAILED);
        }
    }

    // ==================== 查询和统计功能 ====================

    @Operation(summary = "获取用户通知", description = "获取指定用户的通知列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/user/{userId}")
    public Result<PageResult<NotificationResponseDTO.NotificationListResponse>> getUserNotifications(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId,
            Pageable pageable) {
        log.info("获取用户通知: userId={}", userId);
        try {
            NotificationRequestDTO.GetUserNotificationsRequest request =
                new NotificationRequestDTO.GetUserNotificationsRequest();

            PageResult<NotificationResponseDTO.NotificationListResponse> notifications =
                notificationService.getUserNotifications(userId, request);
            return Result.success(notifications);
        } catch (BusinessException e) {
            log.error("获取用户通知失败: userId={}, error={}", userId, e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        } catch (Exception e) {
            log.error("获取用户通知失败: userId={}", userId, e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Operation(summary = "搜索通知", description = "根据关键词搜索通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "搜索成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/search")
    public Result<PageResult<NotificationResponseDTO.NotificationListResponse>> searchNotifications(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "通知类型") @RequestParam(required = false) String type,
            Pageable pageable) {
        log.info("搜索通知: keyword={}, type={}", keyword, type);
        try {
            NotificationRequestDTO.SearchNotificationsRequest request =
                new NotificationRequestDTO.SearchNotificationsRequest();
            request.setKeyword(keyword);
            request.setType(type);

            PageResult<NotificationResponseDTO.NotificationListResponse> notifications =
                notificationService.searchNotifications(null, request);
            return Result.success(notifications);
        } catch (BusinessException e) {
            log.error("搜索通知失败: keyword={}, error={}", keyword, e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        } catch (Exception e) {
            log.error("搜索通知失败: keyword={}", keyword, e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Operation(summary = "获取通知统计", description = "获取通知相关统计数据")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/statistics")
    public Result<NotificationResponseDTO.NotificationStatisticsResponse> getNotificationStatistics(
            @Parameter(description = "开始日期") @RequestParam(required = false) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) LocalDateTime endDate) {
        log.info("获取通知统计: startDate={}, endDate={}", startDate, endDate);
        try {
            NotificationRequestDTO.GetNotificationStatisticsRequest request =
                new NotificationRequestDTO.GetNotificationStatisticsRequest();
            request.setStartDate(startDate);
            request.setEndDate(endDate);

            NotificationResponseDTO.NotificationStatisticsResponse statistics =
                notificationService.getNotificationStatistics(null, request);
            return Result.success(statistics);
        } catch (BusinessException e) {
            log.error("获取通知统计失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.STATISTICS_NOT_AVAILABLE);
        } catch (Exception e) {
            log.error("获取通知统计失败", e);
            return Result.error(NotificationErrorCode.STATISTICS_NOT_AVAILABLE);
        }
    }

    @Operation(summary = "获取失败通知", description = "获取发送失败的通知列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/failed")
    public Result<PageResult<NotificationResponseDTO.NotificationListResponse>> getFailedNotifications(Pageable pageable) {
        log.info("获取失败通知");
        try {
            NotificationRequestDTO.GetFailedNotificationsRequest request =
                new NotificationRequestDTO.GetFailedNotificationsRequest();

            PageResult<NotificationResponseDTO.NotificationListResponse> notifications =
                notificationService.getFailedNotifications(null, request);
            return Result.success(notifications);
        } catch (BusinessException e) {
            log.error("获取失败通知失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        } catch (Exception e) {
            log.error("获取失败通知失败", e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Operation(summary = "获取待发送通知", description = "获取待发送的通知列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/pending")
    public Result<PageResult<NotificationResponseDTO.NotificationListResponse>> getPendingNotifications(Pageable pageable) {
        log.info("获取待发送通知");
        try {
            NotificationRequestDTO.GetPendingNotificationsRequest request =
                new NotificationRequestDTO.GetPendingNotificationsRequest();

            PageResult<NotificationResponseDTO.NotificationListResponse> notifications =
                notificationService.getPendingNotifications(null, request);
            return Result.success(notifications);
        } catch (BusinessException e) {
            log.error("获取待发送通知失败: {}", e.getMessage(), e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        } catch (Exception e) {
            log.error("获取待发送通知失败", e);
            return Result.error(NotificationErrorCode.DATABASE_QUERY_FAILED);
        }
    }

    @Operation(summary = "取消通知", description = "取消待发送的通知")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "取消成功"),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "400", description = "通知状态不允许取消"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/{id}/cancel")
    public Result<Void> cancelNotification(
            @Parameter(description = "通知ID") @PathVariable @NotNull Long id) {
        log.info("取消通知: id={}", id);
        try {
            NotificationRequestDTO.CancelNotificationRequest request =
                new NotificationRequestDTO.CancelNotificationRequest();
            request.setNotificationId(id);

            notificationService.cancelNotification(null, request);
            return Result.success();
        } catch (BusinessException e) {
            log.error("取消通知失败: id={}, error={}", id, e.getMessage(), e);
            return Result.error(NotificationErrorCode.NOTIFICATION_ALREADY_CANCELLED);
        } catch (Exception e) {
            log.error("取消通知失败: id={}", id, e);
            return Result.error(NotificationErrorCode.NOTIFICATION_NOT_FOUND);
        }
    }
}
