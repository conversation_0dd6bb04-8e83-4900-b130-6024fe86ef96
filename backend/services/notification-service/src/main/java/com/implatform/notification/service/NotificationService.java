package com.implatform.notification.service;

import com.implatform.common.webflux.response.PageResult;
import com.implatform.notification.dto.notification.NotificationRequestDTO.*;
import com.implatform.notification.dto.notification.NotificationResponseDTO.*;

import java.util.List;
import java.util.Map;

/**
 * 通知服务接口
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface NotificationService {
    
    /**
     * 发送单个用户通知
     * 
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     */
    void sendNotification(Long userId, String title, String content, String type);
    
    /**
     * 发送批量用户通知
     * 
     * @param userIds 用户ID列表
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     */
    void sendBatchNotification(List<Long> userIds, String title, String content, String type);
    
    /**
     * 发送消息通知
     * 
     * @param userId 用户ID
     * @param messageId 消息ID
     * @param senderName 发送者名称
     * @param content 消息内容
     */
    void sendMessageNotification(Long userId, Long messageId, String senderName, String content);
    
    /**
     * 发送系统通知
     * 
     * @param userId 用户ID
     * @param content 通知内容
     */
    void sendSystemNotification(Long userId, String content);
    
    /**
     * 发送好友请求通知
     * 
     * @param userId 用户ID
     * @param requesterId 请求者ID
     * @param requesterName 请求者名称
     */
    void sendFriendRequestNotification(Long userId, Long requesterId, String requesterName);
    
    /**
     * 发送群组邀请通知
     * 
     * @param userId 用户ID
     * @param groupId 群组ID
     * @param groupName 群组名称
     * @param inviterName 邀请者名称
     */
    void sendGroupInviteNotification(Long userId, Long groupId, String groupName, String inviterName);
    
    /**
     * 发送频道通知
     * 
     * @param channelId 频道ID
     * @param title 通知标题
     * @param content 通知内容
     */
    void sendChannelNotification(Long channelId, String title, String content);
    
    /**
     * 发送自毁消息通知
     *
     * @param userId 用户ID
     * @param messageId 消息ID
     */
    void sendSelfDestructNotification(Long userId, Long messageId);

    /**
     * 发送广播消息通知
     *
     * @param broadcastId 广播ID
     * @param title 通知标题
     * @param content 通知内容
     * @param targetUserIds 目标用户ID列表
     */
    void sendBroadcastNotification(Long broadcastId, String title, String content, List<Long> targetUserIds);

    /**
     * 发送消息阅读并销毁通知
     *
     * @param senderId 发送者ID
     * @param messageId 消息ID
     * @param readerId 阅读者ID
     */
    void sendMessageReadAndDestructedNotification(Long senderId, Long messageId, Long readerId);

    /**
     * 发送消息销毁通知
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param messageId 消息ID
     * @param reason 销毁原因
     */
    void sendMessageDestructedNotification(Long senderId, Long receiverId, Long messageId, Object reason);

    /**
     * 发送频道消息通知
     *
     * @param userId 用户ID
     * @param message 频道消息
     */
    void sendChannelMessageNotification(Long userId, Object message);

    /**
     * 发送频道订阅通知
     *
     * @param channelId 频道ID
     * @param userId 用户ID
     */
    void sendChannelSubscriptionNotification(Long channelId, Long userId);

    /**
     * 发送频道管理员通知
     *
     * @param channelId 频道ID
     * @param userId 用户ID
     */
    void sendChannelAdminNotification(Long channelId, Long userId);

    // ==================== Controller需要的新方法 ====================

    /**
     * 发送通知（Controller使用）
     *
     * @param senderId 发送者ID
     * @param request 发送通知请求
     * @return 通知响应
     */
    NotificationResponse sendNotification(Long senderId, SendNotificationRequest request);

    /**
     * 批量发送通知（Controller使用）
     *
     * @param senderId 发送者ID
     * @param request 批量发送通知请求
     * @return 批量通知响应
     */
    BatchNotificationResponse batchSendNotifications(Long senderId, BatchSendNotificationRequest request);

    /**
     * 获取用户通知列表（Controller使用）
     *
     * @param userId 用户ID
     * @param request 获取用户通知请求
     * @return 通知列表响应
     */
    PageResult<NotificationListResponse> getUserNotifications(Long userId, GetUserNotificationsRequest request);

    /**
     * 获取通知详情（Controller使用）
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 通知详情响应
     */
    NotificationDetailResponse getNotificationDetail(Long notificationId, Long userId);

    /**
     * 标记通知为已读（Controller使用）
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     */
    void markNotificationAsRead(Long notificationId, Long userId);

    /**
     * 批量标记为已读（Controller使用）
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 批量已读响应
     */
    BatchReadResponse batchMarkAsRead(List<Long> notificationIds, Long userId);

    /**
     * 标记所有通知为已读（Controller使用）
     *
     * @param userId 用户ID
     */
    void markAllNotificationsAsRead(Long userId);

    /**
     * 删除通知（Controller使用）
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     */
    void deleteNotification(Long notificationId, Long userId);

    /**
     * 批量删除通知（Controller使用）
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 批量删除响应
     */
    BatchDeleteResponse batchDeleteNotifications(List<Long> notificationIds, Long userId);

    /**
     * 获取通知设置（Controller使用）
     *
     * @param userId 用户ID
     * @return 通知设置响应
     */
    NotificationSettingsResponse getNotificationSettings(Long userId);

    /**
     * 更新通知设置（Controller使用）
     *
     * @param userId 用户ID
     * @param request 更新通知设置请求
     * @return 通知设置响应
     */
    NotificationSettingsResponse updateNotificationSettings(Long userId, UpdateNotificationSettingsRequest request);

    /**
     * 获取未读通知数量（Controller使用）
     *
     * @param userId 用户ID
     * @return 未读数量响应
     */
    UnreadCountResponse getUnreadNotificationCount(Long userId);

    /**
     * 获取通知统计信息（Controller使用）
     *
     * @param days 统计天数
     * @return 统计信息
     */
    Map<String, Object> getNotificationStatistics(int days);

    /**
     * 测试通知（Controller使用）
     *
     * @param request 测试通知请求
     * @param userId 用户ID
     * @return 通知测试响应
     */
    NotificationTestResponse testNotification(TestNotificationRequest request, Long userId);

    // ==================== Push通知相关方法 ====================

    /**
     * 发送Push通知
     *
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param data 附加数据
     */
    void sendPushNotification(Long userId, String title, String content, Map<String, Object> data);

    /**
     * 批量发送Push通知
     *
     * @param userIds 用户ID列表
     * @param title 标题
     * @param content 内容
     * @param data 附加数据
     */
    void batchSendPushNotifications(List<Long> userIds, String title, String content, Map<String, Object> data);

    /**
     * 发送实时消息通知（通过realtime-service）
     *
     * @param userId 用户ID
     * @param messageType 消息类型
     * @param payload 消息载荷
     */
    void sendRealtimeNotification(Long userId, String messageType, Object payload);

    // ==================== Admin管理功能 ====================

    /**
     * 重试发送通知
     *
     * @param adminId 管理员ID
     * @param request 重试请求
     */
    void retryNotification(Long adminId, RetryNotificationRequest request);

    /**
     * 批量重试通知
     *
     * @param adminId 管理员ID
     * @param request 批量重试请求
     * @return 批量操作响应
     */
    BatchOperationResponse batchRetryNotifications(Long adminId, BatchRetryNotificationsRequest request);

    /**
     * 搜索通知
     *
     * @param adminId 管理员ID
     * @param request 搜索请求
     * @return 搜索结果
     */
    PageResult<NotificationListResponse> searchNotifications(Long adminId, SearchNotificationsRequest request);

    /**
     * 获取通知统计
     *
     * @param adminId 管理员ID
     * @param request 统计请求
     * @return 统计响应
     */
    NotificationStatisticsResponse getNotificationStatistics(Long adminId, GetNotificationStatisticsRequest request);

    /**
     * 获取失败通知
     *
     * @param adminId 管理员ID
     * @param request 获取失败通知请求
     * @return 失败通知列表
     */
    PageResult<NotificationListResponse> getFailedNotifications(Long adminId, GetFailedNotificationsRequest request);

    /**
     * 获取待发送通知
     *
     * @param adminId 管理员ID
     * @param request 获取待发送通知请求
     * @return 待发送通知列表
     */
    PageResult<NotificationListResponse> getPendingNotifications(Long adminId, GetPendingNotificationsRequest request);

    /**
     * 取消通知
     *
     * @param adminId 管理员ID
     * @param request 取消通知请求
     */
    void cancelNotification(Long adminId, CancelNotificationRequest request);

    /**
     * 删除通知（Admin使用）
     *
     * @param adminId 管理员ID
     * @param request 删除请求
     */
    void deleteNotification(Long adminId, DeleteNotificationRequest request);

    /**
     * 批量删除通知（Admin使用）
     *
     * @param adminId 管理员ID
     * @param request 批量删除请求
     * @return 批量删除响应
     */
    BatchDeleteResponse batchDeleteNotifications(Long adminId, BatchDeleteNotificationsRequest request);

    /**
     * 发送系统通知给指定用户
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param data 扩展数据
     */
    void sendSystemNotificationToUser(Long userId, String title, String content, Map<String, String> data);
}
